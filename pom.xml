<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.extracme.evcard</groupId>
		<artifactId>evcard</artifactId>
		<version>3.0.1</version>
	</parent>
	<version>2.2.68</version>
	<artifactId>evcard-vipcard-rpc</artifactId>
	<packaging>pom</packaging>
	<modules>
		<module>evcard-vipcard-service-api</module>
		<module>evcard-vipcard-service</module>
	</modules>
	<dependencies>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.83</version>
		</dependency>
	</dependencies>

	<repositories>
		<repository>
			<id>nexus</id>
			<name>Nexus</name>
			<url>http://artifactory.evcard.vip:8081/artifactory/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>



	<pluginRepositories>
		<pluginRepository>
			<id>nexus</id>
			<name>Nexus</name>
			<url>http://artifactory.evcard.vip:8081/artifactory/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

</project>
