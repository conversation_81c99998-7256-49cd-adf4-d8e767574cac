<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.extracme.evcard</groupId>
        <artifactId>evcard-vipcard-rpc</artifactId>
        <version>2.2.68</version>
    </parent>
    <artifactId>evcard-vipcard-service</artifactId>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <pagehelper.version>4.1.1</pagehelper.version>
        <jedis.version>2.9.0</jedis.version>
    </properties>

    <dependencies>
        <!--引入spring-boot-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.google.guava</groupId>-->
<!--            <artifactId>guava</artifactId>-->
<!--            <version>28.2-jre</version>-->
<!--        </dependency>-->
        <!-- 指定guava版本，解决elasticJob 和swagger 依赖版本冲突 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
        </dependency>
        <dependency>
            <groupId>com.extracme.evcard</groupId>
            <artifactId>evcard-vipcard-service-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!--引入spring-boot mybatis相关-->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.2.13</version>
        </dependency>
        <!--		<dependency>-->
        <!--			<groupId>org.mybatis</groupId>-->
        <!--			<artifactId>mybatis</artifactId>-->
        <!--			<version>3.3.1</version>-->
        <!--		</dependency>-->
        <!--		<dependency>-->
        <!--			<groupId>org.mybatis</groupId>-->
        <!--			<artifactId>mybatis-spring</artifactId>-->
        <!--		</dependency>-->
        <!--		<dependency>-->
        <!--			<groupId>com.github.pagehelper</groupId>-->
        <!--			<artifactId>pagehelper</artifactId>-->
        <!--			<version>${pagehelper.version}</version>-->
        <!--		</dependency>-->

        <!--新版本redis工具包-->
        <dependency>
            <groupId>com.extracme.evcard</groupId>
            <artifactId>evcard-redis</artifactId>
            <version>2.7.0</version>
        </dependency>

        <!--apollo配置中心-->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>1.8.0</version>
        </dependency>

        <!-- dubbo&zk-->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-registry-zookeeper</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-recipes</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>

        <!-- 阿里云消息队列 -->
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
            <version>1.8.8.Final</version>
        </dependency>
        <dependency>
            <groupId>com.extracme.evcard</groupId>
            <artifactId>evcard-protobuf</artifactId>
            <version>2.0.0</version>
        </dependency>


        <!--<dependency>
            <groupId>com.dangdang</groupId>
            <artifactId>elastic-job-lite-spring</artifactId>
            <version>2.1.5-dubbo</version>
        </dependency> -->
        <!-- 引入evcard-elastic-job boot版本工具包 & 解决dubbo、elasticjob curator包冲突 -->
        <dependency>
            <groupId>com.extracme.evcard</groupId>
            <artifactId>evcard-elastic-job</artifactId>
            <version>1.1.0</version>
        </dependency>


        <!-- 依赖的evcard服务 -->
        <dependency>
            <groupId>com.extracme.evcard</groupId>
            <artifactId>evcard-vehicle-service-api</artifactId>
            <version>2.26.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.extracme.evcard</groupId>
                    <artifactId>evcard-sts-service-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.extracme.evcard</groupId>
            <artifactId>evcard-order-service-api</artifactId>
            <version>2.31.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>evcard-sts-service-provider-api</artifactId>
                    <groupId>com.extracme.evcard</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.extracme.evcard</groupId>
            <artifactId>evcard-messagepush-service-api</artifactId>
            <version>2.6.3</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <!-- 会员服务 -->
        <dependency>
            <groupId>com.extracme.evcard</groupId>
            <artifactId>evcard-membership-service-api</artifactId>
            <version>4.12.50</version>
        </dependency>
        <dependency>
            <groupId>com.extracme.evcard</groupId>
            <artifactId>evcard-bvm-service-api</artifactId>
            <version>3.0.11</version>
        </dependency>
        <dependency>
            <groupId>com.extracme.evcard</groupId>
            <artifactId>evcard-pay-service-api</artifactId>
            <version>3.7.6</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>net.sourceforge.javacsv</groupId>
            <artifactId>javacsv</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.5.0</version>
        </dependency>

        <dependency>
            <groupId>hq.aesras.example</groupId>
            <artifactId>AesRsaDemo</artifactId>
            <version>1.0.8</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>evcard-vipcard-rpc</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.12.RELEASE</version>
                <configuration>
                    <mainClass>com.extracme.evcard.rpc.vipcard.App</mainClass>
                    <outputDirectory>../target</outputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <!--配置文件的路径-->
                    <configurationFile>${basedir}/src/main/resources/generatorMybatisConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
                <!-- 是否替换资源中的属性 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- <includes> <include>**/*.properties</include> <include>**/*.xml</include>
                    </includes> <filtering>true</filtering> -->
            </resource>
        </resources>
    </build>
</project>
