package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.vipcard.App;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR> chennian.
 * @Date ：Created in 10:32 2020/12/28
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class CardSalesActivityConfigServiceTest {

    @Autowired
    private ICardSalesActivityConfigService cardSalesActivityConfigService;

    @Test
    public void add() throws Exception {
        CardActivityConfigDto configDto = new CardActivityConfigDto();
        configDto.setOrgId("000T");
        configDto.setCardId(100006L);
        //configDto.setStartTime(DateUtil.addDay(new Date(), 1));
        //configDto.setEndTime(DateUtil.addDay(new Date(),7));
        configDto.setActivityName("测试新增活动");
        //configDto.setAdvanceNoticeTime(DateUtil.addMin(new Date(), 60*18));
        configDto.setPersonPurchasesLimit(5);
        configDto.setSalesPrice(new BigDecimal(99.80));
        configDto.setUnderlinePrice(new BigDecimal(199.80));
        configDto.setStock(10000L);
        configDto.setRules("xxx");
        cardSalesActivityConfigService.add(configDto, OperatorDto.getSystemOp());
    }

    @Test
    public void update() throws Exception {
        //运营公司不可修改
        CardActivityConfigDto configDto = new CardActivityConfigDto();
        configDto.setId(100008L);
        configDto.setActivityName("测试月卡333");
        configDto.setOrgId("00");
        configDto.setCardId(100033L);
        configDto.setSalesPrice(new BigDecimal(60));
        configDto.setUnderlinePrice(new BigDecimal(180));
        configDto.setStartTime("20210201110000");
        configDto.setEndTime("20210231240000");
        configDto.setAdvanceNoticeTime("20210201100000");
        configDto.setPersonPurchasesLimit(5);
        configDto.setSalesPrice(new BigDecimal(99.80));
        configDto.setUnderlinePrice(new BigDecimal(199.80));
        configDto.setStock(50000L);
        configDto.setRules("xxx");
        cardSalesActivityConfigService.update(configDto, OperatorDto.getSystemOp());
    }

    @Test
    public void info() throws Exception {
        cardSalesActivityConfigService.getDetail(100003L);
    }

    @Test
    public void list() throws Exception {
        //运营公司不可修改
        CardActivityQueryDto queryDto = new CardActivityQueryDto();
        queryDto.setOrgId("000T");
        //queryDto.setId(100003L);
        //queryDto.setCardId(100006L);
        cardSalesActivityConfigService.queryPage(queryDto);
        queryDto.setOrgId("000T");
        queryDto.setId(100003L);
        queryDto.setCardId(10000L);
        cardSalesActivityConfigService.queryPage(queryDto);
    }

    @Test
    public void delete() throws Exception {
        cardSalesActivityConfigService.delete(100003L, OperatorDto.getSystemOp());
    }
    @Test
    public void publish() throws Exception {
        cardSalesActivityConfigService.publish(100421L, OperatorDto.getSystemOp());
    }
    @Test
    public void start() throws Exception {
        cardSalesActivityConfigService.start(100003L, OperatorDto.getSystemOp());
    }
    @Test
    public void stop() throws Exception {
        cardSalesActivityConfigService.delete(100003L, OperatorDto.getSystemOp());
    }


}
