package com.extracme.evcard.rpc.vipcard.job;

import com.extracme.evcard.rpc.vipcard.App;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/2/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class SuiXiangCardPurchaseOrderCancelJobTest {

    @Resource
    private SuiXiangCardPurchaseOrderCancelJob suiXiangCardPurchaseOrderCancelJob;

    @Test
    public void testSuiXiangCardPurchaseOrderCancelJob() {
        suiXiangCardPurchaseOrderCancelJob.execute(null);
    }

}
