package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.App;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardBaseMapper;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardPriceMapper;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.pay.PayOrderBo;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.pay.PayOrderDto;
import com.extracme.evcard.rpc.vipcard.rest.MdRestClient;
import com.extracme.evcard.rpc.vipcard.rest.entity.CancelPayOrderRes;
import com.extracme.framework.core.bo.PageBeanBO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.apache.ibatis.ognl.DynamicSubscript.mid;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class ISuixiangCardSalesServiceTest {

    @Resource
    private SuixiangCardPriceMapper suixiangCardPriceMapper;

    @Resource
    private SuixiangCardBaseMapper suixiangCardBaseMapper;
    @Resource
    ISuiXiangCardService suiXiangCardService;


    @Resource
    private ISuixiangCardSalesService suixiangCardSalesService;

    @Test
    public void test() throws Exception {
        List<SuixiangCardInfoDto> suixiangCardInfoDtos = suixiangCardPriceMapper.selectInfoByBaseId(3L);
        SuixiangCardInfoDto suixiangCardInfoDto = suixiangCardPriceMapper.selectInfoByPriceId(4L);
        List<Long> priceIds = new ArrayList<>();
        priceIds.add(4L);
        List<SuixiangCardInfoDto> suixiangCardInfoDtos1 = suixiangCardPriceMapper.selectInfoListByPriceIds(priceIds);

        List<SuixiangCardInfoDto> listByCityIdAndCarModelId = suixiangCardPriceMapper.getListByCityIdAndCarModelId(110100,22);

        System.out.println("value");
    }


    @Test
    public void testgetSuiXiangCardDetail() throws BusinessException {
        SuiXiangCardUseQueryDto queryDto = new SuiXiangCardUseQueryDto();
        queryDto.setUserId(9458962L);
        queryDto.setUserCardStatus(-1);
        queryDto.setPageNum(-1);
        queryDto.setPageSize(-1);
        suiXiangCardService.querySuiXiangCardPageByUserId(queryDto);

    }


    @Test
    public void testgetSuiXiangCardActivityList() throws BusinessException {
        GetCanBuyCardListInput queryDto = new GetCanBuyCardListInput();
        queryDto.setCarModelId(11);
        queryDto.setCityId(310100);
        suixiangCardSalesService.getCanBuyCardList(queryDto);

    }

    @Test
    public void test2() throws Exception {
       /* Set<Long> saleCardCityIds = suixiangCardSalesService.getSaleCardCityIds(3);
        List<EffectiveSuiXiangCardInfoDto> l1 = suixiangCardSalesService.getSuiXiangCardListByCity(1L, "上海市");*/

        EffectiveSuiXiangCardDetailDto suiXiangCardDetail = suixiangCardSalesService.getSuiXiangCardDetail(2L, 48L);
        System.out.println("value");
    }

    @Resource
    private ISuiXiangCardTradeService suiXiangCardTradeService;
    @Test
    public void test3() throws Exception {

        PurchaseSuiXiangCardDto dto = new PurchaseSuiXiangCardDto();
        dto.setUserId(3111588L);
        dto.setCardPriceId(233L);
        dto.setQuantity(1);
        OperatorDto systemOp = OperatorDto.getSystemOp();
        PurchaseSuiXiangCardRecordInfo suiXiangCardOrder = suiXiangCardTradeService.createSuiXiangCardOrder(dto,systemOp);
        System.out.println(suiXiangCardOrder);
    }

    @Test
    public void test4() throws Exception {

        PayOrderDto dto =new PayOrderDto();
      /*  dto.setUserId(9457903L);
        dto.setPayChannel(20+"");
        dto.setPayOrderNo("MP230202140003601");*/


        dto.setUserId(3111588L);
        dto.setPayChannel(20+"");
        dto.setPayOrderNo("MP230204160001401");

        OperatorDto systemOp = OperatorDto.getSystemOp();
        PayOrderBo payOrderBo = suiXiangCardTradeService.payeSuiXiangCardOrder(dto, systemOp);
        System.out.println(payOrderBo);
    }


    @Resource
    IMemberCardService memberCardService;
    @Test
    public void test43() throws Exception {
        String im = "{\"activityType\":1,\"authId\":\"17******\",\"discountRate\":0.00,\"goodsModelId\":19,\"needCheckUsedMaxValue\":true,\"orderAmount\":535.03,\"orderCostTime\":5760,\"orderFrozenAmount\":0.00,\"orderPickUpDate\":1675760940000,\"orderReturnDate\":1676106000000,\"orderSeq\":\"MC230207160001001\",\"orderTime\":1675759436000,\"pickUpCity\":310100,\"pickUpStoreId\":\"117\",\"rentAmount\":100.00,\"rentMethod\":4,\"returnCity\":310100,\"returnStoreId\":\"117\",\"unitPrice\":100.00,\"userCardNo\":102,\"vehicleNo\":\"沪MD0289\"}";
        CheckCardByUseConditionInput input = JSON.parseObject(im,CheckCardByUseConditionInput.class);
        QueryAvailableCardInfoDto queryAvailableCardInfoDto = memberCardService.checkCardByUseCondition(input);

        System.out.println(queryAvailableCardInfoDto);
    }




    @Test
    public void test5() throws Exception {

        GetSuiXiangCardDetailForHasBuyInput input = new GetSuiXiangCardDetailForHasBuyInput();
        //input.setPurchaseId(12L);
        input.setType(2);
        input.setUseCardId(2165084L);
        GetSuiXiangCardDetailForHasBuyBo getSuiXiangCardDetailForHasBuyBo = suixiangCardSalesService.querySuiXiangCardDetailForHasBuy(input);
        System.out.println(getSuiXiangCardDetailForHasBuyBo);
    }
    @Test
    public void test55() throws Exception {

        String input = "{\n" +
                "\t\"activityType\": 1,\n" +
                "\t\"authId\": \"15839636666163520463\",\n" +
                "\t\"goodsModelId\": 11,\n" +
                "\t\"orderAmount\": 65.00,\n" +
                "\t\"orderCostTime\": 4320,\n" +
                "\t\"orderEndDate\": \"2024-10-27 13:45:00\",\n" +
                "\t\"orderPickUpDate\": 1729748700000,\n" +
                "\t\"orderReturnDate\": 1730007900000,\n" +
                "\t\"orderSeq\": \"\",\n" +
                "\t\"orderStartDate\": \"2024-10-25 13:45:00\",\n" +
                "\t\"pickUpCity\": 320500,\n" +
                "\t\"pickUpStoreId\": \"115\",\n" +
                "\t\"rentAmount\": 60.00,\n" +
                "\t\"rentMethod\": 4,\n" +
                "\t\"returnCity\": 320500,\n" +
                "\t\"returnStoreId\": \"115\",\n" +
                "\t\"unitPrice\": 20.00,\n" +
                "\t\"vehicleNo\": \"\"\n" +
                "}";
        GetAvailableCardByUseConditionInputDto in = JSON.parseObject(input,GetAvailableCardByUseConditionInputDto.class);
        List<SuiXiangQueryAvailableCardInfoDto> availableCardByUseCondition = suixiangCardSalesService.getAvailableCardByUseCondition(in);
        System.out.println(availableCardByUseCondition);
    }



    @Test
    public void test6() throws Exception {

        SuiXiangCardUseQueryDto in = new SuiXiangCardUseQueryDto();
        in.setUserCardStatus(2);
        in.setUserId(9457367L);

        PageBeanBO<SuixiangCardUseBo> bo = suiXiangCardService.querySuiXiangCardPageByUserId(in);
        System.out.println(bo);

    }

    @Test
    public void test7() throws Exception {
        String input ="{\"applyTime\":1675335266000,\"contractId\":\"MC230202160000501\",\"days\":1,\"operateDto\":{\"operatorId\":-1,\"operatorName\":\"门店订单系统\",\"originSystem\":\"app\"},\"userCardId\":5,\"userId\":9458294}";
        UseSuiXiangCardDaysInput in = JSON.parseObject(input,UseSuiXiangCardDaysInput.class);

        suiXiangCardService.unfreezeSuiXiangCardDays(in);
        System.out.println(1);
    }


    @Resource
    MdRestClient mdRestClient;

    @Test
    public void test8() throws Exception {
        // 支付取消 支付订单
        String orderNo = "SXK124";
        String mid = "2209191300001";
        CancelPayOrderRes cancelPayOrderRes = mdRestClient.cancelPayOrder(mid, orderNo);
        System.out.println(JSON.toJSONString(cancelPayOrderRes));
        if (cancelPayOrderRes == null || cancelPayOrderRes.getCode() != 0) {

            return;
        }

    }


    @Test
    public void test9() throws Exception {
        String in = "{\n" +
                "    \"userCardId\":1,\n" +
                "    \"oldCardStatus\":1,\n" +
                "    \"cardStatus\":3,\n" +
                "    \"operationType\":3,\n" +
                "    \"originSystem\":\"tkxt\",\n" +
                "    \"operatorId\":3654,\n" +
                "    \"operatorName\":\"extracme@hq\",\n" +
                "    \"remark\":\"新建退款审批单\"\n" +
                "}";
        ModifySuiXiangCardUseStatusInput input = JSON.parseObject(in,ModifySuiXiangCardUseStatusInput.class);
        input.setOperatorDto(OperatorDto.getSystemOp());
        input.setOpertionType(3);

        // 支付取消 支付订单
        BaseResponse response = suiXiangCardService.modifySuiXiangCardUseStatus(input);

        System.out.println(JSON.toJSONString(response));

    }



    @Test
    public void test11() throws Exception {

        List<SuiXiangCardPurchaseRecordDto> suiXiangCardPurchaseRecordDtos = suiXiangCardService.queryCardPurchaseInfoByUserId(9458325L);
        System.out.println(suiXiangCardPurchaseRecordDtos);
    }

    @Test
    public void test111() throws Exception {

        QueryUserCardStatusDto queryUserCardStatusDto = suiXiangCardService.queryUserSuiXiangCardStatus(9458325L);
    }


    @Test
    public void test12() throws Exception {

        QueryUserCardInfoDto queryUserCardInfoDto = suiXiangCardService.queryUserAllCardInfo(9455488L);
        System.out.println(queryUserCardInfoDto);
    }


    @Test
    public void pullUnreadOfferedCards() throws Exception {

        List<CardReMindDto> cardReMindDtos = suiXiangCardService.pullUnreadOfferedCards(9455488L, 10);
        System.out.println(JSON.toJSONString(cardReMindDtos));
    }


    @Test
    public void test13() throws Exception {

        try {
            int h= 1/0;
        } catch (Exception e) {
            log.error("yic 1{},e{}",1,e);
        }

        try {
            int h= 1/0;
        } catch (Exception e) {
            log.error("yic 2{}",2,e);
        }
    }


    @Resource
    private IMemberShipService memberShipService;

    @Test
    public void test14() throws Exception {

        // 查询用户
        MembershipBasicInfo membershipBasicInfo = memberShipService.getUserBasicInfoByPkId(1260699L);
        if (membershipBasicInfo == null) {
            throw new BusinessException(StatusCode.USER_INFO_NO_EXIST);
        }
        Integer a =3;
        Integer b =4;
        // 身份认证不通过
        if (!(a.equals(membershipBasicInfo.getLicenseReviewStatus()) && b.equals(membershipBasicInfo.getAuthenticationStatusNew()))) {
            throw new BusinessException(-1, "完成身份认证后方可买卡哦");
        }

    }


}