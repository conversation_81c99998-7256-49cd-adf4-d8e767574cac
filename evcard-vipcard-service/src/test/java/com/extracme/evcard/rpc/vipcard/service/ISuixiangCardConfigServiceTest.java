package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.vipcard.App;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/1
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class ISuixiangCardConfigServiceTest {

    @Resource
    private ISuixiangCardConfigService suixiangCardConfigService;

    @Test
    public void testAddSuiXiangCard(){
        AddSuiXiangCardInput req = new AddSuiXiangCardInput();
        req.setCardName("随享卡充值add");
        req.setOrgCode("01");
        req.setCityId(Arrays.asList("00001", "00002"));
        req.setPreviewTime("2024-10-01 12:00");
        req.setSaleStartTime("2024-10-09 12:00");
        req.setSaleEndTime("2024-11-01 15:00");
        req.setValidTime(12);
        req.setStock(8888);
        req.setIsDisplay(1);
        req.setSingleOrderDuration(999999.9);
        req.setLeasePriceCfgDtoList(null);
        req.setImageNo(1);
        req.setPurchaseNotes("12312331");
        req.setEffectiveImmediately(2);
        req.setOperatorDto(null);
        req.setHolidayAvailable(1);
        List<UnavailableDate> unavailableDate = new ArrayList<>();
        UnavailableDate unavailableDate1 = new UnavailableDate();
        unavailableDate1.setStartDate("20230330");
        unavailableDate1.setEndDate("20230410");
        unavailableDate.add(unavailableDate1);

        UnavailableDate unavailableDate2 = new UnavailableDate();
        unavailableDate2.setStartDate("20230411");
        unavailableDate2.setEndDate("20230412");
        unavailableDate.add(unavailableDate2);

        req.setUnavailableDate(unavailableDate);

        req.setLandingPageFlag(1);
        req.setMergeFlag(1);
        req.setVehicleBrandId(1);

        String json = "{\"cardName\":\"随享卡充值add4\",\"cityId\":[\"310100\",\"130400\",\"370900\",\"130100\",\"320500\",\"320100\",\"140100\",\"331000\",\"510100\",\"350200\",\"350100\",\"510700\"],\"effectiveImmediately\":2,\"holidayAvailable\":2,\"imageNo\":1,\"isDisplay\":1,\"leasePriceCfgDtoList\":[{\"carRentalCfgDtoList\":[{\"crossedPrice\":1,\"modelGroup\":\"车型组1\",\"modelIdList\":[\"8\",\"9\",\"10\",\"11\",\"12\",\"13\",\"14\"],\"price\":1},{\"crossedPrice\":2,\"modelGroup\":\"车型组2\",\"modelIdList\":[\"8\",\"9\",\"10\",\"11\",\"12\",\"14\",\"13\"],\"price\":2},{\"crossedPrice\":3,\"modelGroup\":\"车型组3\",\"modelIdList\":[\"8\",\"9\",\"10\",\"11\",\"13\",\"12\",\"14\"],\"price\":3}],\"leaseTerm\":1,\"rentDayId\":0,\"serviceFeeCfgDtoList\":[]},{\"carRentalCfgDtoList\":[{\"crossedPrice\":4,\"modelGroup\":\"车型组1\",\"modelIdList\":[\"310\",\"308\",\"307\",\"306\"],\"price\":4},{\"crossedPrice\":5,\"modelGroup\":\"车型组2\",\"modelIdList\":[\"308\",\"307\",\"306\",\"305\",\"304\"],\"price\":5,\"landingPagePicUrl\":\"https://evcard.oss-cn-shanghai.aliyuncs.com/prod/suixiangcard/suixiangcard_style_1_1.png\"},{\"landingPagePicUrl\":\"https://evcard.oss-cn-shanghai.aliyuncs.com/prod/suixiangcard/suixiangcard_style_1_1.png\",\"crossedPrice\":6,\"modelGroup\":\"车型组3\",\"modelIdList\":[\"313\",\"312\",\"311\",\"310\"],\"price\":6}],\"leaseTerm\":2,\"rentDayId\":0,\"serviceFeeCfgDtoList\":[]}],\"operatorDto\":{\"operatorId\":6871,\"operatorName\":\"夏磊\",\"originSystem\":\"md-user-service\"},\"orgCode\":\"00\",\"previewTime\":\"\",\"purchaseLimitNum\":0,\"purchaseNotes\":\"1111\",\"saleEndTime\":\"2024-10-30 00:00\",\"saleStartTime\":\"2024-10-01 16:50\",\"singleOrderDuration\":1.0,\"stock\":1000,\"validTime\":12}";
        AddSuiXiangCardInput input = JSON.parseObject(json, AddSuiXiangCardInput.class);
        input.setLandingPageFlag(1);
        input.setMergeFlag(1);
        input.setVehicleBrandId(1);
        AddSuiXiangCardOutput resp = suixiangCardConfigService.addSuiXiangCard(input);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void testQuerySuiXiangCardList() {
        QuerySuiXiangCardListInput req = new QuerySuiXiangCardListInput();
        req.setSaleMin("");
        req.setSaleMax("");
        req.setLeftStockMin("");
        req.setLeftStockMax("");
        req.setCardName("");
        req.setCardId(0l);
        req.setCardStatus(0);
        req.setCityId("");
        req.setIsDisplay(0);
        req.setOrgCode("");
        req.setPageNum(1);
        req.setPageSize(10);
        req.setPreviewTime("");
        req.setSaleStartTime("2023-02-10");
        req.setSaleEndTime("2023-02-11");
        QuerySuiXiangCardListOutput resp = suixiangCardConfigService.querySuiXiangCardList(req);
    }

    @Test
    public void testQuerySuiXiangCardDetail() {
        QuerySuiXiangCardDetailInput req = new QuerySuiXiangCardDetailInput();
        req.setId(1479);
        QuerySuiXiangCardDetailOutput resp = suixiangCardConfigService.querySuiXiangCardDetail(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void testModifySuiXiangCard() {
       // ModifySuiXiangCardInput req = JSON.parseObject("{\"id\":4,\"effectiveImmediately\":2,\"cardName\":\"测试一下2宋\",\"orgCode\":\"008H\",\"cityId\":[\"330200\",\"310100\"],\"previewTime\":\"2023-02-08 00:00\",\"saleStartTime\":\"2023-02-15 00:00\",\"saleEndTime\":\"2023-02-23 00:00\",\"validTime\":2,\"stock\":77,\"isDisplay\":1,\"singleOrderDuration\":0.6,\"imageNo\":2,\"purchaseNotes\":\"购买须知2222\",\"leasePriceCfgDtoList\":[{\"leaseTerm\":1,\"carRentalCfgDtoList\":[{\"price\":11,\"crossedPrice\":44,\"modelGroup\":\"车型组1\",\"modelIdList\":[\"22\"],\"modelName\":\"APP车型名称\"},{\"price\":22,\"crossedPrice\":55,\"modelGroup\":\"车型组2\",\"modelIdList\":[\"22\"],\"modelName\":\"APP车型名称\"},{\"price\":33,\"crossedPrice\":66,\"modelGroup\":\"车型组3\",\"modelIdList\":[\"22\"],\"modelName\":\"APP车型名称\"}],\"serviceFeeCfgDtoList\":[{\"feeType\":1,\"crossedPrice\":112},{\"feeType\":2,\"crossedPrice\":114},{\"feeType\":3,\"crossedPrice\":\"11577\"}]}],\"operatorDto\":{\"operatorId\":111,\"operatorName\":\"test\"}}", ModifySuiXiangCardInput.class);
        ModifySuiXiangCardInput req = new ModifySuiXiangCardInput();
        req = JSON.parseObject("{\"cardName\":\"随享卡充值add3\",\"cityId\":[\"310100\"],\"effectiveImmediately\":2,\"holidayAvailable\":2,\"id\":1479,\"imageNo\":1,\"isDisplay\":1,\"leasePriceCfgDtoList\":[{\"carRentalCfgDtoList\":[{\"crossedPrice\":1,\"modelGroup\":\"车型组1\",\"modelIdList\":[\"8\"],\"price\":1},{\"crossedPrice\":2,\"modelGroup\":\"车型组2\",\"modelIdList\":[\"8\"],\"price\":2},{\"crossedPrice\":3,\"modelGroup\":\"车型组3\",\"modelIdList\":[\"8\"],\"price\":3}],\"leaseTerm\":1,\"rentDayId\":0,\"serviceFeeCfgDtoList\":[]},{\"carRentalCfgDtoList\":[{\"crossedPrice\":4,\"modelGroup\":\"车型组1\",\"modelIdList\":[\"8\"],\"price\":4},{\"crossedPrice\":5,\"modelGroup\":\"车型组2\",\"modelIdList\":[\"8\"],\"price\":5},{\"crossedPrice\":6,\"modelGroup\":\"车型组3\",\"modelIdList\":[\"8\"],\"price\":6}],\"leaseTerm\":2,\"rentDayId\":0,\"serviceFeeCfgDtoList\":[]}],\"operatorDto\":{\"operatorId\":6871,\"operatorName\":\"夏磊\",\"originSystem\":\"md-user-service\"},\"orgCode\":\"00\",\"previewTime\":\"\",\"purchaseLimitNum\":0,\"purchaseNotes\":\"1111\",\"saleEndTime\":\"2024-10-30 00:00\",\"saleStartTime\":\"2024-10-01 16:50\",\"singleOrderDuration\":1.0,\"stock\":1000,\"validTime\":12,\"landingPageFlag\":2}",ModifySuiXiangCardInput.class);

        BaseResponse resp = suixiangCardConfigService.modifySuiXiangCard(req);
        /*ModifySuiXiangCardInput req = new ModifySuiXiangCardInput();
        req.setId(383);
        req.setCardName("随享卡");
        req.setOrgCode("000T");
        List<String> cityIdList = new ArrayList<>();
        cityIdList.add("110100");
        cityIdList.add("130400");
        cityIdList.add("320800");
        req.setCityId(cityIdList);
        req.setSaleStartTime("2023-05-07 00:00");
        req.setSaleEndTime("2023-05-08 00:00");
        req.setValidTime(1);
        req.setStock(1);
        req.setIsDisplay(1);
        req.setSingleOrderDuration(1);
        req.setImageNo(1);
        req.setPurchaseNotes("ceshi");
        req.setEffectiveImmediately(2);
        req.setHolidayAvailable(1);
        List<UnavailableDate> unavailableDateList = new ArrayList<>();
        UnavailableDate d = new UnavailableDate();
        d.setStartDate("20230506");
        d.setEndDate("20230507");
        unavailableDateList.add(d);
        req.setUnavailableDate(unavailableDateList);
        //req = JSON.parseObject("{\"leasePriceCfgDtoList\":[{\"leaseTerm\":1,\"carRentalCfgDtoList\":[{\"price\":1,\"crossedPrice\":1,\"modelGroup\":\"车型组3\",\"modelIdList\":[\"19\"],\"modelName\":\"荣威Ei5分时租赁版\"}],\"serviceFeeCfgDtoList\":[]}],\"operatorDto\":{\"operatorId\":111,\"operatorName\":\"test\"}}", ModifySuiXiangCardInput.class);
        req = JSON.parseObject("{\"id\":383,\"effectiveImmediately\":2,\"cardName\":\"随享卡\",\"orgCode\":\"000T\",\"cityId\":[\"110100\",\"130400\",\"320800\",\"610100\",\"530100\",\"120100\",\"510700\",\"350100\",\"350200\",\"140100\",\"130100\",\"310100\"],\"saleStartTime\":\"2023-05-07 00:00\",\"saleEndTime\":\"2023-05-08 00:00\",\"validTime\":1,\"stock\":1,\"isDisplay\":1,\"singleOrderDuration\":1,\"imageNo\":1,\"purchaseNotes\":\"测试\",\"leasePriceCfgDtoList\":[{\"leaseTerm\":1,\"carRentalCfgDtoList\":[{\"price\":1,\"crossedPrice\":1,\"modelGroup\":\"车型组3\",\"modelIdList\":[\"19\"],\"modelName\":\"荣威Ei5分时租赁版\"}],\"serviceFeeCfgDtoList\":[]}],\"holidayAvailable\":2,\"unavailableDate\":[{\"startDate\":\"20230506\",\"endDate\":\"20230507\"}]}",ModifySuiXiangCardInput.class);
        BaseResponse resp = suixiangCardConfigService.modifySuiXiangCard(req);*/
    }

    @Test
    public void testQuerySuiXiangCardPurchaseRecord() {
        QuerySuiXiangCardPurchaseRecordInput req = new QuerySuiXiangCardPurchaseRecordInput();
        req.setPageNum(1);
        req.setPageSize(10);
        QuerySuiXiangCardPurchaseRecordOutput resp = suixiangCardConfigService.querySuiXiangCardPurchaseRecord(req);
    }

    @Test
    public void testSearchOperateLog() {
        SearchOperateLogInput req = new SearchOperateLogInput();
        req.setOperateType(1);
        req.setForeignId("4");
        req.setPageNum(1);
        req.setPageSize(1);
        SearchOperateLogOutput resp = suixiangCardConfigService.searchOperateLog(req);
    }

    @Test
    public void testExportSuiXiangCardPurchaseRecord() {
        ExportSuiXiangCardPurchaseRecordInput req = new ExportSuiXiangCardPurchaseRecordInput();
        req.setPurchaseMethod(1);
        ExportSuiXiangCardPurchaseRecordOutput resp = suixiangCardConfigService.exportSuiXiangCardPurchaseRecord(req);
    }

    @Autowired
    private DataSource dataSource;

    @Test
    public void testGiveSuiXiangCard() throws SQLException {
        long t1 = System.currentTimeMillis();

        GiveSuiXiangCardInput req = new GiveSuiXiangCardInput();
        req.setListType(2);
        //req.setFileUrl("http://evcard.oss-cn-shanghai.aliyuncs.com/files/_ali_7af44d93dca64a08bb0da04f84407c96.md/dev/b20195.xlsx");
        req.setFileUrl("http://evcard.oss-cn-shanghai.aliyuncs.com/files/_ali_4c40a8b9aeea40a0ba88253d47705b1f.md/dev/d67594.xlsx");
        req.setId(1442);
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setOperatorId(1L);
        operatorDto.setOperatorName("xialei");
        req.setOperatorDto(operatorDto);
//        private OperatorDto operatorDto; // 操作日志信息
        GiveSuiXiangCardOutput resp = suixiangCardConfigService.giveSuiXiangCard(req);

        long t2 = System.currentTimeMillis();

        System.out.println("时间为:" + String.valueOf(t2-t1));
        System.out.println(resp.toString());
    }

    @Test
    public void testQueryGiveSuiXiangCard() {
        QueryGiveSuiXiangCardInput giveSuiXiangCardInput = new QueryGiveSuiXiangCardInput();
        giveSuiXiangCardInput.setId(15);

        QueryGiveSuiXiangCardOutput queryGiveSuiXiangCardOutput = suixiangCardConfigService.queryGiveSuiXiangCard(giveSuiXiangCardInput);
        System.out.println(queryGiveSuiXiangCardOutput.getBaseResponse().getMessage());
        System.out.println(queryGiveSuiXiangCardOutput.getBaseResponse().getCode());
    }

    @Test
    public void testSendSuiXiangCard() {
        SendSuiXiangCardInput input = new SendSuiXiangCardInput();

        input.setId(1442);
        List<String> authIds = new ArrayList<>();
        authIds.add("29523223562105831089");
        input.setAuthIds(authIds);
        input.setRentDaysId("2148");
        input.setPrice("4");
        input.setListType(2);
        input.setNumber(1);

        SendSuiXiangCardOutput output = suixiangCardConfigService.sendSuiXiangCard(input);
        System.out.println(output.getBaseResponse().getCode());
        System.out.println(output.getBaseResponse().getMessage());
        System.out.println(suixiangCardConfigService.sendSuiXiangCard(input));
    }

    @Autowired
    private ISuiXiangCardService suiXiangCardService;

    @Test
    public void testQuerySuiXiangCardRentDay() {
        QuerySuiXiangCardRentDayInput querySuiXiangCardRentDayInput = new QuerySuiXiangCardRentDayInput();
        querySuiXiangCardRentDayInput.setCardBaseId(1442);
        System.out.println(suiXiangCardService.querySuiXiangCardRentDay(querySuiXiangCardRentDayInput));
    }



}
