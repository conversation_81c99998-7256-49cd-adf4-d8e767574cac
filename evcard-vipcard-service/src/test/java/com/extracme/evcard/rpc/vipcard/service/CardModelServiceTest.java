package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.vipcard.App;
import com.extracme.evcard.rpc.vipcard.dto.CardConfigDto;
import com.extracme.evcard.rpc.vipcard.dto.CardModelListViewDto;
import com.extracme.evcard.rpc.vipcard.dto.CardModelQueryDto;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.job.PurchaseOrderCancelJob;
import com.extracme.evcard.rpc.vipcard.job.VipCardOverTimeJob;
import com.extracme.evcard.rpc.vipcard.rest.MdRestClient;
import com.extracme.evcard.rpc.vipcard.rest.entity.PayOrderIsPayingOrNotRes;
import com.extracme.evcard.rpc.vipcard.util.ConfigUtil;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class CardModelServiceTest {
    @Resource
    ICardModelService cardModelService;

    @Resource
    IMemberCardService memberCardService;

    @Test
    public void test1() throws Exception {
        CardModelListViewDto dto = cardModelService.getCardModelDetailById(100332L);
        String value = ConfigUtil.getConfigPropertiesStr("web_url");
        System.out.println("value");
    }


    @Resource
    PurchaseOrderCancelJob purchaseOrderCancelJob;

    @Test
    public void tt() {
        purchaseOrderCancelJob.execute(null);
    }


    @Test
    public void testAdd() throws Exception {

//        Date a  = DateUtil.getDateFromStr("000000", DateUtil.timeShort);
//        a = DateUtil.getDateFromStr("23:00:00", DateUtil.timeShort);
//        a = DateUtil.getDateFromStr("28:00:00", DateUtil.timeShort);


        CardConfigDto configDto = new CardConfigDto();
        configDto.setCardType(1);
        configDto.setAvailableDaysOfWeek(Arrays.asList(1,3,5));
        configDto.setCardName("测试新增222");
        configDto.setDiscount(88);
        configDto.setCityIds(Arrays.asList(310100L));
        configDto.setDurationLimit(10);
        configDto.setMaxValue(new BigDecimal(500));
        configDto.setOrgId("000T");
        //configDto.setStartTime("000000");
        //configDto.setEndTime("200000");
        //configDto.setRentMethods(Arrays.asList(2,3));
        configDto.setRentMethodGroups(Arrays.asList(1,2));
        configDto.setStoreIds(Arrays.asList(100L, 104L));
        //configDto.setRentMethods(Arrays.asList(0,3));
        configDto.setRules("xxx");
        configDto.setVehicleModels(Arrays.asList(100L,101L));
        configDto.setBackUrl("/vipcard/card_style_1.png");
        //cardModelService.add(configDto, OperatorDto.getSystemOp());
        //configDto.setCardId(100043L);
        cardModelService.add(configDto, OperatorDto.getSystemOp());
    }

    @Test
    public void testQueryPage() throws Exception {
        CardModelQueryDto queryDto = new CardModelQueryDto();
        //queryDto.setCardId(100006L);
//        queryDto.setCardName("测试新增");
//        queryDto.setCardType(1);
//        queryDto.setCityId(130100L);
//        queryDto.setRentMethods(Arrays.asList(0,3));
//        queryDto.setOrgId("00");
        queryDto.setRentMethods(Arrays.asList(1, 3));
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        cardModelService.queryPage(queryDto);
    }

    @Test
    public void testGetCardModelById() throws Exception {
        cardModelService.getCardModelById(100006L);
    }


    @Resource
    MdRestClient mdRestClient;
    @Test
    public void testMdRestApi() throws Exception {
        PayOrderIsPayingOrNotRes resp = mdRestClient.orderIsPayingOrNot("MP220715150000301");
        System.out.println(JSON.toJSONString(resp));
    }




}