package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.vipcard.App;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.QuerySuixiangCardRefundLogInput;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.QuerySuixiangCardRefundLogOutput;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class SuixiangCardRefundLogServiceTest {

    @Autowired
    private ISuixiangCardRefundService suixiangCardRefundService;

    @Test
    public void testPage() {
        QuerySuixiangCardRefundLogInput input = new QuerySuixiangCardRefundLogInput();
        input.setPageNum(1);
        input.setPageSize(10);
        suixiangCardRefundService.querySuixiangCardRefundLog(input);
    }

    @Test
    public void testRefundStatus() {
        suixiangCardRefundService.manualRefundSuixiangCard(2164772L, null);
        //suixiangCardRefundService.saveSuixiangCardRefundLog(2164772L, 1);
    }

    @Test
    public void testRefundLog() {
        QuerySuixiangCardRefundLogInput input = new QuerySuixiangCardRefundLogInput();
        input.setRefundStartTime("");
        input.setRefundEndTime("");
        QuerySuixiangCardRefundLogOutput querySuixiangCardRefundLogOutput = suixiangCardRefundService.querySuixiangCardRefundLog(input);
        System.out.println(querySuixiangCardRefundLogOutput);
    }
}
