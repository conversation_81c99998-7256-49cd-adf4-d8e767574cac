package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.vipcard.App;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.service.store.GoodsModelService;
import com.extracme.evcard.rpc.vipcard.service.store.StoreServ;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static com.alibaba.fastjson.JSON.parseObject;


/**
 * <AUTHOR> chennian.
 * @Date ：Created in 10:32 2020/12/28
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class ICardSalesActivityServiceTest {

    @Autowired
    private ICardSalesActivityService cardSalesActivityService;

    @Autowired
    private IMemberCardService memberCardService;
    @Autowired
    private ICardTradeService cardTradeService;

    @Test
    public void getEffectiveActivityCard() throws Exception {
//        String s = "{\"orderCostTime\":1440,\"orderPickUpDate\":1657506600000,\"pickUpShopSeq\":\"154\",\"pickUpStoreId\":\"154\",\"rentAmount\":100,\"rentMethod\":4,\"returnShopSeq\":\"154\",\"returnStoreId\":\"154\",\"userId\":3111865,\"vehicleModelSeq\":19}";
//        GetEffectiveActivityCardInput input = JSON.parseObject(s, GetEffectiveActivityCardInput.class);
//        List<CanUseActivityCardInfoDto> list = cardSalesActivityService.getEffectiveActivityCard(input);
//        for (CanUseActivityCardInfoDto infoDto : list) {
//            System.out.println(JSON.toJSONString(infoDto));
//        }

        GetEffectiveActivityCardInput input = JSON.parseObject("{\"orderCostTime\":20,\"orderPickUpDate\":1655611200000,\"pickUpCity\":320500,\"pickUpShopSeq\":\"115\",\"rentAmount\":20.00,\"rentMethod\":3,\"returnCity\":320500,\"returnShopSeq\":\"115\",\"userId\":10488,\"vehicleModelSeq\":9}", GetEffectiveActivityCardInput.class);
        List<CanUseActivityCardInfoDto> list = cardSalesActivityService.getEffectiveActivityCard(input);
        for (CanUseActivityCardInfoDto infoDto : list) {
            System.out.println(JSON.toJSONString(infoDto));
        }
    }

    @Test
    public void getEffectiveActivityCardList() throws Exception {
        List<EffectiveActivityCardInfoDto> list = cardSalesActivityService.getEffectiveActivityCardList(9452758L, "成都市");
        for (EffectiveActivityCardInfoDto infoDto : list) {
            System.out.println(JSON.toJSONString(infoDto));
        }
    }
    @Test
    public void getEffectiveActivityCardDetail() throws Exception {
        EffectiveActivityCardDetailDto effectiveActivityCardDetail = cardSalesActivityService.getEffectiveActivityCardDetail(9452758L, 100020L);
        System.out.println(JSON.toJSONString(effectiveActivityCardDetail));
    }
    @Test
    public void getEffectiveActivityCardByOrder() throws Exception {
        List<CanUseActivityCardInfoDto> list = cardSalesActivityService.getEffectiveActivityCardByOrder("C2021011414090000010", new BigDecimal(260));
        for (CanUseActivityCardInfoDto infoDto : list) {
            System.out.println(JSON.toJSONString(infoDto));
        }
    }
    @Test
    public void getAvailableCardByOrder() throws Exception {
        GetAvailableCardByOrderInputDto inputDto = new GetAvailableCardByOrderInputDto("********613090000012", new BigDecimal(55), new BigDecimal(23), new BigDecimal(23), new BigDecimal(23), 3);
        List<QueryAvailableCardInfoDto> list = memberCardService.getAvailableCardByOrder(inputDto);
        for (QueryAvailableCardInfoDto infoDto : list) {
            System.out.println(JSON.toJSONString(infoDto));
        }
    }
    @Test
    public void getPurchaseRecordById() throws Exception {
        PurchaseRecordInfo list = cardTradeService.getPurchaseRecordById(1L);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void getAvailableCardByUseCondition222() throws Exception {
        String s = "{\"activityType\":1,\"authId\":\"******************\",\"orderAmount\":17.00,\"orderCostTime\":2880,\"orderPickUpDate\":1656587700000,\"orderReturnDate\":1656760500000,\"orderSeq\":\"\",\"returnShopSeq\":\"3\",\"pickUpShopSeq\":\"3\",\"pickUpStoreId\":\"108\",\"rentAmount\":9.00,\"rentMethod\":4,\"returnStoreId\":\"108\",\"unitPrice\":4.50,\"GoodsModelId\":13,\"vehicleNo\":\"\"}";
        String s2 = "{\"activityType\":7,\"authId\":\"******************\",\"orderAmount\":420.00,\"orderCostTime\":5760,\"orderPickUpDate\":1645519140000,\"orderReturnDate\":1645864740000,\"pickUpShopSeq\":\"3\",\"rentAmount\":420.00,\"rentMethod\":3,\"returnShopSeq\":\"3\",\"unitPrice\":150,\"vehicleModelSeq\":254,\"vehicleNo\":\"\"}";

        String s3="{\"activityType\":1,\"authId\":\"15839636060172833208\",\"orderAmount\":34,\"orderCostTime\":4320,\"orderPickUpDate\":1657244700000,\"orderReturnDate\":1657503900000,\"pickUpShopSeq\":\"117\",\"pickUpStoreId\":\"117\",\"rentAmount\":34,\"rentMethod\":4,\"returnShopSeq\":\"117\",\"returnStoreId\":\"117\",\"unitPrice\":34,\"vehicleModelSeq\":11}";

        String s4= "{\"activityType\":1,\"authId\":\"17666000085171719910\",\"goodsModelId\":11,\"orderAmount\":36.90,\"orderCostTime\":4320,\n" +
                "\"orderPickUpDate\":1657626300000,\"orderReturnDate\":1657885500000,\"orderSeq\":\"\",\"pickUpCity\":310100,\"pickUpStoreId\":\"117\",\n" +
                "\"rentAmount\":35.00,\"rentMethod\":4,\"returnCity\":310100,\"returnStoreId\":\"117\",\"unitPrice\":11.67,\"vehicleNo\":\"\"}";
        GetAvailableCardByUseConditionInputDto inputDTO1 = parseObject(s4,GetAvailableCardByUseConditionInputDto.class);
        List<QueryAvailableCardInfoDto> list1 = memberCardService.getAvailableCardByUseCondition(inputDTO1);
        for (QueryAvailableCardInfoDto infoDto : list1) {
            System.out.println(JSON.toJSONString(infoDto));
        }

        GetAvailableCardByUseConditionInputDto inputDTO = parseObject(s,GetAvailableCardByUseConditionInputDto.class);
        List<QueryAvailableCardInfoDto> list = memberCardService.getAvailableCardByUseCondition(inputDTO);
        for (QueryAvailableCardInfoDto infoDto : list) {
            System.out.println(JSON.toJSONString(infoDto));
        }


        String s1 = "";
        GetAvailableCardByUseConditionInputDto inputDTO2 = parseObject(s2,GetAvailableCardByUseConditionInputDto.class);
        List<QueryAvailableCardInfoDto> list2 = memberCardService.getAvailableCardByUseCondition(inputDTO2);
    }


    @Test
    public void getAvailableCardByUseCondition21() throws Exception {
      String s2 = "{\n" +
              "\t\"activityType\": 1,\n" +
              "\t\"authId\": \"15839636666163520463\",\n" +
              "\t\"orderAmount\": 7,\n" +
              "\t\"orderCostTime\": 1440,\n" +
              "\t\"orderPickUpDate\": 1730426400000,\n" +
              "\t\"orderReturnDate\": 1730512800000,\n" +
              "\t\"pickUpShopSeq\": \"0\",\n" +
              "\t\"pickUpStoreId\": \"125\",\n" +
              "\t\"rentAmount\": 10,\n" +
              "\t\"rentMethod\": 4,\n" +
              "\t\"returnShopSeq\": \"0\",\n" +
              "\t\"returnStoreId\": \"115\",\n" +
              "\t\"unitPrice\": 10,\n" +
              "\t\"vehicleModelSeq\": 11\n" +
              "}";
        GetAvailableCardByUseConditionInputDto inputDTO2 = parseObject(s2,GetAvailableCardByUseConditionInputDto.class);
        List<QueryAvailableCardInfoDto> list2 = memberCardService.getAvailableCardByUseCondition(inputDTO2);
    }

    @Test
    public void getAvailableCardByUseCondition() throws Exception {
        String s = "{\"activityType\":1,\"authId\":\"******************\",\"orderAmount\":17.00,\"orderCostTime\":2880,\"orderPickUpDate\":1656587700000,\"orderReturnDate\":1656760500000,\"orderSeq\":\"\",\"pickUpCity\":310100,\"pickUpStoreId\":\"108\",\"rentAmount\":9.00,\"rentMethod\":4,\"returnCity\":310100,\"returnStoreId\":\"108\",\"unitPrice\":4.50,\"GoodsModelId\":13,\"vehicleNo\":\"\"}";
        String s2 = "{\"activityType\":7,\"authId\":\"******************\",\"orderAmount\":420.00,\"orderCostTime\":5760,\"orderPickUpDate\":1645519140000,\"orderReturnDate\":1645864740000,\"pickUpShopSeq\":\"3\",\"rentAmount\":420.00,\"rentMethod\":3,\"returnShopSeq\":\"3\",\"unitPrice\":150,\"vehicleModelSeq\":254,\"vehicleNo\":\"\"}";
        GetAvailableCardByUseConditionInputDto inputDTO = parseObject(s,GetAvailableCardByUseConditionInputDto.class);
        List<QueryAvailableCardInfoDto> list = memberCardService.getAvailableCardByUseCondition(inputDTO);
        for (QueryAvailableCardInfoDto infoDto : list) {
            System.out.println(JSON.toJSONString(infoDto));
        }

        GetAvailableCardByUseConditionInputDto inputDTO2 = parseObject(s2,GetAvailableCardByUseConditionInputDto.class);
        List<QueryAvailableCardInfoDto> list2 = memberCardService.getAvailableCardByUseCondition(inputDTO2);
    }
    @Test
    public void checkCardByUseCondition() throws Exception {
        String s= "{\"activityType\":1,\"authId\":\"15638506763150855962\",\"orderAmount\":19.00,\"orderCostTime\":2880,\"orderPickUpDate\":1656060300000,\"orderReturnDate\":1656233100000,\"orderSeq\":\"\",\"pickUpCity\":310100,\"pickUpStoreId\":\"108\",\"rentAmount\":11.00,\"rentMethod\":4,\"returnCity\":310100,\"returnStoreId\":\"108\",\"unitPrice\":5.50,\"vehicleModelSeq\":13,\"vehicleNo\":\"\"}";
        GetAvailableCardByUseConditionInputDto inputDTO3 = parseObject(s,GetAvailableCardByUseConditionInputDto.class);
        List<QueryAvailableCardInfoDto> list2 = memberCardService.getAvailableCardByUseCondition(inputDTO3);
        System.out.println(JSON.toJSONString(list2));


        //String s = "{\"activityType\":1,\"authId\":\"18811110003100306048\",\"orderAmount\":400,\"orderCostTime\":7215,\"orderPickUpDate\":1646357160000,\"orderReturnDate\":1646790060000,\"orderSeq\":\"C2022030409260000015\",\"pickUpShopSeq\":\"3\",\"rentAmount\":400,\"rentMethod\":3,\"returnShopSeq\":\"3\",\"unitPrice\":200,\"vehicleModelSeq\":243}";
        GetAvailableCardByUseConditionInputDto inputDTO = parseObject(s,GetAvailableCardByUseConditionInputDto.class);
        QueryAvailableCardInfoDto list = memberCardService.checkCardByUseCondition(inputDTO, 1004030L, new BigDecimal(0.95), DateUtil.getDateFromStr("2022-03-04 09:26:19", DateUtil.simple), new BigDecimal(22.50), true);
        System.out.println(JSON.toJSONString(list));

    }


    @Test
    public void getAvailableCardByUseConditionMD() throws Exception {

        String sa = "{\"activityType\":2,\"authId\":\"18911110003160911017\",\"goodsModelId\":11,\"orderAmount\":989.15,\"orderCostTime\":7200,\"orderPickUpDate\":1658808900000,\"orderReturnDate\":1659240900000,\"orderSeq\":\"\",\"pickUpCity\":310100,\"pickUpStoreId\":\"117\",\"rentAmount\":989.15,\"rentMethod\":4,\"returnCity\":310100,\"returnStoreId\":\"117\",\"unitPrice\":10.00,\"vehicleNo\":\"\"}";
        GetAvailableCardByUseConditionInputDto inputDTO11 = parseObject(sa,GetAvailableCardByUseConditionInputDto.class);
        List<QueryAvailableCardInfoDto> list = memberCardService.getAvailableCardByUseCondition(inputDTO11);
        for (QueryAvailableCardInfoDto infoDto : list) {
            System.out.println(JSON.toJSONString(infoDto));
        }



        //。。。。
        String s = "{\"activityType\":7,\"authId\":\"******************\",\"orderAmount\":420.00,\"orderCostTime\":5760,\"orderPickUpDate\":1645519140000,\"orderReturnDate\":1645864740000,\"pickUpShopSeq\":\"3\",\"rentAmount\":420.00,\"rentMethod\":3,\"returnShopSeq\":\"3\",\"unitPrice\":150,\"vehicleModelSeq\":254,\"vehicleNo\":\"\"}";
        GetAvailableCardByUseConditionInputDto inputDTO = parseObject(s,GetAvailableCardByUseConditionInputDto.class);
        List<QueryAvailableCardInfoDto> list11 = memberCardService.getAvailableCardByUseCondition(inputDTO);
        for (QueryAvailableCardInfoDto infoDto : list) {
            System.out.println(JSON.toJSONString(infoDto));
        }

        s = "{\"activityType\":7,\"authId\":\"******************\",\"orderAmount\":420.00,\"orderCostTime\":5760,\"orderPickUpDate\":1645519140000,\"orderReturnDate\":1645864740000,\"pickUpStoreId\":\"100\",\"rentAmount\":420.00,\"rentMethod\":3,\"returnStoreId\":\"3\",\"unitPrice\":150,\"pickUpCity\":110100,\"returnCity\":310100,\"vehicleModelSeq\":254,\"vehicleNo\":\"\"}";
        inputDTO = parseObject(s,GetAvailableCardByUseConditionInputDto.class);
        list = memberCardService.getAvailableCardByUseCondition(inputDTO);
        for (QueryAvailableCardInfoDto infoDto : list) {
            System.out.println(JSON.toJSONString(infoDto));
        }
    }

    @Resource
    private StoreServ storeServ;

    @Resource
    private GoodsModelService goodsModelService;

    @Test
    public void testConfigLoader() {
        storeServ.getStoreById("117");
        goodsModelService.getVehicleModelList();
        goodsModelService.getGoodsVehicleModel(111);

    }
}
