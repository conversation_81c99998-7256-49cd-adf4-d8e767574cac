package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.OSSObject;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.App;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;
import com.extracme.evcard.rpc.vipcard.util.FileUtil;
import com.extracme.evcard.rpc.vipcard.util.ZipUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class SuixiangCardCdkServiceTest {

    @Autowired
    private ISuixiangCardCdkService suixiangCardCdkService;

    @Test
    public void testZip() throws IOException {
        Map<String, InputStream> map = new HashMap<>();
        OSSObject ossObject1 = FileUtil.downloadStream("dev" + "/qrCode/wechat/HFQ  租期2天20241119090732.zip");
        OSSObject ossObject2 = FileUtil.downloadStream("test" + "/qrCode/wechat/20241119090740184_1731978460184.png");
        map.put("HFQ  租期2天20241118150845.zip", ossObject1.getObjectContent());
        map.put("20241119090740184_1731978460184.png", ossObject2.getObjectContent());
        ZipUtil.zipStreams(map, "/data/abc.zip");
    }


    @Test
    public void testGenerateSuixiangCardCdk() {
        GenerateSuiXiangCardCdkInput generateSuiXiangCardCdkInput = new GenerateSuiXiangCardCdkInput();
        List<SuiXiangCardCdkDto> suiXiangCardCdkDtos = new ArrayList<>();
        SuiXiangCardCdkDto suiXiangCardCdkDto = new SuiXiangCardCdkDto();
        suiXiangCardCdkDto.setCardBaseId(1442L);
        // suiXiangCardCdkDto.setCardName("测试卡");
        suiXiangCardCdkDto.setCardPriceId(3195L);
        suiXiangCardCdkDto.setCardRentId(2148L);
        suiXiangCardCdkDto.setActDesc("租期2天 - 售价0.03元");
        suiXiangCardCdkDto.setNum(1000);
        suiXiangCardCdkDto.setThirdSalesPrice(new BigDecimal("0.03"));
        suiXiangCardCdkDtos.add(suiXiangCardCdkDto);
        generateSuiXiangCardCdkInput.setSuiXiangCardCdkDtos(suiXiangCardCdkDtos);
        generateSuiXiangCardCdkInput.setCardBaseId(1442L);
        generateSuiXiangCardCdkInput.setPurpose(2);
        suixiangCardCdkService.generateSuixiangCardCdk(generateSuiXiangCardCdkInput);
    }


    @Test
    public void queryAndExportSuixiangCardCdkDetail() {
        // Arrange
        QueryAndExportSuiXiangCardCdkInput input = new QueryAndExportSuiXiangCardCdkInput();
        input.setSuiXiangCardCdkConfigId(290);
        input.setNeedQrFlag(true);
        // Act
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setOperatorId(1111L);
        operatorDto.setOperatorName("xialei");
        input.setOperatorDto(operatorDto);
        QueryAndExportSuiXiangCardCdkOutput actualOutput = suixiangCardCdkService.queryAndExportSuiXiangCardCdk(input);
        System.out.println(actualOutput);
    }

    @Test
    public void querySuixiangCardCdkConfigDetailByCardBaseId() {
        // Arrange
        QuerySuiXiangCardCdkConfigDetailInput input = new QuerySuiXiangCardCdkConfigDetailInput();
        input.setCardBaseId(1442l);
        // Act
        QuerySuiXiangCardCdkConfigDetailOutput querySuiXiangCardCdkConfigDetailOutput = suixiangCardCdkService.querySuiXiangCardCdkConfigDetail(input);

        System.out.println(querySuiXiangCardCdkConfigDetailOutput);
    }


    @Test
    public void querySuixiangCardCdkConfigByCardBaseId() {
        // Arrange
        QuerySuiXiangCardCdkConfigDetailInput input = new QuerySuiXiangCardCdkConfigDetailInput();
        input.setCardBaseId(1442l);
        // Act
        QuerySuiXiangCardCdkConfigOutput querySuiXiangCardCdkConfigOutput = suixiangCardCdkService.querySuiXiangCardCdkConfig(input);


        System.out.println(querySuiXiangCardCdkConfigOutput);
    }

    @Test
    public void testActivateSuixiangCardCdk() {
        ActivateSuixiangCardCdkInput activateSuixiangCardCdkInput = new ActivateSuixiangCardCdkInput();
        activateSuixiangCardCdkInput.setCdkey("vx5tcv6fzeqi0um");
        activateSuixiangCardCdkInput.setMid("2407191500001");
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setOperatorName("admin");
        operatorDto.setOperatorId(-1L);
        operatorDto.setRemark("");
        operatorDto.setOriginSystem("vipcard-rpc");
        activateSuixiangCardCdkInput.setOperatorDto(operatorDto);
        suixiangCardCdkService.activateSuixiangCardCdk(activateSuixiangCardCdkInput);
    }

    @Test
    public void testOfferSuixiangCardPreCdk() {
        OfferSuixiangCardPreCdkInput offerSuixiangCardPreCdkInput = new OfferSuixiangCardPreCdkInput();
        offerSuixiangCardPreCdkInput.setMid("2410091700002");
        offerSuixiangCardPreCdkInput.setObtainType(1);
        OfferSuixiangCardPreCdkOutput offerSuixiangCardPreCdkOutput = suixiangCardCdkService.offerSuixiangCardPreCdk(offerSuixiangCardPreCdkInput);
    }

    @Test
    public void testActivateSuixiangCardCdkMerge() {
        ActivateSuixiangCardCdkInput activateSuixiangCardCdkInput = new ActivateSuixiangCardCdkInput();
        activateSuixiangCardCdkInput.setCdkey("hxqzopy6fv0087lr");
        activateSuixiangCardCdkInput.setMobile("18888888801");

        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setOperatorName("admin");
        operatorDto.setOperatorId(-1L);
        operatorDto.setRemark("");
        operatorDto.setOriginSystem("vipcard-rpc");
        activateSuixiangCardCdkInput.setOperatorDto(operatorDto);
        ActivateSuixiangCardCdkOutput output = suixiangCardCdkService.activateSuixiangCardCdk(activateSuixiangCardCdkInput);
        System.out.println(JSON.toJSONString(output));
    }
}
