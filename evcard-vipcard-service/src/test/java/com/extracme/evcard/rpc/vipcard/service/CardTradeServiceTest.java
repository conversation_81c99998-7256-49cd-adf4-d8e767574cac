package com.extracme.evcard.rpc.vipcard.service;
import java.util.Date;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.App;
import com.extracme.evcard.rpc.vipcard.dao.MmpUserCardDiscountInfoMapper;
import com.extracme.evcard.rpc.vipcard.dto.DeductFrozenDiscountAmountDto;
import com.extracme.evcard.rpc.vipcard.dto.FreezeDiscountAmountDto;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.UnfreezeDiscountAmountDto;
import com.extracme.evcard.rpc.vipcard.model.MmpUserCardDiscountInfo;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

import static org.junit.Assert.*;

//@Transactional
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class CardTradeServiceTest {

    @Autowired
    ICardTradeService cardTradeService;
    @Autowired
    MmpUserCardDiscountInfoMapper mmpUserCardDiscountInfoMapper;

//    @Before
//    public void before() {
//
//        MmpUserCardDiscountInfo record = new MmpUserCardDiscountInfo();
//        record.setUserCardNo(99999999L);
//        record.setStartTime(DateUtil.getDateFromStr("2022-02-17 00:00:00", "yyyy-MM-dd hh:mm:ss"));
//        record.setEndTime(DateUtil.getDateFromStr("2022-02-18 00:00:00", "yyyy-MM-dd hh:mm:ss"));
//        record.setTotalDiscountAmount(new BigDecimal("100"));
//        record.setDiscountAmount(new BigDecimal("25"));
//        record.setStatus(0);
//        record.setCreateTime(new Date());
//        record.setCreateOperId(0L);
//        record.setCreateOperName("chennian");
//        record.setUpdateTime(new Date());
//        record.setUpdateOperId(0L);
//        record.setUpdateOperName("chennian");
//        record.setFrozenAmount(new BigDecimal("0"));
//
//        mmpUserCardDiscountInfoMapper.insertSelective(record);
//    }

    @Test
    public void freezeDiscountAmount() {

        try {
            FreezeDiscountAmountDto input = new FreezeDiscountAmountDto();
            input.setUserCardNo(99999999L);
            input.setFreezeTime(DateUtil.getDateFromStr("2022-02-17 11:36:02", "yyyy-MM-dd hh:mm:ss"));
            input.setAmount(BigDecimal.valueOf(30));


            OperatorDto operateDto = new OperatorDto();
            operateDto.setOperatorId(1L);
            operateDto.setOperatorName("shenhuaxin");
            cardTradeService.freezeDiscountAmount(input, operateDto);
        } catch (BusinessException e) {
            e.printStackTrace();
        }
    }

//    @Test
//    public void unfreezeDiscountAmount() {
//        UnfreezeDiscountAmountDto input = new UnfreezeDiscountAmountDto();
//        input.setUserCardNo(99999999L);
//        input.setFreezeTime(DateUtil.getDateFromStr("2022-02-17 11:36:02", "yyyy-MM-dd hh:mm:ss"));
//        input.setAmount(BigDecimal.valueOf(30));
//
//
//        OperatorDto operateDto = new OperatorDto();
//        operateDto.setOperatorId(1L);
//        operateDto.setOperatorName("shenhuaxin");
//
//        try {
//            cardTradeService.unfreezeDiscountAmount(input, operateDto);
//        } catch (BusinessException e) {
//            e.printStackTrace();
//        }
//    }

    @Test
    public void deductFrozenDiscountAmount() {
        DeductFrozenDiscountAmountDto input = new DeductFrozenDiscountAmountDto();
        input.setUserCardNo(99999999L);
        input.setFreezeTime(DateUtil.getDateFromStr("2022-02-17 11:36:02", "yyyy-MM-dd hh:mm:ss"));
        input.setAmount(BigDecimal.valueOf(30));


        OperatorDto operateDto = new OperatorDto();
        operateDto.setOperatorId(1L);
        operateDto.setOperatorName("shenhuaxin");

        try {
            cardTradeService.deductFrozenDiscountAmount(input, operateDto);
        } catch (BusinessException e) {
            e.printStackTrace();
        }
    }

}