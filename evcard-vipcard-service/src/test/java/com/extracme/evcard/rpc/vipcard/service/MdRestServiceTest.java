package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.App;
import com.extracme.evcard.rpc.vipcard.rest.MdRestClient;
import com.extracme.evcard.rpc.vipcard.rest.entity.RefundForCriticalPointPaymentRes;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MdRestServiceTest {

    @Autowired
    private MdRestClient mdRestClient;

    @Test
    public void refundForCriticalPointPayment() {
        //String pay = "MP241206160000501";
        String pay = "MP241206160000502";
        try {
            RefundForCriticalPointPaymentRes refundForCriticalPointPaymentRes = mdRestClient.refundForCriticalPointPayment(pay);
            System.out.println(JSON.toJSONString(refundForCriticalPointPaymentRes));
        } catch (BusinessException e) {
            System.out.println(e);
        }
    }


}
