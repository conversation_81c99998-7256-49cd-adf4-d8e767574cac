package com.extracme.evcard.rpc.vipcard.job;

import com.extracme.evcard.rpc.vipcard.App;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/2/4
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class SuiXiangCardSaleNoticeJobTest {

    @Resource
    private SuiXiangCardSaleNoticeJob suiXiangCardSaleNoticeJob;

    @Test
    public void testSuiXiangCardSaleNoticeJob() {
        suiXiangCardSaleNoticeJob.execute(null);
    }

}
