package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.App;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import com.extracme.framework.core.bo.PageBeanBO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberCardServiceTest {
    @Resource
    IMemberCardService memberCardService;

    @Resource
    ICardTradeService cardTradeService;


    @Test
    public void testMemberCard(){
        QueryUserCardInfoDto queryUserCardInfoDto = memberCardService.queryUserVipCardInfo(1385185L);
        System.out.println(JSON.toJSONString(queryUserCardInfoDto));
    }

    @Test
    public void queryUserCardDetail() {
        try {

            //memberCardService.queryCardInfoById(100332L, 0, 9452573L);
            QueryCardInfoDetail res = memberCardService.queryUserCardDetail(1006195L,9452625L);
            System.out.println(JSON.toJSONString(res));
        }catch (Exception e){

        }
    }

    @Test
    public void userCardStatus() {
        try {
            memberCardService.userCardStatus(9452854L);
        }catch (Exception e){

        }
    }


    @Test
    public void payCarPurchaseCallBack() throws BusinessException {
        String s = "{\"cardActivityId\":100085,\"operator\":\"pay-rpc\",\"outTradeSeq\":\"6220210107094313039WVl111\",\"quantity\":1,\"realAmount\":12.21,\"type\":1,\"userId\":1385229}";
        AddWaitPayCarPurchaseRecord carPurchaseRecord = JSONObject.parseObject(s,AddWaitPayCarPurchaseRecord.class);
//        AddWaitPayCarPurchaseRecord purchaseRecord = new AddWaitPayCarPurchaseRecord(1,new BigDecimal(1),new BigDecimal(1),null,10488L,100000L,100000L,1,12D,"123","123");
        cardTradeService.payCarPurchaseCallBack(carPurchaseRecord);
    }


    @Test
    public void testBuyCard1() throws BusinessException {
        PurchaseCardDto input = new PurchaseCardDto();
        input.setActivityId(100021L);
        input.setUserId(3345731L);
        input.setQuantity(2);
        input.setRealAmount(40.0);
        input.setDiscountAmount(BigDecimal.ZERO);
        cardTradeService.purchaseCard(input, OperatorDto.getSystemOp());
    }

    @Test
    public void testBuyCard2() throws BusinessException {
        cardTradeService.updateOutTradeSeq(69L, "fff", OperatorDto.getSystemOp());
    }


    @Test
    public void testBuyCard3() throws BusinessException {
        AddWaitPayCarPurchaseRecord callback = new AddWaitPayCarPurchaseRecord();
        callback.setPurchaseId(69L);
        callback.setCardActivityId(100021L);
        callback.setUserId(34444L);
        callback.setQuantity(2);
        callback.setRealAmount(40.0);
        callback.setDiscountAmount(BigDecimal.ZERO);
        callback.setType(1);
        callback.setOutTradeSeq("fff");
        callback.setOperator("aa");
        cardTradeService.payCarPurchaseCallBack(callback);
    }

    @Test
    public void batchOfferCards() throws BusinessException {
        CardBatchOfferDto batchOfferDto = new CardBatchOfferDto();
        batchOfferDto.setActivityId(100085L);
        batchOfferDto.setOfferType(1);
        batchOfferDto.setRequestId("fajgkagjkagjakga");
        List<CardOfferDto> list = new ArrayList<>();
        list.add(new CardOfferDto(1385229L, 2));
        list.add(new CardOfferDto(3111533L, 1));
        list.add(new CardOfferDto(9453761L, 2));
        batchOfferDto.setList(list);
        cardTradeService.batchOfferCards(batchOfferDto, OperatorDto.getSystemOp());
    }

    @Test
    public void userCardHistory() throws BusinessException {
        String s = "{\"cardActivityId\":100008,\"operator\":\"pay-rpc\",\"outTradeSeq\":\"6220210107094313039WVl\",\"quantity\":1,\"realAmount\":12.21,\"type\":1,\"userId\":3346200}";
        UserCardOperationDto carPurchaseRecord = new UserCardOperationDto();
        carPurchaseRecord.setAmount(new BigDecimal(123));
        carPurchaseRecord.setAuthId("******************");
        carPurchaseRecord.setDiscountAmount(new BigDecimal(12));
        carPurchaseRecord.setOrderSeq("C2021010616560000015");
        carPurchaseRecord.setOriginSystem("app");
        carPurchaseRecord.setUserCardNo(1000047L);
//        AddWaitPayCarPurchaseRecord purchaseRecord = new AddWaitPayCarPurchaseRecord(1,new BigDecimal(1),new BigDecimal(1),null,10488L,100000L,100000L,1,12D,"123","123");
        cardTradeService.userCardHistory(carPurchaseRecord);
    }
    @Test
    public void testAgencyCardOffer(){
        Set<Long> userIds = new HashSet<>();
        userIds.add(10488L);
        userIds.add(5074L);
        userIds.add(100L);
        memberCardService.batchOfferCorporateCard(userIds, "000N", true, OperatorDto.getSystemOp());
    }

    @Test
    public void testAgencyCardDisable(){
        Set<Long> userIds = new HashSet<>();
        userIds.add(10488L);
        memberCardService.batchDisableCorporateCard(userIds, "000N", OperatorDto.getSystemOp());
    }

    @Test
    public void testQueryPageByUserId(){
        MemberCardQueryDto queryDto = new MemberCardQueryDto();
        queryDto.setCardType(3);
//        queryDto.setStartDate("20230212");
//        queryDto.setEndDate("20230422");
//        queryDto.setUserCardStatus(2);
//        queryDto.setPageNum(1);
//        queryDto.setPageSize(10);
        PageBeanBO<MemberCardListViewDto> memberCardListViewDtoPageBeanBO = memberCardService.queryPageByUserId(10299559L, queryDto);
        System.out.println(memberCardListViewDtoPageBeanBO);
    }

    @Test
    public void queryUserPurchaseListByCardId(){
        cardTradeService.queryUserPurchaseListByCardId(10488L, 100001L);
    }

    @Test
    public void cancelMemberCard(){
        memberCardService.cancelMemberCard(1384589L, 100286L, OperatorDto.getSystemOp());
    }

    @Test
    public void queryPurchasePage(){
        CardPurchasePageQueryDto queryDto = new CardPurchasePageQueryDto();
        queryDto.setUserId(10488L);
        cardTradeService.queryPurchaseRecordsPage(queryDto);
    }

    @Test
    public void getCardPurchaseDetail(){
        memberCardService.getCardPurchaseDetail(35L);
    }

    @Test
    public void getCardDiscountInfo() {
        CardDiscountInfoQueryDto queryDto = new CardDiscountInfoQueryDto();
        queryDto.setUserCardNo(1002307L);
        memberCardService.selectCardDiscountPage(queryDto);
    }

    @Test
    public void selectUserCardHistoryPage() {
        UserCardHistoryQueryDto queryDto = new UserCardHistoryQueryDto();
        queryDto.setUserCardNo(100008L);
        memberCardService.selectUserCardHistoryPage(queryDto);
    }


    @Test
    public void pullUnreadOfferedCards(){
        memberCardService.pullUnreadOfferedCards(890L, 10);
    }

//    @Resource
//    PurchaseOrderCancelJob purchaseOrderCancelJob;
//    @Test
//    public void ttt(){
//        purchaseOrderCancelJob.execute(null);
//    }

    @Test
    public void cancelFrozenCard() {
        OperatorDto operateDto = new OperatorDto();
        operateDto.setOperatorId(1L);
        operateDto.setOperatorName("unitTest");
        memberCardService.cancelMemberCard(99999L, 100297L, operateDto);
    }

    @Test
    public void cancelAfterDeductAmount() {
        DeductFrozenDiscountAmountDto input = new DeductFrozenDiscountAmountDto();
        input.setUserCardNo(99999L);
        input.setFreezeTime(DateUtil.getDateFromStr("2022-04-13 11:36:02", "yyyy-MM-dd hh:mm:ss"));
        input.setAmount(BigDecimal.valueOf(10));
        input.setOrderSeq("C382358927582TEST");

        OperatorDto operateDto = new OperatorDto();
        operateDto.setOperatorId(1L);
        operateDto.setOperatorName("unitTest");
        try {
            cardTradeService.deductFrozenDiscountAmount(input, operateDto);
        } catch (BusinessException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void cancelAfterUnfreeze() {
        UnfreezeDiscountAmountDto input = new UnfreezeDiscountAmountDto();
        input.setUserCardNo(99999L);
        input.setFreezeTime(DateUtil.getDateFromStr("2022-04-13 11:36:02", "yyyy-MM-dd hh:mm:ss"));
        input.setAmount(BigDecimal.valueOf(10));

        OperatorDto operateDto = new OperatorDto();
        operateDto.setOperatorId(1L);
        operateDto.setOperatorName("unitTest");
        try {
            cardTradeService.unfreezeDiscountAmount(input, operateDto);
        } catch (BusinessException e) {
            e.printStackTrace();
        }
    }
}