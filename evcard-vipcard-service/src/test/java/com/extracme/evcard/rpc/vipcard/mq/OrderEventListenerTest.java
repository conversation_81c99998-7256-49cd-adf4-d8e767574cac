package com.extracme.evcard.rpc.vipcard.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.extracme.evcard.mq.bean.md.MdEventEnum;
import com.extracme.evcard.rpc.vipcard.App;
import com.extracme.evcard.rpc.vipcard.dto.CardRefundDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/2/4
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class OrderEventListenerTest {

   /* @Resource
    private OrderEventListener orderEventListener;

    @Test
    public void testOrderEventListener() {
        CardRefundDto dto = new CardRefundDto();
        dto.setPayOrderNo("MP230202090000301");

        Message message = new Message();
        message.setTag(MdEventEnum.CARD_REFUND_SUCCESS.getTag());
        message.setBody(JSON.toJSONString(dto).getBytes());
        orderEventListener.consume(message, null);
    }*/


}
