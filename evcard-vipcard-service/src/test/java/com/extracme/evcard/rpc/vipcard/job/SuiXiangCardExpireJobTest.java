package com.extracme.evcard.rpc.vipcard.job;

import com.extracme.evcard.rpc.vipcard.App;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/2/4
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class SuiXiangCardExpireJobTest {

    @Resource
    private SuiXiangCardExpireJob suiXiangCardExpireJob;

    @Test
    public void testSuiXiangCardExpireJob() {
        suiXiangCardExpireJob.execute(null);
    }

}
