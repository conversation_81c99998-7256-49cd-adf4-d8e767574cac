<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.SuixiangCardUseMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUse">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_base_id" jdbcType="BIGINT" property="cardBaseId" />
    <result column="card_price_id" jdbcType="BIGINT" property="cardPriceId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="purchase_id" jdbcType="BIGINT" property="purchaseId" />
    <result column="card_type" jdbcType="INTEGER" property="cardType" />
    <result column="card_name" jdbcType="VARCHAR" property="cardName" />
    <result column="card_status" jdbcType="INTEGER" property="cardStatus" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="expires_time" jdbcType="TIMESTAMP" property="expiresTime" />
    <result column="total_order" jdbcType="INTEGER" property="totalOrder" />
    <result column="total_discount_amount" jdbcType="DECIMAL" property="totalDiscountAmount" />
    <result column="init_days" jdbcType="INTEGER" property="initDays" />
    <result column="available_days" jdbcType="INTEGER" property="availableDays" />
    <result column="used_days" jdbcType="INTEGER" property="usedDays" />
    <result column="frozen_days" jdbcType="INTEGER" property="frozenDays" />
    <result column="active_flag" jdbcType="INTEGER" property="activeFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>

  <resultMap id="BaseResultMap2" type="com.extracme.evcard.rpc.vipcard.dto.SuixiangCardUseCountDto">
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="cnt" jdbcType="INTEGER" property="count" />
  </resultMap>

  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    id, card_base_id, card_price_id, user_id, purchase_id, card_type, card_name, card_status, 
    start_time, expires_time, total_order, total_discount_amount, init_days, available_days, 
    used_days, frozen_days, active_flag, create_time, create_oper_id, create_oper_name, 
    update_time, update_oper_id, update_oper_name, is_deleted
  </sql>

  <sql id="Base_Column_List2">
    u.id, u.card_base_id, u.card_price_id, u.user_id, u.purchase_id, u.card_type, u.card_name, u.card_status,
    u.start_time, u.expires_time, u.total_order, u.total_discount_amount, u.init_days, u.available_days,
    u.used_days, u.frozen_days, u.active_flag, u.create_time, u.create_oper_id, u.create_oper_name,
    u.update_time, u.update_oper_id, u.update_oper_name, u.is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from suixiang_card_use
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from suixiang_card_use
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    delete from suixiang_card_use
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUse" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    insert into suixiang_card_use (id, card_base_id, card_price_id, 
      user_id, purchase_id, card_type, 
      card_name, card_status, start_time, 
      expires_time, total_order, total_discount_amount, 
      init_days, available_days, used_days, 
      frozen_days, active_flag, create_time, 
      create_oper_id, create_oper_name, update_time, 
      update_oper_id, update_oper_name, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{cardBaseId,jdbcType=BIGINT}, #{cardPriceId,jdbcType=BIGINT}, 
      #{userId,jdbcType=BIGINT}, #{purchaseId,jdbcType=BIGINT}, #{cardType,jdbcType=INTEGER}, 
      #{cardName,jdbcType=VARCHAR}, #{cardStatus,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP}, 
      #{expiresTime,jdbcType=TIMESTAMP}, #{totalOrder,jdbcType=INTEGER}, #{totalDiscountAmount,jdbcType=DECIMAL},
      #{initDays,jdbcType=INTEGER}, #{availableDays,jdbcType=INTEGER}, #{usedDays,jdbcType=INTEGER}, 
      #{frozenDays,jdbcType=INTEGER}, #{activeFlag,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUse" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    insert into suixiang_card_use
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cardBaseId != null">
        card_base_id,
      </if>
      <if test="cardPriceId != null">
        card_price_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="purchaseId != null">
        purchase_id,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="cardName != null">
        card_name,
      </if>
      <if test="cardStatus != null">
        card_status,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="expiresTime != null">
        expires_time,
      </if>
      <if test="totalOrder != null">
        total_order,
      </if>
      <if test="totalDiscountAmount != null">
        total_discount_amount,
      </if>
      <if test="initDays != null">
        init_days,
      </if>
      <if test="availableDays != null">
        available_days,
      </if>
      <if test="usedDays != null">
        used_days,
      </if>
      <if test="frozenDays != null">
        frozen_days,
      </if>
      <if test="activeFlag != null">
        active_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cardBaseId != null">
        #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null">
        #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="purchaseId != null">
        #{purchaseId,jdbcType=BIGINT},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=INTEGER},
      </if>
      <if test="cardName != null">
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="cardStatus != null">
        #{cardStatus,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expiresTime != null">
        #{expiresTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalOrder != null">
        #{totalOrder,jdbcType=INTEGER},
      </if>
      <if test="totalDiscountAmount != null">
        #{totalDiscountAmount,jdbcType=DECIMAL},
      </if>
      <if test="initDays != null">
        #{initDays,jdbcType=INTEGER},
      </if>
      <if test="availableDays != null">
        #{availableDays,jdbcType=INTEGER},
      </if>
      <if test="usedDays != null">
        #{usedDays,jdbcType=INTEGER},
      </if>
      <if test="frozenDays != null">
        #{frozenDays,jdbcType=INTEGER},
      </if>
      <if test="activeFlag != null">
        #{activeFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    select count(*) from suixiang_card_use
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    update suixiang_card_use
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardBaseId != null">
        card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.cardPriceId != null">
        card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.purchaseId != null">
        purchase_id = #{record.purchaseId,jdbcType=BIGINT},
      </if>
      <if test="record.cardType != null">
        card_type = #{record.cardType,jdbcType=INTEGER},
      </if>
      <if test="record.cardName != null">
        card_name = #{record.cardName,jdbcType=VARCHAR},
      </if>
      <if test="record.cardStatus != null">
        card_status = #{record.cardStatus,jdbcType=INTEGER},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expiresTime != null">
        expires_time = #{record.expiresTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.totalOrder != null">
        total_order = #{record.totalOrder,jdbcType=INTEGER},
      </if>
      <if test="record.totalDiscountAmount != null">
        total_discount_amount = #{record.totalDiscountAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.initDays != null">
        init_days = #{record.initDays,jdbcType=INTEGER},
      </if>
      <if test="record.availableDays != null">
        available_days = #{record.availableDays,jdbcType=INTEGER},
      </if>
      <if test="record.usedDays != null">
        used_days = #{record.usedDays,jdbcType=INTEGER},
      </if>
      <if test="record.frozenDays != null">
        frozen_days = #{record.frozenDays,jdbcType=INTEGER},
      </if>
      <if test="record.activeFlag != null">
        active_flag = #{record.activeFlag,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null">
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null">
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null">
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null">
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    update suixiang_card_use
    set id = #{record.id,jdbcType=BIGINT},
      card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      purchase_id = #{record.purchaseId,jdbcType=BIGINT},
      card_type = #{record.cardType,jdbcType=INTEGER},
      card_name = #{record.cardName,jdbcType=VARCHAR},
      card_status = #{record.cardStatus,jdbcType=INTEGER},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      expires_time = #{record.expiresTime,jdbcType=TIMESTAMP},
      total_order = #{record.totalOrder,jdbcType=INTEGER},
      total_discount_amount = #{record.totalDiscountAmount,jdbcType=DECIMAL},
      init_days = #{record.initDays,jdbcType=INTEGER},
      available_days = #{record.availableDays,jdbcType=INTEGER},
      used_days = #{record.usedDays,jdbcType=INTEGER},
      frozen_days = #{record.frozenDays,jdbcType=INTEGER},
      active_flag = #{record.activeFlag,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUse">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    update suixiang_card_use
    <set>
      <if test="cardBaseId != null">
        card_base_id = #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null">
        card_price_id = #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="purchaseId != null">
        purchase_id = #{purchaseId,jdbcType=BIGINT},
      </if>
      <if test="cardType != null">
        card_type = #{cardType,jdbcType=INTEGER},
      </if>
      <if test="cardName != null">
        card_name = #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="cardStatus != null">
        card_status = #{cardStatus,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expiresTime != null">
        expires_time = #{expiresTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalOrder != null">
        total_order = #{totalOrder,jdbcType=INTEGER},
      </if>
      <if test="totalDiscountAmount != null">
        total_discount_amount = #{totalDiscountAmount,jdbcType=DECIMAL},
      </if>
      <if test="initDays != null">
        init_days = #{initDays,jdbcType=INTEGER},
      </if>
      <if test="availableDays != null">
        available_days = #{availableDays,jdbcType=INTEGER},
      </if>
      <if test="usedDays != null">
        used_days = #{usedDays,jdbcType=INTEGER},
      </if>
      <if test="frozenDays != null">
        frozen_days = #{frozenDays,jdbcType=INTEGER},
      </if>
      <if test="activeFlag != null">
        active_flag = #{activeFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUse">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 16:57:10 CST 2023.
    -->
    update suixiang_card_use
    set card_base_id = #{cardBaseId,jdbcType=BIGINT},
      card_price_id = #{cardPriceId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      purchase_id = #{purchaseId,jdbcType=BIGINT},
      card_type = #{cardType,jdbcType=INTEGER},
      card_name = #{cardName,jdbcType=VARCHAR},
      card_status = #{cardStatus,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      expires_time = #{expiresTime,jdbcType=TIMESTAMP},
      total_order = #{totalOrder,jdbcType=INTEGER},
      total_discount_amount = #{totalDiscountAmount,jdbcType=DECIMAL},
      init_days = #{initDays,jdbcType=INTEGER},
      available_days = #{availableDays,jdbcType=INTEGER},
      used_days = #{usedDays,jdbcType=INTEGER},
      frozen_days = #{frozenDays,jdbcType=INTEGER},
      active_flag = #{activeFlag,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectListByUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from suixiang_card_use
    where user_id = #{userId,jdbcType=BIGINT}
    and card_status = #{cardStatus,jdbcType=BIGINT}
    and is_deleted = 0
  </select>

  <select id="selectListByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List2" />
    from suixiang_card_use u
    left join suixiang_card_price t3 on t3.id = u.card_price_id
    left join suixiang_card_base t1 on t1.id = u.card_base_id
    where u.user_id = #{userId,jdbcType=Long}
    and u.is_deleted = 0
    <if test="cardStatus != null">
        and u.card_status = #{cardStatus,jdbcType=BIGINT}
    </if>
    <if test="carModelId != null">
      and (find_in_set(#{carModelId},t3.car_model_ids))
    </if>
    <if test="cityId != null">
      and (find_in_set(#{cityId},t1.city_id))
    </if>
  </select>


  <select id="selectCardListByUserId" parameterType="com.extracme.evcard.rpc.vipcard.dto.suixiang.SuiXiangCardUseQueryDto" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.suixiang_card_use
    where is_deleted = 0
    and user_id = #{userId}

    <if test="condition.cardName != null and condition.cardName != '' ">
      and card_name like concat('%',#{condition.cardName},'%')
    </if>


    <if test="condition.userCardStatus != null and condition.userCardStatus != -1">
      <!-- 非已过期 -->
      <if test=" condition.userCardStatus != 2 "> and card_status = #{condition.userCardStatus}</if>
      <!-- 已过期 -->
      <if test=" condition.userCardStatus == 2 "> and card_status = 2 AND expires_time &lt; now() </if>
    </if>

    <if test="condition.startDate != null and condition.startDate != '' ">
      and start_time &lt; #{condition.startDate}
    </if>
    <if test="condition.endDate != null and condition.endDate != '' ">
      and (expires_time is null or expires_time &gt;= #{condition.endDate})
    </if>
    order by create_time desc
  </select>

  <update id="expireSuiXiangCardUse">
    update ${issSchema}.suixiang_card_use
      set card_status = 2,
          update_oper_name = 'system'
      where card_type = 1
            and card_status = 1
            and is_deleted = 0
            and (expires_time is null or expires_time &lt; now())
  </update>

    <select id="selectCardListByUserIdForMmp" parameterType="com.extracme.evcard.rpc.vipcard.dto.MemberCardQueryDto" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List" />
        from ${issSchema}.suixiang_card_use
        where is_deleted = 0
              and user_id = #{userId}
            <if test="condition.cardId != null and condition.cardId != '' ">
                and card_base_id = #{condition.cardId}
            </if>
            <if test="condition.cardName != null and condition.cardName != '' ">
                and card_name like concat('%',#{condition.cardName},'%')
            </if>
            <if test="condition.userCardStatus != null">
                <!-- 1生效中 -->
                <if test=" condition.userCardStatus==1 ">
                  and card_status = 1 and expires_time >= now()
                </if>
                <!-- 2已失效，对应库表里1和3/8以外的，都认为是失效 -->
                <if test=" condition.userCardStatus==2 ">
                  and (card_status not in(1, 3, 8) or (card_status = 1 and expires_time &lt; now()))
                </if>
                <!-- 3已冻结，对应库表里3和8 -->
                <if test=" condition.userCardStatus==3 ">
                  and card_status in(3, 8)
                </if>
            </if>
            <if test="condition.startDate != null and condition.startDate != '' ">
                and start_time &gt;= #{condition.startDate}
            </if>
            <if test="condition.endDate != null and condition.endDate != '' ">
                and (expires_time is null or expires_time &lt;= #{condition.endDate})
            </if>
        order by expires_time desc
    </select>

  <select id="selectForPage" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.suixiang_card_use
    where card_type = 1
      and card_status in(1, 3)
      and is_deleted = 0
      and (expires_time is null or expires_time &lt; now())
      and id >= #{minId}
    order by id
      limit 100
  </select>

  <select id="selectCountByUserId" parameterType="map" resultMap="BaseResultMap2">
    select user_id,
           count(1) as cnt
    from ${issSchema}.suixiang_card_use
    where card_type = 1
        and card_status in(1, 3)
        and is_deleted = 0
        <if test="userIdList != null and userIdList.size > 0">
          and user_id in
          <foreach collection="userIdList" item="item" index="index" separator="," open="(" close=")">
            #{item}
          </foreach>
        </if>
    group by user_id
  </select>


  <select id="selectCanMergeCardList" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List2" />
    FROM
    ${issSchema}.suixiang_card_use u
    LEFT JOIN ${issSchema}.suixiang_card_base b ON u.card_base_id = b.id
    LEFT JOIN ${issSchema}.suixiang_card_purchase_record p ON u.purchase_id = p.id
    WHERE
    u.card_base_id = #{cardBaseId,jdbcType=BIGINT}
    AND u.user_id = #{userId,jdbcType=BIGINT}
    AND u.card_status = 1
    AND b.merge_flag = 1
    AND p.issue_type = 3
    order by u.id desc
  </select>

  <select id="selectByCdkId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List2" />
    FROM
    ${issSchema}.suixiang_card_use u
    LEFT JOIN ${issSchema}.suixiang_card_purchase_record p ON u.purchase_id = p.id
    WHERE
    p.cdk_id = #{cdkId,jdbcType=BIGINT}
    limit 1
  </select>

  <update id="mergeSuiXiangCardUse" parameterType="com.extracme.evcard.rpc.vipcard.dto.SuiXiangCardMergeDto">
    update ${issSchema}.suixiang_card_use
    set
      init_days = init_days + #{operateDay,jdbcType=INTEGER},
      available_days = available_days + #{operateDay,jdbcType=INTEGER},
      expires_time = #{expiresTime,jdbcType=TIMESTAMP},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      merge_flag = 1
    where id = #{suiXiangCardUseId,jdbcType=BIGINT}
    and card_status = 1
  </update>

</mapper>