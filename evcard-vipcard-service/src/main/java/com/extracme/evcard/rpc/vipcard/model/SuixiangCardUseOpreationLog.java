package com.extracme.evcard.rpc.vipcard.model;

import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.UseSuiXiangCardDaysInput;

import java.math.BigDecimal;
import java.util.Date;

public class SuixiangCardUseOpreationLog {


    public static SuixiangCardUseOpreationLog getLogFromSuixiangCardUse(SuixiangCardUse use, Long operationType, OperatorDto dto){
        SuixiangCardUseOpreationLog log = new SuixiangCardUseOpreationLog();
        log.setCardUseId(use.getId());
        log.setCardPriceId(use.getCardPriceId());
        log.setPurchaseId(use.getPurchaseId());
        log.setCardGroup(1);
        log.setOperationType(operationType);
        log.setInitDays(use.getInitDays());
        log.setAvailableDays(use.getAvailableDays());
        log.setUsedDays(use.getUsedDays());
        log.setFrozenDays(use.getFrozenDays());

        log.setOriginSystem(dto.getOriginSystem());
        log.setCreateOperId(dto.getOperatorId());
        log.setCreateOperName(dto.getOperatorName());
        log.setCreateTime(new Date());
        log.setMiscDesc(dto.getRemark());
        return log;
    }

    public static SuixiangCardUseOpreationLog getLogFromSuixiangCardUse2(SuixiangCardUse use, Long operationType, UseSuiXiangCardDaysInput input){
        OperatorDto operateDto = input.getOperateDto();
        SuixiangCardUseOpreationLog log = getLogFromSuixiangCardUse(use, operationType, operateDto);
        log.setOrderSeq(input.getContractId());
        log.setDiscountAmount(input.getTotalAmount());
        log.setOrderOperationDays(input.getDays());
        log.setCreateTime(input.getApplyTime());
        return log;
    }

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.card_use_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Long cardUseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.card_price_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Long cardPriceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.purchase_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Long purchaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.card_group
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Integer cardGroup;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.operation_type
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Long operationType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.origin_system
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private String originSystem;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.ref_key
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private String refKey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.order_seq
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private String orderSeq;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.order_operation_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Integer orderOperationDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.discount_amount
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private BigDecimal discountAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.amount
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private BigDecimal amount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.real_amount
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private BigDecimal realAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.misc_desc
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.init_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Integer initDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.available_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Integer availableDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.used_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Integer usedDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.frozen_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Integer frozenDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.create_time
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.create_oper_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.create_oper_name
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.update_time
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.update_oper_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.update_oper_name
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_opreation_log.is_deleted
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    private Integer isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.id
     *
     * @return the value of suixiang_card_use_opreation_log.id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.id
     *
     * @param id the value for suixiang_card_use_opreation_log.id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.card_use_id
     *
     * @return the value of suixiang_card_use_opreation_log.card_use_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Long getCardUseId() {
        return cardUseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.card_use_id
     *
     * @param cardUseId the value for suixiang_card_use_opreation_log.card_use_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setCardUseId(Long cardUseId) {
        this.cardUseId = cardUseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.card_price_id
     *
     * @return the value of suixiang_card_use_opreation_log.card_price_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Long getCardPriceId() {
        return cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.card_price_id
     *
     * @param cardPriceId the value for suixiang_card_use_opreation_log.card_price_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setCardPriceId(Long cardPriceId) {
        this.cardPriceId = cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.purchase_id
     *
     * @return the value of suixiang_card_use_opreation_log.purchase_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Long getPurchaseId() {
        return purchaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.purchase_id
     *
     * @param purchaseId the value for suixiang_card_use_opreation_log.purchase_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setPurchaseId(Long purchaseId) {
        this.purchaseId = purchaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.card_group
     *
     * @return the value of suixiang_card_use_opreation_log.card_group
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Integer getCardGroup() {
        return cardGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.card_group
     *
     * @param cardGroup the value for suixiang_card_use_opreation_log.card_group
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setCardGroup(Integer cardGroup) {
        this.cardGroup = cardGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.operation_type
     *
     * @return the value of suixiang_card_use_opreation_log.operation_type
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Long getOperationType() {
        return operationType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.operation_type
     *
     * @param operationType the value for suixiang_card_use_opreation_log.operation_type
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setOperationType(Long operationType) {
        this.operationType = operationType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.origin_system
     *
     * @return the value of suixiang_card_use_opreation_log.origin_system
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public String getOriginSystem() {
        return originSystem;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.origin_system
     *
     * @param originSystem the value for suixiang_card_use_opreation_log.origin_system
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setOriginSystem(String originSystem) {
        this.originSystem = originSystem;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.ref_key
     *
     * @return the value of suixiang_card_use_opreation_log.ref_key
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public String getRefKey() {
        return refKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.ref_key
     *
     * @param refKey the value for suixiang_card_use_opreation_log.ref_key
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setRefKey(String refKey) {
        this.refKey = refKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.order_seq
     *
     * @return the value of suixiang_card_use_opreation_log.order_seq
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public String getOrderSeq() {
        return orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.order_seq
     *
     * @param orderSeq the value for suixiang_card_use_opreation_log.order_seq
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.order_operation_days
     *
     * @return the value of suixiang_card_use_opreation_log.order_operation_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Integer getOrderOperationDays() {
        return orderOperationDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.order_operation_days
     *
     * @param orderOperationDays the value for suixiang_card_use_opreation_log.order_operation_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setOrderOperationDays(Integer orderOperationDays) {
        this.orderOperationDays = orderOperationDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.discount_amount
     *
     * @return the value of suixiang_card_use_opreation_log.discount_amount
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.discount_amount
     *
     * @param discountAmount the value for suixiang_card_use_opreation_log.discount_amount
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.amount
     *
     * @return the value of suixiang_card_use_opreation_log.amount
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.amount
     *
     * @param amount the value for suixiang_card_use_opreation_log.amount
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.real_amount
     *
     * @return the value of suixiang_card_use_opreation_log.real_amount
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public BigDecimal getRealAmount() {
        return realAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.real_amount
     *
     * @param realAmount the value for suixiang_card_use_opreation_log.real_amount
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.misc_desc
     *
     * @return the value of suixiang_card_use_opreation_log.misc_desc
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.misc_desc
     *
     * @param miscDesc the value for suixiang_card_use_opreation_log.misc_desc
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.init_days
     *
     * @return the value of suixiang_card_use_opreation_log.init_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Integer getInitDays() {
        return initDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.init_days
     *
     * @param initDays the value for suixiang_card_use_opreation_log.init_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setInitDays(Integer initDays) {
        this.initDays = initDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.available_days
     *
     * @return the value of suixiang_card_use_opreation_log.available_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Integer getAvailableDays() {
        return availableDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.available_days
     *
     * @param availableDays the value for suixiang_card_use_opreation_log.available_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setAvailableDays(Integer availableDays) {
        this.availableDays = availableDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.used_days
     *
     * @return the value of suixiang_card_use_opreation_log.used_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Integer getUsedDays() {
        return usedDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.used_days
     *
     * @param usedDays the value for suixiang_card_use_opreation_log.used_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setUsedDays(Integer usedDays) {
        this.usedDays = usedDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.frozen_days
     *
     * @return the value of suixiang_card_use_opreation_log.frozen_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Integer getFrozenDays() {
        return frozenDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.frozen_days
     *
     * @param frozenDays the value for suixiang_card_use_opreation_log.frozen_days
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setFrozenDays(Integer frozenDays) {
        this.frozenDays = frozenDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.create_time
     *
     * @return the value of suixiang_card_use_opreation_log.create_time
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.create_time
     *
     * @param createTime the value for suixiang_card_use_opreation_log.create_time
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.create_oper_id
     *
     * @return the value of suixiang_card_use_opreation_log.create_oper_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.create_oper_id
     *
     * @param createOperId the value for suixiang_card_use_opreation_log.create_oper_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.create_oper_name
     *
     * @return the value of suixiang_card_use_opreation_log.create_oper_name
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.create_oper_name
     *
     * @param createOperName the value for suixiang_card_use_opreation_log.create_oper_name
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.update_time
     *
     * @return the value of suixiang_card_use_opreation_log.update_time
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.update_time
     *
     * @param updateTime the value for suixiang_card_use_opreation_log.update_time
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.update_oper_id
     *
     * @return the value of suixiang_card_use_opreation_log.update_oper_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_use_opreation_log.update_oper_id
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.update_oper_name
     *
     * @return the value of suixiang_card_use_opreation_log.update_oper_name
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_use_opreation_log.update_oper_name
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_opreation_log.is_deleted
     *
     * @return the value of suixiang_card_use_opreation_log.is_deleted
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_opreation_log.is_deleted
     *
     * @param isDeleted the value for suixiang_card_use_opreation_log.is_deleted
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}