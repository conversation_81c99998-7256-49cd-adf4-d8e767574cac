package com.extracme.evcard.rpc.vipcard.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.config.HqEncryptConfig;
import com.extracme.evcard.rpc.vipcard.rest.entity.*;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class MdRestClient extends AbstractRestClient {

    @Resource
    private HqEncryptConfig hqEncryptConfig;


    public GetCachedGoodsModelRes getCachedGoodsModel(String cfgMd5) throws BusinessException {
        GetCachedConfigReq request = new GetCachedConfigReq();
        request.setCfgMd5(cfgMd5);
        return postForObject(mdRestApiConfig.getGetCachedGoodsModelUrl(), request, GetCachedGoodsModelRes.class);
    }

    public GetCachedStoreListRes getCachedStoreList(String cfgMd5) throws BusinessException {
        GetCachedConfigReq request = new GetCachedConfigReq();
        request.setCfgMd5(cfgMd5);
        return postForObject(mdRestApiConfig.getGetCachedStoreListUrl(), null, GetCachedStoreListRes.class);
    }

    public GetMappedGoodVehicleModelRes getGoodsModelByVehicleModel(Long vehicleModelSeq) throws BusinessException {
        GetMappedGoodVehicleModelReq request = new GetMappedGoodVehicleModelReq();
        request.setVehicleModelSeq(vehicleModelSeq);
        return postForObject(mdRestApiConfig.getGetGoodsModelByVehicleModelUrl(), request, GetMappedGoodVehicleModelRes.class);
    }

    public PayOrderIsPayingOrNotRes orderIsPayingOrNot(String payOrderNo) throws BusinessException {
        PayOrderIsPayingOrNotReq request = new PayOrderIsPayingOrNotReq();
        request.setPayOrderNo(payOrderNo);
        return postForObject(mdRestApiConfig.getPayOrderIsPayingOrNotUrl(), request, PayOrderIsPayingOrNotRes.class);
    }

    //TODO 缓存处理
    public GetAllGoodsModeInfoRes getAllGoodsModeInfo() throws BusinessException {
        GetAllGoodsModeInfoReq request = new GetAllGoodsModeInfoReq();
        request.setTenantId(0L);
        return postForObject(mdRestApiConfig.getGetAllGoodsModeInfoUrl(), request, GetAllGoodsModeInfoRes.class);
    }

    public CreatelPayOrderRes createPayOrder(CreatePayOrderReq request) throws BusinessException {
        log.info("调用支付的createPayOrder接口，请求：{}", JSON.toJSONString(request));
        CreatelPayOrderRes res = postForObject(mdRestApiConfig.getCreatePayOrderUrl(), request, CreatelPayOrderRes.class);
        log.info("调用支付的createPayOrder接口，应答：{}", JSON.toJSONString(res));
        return res;
    }

    public CancelPayOrderRes cancelPayOrder(String mid,String orderNo) throws BusinessException {
        CancelPayOrderReq request = new CancelPayOrderReq();
        request.setMid(mid);
        request.setOrderNo(orderNo);
        log.info("调用支付的cancelPayOrder接口，请求：{}", JSON.toJSONString(request));
        CancelPayOrderRes res = postForObject(mdRestApiConfig.getCancelPayOrderUrl(), request, CancelPayOrderRes.class);
        log.info("调用支付的cancelPayOrder接口，应答：{}", JSON.toJSONString(res));
        return res;
    }

    public RefundForCriticalPointPaymentRes refundForCriticalPointPayment(String payOrderNo) throws BusinessException {
        log.info("调用支付的 refundForCriticalPointPayment 接口，请求：{}", JSON.toJSONString(payOrderNo));

        Map<String,Object> request = new HashMap<>();
        request.put("payOrderNo", payOrderNo);
        String enCodeRequest = null;
        try {
            enCodeRequest = ComUtils.buildNstRequestBody(request, hqEncryptConfig.outPublicKey, hqEncryptConfig.innerPrivateKey);
        } catch (Exception e) {
            log.error("refundForCriticalPointPayment 入参加密失败,payOrderNo={}",payOrderNo,e);
            RefundForCriticalPointPaymentRes refundForCriticalPointPaymentRes = new RefundForCriticalPointPaymentRes();
            refundForCriticalPointPaymentRes.setCode(-1);
            refundForCriticalPointPaymentRes.setMessage("入参加密失败");
            return refundForCriticalPointPaymentRes;
        }
        RefundForCriticalPointPaymentRes res = postForObject(mdRestApiConfig.getRefundForCriticalPointPaymentUrl(), enCodeRequest, RefundForCriticalPointPaymentRes.class);
        log.info("调用支付的 refundForCriticalPointPayment 接口，应答：{}", JSON.toJSONString(res));
        return res;
    }



    public PaySuiXiangCardOrderRes paySuiXiangCardOrder(PaySuiXiangCardOrderReq request) throws BusinessException {
        log.info("调用支付的paySuiXiangCardOrder接口，请求：{}", JSON.toJSONString(request));
        PaySuiXiangCardOrderRes res = postForObject(mdRestApiConfig.getPaySuiXiangCardOrderUrl(), request, PaySuiXiangCardOrderRes.class);
        log.info("调用支付的paySuiXiangCardOrder接口，应答：{}", JSON.toJSONString(res));
        return res;
    }

    public ReturnCardFeeRes returnCardFee(ReturnCardFeeReq request) throws BusinessException {
        log.info("调用支付的returnCardFee接口，请求：{}", JSON.toJSONString(request));
        ReturnCardFeeRes resp = postForObject(mdRestApiConfig.getReturnCardFeeUrl(), request, ReturnCardFeeRes.class);
        log.info("调用支付的returnCardFee接口，应答：{}", JSON.toJSONString(resp));
        return resp;
    }

    public GetCacheLowAndPeakSeasonConfigRes getCacheLowAndPeakSeasonConfig(String cfgMd5) throws BusinessException {
        GetCacheLowAndPeakSeasonConfigReq request = new GetCacheLowAndPeakSeasonConfigReq();
        request.setCfgMd5(cfgMd5);
        log.info("调用支付的 getCacheLowAndPeakSeasonConfig 接口，请求：{}", JSON.toJSONString(request));
        GetCacheLowAndPeakSeasonConfigRes res = postForObject(mdRestApiConfig.getGetCacheLowAndPeakSeasonConfigUrl(), request, GetCacheLowAndPeakSeasonConfigRes.class);
        log.info("调用支付的 getCacheLowAndPeakSeasonConfig 接口，应答：{}", JSON.toJSONString(res));
        return res;
    }

}
