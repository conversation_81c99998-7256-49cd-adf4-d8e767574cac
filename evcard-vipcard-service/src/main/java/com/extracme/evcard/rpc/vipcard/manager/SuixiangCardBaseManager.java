package com.extracme.evcard.rpc.vipcard.manager;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.vipcard.dao.*;
import com.extracme.evcard.rpc.vipcard.dto.ServiceFeeCfgDto;
import com.extracme.evcard.rpc.vipcard.dto.SuiXiangCardMergeDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.PurchaseSuiXiangCardRecordInfo;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.UseSuiXiangCardDaysInput;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.pay.ContractItemFeeDto;
import com.extracme.evcard.rpc.vipcard.enums.*;
import com.extracme.evcard.rpc.vipcard.model.*;
import com.extracme.evcard.rpc.vipcard.rest.MdRestClient;
import com.extracme.evcard.rpc.vipcard.rest.entity.CreatePayOrderReq;
import com.extracme.evcard.rpc.vipcard.rest.entity.CreatePayOrderResData;
import com.extracme.evcard.rpc.vipcard.rest.entity.CreatelPayOrderRes;
import com.extracme.evcard.rpc.vipcard.service.inner.CityServ;
import com.extracme.evcard.rpc.vipcard.service.inner.OrgService;
import com.extracme.evcard.rpc.vipcard.service.store.ConfigLoader;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
@Slf4j
@Service
public class SuixiangCardBaseManager {

    private static final String LOG_FORMAT_STR = "%s：%s->%s；";
    private static final String LOG_FORMAT_NUM = "%s：%d->%d；";
    @Resource
    private SuixiangCardBaseMapper suixiangCardBaseMapper;
    @Resource
    private SuixiangCardRentDaysMapper suixiangCardRentDaysMapper;
    @Resource
    private SuixiangCardPriceMapper suixiangCardPriceMapper;
    @Resource
    private SuixiangCardConfigOperationLogMapper suixiangCardConfigOperationLogMapper;
    @Resource
    private SuixiangCardPurchaseRecordMapper suixiangCardPurchaseRecordMapper;
    @Resource
    private SuixiangCardPurchaseRecordLogMapper suixiangCardPurchaseRecordLogMapper;
    @Resource
    private SuixiangCardUseMapper suixiangCardUseMapper;
    @Resource
    private SuixiangCardUseOpreationLogMapper suixiangCardUseOpreationLogMapper;
    @Resource
    private SuixiangCardCdkMapper suixiangCardCdkMapper;
    @Resource
    private SuixiangCardUseTempMapper suixiangCardUseTempMapper;
    @Resource
    private OrgService orgService;
    @Resource
    private CityServ cityServ;
    @Resource
    private ConfigLoader configLoader;
    @Resource
    private MdRestClient mdRestClient;
    @Resource
    private IMessagepushServ messagepushServ;
    @Value("${ons.topic.delay}")
    private String delayTopic;

    @Transactional(rollbackFor = Exception.class)
    public Long addSuiXiangCard(SuixiangCardBase base,
                                List<SuixiangCardRentDays> rentDaysList,
                                SuixiangCardConfigOperationLog operationLog) {
        suixiangCardBaseMapper.insertSelective(base);
        Long baseId = base.getId();

        for (SuixiangCardRentDays rentDays : rentDaysList) {
            rentDays.setCardBaseId(baseId);
            suixiangCardRentDaysMapper.insertSelective(rentDays);
            Long rentDaysId = rentDays.getId();

            for (SuixiangCardPrice cardPrice : rentDays.getPriceList()) {
                cardPrice.setCardBaseId(baseId);
                cardPrice.setCardRentId(rentDaysId);
                suixiangCardPriceMapper.insertSelective(cardPrice);
            }
        }

        operationLog.setCardBaseId(baseId);
        suixiangCardConfigOperationLogMapper.insertSelective(operationLog);

        return baseId;
    }

    @Transactional(rollbackFor = Exception.class)
    public void modifySuiXiangCard(SuixiangCardBase base,
                                   List<SuixiangCardRentDays> rentDaysList,
                                   SuixiangCardConfigOperationLog operationLog,
                                   SuixiangCardBase baseOrg) {
        int ret = suixiangCardBaseMapper.updateByPrimaryKey2(base);
        if (ret != 1) {
            log.error("更新suixiang_card_base表失败！base={}", JSON.toJSONString(base));
            throw new RuntimeException("更新suixiang_card_base表失败！base=" + JSON.toJSONString(base));
        }

        // 查询老的rentDaysOrgList
        SuixiangCardRentDaysExample rentDaysExample = new SuixiangCardRentDaysExample();
        rentDaysExample.createCriteria()
                .andCardBaseIdEqualTo(base.getId())
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardRentDays> rentDaysOrgList = suixiangCardRentDaysMapper.selectByExample(rentDaysExample);
        if (CollectionUtils.isEmpty(rentDaysOrgList)) {
            log.error("查询不到老的suixiang_card_rent_days表记录！baseId={}", base.getId());
            throw new RuntimeException("查询不到老的suixiang_card_rent_days表记录！baseId=" + base.getId());
        }
        List<Long> rentDaysIdList = rentDaysOrgList.stream().map(item -> item.getId()).collect(Collectors.toList());

        // 老的rentDaysOrgList进行逻辑删除
        SuixiangCardRentDays rentDaysUpdate = new SuixiangCardRentDays();
        rentDaysUpdate.setIsDeleted(IsDeleteEnum.DELETE.getIsDelete());
        ret = suixiangCardRentDaysMapper.updateByExampleSelective(rentDaysUpdate, rentDaysExample);
        if (ret < 1) {
            log.error("suixiang_card_rent_days表老记录逻辑删除失败！baseId={}", base.getId());
            throw new RuntimeException("suixiang_card_rent_days表老记录逻辑删除失败！baseId=" + base.getId());
        }

        // 查询老的priceOrgList
        SuixiangCardPriceExample priceExample = new SuixiangCardPriceExample();
        priceExample.createCriteria()
                .andCardBaseIdEqualTo(base.getId())
                .andCardRentIdIn(rentDaysIdList)
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardPrice> priceOrgList = suixiangCardPriceMapper.selectByExample(priceExample);
        if (CollectionUtils.isEmpty(priceOrgList)) {
            log.error("查询不到老的suixiang_card_price表记录！baseId={},rentId={}", base.getId(), JSON.toJSONString(rentDaysIdList));
            throw new RuntimeException("查询不到老的suixiang_card_price表记录！baseId=" + base.getId() + ",rentId=" + JSON.toJSONString(rentDaysIdList));
        }

        // 老的priceOrgList进行逻辑删除
        SuixiangCardPrice priceUpdate = new SuixiangCardPrice();
        priceUpdate.setIsDeleted(IsDeleteEnum.DELETE.getIsDelete());
        ret = suixiangCardPriceMapper.updateByExampleSelective(priceUpdate, priceExample);
        if (ret < 1) {
            log.error("suixiang_card_price表老记录逻辑删除失败！baseId={},rentId={}", base.getId(), JSON.toJSONString(rentDaysIdList));
            throw new RuntimeException("suixiang_card_price表老记录逻辑删除失败！baseId=" + base.getId() + ",rentId=" + JSON.toJSONString(rentDaysIdList));
        }

        // 新增suixiang_card_rent_days和suixiang_card_price表记录
        for (SuixiangCardRentDays rentDays : rentDaysList) {
            rentDays.setCardBaseId(base.getId());
            suixiangCardRentDaysMapper.insertSelective(rentDays);
            Long rentDaysId = rentDays.getId();

            for (SuixiangCardPrice cardPrice : rentDays.getPriceList()) {
                cardPrice.setCardBaseId(base.getId());
                cardPrice.setCardRentId(rentDaysId);
                suixiangCardPriceMapper.insertSelective(cardPrice);
            }
        }

        // 把老的priceOrgList合并到老的rentDaysOrgList中
        Map<Long, List<SuixiangCardPrice>> rentDaysPriceMap = priceOrgList.stream().collect(Collectors.groupingBy(SuixiangCardPrice::getCardRentId));
        for (SuixiangCardRentDays rentDays : rentDaysOrgList) {
            rentDays.setPriceList(rentDaysPriceMap.get(rentDays.getId()));
        }

        // 拼装操作日志内容
        StringBuffer content = appendContent(baseOrg, base, rentDaysOrgList, rentDaysList);

        // 新增操作员日志
        operationLog.setCardBaseId(base.getId());
        operationLog.setOperationContent(ComUtils.splitStr(content.toString(), 1000));
        suixiangCardConfigOperationLogMapper.insertSelective(operationLog);
    }

    private StringBuffer appendContent(SuixiangCardBase baseOrg, SuixiangCardBase base,
                                       List<SuixiangCardRentDays> rentDaysOrgList, List<SuixiangCardRentDays> rentDaysList) {
        StringBuffer content = new StringBuffer();
        if (ObjectUtils.notEqual(baseOrg.getCardName(), base.getCardName())) {
            content.append(String.format(LOG_FORMAT_STR, "随享卡名称", baseOrg.getCardName(), base.getCardName()));
        }
        if (ObjectUtils.notEqual(baseOrg.getOrgId(), base.getOrgId())) {
            content.append(String.format(LOG_FORMAT_STR, "运营公司", orgService.getOrgNameByOrgId(baseOrg.getOrgId()), orgService.getOrgNameByOrgId(base.getOrgId())));
        }
        if (ObjectUtils.notEqual(baseOrg.getCityId(), base.getCityId())) {
            content.append(String.format(LOG_FORMAT_STR, "可用区域", getCityNamesByCityId(baseOrg.getCityId()), getCityNamesByCityId(base.getCityId())));
        }
        if (ObjectUtils.notEqual(baseOrg.getAdvanceNoticeTime(), base.getAdvanceNoticeTime())) {
            content.append(String.format(LOG_FORMAT_STR, "预告时间",
                    baseOrg.getAdvanceNoticeTime() == null ? " " : DateUtil.getFormatDate(baseOrg.getAdvanceNoticeTime(), DateUtil.simple),
                    base.getAdvanceNoticeTime() == null ? " " : DateUtil.getFormatDate(base.getAdvanceNoticeTime(), DateUtil.simple)));
        }
        if (ObjectUtils.notEqual(baseOrg.getSaleStartTime(), base.getSaleStartTime())) {
            content.append(String.format(LOG_FORMAT_STR, "售卖开始时间",
                    DateUtil.getFormatDate(baseOrg.getSaleStartTime(), DateUtil.simple),
                    DateUtil.getFormatDate(base.getSaleStartTime(), DateUtil.simple)));
        }
        if (ObjectUtils.notEqual(baseOrg.getSaleEndTime(), base.getSaleEndTime())) {
            content.append(String.format(LOG_FORMAT_STR, "售卖结束时间",
                    DateUtil.getFormatDate(baseOrg.getSaleEndTime(), DateUtil.simple),
                    DateUtil.getFormatDate(base.getSaleEndTime(), DateUtil.simple)));
        }
        if (ObjectUtils.notEqual(baseOrg.getValidDaysType(), base.getValidDaysType())) {
            content.append(String.format(LOG_FORMAT_STR, "有效期",
                    SuixiangCardValidDaysTypeEnum.getDescByType(baseOrg.getValidDaysType()),
                    SuixiangCardValidDaysTypeEnum.getDescByType(base.getValidDaysType())));
        }
        if (ObjectUtils.notEqual(baseOrg.getInitStock(), base.getInitStock())) {
            content.append(String.format(LOG_FORMAT_NUM, "库存", baseOrg.getInitStock(), base.getInitStock()));
        }
        if (ObjectUtils.notEqual(baseOrg.getDisplayFlag(), base.getDisplayFlag())) {
            content.append(String.format(LOG_FORMAT_STR, "是否对外展示",
                    SuiXiangCardDisplayFlagEnum.getDescByDisplayFlag(baseOrg.getDisplayFlag()),
                    SuiXiangCardDisplayFlagEnum.getDescByDisplayFlag(base.getDisplayFlag())));
        }
        if (ObjectUtils.notEqual(baseOrg.getSingleOrderDuration(), base.getSingleOrderDuration())) {
            content.append(String.format(LOG_FORMAT_STR, "单订单时长", baseOrg.getSingleOrderDuration(), base.getSingleOrderDuration()));
        }

        if (ObjectUtils.notEqual(baseOrg.getLandingPageFlag(), base.getLandingPageFlag())) {
            content.append(String.format(LOG_FORMAT_STR, "是否有落地页", CommonFlagEnum.getDescByFlag(baseOrg.getLandingPageFlag()), CommonFlagEnum.getDescByFlag(base.getLandingPageFlag())));
        }

        if (ObjectUtils.notEqual(baseOrg.getStyleType(), base.getStyleType())) {
            content.append(String.format(LOG_FORMAT_STR, "卡面图片", "图片" + baseOrg.getStyleType(), "图片" + base.getStyleType()));
        }
        if (ObjectUtils.notEqual(baseOrg.getRules(), base.getRules())) {
            content.append(String.format(LOG_FORMAT_STR, "使用须知", baseOrg.getRules(), base.getRules()));
        }

        content.append(String.format(LOG_FORMAT_STR, "租期价格配置", appendRentDaysContent(rentDaysOrgList), appendRentDaysContent(rentDaysList)));
        return content;
    }

    private String appendRentDaysContent(List<SuixiangCardRentDays> rentDaysList) {
        StringBuffer content = new StringBuffer();
        content.append("[");
        for (SuixiangCardRentDays rentDays : rentDaysList) {
            content.append("{");
            content.append("租期:").append(rentDays.getRentDays());

            List<ServiceFeeCfgDto> feeList = JSON.parseArray(rentDays.getServiceFees(), ServiceFeeCfgDto.class);
            if (CollectionUtils.isNotEmpty(feeList)) {
                for (ServiceFeeCfgDto fee : feeList) {
                    content.append(Constants.STR_COMMA)
                            .append(SuiXiangCardServiceFeeEnum.getDescByFeeType(fee.getFeeType()))
                            .append(":")
                            .append(fee.getCrossedPrice())
                            .append("元");
                }
            }

            List<SuixiangCardPrice> priceList = rentDays.getPriceList();
            if (CollectionUtils.isNotEmpty(priceList)) {
                content.append(",车辆租金:[");
                int size = priceList.size();
                for (int i = 0; i < size; i++) {
                    SuixiangCardPrice price = priceList.get(i);
                    String modelNames = getCarModelNamesByCarModelIds(price.getCarModelIds());
                    content.append("{");
                    content.append("售价:").append(price.getSalesPrice()).append("元");
                    content.append(",划线价格:").append(price.getUnderlinePrice()).append("元");
                    content.append(",车型组:").append(price.getCarModelGroup());
                    content.append(",可用车型:").append(modelNames);
                    content.append(",落地页引导图:").append(price.getLandingPagePicUrl());
                    content.append(",落地页头图:").append(price.getLandingPageHeadPicUrl());
                    if (i < size - 1) {
                        content.append("},");
                    } else {
                        content.append("}");
                    }
                }
                content.append("]");
            }

            content.append("}");
        }
        content.append("]");
        return content.toString();
    }

    public String getCityNamesByCityId(String cityId) {
        StringBuffer cityNames = new StringBuffer();
        if (StringUtils.isNotBlank(cityId)) {
            if (cityId.contains("-1")) {
                cityNames.append("全部城市");
            } else {
                String[] cityIdOrgArray = cityId.split(Constants.STR_COMMA);
                int len = cityIdOrgArray.length;
                for (int i = 0; i < len; i++) {
                    cityNames.append(cityServ.getCitynameByCityid(Long.parseLong(cityIdOrgArray[i])));
                    if (i < len - 1) {
                        cityNames.append(Constants.STR_SPLIT_ZH);
                    }
                }
            }
        }
        return cityNames.toString();
    }

    @Transactional(rollbackFor = Exception.class)
    public void onlineSuiXiangCard(SuixiangCardBase base, SuixiangCardConfigOperationLog operationLog) {
        SuixiangCardBase baseRecord = new SuixiangCardBase();
        baseRecord.setCardStatus(base.getCardStatus());
        baseRecord.setUpdateOperId(base.getUpdateOperId());
        baseRecord.setUpdateOperName(base.getUpdateOperName());

        SuixiangCardBaseExample example = new SuixiangCardBaseExample();
        example.createCriteria()
                .andIdEqualTo(base.getId())
                .andCardStatusEqualTo(SuiXiangCardBaseCardStatusEnum.TO_BE_SUBMITTED.getStatus());

        int ret = suixiangCardBaseMapper.updateByExampleSelective(baseRecord, example);
        if (ret != 1) {
            log.error("上架随享卡，更新suixiang_card_base表失败！base={}", JSON.toJSONString(base));
            throw new RuntimeException("上架随享卡，更新suixiang_card_base表失败！base=" + JSON.toJSONString(base));
        }

        operationLog.setCardBaseId(base.getId());
        suixiangCardConfigOperationLogMapper.insertSelective(operationLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public void offlineSuiXiangCard(SuixiangCardBase base, SuixiangCardConfigOperationLog operationLog) {
        SuixiangCardBase baseRecord = new SuixiangCardBase();
        baseRecord.setCardStatus(base.getCardStatus());
        baseRecord.setUpdateOperId(base.getUpdateOperId());
        baseRecord.setUpdateOperName(base.getUpdateOperName());

        List<Integer> statusList = new ArrayList<>();
        statusList.add(SuiXiangCardBaseCardStatusEnum.TO_BE_PUT_ON_SHELF.getStatus());
        statusList.add(SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus());

        SuixiangCardBaseExample example = new SuixiangCardBaseExample();
        example.createCriteria()
                .andIdEqualTo(base.getId())
                .andCardStatusIn(statusList);

        int ret = suixiangCardBaseMapper.updateByExampleSelective(baseRecord, example);
        if (ret != 1) {
            log.error("下架随享卡，更新suixiang_card_base表失败！base={}", JSON.toJSONString(base));
            throw new RuntimeException("下架随享卡，更新suixiang_card_base表失败！base=" + JSON.toJSONString(base));
        }

        operationLog.setCardBaseId(base.getId());
        suixiangCardConfigOperationLogMapper.insertSelective(operationLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public void giveSuiXiangCard(SuixiangCardBase baseUpdate, SuixiangCardPurchaseRecord record,
                                 SuixiangCardPurchaseRecordLog recordLog, List<SuixiangCardUse> use,
                                 List<SuixiangCardUseOpreationLog> useLog) {
        int ret = suixiangCardBaseMapper.updateStock(baseUpdate);
        if (ret != 1) {
            log.error("更新suixiang_card_base表失败！极可能是库存不足！base={}", JSON.toJSONString(baseUpdate));
            throw new RuntimeException("库存不足");
        }

        suixiangCardPurchaseRecordMapper.insertSelective(record);

        recordLog.setPurchaseId(record.getId());
        suixiangCardPurchaseRecordLogMapper.insertSelective(recordLog);

        List<String> cardUseIds = new ArrayList<>();
        for (int i = 0; i < use.size(); i++) {
            use.get(i).setPurchaseId(record.getId());
            suixiangCardUseMapper.insertSelective(use.get(i));

            useLog.get(i).setCardUseId(use.get(i).getId());
            useLog.get(i).setPurchaseId(record.getId());
            suixiangCardUseOpreationLogMapper.insertSelective(useLog.get(i));

            cardUseIds.add(use.get(i).getId() + "");
        }


        SuixiangCardPurchaseRecord recordUpdate = new SuixiangCardPurchaseRecord();
        recordUpdate.setId(record.getId());
        recordUpdate.setCardUseIds(String.join(",", cardUseIds));
        recordUpdate.setUpdateOperId(record.getCreateOperId());
        recordUpdate.setUpdateOperName(record.getCreateOperName());
        ret = suixiangCardPurchaseRecordMapper.updateByPrimaryKeySelective(recordUpdate);
        if (ret != 1) {
            log.error("更新suixiang_card_purchase_record表失败！record={}", JSON.toJSONString(recordUpdate));
            throw new RuntimeException("更新suixiang_card_purchase_record表失败！record=" + JSON.toJSONString(recordUpdate));
        }

//        // 如果库存为0了（由于购卡的时候是先冻结，库存会有来回，所以需要用 初始库存=销量 来判断没库存了），需要更新状态为 下架
//        SuixiangCardBase baseResult = suixiangCardBaseMapper.selectByPrimaryKey(baseUpdate.getId());
//        if (baseResult == null) {
//            log.error("根据随享卡id[{}]查询不到随享卡的配置记录", baseUpdate.getId());
//            throw new RuntimeException("根据随享卡id[" + baseUpdate.getId() + "]查询不到随享卡的配置记录");
//        }
//        if (baseResult.getInitStock().equals(baseResult.getSales())) {
//            SuixiangCardBase baseUpdate2 = new SuixiangCardBase();
//            baseUpdate2.setId(baseUpdate.getId());
//            baseUpdate2.setCardStatus(SuiXiangCardBaseCardStatusEnum.OFF_SHELF.getStatus());
//            baseUpdate2.setUpdateOperId(record.getCreateOperId());
//            baseUpdate2.setUpdateOperName(record.getCreateOperName());
//            ret = suixiangCardBaseMapper.updateByPrimaryKeySelective(baseUpdate2);
//            if (ret != 1) {
//                log.error("更新suixiang_card_base表失败！base={}", JSON.toJSONString(baseUpdate2));
//                throw new RuntimeException("赠送后库存为0，更新状态失败！");
//            }
//        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void createSuiXiangCardOrder(SuixiangCardBase baseUpdate, SuixiangCardPurchaseRecord record,
                                        CreatePayOrderReq createPayOrderReq, SuixiangCardPurchaseRecordLog recordLog) throws BusinessException {
        // 1 suixiang_card_purchase_record 增加记录
        int ret = 0;
        suixiangCardPurchaseRecordMapper.insertSelective(record);
        Long purchaseId = record.getId();

        //2 创建支付订单
        // 存purchaseId 记录
        createPayOrderReq.setOrderNo("SXK" + purchaseId);
        createPayOrderReq.setContractId(purchaseId.toString());
        ArrayList<ContractItemFeeDto> contractItemFeeDtos = Lists.newArrayList(new ContractItemFeeDto(32, record.getRealAmount(), purchaseId));
        createPayOrderReq.setFeeInfo(JSON.toJSONString(contractItemFeeDtos));

        CreatelPayOrderRes payOrder = mdRestClient.createPayOrder(createPayOrderReq);
        if (payOrder == null || payOrder.getCode() != 0) {
            log.error("创建随享卡订单：调用支付模块创建支付单接口失败, payOrder={}, createPayOrderReq={}", JSON.toJSONString(payOrder), JSON.toJSONString(createPayOrderReq));
            throw new BusinessException(StatusCode.RECREATE_BILL_FAILED);
        }
        CreatePayOrderResData data = payOrder.getData();
        String payOrderNo = data.getPayOrderNo();  // 支付订单号
        if (StringUtils.isBlank(payOrderNo)) {
            log.error("创建随享卡订单：调用支付模块创建支付单接口,payOrderNo为空, payOrder={}, createPayOrderReq={}", JSON.toJSONString(payOrder), JSON.toJSONString(createPayOrderReq));
            throw new BusinessException(StatusCode.RECREATE_BILL_FAILED);
        }

        // 3 更新suixiang_card_purchase_record
        SuixiangCardPurchaseRecord recordUpdate = new SuixiangCardPurchaseRecord();
        recordUpdate.setId(record.getId());
        recordUpdate.setPayOrderNo(payOrderNo);
        recordUpdate.setUpdateOperId(record.getCreateOperId());
        recordUpdate.setUpdateOperName(record.getCreateOperName());
        ret = suixiangCardPurchaseRecordMapper.updateByPrimaryKeySelective(recordUpdate);
        if (ret != 1) {
            log.error("createSuiXiangCardOrder更新suixiang_card_purchase_record表失败！record={}", JSON.toJSONString(recordUpdate));
            throw new BusinessException(StatusCode.RECREATE_BILL_FAILED);
        }

        // 4 记录日志
        recordLog.setPurchaseId(record.getId());
        recordLog.setPayOrderNo(payOrderNo);
        suixiangCardPurchaseRecordLogMapper.insertSelective(recordLog);

        // 5 更新库存
        ret = suixiangCardBaseMapper.updateStock(baseUpdate);
        if (ret != 1) {
            log.error("createSuiXiangCardOrder更新suixiang_card_base表失败！极可能是库存不足！base={}", JSON.toJSONString(baseUpdate));
            throw new BusinessException("库存不足");
        }

        PurchaseSuiXiangCardRecordInfo recordInfo = new PurchaseSuiXiangCardRecordInfo();
        SuixiangCardPurchaseRecord newRecord = suixiangCardPurchaseRecordMapper.selectByPrimaryKey(purchaseId);
        BeanUtils.copyProperties(newRecord, recordInfo);
        recordInfo.setPlanCancelTime(recordInfo.gainCancelTime());
        // 6 超时取消任务
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                log.info("创建随享卡订单|推送延时消息，purchaseId= {}.", recordInfo.getId());
                try {
                    recordInfo.setCancelTime(recordInfo.gainCancelTime());
                    log.info("创建随享卡订单|推送延时消息，purchaseId={}, createTime={}, cancelTime={}",
                            recordInfo.getId(), recordInfo.getCreateTime(), recordInfo.getCancelTime());

                    byte[] body = ProtobufUtil.serializeProtobuf(recordInfo);
                    messagepushServ.pushMq(delayTopic, EventEnum.SUIXIANGCARD_PURCHASE_CANCEL.getTag(), body, recordInfo.getCancelTime().getTime());
                } catch (Exception e) {
                    log.error("创建随享卡订单|推送延时消息失败，purchaseId=" + recordInfo.getId(), e);
                }
            }
        });

    }


    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(SuixiangCardBase cardBase, SuixiangCardPurchaseRecord updateRecord, SuixiangCardPurchaseRecordLog recordLog, String mid, SuixiangCardPurchaseRecord purchaseRecord) throws BusinessException {
        int ret = 0;
      /*
            取消订单
       // 支付取消 支付订单
        String orderNo = "SXK" + purchaseRecord.getId();
        CancelPayOrderRes cancelPayOrderRes = mdRestClient.cancelPayOrder(mid, orderNo);
        if (cancelPayOrderRes == null || cancelPayOrderRes.getCode() != 0) {
            log.error("cancelOrder 取消随享卡 支付订单失败，cancelPayOrderRes[{}]", JSON.toJSONString(cancelPayOrderRes));
            return;
        }*/


        // 更新随想卡购买记录 状态
        ret = suixiangCardPurchaseRecordMapper.updateByPrimaryKeySelective(updateRecord);
        if (ret != 1) {
            log.error("取消订单：购随享卡卡订单待支付，开始自动取消,更新状态失败,purchaseRecord[{}]", JSON.toJSONString(updateRecord));
            throw new BusinessException(-1, "自动取消失败");
        }

        // 新增 随享卡购买记录日志
        suixiangCardPurchaseRecordLogMapper.insertSelective(recordLog);

        // 释放库存 todo 如果库存由0变成1 是否需要上架
        ret = suixiangCardBaseMapper.updateStock(cardBase);
        if (ret != 1) {
            log.error("取消订单：购随享卡卡订单待支付，自动取消,释放库存失败，purchaseId={}, PayOrderNo={}",
                    purchaseRecord.getId(), purchaseRecord.getPayOrderNo());
            throw new BusinessException(-1, "自动取消失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void modifySuiXiangCardUseStatus(SuixiangCardUse suixiangCardUseUpdate, SuixiangCardUseExample example, SuixiangCardUseOpreationLog insertUseLog) throws BusinessException {
        int ret = suixiangCardUseMapper.updateByExampleSelective(suixiangCardUseUpdate, example);
        if (ret != 1) {
            log.error("修改随享卡状态，更新状态失败,suixiangCardUseUpdate[{}]，insertUseLog[{}]", JSON.toJSONString(suixiangCardUseUpdate), JSON.toJSONString(insertUseLog));
            throw new BusinessException(-1, "修改失败");
        }
        suixiangCardUseOpreationLogMapper.insertSelective(insertUseLog);
    }

    /**
     * 冻结天数
     *
     * @param suixiangCardUseUpdate
     * @param insertUseLog
     * @throws BusinessException
     */
    @Transactional(rollbackFor = Exception.class)
    public void freezeSuiXiangCardDays(SuixiangCardUse suixiangCardUseUpdate, SuixiangCardUseExample useExample, SuixiangCardUseOpreationLog insertUseLog) throws BusinessException {
        try {
            int ret = suixiangCardUseMapper.updateByExampleSelective(suixiangCardUseUpdate, useExample);
            if (ret != 1) {
                log.error("冻结随享卡天数，更新失败,suixiangCardUseUpdate[{}]，insertUseLog[{}]", JSON.toJSONString(suixiangCardUseUpdate), JSON.toJSONString(insertUseLog));
                throw new BusinessException(-1, "冻结失败");
            }
            suixiangCardUseOpreationLogMapper.insertSelective(insertUseLog);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e1) {
            throw new BusinessException(-1, "冻结失败");
        }
    }


    /**
     * 解冻天数
     *
     * @param suixiangCardUseUpdate
     * @param insertUseLog
     * @throws BusinessException
     */
    @Transactional(rollbackFor = Exception.class)
    public void unfreezeSuiXiangCardDays(SuixiangCardUse suixiangCardUseUpdate, SuixiangCardUseExample useExample, SuixiangCardUseOpreationLog insertUseLog) throws BusinessException {
        try {
            int ret = suixiangCardUseMapper.updateByExampleSelective(suixiangCardUseUpdate, useExample);
            if (ret != 1) {
                log.error("解冻随享卡天数，更新失败,suixiangCardUseUpdate[{}]，insertUseLog[{}]", JSON.toJSONString(suixiangCardUseUpdate), JSON.toJSONString(insertUseLog));
                throw new BusinessException(-1, "解冻失败");
            }
            suixiangCardUseOpreationLogMapper.insertSelective(insertUseLog);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e1) {
            throw new BusinessException(-1, "解冻失败");
        }
    }

    /**
     * 扣除天数
     *
     * @param suixiangCardUseUpdate
     * @param insertUseLog
     * @throws BusinessException
     */
    @Transactional(rollbackFor = Exception.class)
    public void deductFreezeSuiXiangCardDays(SuixiangCardUse suixiangCardUseUpdate, SuixiangCardUseExample useExample, SuixiangCardUseOpreationLog insertUseLog) throws BusinessException {
        try {
            int ret = suixiangCardUseMapper.updateByExampleSelective(suixiangCardUseUpdate, useExample);
            if (ret != 1) {
                log.error("扣除随享卡冻结天数，更新失败,suixiangCardUseUpdate[{}]，insertUseLog[{}]", JSON.toJSONString(suixiangCardUseUpdate), JSON.toJSONString(insertUseLog));
                throw new BusinessException(-1, "扣除失败");
            }
            suixiangCardUseOpreationLogMapper.insertSelective(insertUseLog);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e1) {
            throw new BusinessException(-1, "扣除失败");
        }
    }

    /**
     * 修改订单
     * 先解冻天数，再冻结天数
     *
     * @param unFreezeUse
     * @param unFreezeExample
     * @param unFreezeUseLog
     * @param freezeUse
     * @param freezeExample
     * @param freezeInput
     * @throws BusinessException
     */
    @Transactional(rollbackFor = Exception.class)
    public void modifyOrderOperateSuiXiangCardDays(SuixiangCardUse unFreezeUse, SuixiangCardUseExample unFreezeExample, SuixiangCardUseOpreationLog unFreezeUseLog,
                                                   SuixiangCardUse freezeUse, SuixiangCardUseExample freezeExample, UseSuiXiangCardDaysInput freezeInput) throws BusinessException {
        try {
            int ret = suixiangCardUseMapper.updateByExampleSelective(unFreezeUse, unFreezeExample);
            if (ret != 1) {
                log.error("unFreezeAndFreezeSuiXiangCardDays 解冻随享卡天数，更新失败,unFreezeUse[{}]，unFreezeUseLog[{}]", JSON.toJSONString(unFreezeUse), JSON.toJSONString(unFreezeUseLog));
                throw new BusinessException(-1, "解冻失败");
            }
            suixiangCardUseOpreationLogMapper.insertSelective(unFreezeUseLog);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e1) {
            throw new BusinessException(-1, "解冻失败");
        }

        try {
            SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(freezeInput.getUserCardId());
            int ret = suixiangCardUseMapper.updateByExampleSelective(freezeUse, freezeExample);
            if (ret != 1) {
                log.error("modifyOrderOperateSuiXiangCardDays冻结随享卡天数，更新失败,freezeUse[{}]，freezeInput[{}]", JSON.toJSONString(freezeUse), JSON.toJSONString(freezeInput));
                throw new BusinessException(-1, "冻结失败");
            }
            SuixiangCardUseOpreationLog freezeUseLog = SuixiangCardUseOpreationLog.getLogFromSuixiangCardUse2(suixiangCardUse, SuiXiangCardUseLogOperationTypeEnum.FREEZE.getOperationType(), freezeInput);
            suixiangCardUseOpreationLogMapper.insertSelective(freezeUseLog);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e1) {
            throw new BusinessException(-1, "冻结失败");
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateCardUseAndLog(SuixiangCardUse item,
                                    SuiXiangCardStatusEnum source,
                                    SuiXiangCardStatusEnum target,
                                    SuiXiangCardUseLogOperationTypeEnum useLogType) {
        // 更新状态
        SuixiangCardUse record = new SuixiangCardUse();
        record.setCardStatus(target.getStatus());
        record.setUpdateOperId(Constants.OPER_ID_SYSTEM);
        record.setUpdateOperName(Constants.OPER_NAME_SYSTEM);

        SuixiangCardUseExample example = new SuixiangCardUseExample();
        SuixiangCardUseExample.Criteria criteria = example.createCriteria()
                .andIdEqualTo(item.getId());
        if (source != null) {
            criteria.andCardStatusEqualTo(source.getStatus());
        }
        int ret = suixiangCardUseMapper.updateByExampleSelective(record, example);
        if (ret != 1) {
            log.info("更新suixiang_card_use表状态失败！id[{}]", item.getId());
            throw new RuntimeException("更新suixiang_card_use表状态失败！id[" + item.getId() + "]");
        }

        // 记录log
        SuixiangCardUseOpreationLog useLog = new SuixiangCardUseOpreationLog();
        useLog.setCardUseId(item.getId());
        useLog.setCardPriceId(item.getCardPriceId());
        useLog.setCardGroup(1); // 卡类别：1-随享卡
        useLog.setOperationType(useLogType.getOperationType());
        useLog.setOriginSystem(Constants.VIPCARD_SYSTEM_CODE);
        useLog.setInitDays(item.getInitDays());
        useLog.setAvailableDays(item.getAvailableDays());
        useLog.setUsedDays(item.getUsedDays());
        useLog.setFrozenDays(item.getFrozenDays());
        useLog.setCreateOperName(Constants.OPER_NAME_SYSTEM);
        useLog.setPurchaseId(item.getPurchaseId());
        suixiangCardUseOpreationLogMapper.insertSelective(useLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<String> paySuccessCallBack(SuixiangCardPurchaseRecord purchaseRecord, List<SuixiangCardUse> insertCardUse, List<SuixiangCardUseOpreationLog> insertUseLog
            , SuixiangCardPurchaseRecord updatePurchaseRecord, SuixiangCardPurchaseRecordLog insertPurchaseRecordLog, boolean soldOut, SuixiangCardBase updateBase) throws BusinessException {

        List<String> cardUseIds = new ArrayList<>();
        for (int i = 0; i < insertCardUse.size(); i++) {
            // 新增使用记录
            suixiangCardUseMapper.insertSelective(insertCardUse.get(i));

            // 使用卡历史记录
            insertUseLog.get(i).setCardUseId(insertCardUse.get(i).getId());
            suixiangCardUseOpreationLogMapper.insertSelective(insertUseLog.get(i));
            cardUseIds.add(insertCardUse.get(i).getId() + "");
        }
        // 购卡记录 状态修改
        updatePurchaseRecord.setCardUseIds(String.join(",", cardUseIds));
        int ret = suixiangCardPurchaseRecordMapper.updateByPrimaryKeySelective(updatePurchaseRecord);
        if (ret != 1) {
            log.info("支付回调：购随享卡卡订单,更新状态失败，purchaseId={}, PayOrderNo={}", purchaseRecord.getId(), purchaseRecord.getPayOrderNo());
            throw new BusinessException(-1, "支付回调，更新支付状态失败");
        }
        // 购卡历史记录
        suixiangCardPurchaseRecordLogMapper.insertSelective(insertPurchaseRecordLog);

        //卖完不自动下架
       /* if (soldOut) {
            SuixiangCardBase base = new SuixiangCardBase();
            base.setId(purchaseRecord.getCardBaseId());
            base.setCardStatus(SuiXiangCardBaseCardStatusEnum.OFF_SHELF.getStatus());
            base.setUpdateOperId(insertCardUse.getCreateOperId());
            base.setUpdateOperName(ComUtils.splitStr(insertCardUse.getCreateOperName(), Constants.OPER_NAME_LENGTH));
            ret = suixiangCardBaseMapper.updateByPrimaryKeySelective(base);
            if (ret != 1) {
                log.error("支付回调：下架活动。更新suixiang_card_base表失败！base={}", JSON.toJSONString(base));
                throw new BusinessException(-1,"支付回调，下架时更新失败");
            }
        }*/

        // 更新销量
        ret = suixiangCardBaseMapper.updateStock(updateBase);
        if (ret != 1) {
            log.error("支付回调：更新suixiang_card_base表失败！base={}", JSON.toJSONString(updateBase));
            throw new BusinessException(-1, "支付回调，更新销量失败");
        }
        return cardUseIds;
    }

    /**
     * 兑换cdk发卡
     *
     * @param suixiangCardCdk
     * @param record
     * @param recordLog
     * @param use
     * @param useLog
     */
    @Transactional(rollbackFor = Exception.class)
    public void giveSuiXiangCard(SuixiangCardCdk suixiangCardCdk,
                                 SuixiangCardPurchaseRecord record,
                                 SuixiangCardPurchaseRecordLog recordLog, List<SuixiangCardUse> use,
                                 List<SuixiangCardUseOpreationLog> useLog) {
        int ret = 0;
        suixiangCardPurchaseRecordMapper.insertSelective(record);

        recordLog.setPurchaseId(record.getId());
        suixiangCardPurchaseRecordLogMapper.insertSelective(recordLog);

        List<String> cardUseIds = new ArrayList<>();
        for (int i = 0; i < use.size(); i++) {
            use.get(i).setPurchaseId(record.getId());
            suixiangCardUseMapper.insertSelective(use.get(i));

            useLog.get(i).setCardUseId(use.get(i).getId());
            useLog.get(i).setPurchaseId(record.getId());
            suixiangCardUseOpreationLogMapper.insertSelective(useLog.get(i));

            cardUseIds.add(use.get(i).getId() + "");
        }

        if (CollectionUtils.isNotEmpty(cardUseIds)) {
            // 目前一个cdk对应一个卡
            suixiangCardCdk.setCardUseId(Long.valueOf(cardUseIds.get(0)));
            ret = suixiangCardCdkMapper.updateByPrimaryKeySelective(suixiangCardCdk);
            if (ret == 0) {
                log.error("更新suixiang_card_cdk表失败！record={}", JSON.toJSONString(suixiangCardCdk));
                throw new RuntimeException("更新suixiang_card_cdk表失败！record=" + JSON.toJSONString(suixiangCardCdk));
            }
        }

        SuixiangCardPurchaseRecord recordUpdate = new SuixiangCardPurchaseRecord();
        recordUpdate.setId(record.getId());
        recordUpdate.setCardUseIds(cardUseIds.get(0));
        recordUpdate.setCardUseId(Long.valueOf(cardUseIds.get(0)));
        recordUpdate.setUpdateOperId(record.getCreateOperId());
        recordUpdate.setUpdateOperName(record.getCreateOperName());
        ret = suixiangCardPurchaseRecordMapper.updateByPrimaryKeySelective(recordUpdate);
        if (ret != 1) {
            log.error("更新suixiang_card_purchase_record表失败！record={}", JSON.toJSONString(recordUpdate));
            throw new RuntimeException("更新suixiang_card_purchase_record表失败！record=" + JSON.toJSONString(recordUpdate));
        }


    }

    @Transactional(rollbackFor = Exception.class)
    public void giveSuiXiangCard(SuixiangCardUseTemp suixiangCardUseTemp,
                                 SuixiangCardCdk suixiangCardCdk,
                                 SuixiangCardPurchaseRecord record,
                                 SuixiangCardPurchaseRecordLog recordLog, List<SuixiangCardUse> use,
                                 List<SuixiangCardUseOpreationLog> useLog) {

        giveSuiXiangCard(suixiangCardCdk, record, recordLog, use, useLog);
        suixiangCardUseTempMapper.updateByPrimaryKey(suixiangCardUseTemp);
    }

    @Transactional(rollbackFor = Exception.class)
    public void mergeSuiXiangCard(SuixiangCardCdk suixiangCardCdk, SuixiangCardPurchaseRecord suixiangCardPurchaseRecord, SuixiangCardPurchaseRecordLog recordLog, SuiXiangCardMergeDto suixiangCardMergeDto, SuixiangCardUseOpreationLog useLog, SuixiangCardUseOpreationLog newUseLog) {
        int ret = 0;
        suixiangCardPurchaseRecordMapper.insertSelective(suixiangCardPurchaseRecord);

        recordLog.setPurchaseId(suixiangCardPurchaseRecord.getId());
        suixiangCardPurchaseRecordLogMapper.insertSelective(recordLog);

        ret = suixiangCardUseMapper.mergeSuiXiangCardUse(suixiangCardMergeDto);
        if (ret == 0) {
            log.error("更新 suixiang_card_use 表失败！suixiangCardMergeDto={}", JSON.toJSONString(suixiangCardMergeDto));
            throw new RuntimeException("更新suixiang_card_use表失败！suixiangCardMergeDto=" + JSON.toJSONString(suixiangCardMergeDto));
        }

        // 新增卡使用记录日志
        newUseLog.setPurchaseId(suixiangCardPurchaseRecord.getId());
        suixiangCardUseOpreationLogMapper.insertSelective(newUseLog);
        suixiangCardUseOpreationLogMapper.insertSelective(useLog);

        ret = suixiangCardCdkMapper.updateByPrimaryKeySelective(suixiangCardCdk);
        if (ret == 0) {
            log.error("更新suixiang_card_cdk表失败！record={}", JSON.toJSONString(suixiangCardCdk));
            throw new RuntimeException("更新suixiang_card_cdk表失败！record=" + JSON.toJSONString(suixiangCardCdk));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void mergeSuiXiangCard(SuixiangCardUseTemp suixiangCardUseTemp, SuixiangCardCdk suixiangCardCdk, SuixiangCardPurchaseRecord suixiangCardPurchaseRecord, SuixiangCardPurchaseRecordLog recordLog, SuiXiangCardMergeDto suixiangCardMergeDto, SuixiangCardUseOpreationLog useLog, SuixiangCardUseOpreationLog newUseLog) {
        mergeSuiXiangCard(suixiangCardCdk, suixiangCardPurchaseRecord, recordLog, suixiangCardMergeDto, useLog, newUseLog);
        suixiangCardUseTempMapper.updateByPrimaryKey(suixiangCardUseTemp);
    }

    /**
     * 根据车型ID获取车型名称
     *
     * @param carModelIds 车型ID字符串，逗号分隔
     * @return 车型名称字符串
     */
    public String getCarModelNamesByCarModelIds(String carModelIds) {
        StringBuffer carModelNames = new StringBuffer();
        if (StringUtils.isNotBlank(carModelIds)) {
            if (carModelIds.contains("-1")) {
                carModelNames.append("全部车型");
            } else {
                String[] carModelIdArray = carModelIds.split(Constants.STR_COMMA);
                int len = carModelIdArray.length;
                for (int i = 0; i < len; i++) {
                    // 使用configLoader获取缓存的车型信息
                    String carModelName = configLoader.getGoodsModelInfo(Long.parseLong(carModelIdArray[i]));
                    if (StringUtils.isNotBlank(carModelName)) {
                        carModelNames.append(carModelName);
                        if (i < len - 1) {
                            carModelNames.append(Constants.STR_SPLIT_ZH);
                        }
                    }
                }
            }
        }
        return carModelNames.toString();
    }


}
