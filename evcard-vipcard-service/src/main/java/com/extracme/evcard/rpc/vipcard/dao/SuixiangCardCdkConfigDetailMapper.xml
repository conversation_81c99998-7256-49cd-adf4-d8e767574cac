<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.SuixiangCardCdkConfigDetailMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigDetail" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="card_cdk_config_id" property="cardCdkConfigId" jdbcType="BIGINT" />
    <result column="card_base_id" property="cardBaseId" jdbcType="BIGINT" />
    <result column="card_rent_id" property="cardRentId" jdbcType="BIGINT" />
    <result column="card_price_id" property="cardPriceId" jdbcType="BIGINT" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="act_desc" property="actDesc" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
    <result column="third_sales_price" jdbcType="DECIMAL" property="thirdSalesPrice" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    id, card_cdk_config_id, card_base_id, card_rent_id, card_price_id, quantity, act_desc, 
    is_deleted, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, 
    update_oper_name, third_sales_price
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigDetailExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from suixiang_card_cdk_config_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from suixiang_card_cdk_config_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    delete from suixiang_card_cdk_config_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigDetail" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    insert into suixiang_card_cdk_config_detail (id, card_cdk_config_id, card_base_id, 
      card_rent_id, card_price_id, quantity, 
      act_desc, is_deleted, create_time, 
      create_oper_id, create_oper_name, update_time, 
      update_oper_id, update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{cardCdkConfigId,jdbcType=BIGINT}, #{cardBaseId,jdbcType=BIGINT}, 
      #{cardRentId,jdbcType=BIGINT}, #{cardPriceId,jdbcType=BIGINT}, #{quantity,jdbcType=INTEGER}, 
      #{actDesc,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigDetail" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    insert into suixiang_card_cdk_config_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="cardCdkConfigId != null" >
        card_cdk_config_id,
      </if>
      <if test="cardBaseId != null" >
        card_base_id,
      </if>
      <if test="cardRentId != null" >
        card_rent_id,
      </if>
      <if test="cardPriceId != null" >
        card_price_id,
      </if>
      <if test="quantity != null" >
        quantity,
      </if>
      <if test="actDesc != null" >
        act_desc,
      </if>
      <if test="isDeleted != null" >
        is_deleted,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
      <if test="thirdSalesPrice != null">
        third_sales_price,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cardCdkConfigId != null" >
        #{cardCdkConfigId,jdbcType=BIGINT},
      </if>
      <if test="cardBaseId != null" >
        #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardRentId != null" >
        #{cardRentId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null" >
        #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="quantity != null" >
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="actDesc != null" >
        #{actDesc,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="thirdSalesPrice != null">
        #{thirdSalesPrice,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigDetailExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    select count(*) from suixiang_card_cdk_config_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    update suixiang_card_cdk_config_detail
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardCdkConfigId != null" >
        card_cdk_config_id = #{record.cardCdkConfigId,jdbcType=BIGINT},
      </if>
      <if test="record.cardBaseId != null" >
        card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.cardRentId != null" >
        card_rent_id = #{record.cardRentId,jdbcType=BIGINT},
      </if>
      <if test="record.cardPriceId != null" >
        card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="record.quantity != null" >
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.actDesc != null" >
        act_desc = #{record.actDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null" >
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null" >
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null" >
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null" >
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    update suixiang_card_cdk_config_detail
    set id = #{record.id,jdbcType=BIGINT},
      card_cdk_config_id = #{record.cardCdkConfigId,jdbcType=BIGINT},
      card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      card_rent_id = #{record.cardRentId,jdbcType=BIGINT},
      card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      quantity = #{record.quantity,jdbcType=INTEGER},
      act_desc = #{record.actDesc,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigDetail" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    update suixiang_card_cdk_config_detail
    <set >
      <if test="cardCdkConfigId != null" >
        card_cdk_config_id = #{cardCdkConfigId,jdbcType=BIGINT},
      </if>
      <if test="cardBaseId != null" >
        card_base_id = #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardRentId != null" >
        card_rent_id = #{cardRentId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null" >
        card_price_id = #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="actDesc != null" >
        act_desc = #{actDesc,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigDetail" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 26 19:40:31 CST 2024.
    -->
    update suixiang_card_cdk_config_detail
    set card_cdk_config_id = #{cardCdkConfigId,jdbcType=BIGINT},
      card_base_id = #{cardBaseId,jdbcType=BIGINT},
      card_rent_id = #{cardRentId,jdbcType=BIGINT},
      card_price_id = #{cardPriceId,jdbcType=BIGINT},
      quantity = #{quantity,jdbcType=INTEGER},
      act_desc = #{actDesc,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectThirdSalesPriceByBaseId"
          resultType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkThirdSaleDto">
    SELECT
    ccd.third_sales_price as thirdSalesPrice,
    ccd.card_base_id as cardBaseId,
    ccd.card_rent_id as cardRentId,
    ccd.card_price_id as cardPriceId,
    ccd.id as cdkConfigDetailId,
    ccd.card_cdk_config_id as cdkConfigId
    FROM
    suixiang_card_cdk_config_detail ccd
    JOIN suixiang_card_cdk_config cc ON cc.id = ccd.card_cdk_config_id
    WHERE
    cc.purpose = 2
    AND ccd.card_base_id = #{cardBaseId}
  </select>

  <select id="selectThirdSalesPriceInfoByBaseId"
          resultType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkThirdSaleDto">
    SELECT
    ccd.third_sales_price as thirdSalesPrice,
    ccd.card_base_id as cardBaseId,
    ccd.card_rent_id as cardRentId,
    ccd.card_price_id as cardPriceId,
    ccd.id as cdkConfigDetailId,
    ccd.card_cdk_config_id as cdkConfigId,
    ccp.car_model_ids as carModelIds,
    cr.rent_days as rentDays
    FROM
    suixiang_card_cdk_config_detail ccd
    JOIN suixiang_card_cdk_config cc ON cc.id = ccd.card_cdk_config_id
    left join suixiang_card_price ccp on ccp.id = ccd.card_price_id
    left join suixiang_card_rent_days cr on cr.id = ccd.card_rent_id
    WHERE
    cc.purpose = 2
    AND ccd.card_base_id = #{cardBaseId}
  </select>


</mapper>