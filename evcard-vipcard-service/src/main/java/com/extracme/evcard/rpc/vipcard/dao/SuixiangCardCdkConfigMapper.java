package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfig;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardCdkConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    int countByExample(SuixiangCardCdkConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    int insert(SuixiangCardCdkConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    int insertSelective(SuixiangCardCdkConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    List<SuixiangCardCdkConfig> selectByExample(SuixiangCardCdkConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    SuixiangCardCdkConfig selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    int updateByExampleSelective(@Param("record") SuixiangCardCdkConfig record, @Param("example") SuixiangCardCdkConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    int updateByExample(@Param("record") SuixiangCardCdkConfig record, @Param("example") SuixiangCardCdkConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    int updateByPrimaryKeySelective(SuixiangCardCdkConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    int updateByPrimaryKey(SuixiangCardCdkConfig record);
}