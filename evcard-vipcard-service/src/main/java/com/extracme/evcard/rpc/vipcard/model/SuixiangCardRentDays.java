package com.extracme.evcard.rpc.vipcard.model;

import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuiXiangCardRentDayDto;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class SuixiangCardRentDays {

    /**
     * 转换dto
     * @return
     */
    public SuiXiangCardRentDayDto toDto(){
        SuiXiangCardRentDayDto suiXiangCardRentDayDto = new SuiXiangCardRentDayDto();
        BeanUtils.copyProperties(this,suiXiangCardRentDayDto);
        return suiXiangCardRentDayDto;
    }

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.card_base_id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private Long cardBaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private Integer rentDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.total_service_fees_amout
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private BigDecimal totalServiceFeesAmout;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.create_time
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.update_time
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private Integer isDeleted;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_rent_days.service_fees
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    private String serviceFees;

    /**
     * 随享卡价格表的对象列表，用于插库的时候，两张表的关联
     *
     */
    List<SuixiangCardPrice> priceList;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.id
     *
     * @return the value of suixiang_card_rent_days.id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.id
     *
     * @param id the value for suixiang_card_rent_days.id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.card_base_id
     *
     * @return the value of suixiang_card_rent_days.card_base_id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public Long getCardBaseId() {
        return cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.card_base_id
     *
     * @param cardBaseId the value for suixiang_card_rent_days.card_base_id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setCardBaseId(Long cardBaseId) {
        this.cardBaseId = cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.rent_days
     *
     * @return the value of suixiang_card_rent_days.rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public Integer getRentDays() {
        return rentDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.rent_days
     *
     * @param rentDays the value for suixiang_card_rent_days.rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setRentDays(Integer rentDays) {
        this.rentDays = rentDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.total_service_fees_amout
     *
     * @return the value of suixiang_card_rent_days.total_service_fees_amout
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public BigDecimal getTotalServiceFeesAmout() {
        return totalServiceFeesAmout;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.total_service_fees_amout
     *
     * @param totalServiceFeesAmout the value for suixiang_card_rent_days.total_service_fees_amout
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setTotalServiceFeesAmout(BigDecimal totalServiceFeesAmout) {
        this.totalServiceFeesAmout = totalServiceFeesAmout;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.create_time
     *
     * @return the value of suixiang_card_rent_days.create_time
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.create_time
     *
     * @param createTime the value for suixiang_card_rent_days.create_time
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.create_oper_id
     *
     * @return the value of suixiang_card_rent_days.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.create_oper_id
     *
     * @param createOperId the value for suixiang_card_rent_days.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.create_oper_name
     *
     * @return the value of suixiang_card_rent_days.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.create_oper_name
     *
     * @param createOperName the value for suixiang_card_rent_days.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.update_time
     *
     * @return the value of suixiang_card_rent_days.update_time
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.update_time
     *
     * @param updateTime the value for suixiang_card_rent_days.update_time
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.update_oper_id
     *
     * @return the value of suixiang_card_rent_days.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_rent_days.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.update_oper_name
     *
     * @return the value of suixiang_card_rent_days.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_rent_days.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.is_deleted
     *
     * @return the value of suixiang_card_rent_days.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.is_deleted
     *
     * @param isDeleted the value for suixiang_card_rent_days.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_rent_days.service_fees
     *
     * @return the value of suixiang_card_rent_days.service_fees
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public String getServiceFees() {
        return serviceFees;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_rent_days.service_fees
     *
     * @param serviceFees the value for suixiang_card_rent_days.service_fees
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    public void setServiceFees(String serviceFees) {
        this.serviceFees = serviceFees;
    }

    public List<SuixiangCardPrice> getPriceList() {
        return priceList;
    }

    public void setPriceList(List<SuixiangCardPrice> priceList) {
        this.priceList = priceList;
    }
}