package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.vipcard.dao.*;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;
import com.extracme.evcard.rpc.vipcard.enums.*;
import com.extracme.evcard.rpc.vipcard.manager.SuixiangCardBaseManager;
import com.extracme.evcard.rpc.vipcard.model.*;
import com.extracme.evcard.rpc.vipcard.service.inner.MemberCardInnerService;
import com.extracme.evcard.rpc.vipcard.service.inner.OrgService;
import com.extracme.evcard.rpc.vipcard.service.store.ConfigLoader;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import com.extracme.evcard.rpc.vipcard.util.RestfulHttpClientUtils;
import com.extracme.evcard.rpc.vipcard.util.compare.CardInfoComparator;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SuiXiangCardService implements ISuiXiangCardService {

    @Resource
    private SuixiangCardUseMapper suixiangCardUseMapper;
    @Resource
    private SuixiangCardPriceMapper cardPriceMapper;
    @Resource
    private SuixiangCardPurchaseRecordMapper cardPurchaseRecordMapper;
    @Resource
    private MmpCardDefMapper mmpCardDefMapper;
    @Resource
    private MmpCardPurchaseRecordMapper mmpCardPurchaseRecordMapper;
    @Resource
    private MemberCardInnerService memberCardInnerService;
    @Resource
    private SuixiangCardBaseManager suixiangCardBaseManager;
    @Resource
    private SuixiangCardUseOpreationLogMapper suixiangCardUseOpreationLogMapper;
    @Resource
    private SuiXiangCardService suiXiangCardService;

    @Resource
    private SuixiangCardRentDaysMapper suixiangCardRentDaysMapper;

    @Resource
    private SuixiangCardPriceMapper suixiangCardPriceMapper;

    @Resource
    private SuixiangCardBaseMapper suixiangCardBaseMapper;

    @Resource
    private SuixiangCardUseTempMapper suixiangCardUseTempMapper;

    @Value("${md.rest.api.baseUrl}")
    private String requestUrl;

    @Autowired
    private ConfigLoader configLoader;


    @Override
    public PageBeanBO<SuixiangCardUseBo> querySuiXiangCardPageByUserId(SuiXiangCardUseQueryDto queryDto) throws BusinessException {
        try {
            if (queryDto == null) {
                throw new BusinessException(-1, "参数为空");
            }
            Long userId = queryDto.getUserId();

            PageBeanBO<SuixiangCardUseBo> pageBeanBO = new PageBeanBO<>();
            if (queryDto.getPageNum() == null) {
                queryDto.setPageNum(1);
            }
            if (queryDto.getPageSize() == null) {
                queryDto.setPageSize(10);
            }
            List<SuixiangCardUseBo> boList = new ArrayList<>();
            List<UnavailableDate> holidayConfigurationList =new ArrayList<>();
            //查询节假日
            JSONObject req = new JSONObject();
            req.put("particularYear", "");
            JSONObject contractRes = RestfulHttpClientUtils.sendRestHttp(requestUrl + "mdadmin/store/inner/searchHolidayConfiguration", "POST", req.toJSONString());
            if (contractRes != null && contractRes.getString("code").equals("0")) {
                JSONObject data = contractRes.getJSONObject("data");
                JSONArray holidayConfiguration = data.getJSONArray("cfg");
                List<UnavailableDate> unavailableDateList = JSONObject.parseArray(holidayConfiguration.toJSONString(), UnavailableDate.class);
                if(CollectionUtils.isNotEmpty(unavailableDateList)){
                   for(UnavailableDate unavailableDate : unavailableDateList){
                       UnavailableDate unavailableDate1 = new UnavailableDate();
                       unavailableDate1.setStartDate(unavailableDate.getStartDate().replaceAll("-",""));
                       unavailableDate1.setEndDate(unavailableDate.getEndDate().replaceAll("-",""));
                       holidayConfigurationList.add(unavailableDate1);
                   }
                }
            }

            // -1 和 -1 代表查询所有
            Page page = new Page(1,1);
            boolean isQueryAll = false;
            if (queryDto.getPageNum() == -1 && queryDto.getPageSize() == -1) {
                isQueryAll = true;
                page.setTotal(0);
            }else{
                page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
            }

            List<SuixiangCardUse> cardUsesList = suixiangCardUseMapper.selectCardListByUserId(userId, queryDto);

            if (CollectionUtils.isNotEmpty(cardUsesList)) {
                List<Long> cardPriceIdList = cardUsesList.stream().map(SuixiangCardUse::getCardPriceId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(cardPriceIdList)) {
                    List<SuixiangCardInfoDto> cardInfoDtoList = cardPriceMapper.selectInfoListByPriceIds(cardPriceIdList);
                    if (CollectionUtils.isNotEmpty(cardInfoDtoList)) {
                        Map<Long, List<SuixiangCardInfoDto>> map = cardInfoDtoList.stream().collect(Collectors.groupingBy(SuixiangCardInfoDto::getCardPriceId));
                        cardUsesList.stream().forEach(useCardItem -> {
                            SuixiangCardUseBo bo = new SuixiangCardUseBo();
                            bo.setCardUseDto(useCardItem.toDto());

                            List<SuixiangCardInfoDto> list = map.get(useCardItem.getCardPriceId());
                            if (CollectionUtils.isNotEmpty(list)) {
                                List<UnavailableDate> unavailableDates = new ArrayList<>();
                                SuixiangCardInfoDto c = list.get(0);
                                // 全部车型
                                String carModelIds = c.getCarModelIds();
                                if (StringUtils.isNotEmpty(carModelIds) && carModelIds.contains("-1")) {
                                    Set<Long> goodsModelIds = configLoader.getGoodsModelIds();
                                    String goodsModelIdsStr = StringUtils.join(goodsModelIds, ',');
                                    c.setCarModelIds(goodsModelIdsStr);
                                    log.info("将-1转化成真正的全部车型，goodsModelIds={},useCardItem={}",JSON.toJSONString(goodsModelIds),JSON.toJSONString(useCardItem));
                                }
                                bo.setCardInfoDto(c);
                                if(StringUtils.isNotEmpty(c.getUnavailableDate())){
                                    unavailableDates = JSON.parseArray(c.getUnavailableDate(),UnavailableDate.class);
                                }

                                if(c.getHolidayAvailable()==2 && CollectionUtils.isNotEmpty(holidayConfigurationList)){
                                    unavailableDates.addAll(holidayConfigurationList);
                                }
                                bo.setUnavailableDates(unavailableDates);
                                boList.add(bo);
                            }
                        });
                    }
                }

                if (isQueryAll) {
                    page.setTotal(cardUsesList.size());
                    page.setPageNum(1);
                    page.setPageSize(cardUsesList.size());
                }
            }


            PageBO pageBO = new PageBO();
            pageBO.setTotal(page.getTotal());
            pageBO.setPageNum(page.getPageNum());
            pageBO.setPageSize(page.getPageSize());
            pageBeanBO.setPage(pageBO);
            pageBeanBO.setList(boList);
            return pageBeanBO;
        } catch (Exception e) {
            log.error("查询用户已购买的随享卡异常,e[{}]", e);
            throw new BusinessException(-1, "查询失败");
        }
    }

    @Override
    public Boolean hasEffectiveSuiXiangCard(Long userId) {
        try {
            if (userId == null) {
                return false;
            }
            SuixiangCardUseExample example = new SuixiangCardUseExample();
            List<Integer> cardStatusList = new ArrayList<>();
            cardStatusList.add(SuiXiangCardStatusEnum.FROZENED.getStatus());
            cardStatusList.add(SuiXiangCardStatusEnum.EFFECTIVE.getStatus());

            example.createCriteria()
                    .andCardTypeEqualTo(1) // 卡类别：1随享卡
                    .andCardStatusIn(cardStatusList)
                    .andUserIdEqualTo(userId)
                    .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
            List<SuixiangCardUse> cardUsesList = suixiangCardUseMapper.selectByExample(example);

            if (CollectionUtils.isNotEmpty(cardUsesList)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("查询用户是否有 生效中的随想卡异常,userId[{}],e[{}]", userId, e);
            return false;
        }
    }

    @Override
    public Integer getPurchaseSuiXiangCardNum(Long userId,Long baseCardId) {
        try {
            if (userId == null) {
                return 0;
            }

            List<Integer> paymentStatusList = new ArrayList<>();
            paymentStatusList.add(SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus());
            paymentStatusList.add(SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus());

            SuixiangCardPurchaseRecordExample example = new SuixiangCardPurchaseRecordExample();
            example.createCriteria()
                    .andUserIdEqualTo(userId)
                    .andCardBaseIdEqualTo(baseCardId)
                    .andPaymentStatusIn(paymentStatusList)
                    .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
            List<SuixiangCardPurchaseRecord> list = cardPurchaseRecordMapper.selectByExample(example);

            int cardNum = 0;
            if (CollectionUtils.isNotEmpty(list)) {
                cardNum = list.stream().mapToInt(SuixiangCardPurchaseRecord::getQuantity).sum();
            }
            return cardNum;
        } catch (Exception e) {
            log.error("查询用户 已购买的随享卡数量异常,userId[{}],baseCardId[{}],e[{}]", userId,baseCardId, e);
            return null;
        }
    }

    @Override
    public Integer getPurchaseSuiXiangCardNum(String mobile,Long baseCardId) {
        try {
            List<Integer> paymentStatusList = new ArrayList<>();
            paymentStatusList.add(SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus());
            paymentStatusList.add(SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus());

            SuixiangCardUseTempExample example = new SuixiangCardUseTempExample();
            example.createCriteria()
                    .andMobilePhoneEqualTo(mobile)
                    .andCardBaseIdEqualTo(baseCardId)
                    .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
            List<SuixiangCardUseTemp> list = suixiangCardUseTempMapper.selectByExample(example);

            int cardNum = 0;
            if (CollectionUtils.isNotEmpty(list)) {
                cardNum = list.stream().mapToInt(SuixiangCardUseTemp::getQuantity).sum();
            }
            return cardNum;
        } catch (Exception e) {
            log.error("查询用户 已购买的随享卡数量异常,mobile[{}],baseCardId[{}],", mobile,baseCardId, e);
            return null;
        }
    }

    @Resource
    private SuixiangCardRemindMapper suixiangCardRemindMapper;

    @Override
    public List<Long> queryCardHasRemind(Long userId, List<Long> cardBaseIds) {
        if (userId == null) {
            return null;
        }

        SuixiangCardRemindExample example = new SuixiangCardRemindExample();
        /*example.createCriteria().andIsDeletedEqualTo(0).andUserIdEqualTo(userId)
                .andCardBaseIdIn(cardBaseIds).andRemindStatusEqualTo(1);*/
        // TODO 该表记录 的是所有设置过提醒的人
        example.createCriteria().andIsDeletedEqualTo(0).andUserIdEqualTo(userId).andCardBaseIdIn(cardBaseIds);

        List<SuixiangCardRemind> suixiangCardReminds = suixiangCardRemindMapper.selectByExample(example);

        if (CollectionUtils.isNotEmpty(suixiangCardReminds)) {
            return suixiangCardReminds.stream().map(SuixiangCardRemind::getCardBaseId).collect(Collectors.toList());
        }
        return null;
    }


    @Override
    public void submitCardRemind(UserRemindActivityDto userRemindActivityDto) throws BusinessException {
        Long cardBaseId = userRemindActivityDto.getActivityId();
        Long userId = userRemindActivityDto.getUserId();

        if (cardBaseId == null || userId == null) {
            throw new BusinessException(-1, "参数为空");
        }

        try {
            SuixiangCardRemindExample example = new SuixiangCardRemindExample();
            example.createCriteria().andUserIdEqualTo(userId).andCardBaseIdEqualTo(cardBaseId);
            List<SuixiangCardRemind> suixiangCardReminds = suixiangCardRemindMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(suixiangCardReminds)) {
                SuixiangCardRemind remind = new SuixiangCardRemind();
                remind.setCardBaseId(cardBaseId);
                remind.setUserId(userId);
                remind.setCreateOperId(userId);
                remind.setCreateTime(new Date());
                suixiangCardRemindMapper.insertSelective(remind);
            }
        } catch (Exception e) {
            throw new BusinessException(-1, "设置提醒失败");
        }
    }

    @Override
    public List<SuiXiangCardPurchaseRecordDto> queryCardPurchaseInfoByUserId(Long userId) {
        SuixiangCardPurchaseRecordExample example = new SuixiangCardPurchaseRecordExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete()).andUserIdEqualTo(userId);
        List<SuixiangCardPurchaseRecord> list = cardPurchaseRecordMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        List<SuiXiangCardPurchaseRecordDto> cardPurchaseRecordDtos = new ArrayList<>();
        list.stream().forEach(p -> {
            try {
                SuiXiangCardPurchaseRecordDto cardPurchaseRecordDto = new SuiXiangCardPurchaseRecordDto();
                BeanCopyUtils.copyProperties(p, cardPurchaseRecordDto);
                cardPurchaseRecordDto.setPurchaseId(p.getId());
                cardPurchaseRecordDto.setCardType(2);
                cardPurchaseRecordDto.setRealAmount(p.getRealAmount().doubleValue());
                if (p.getPayTime() != null) {
                    cardPurchaseRecordDto.setPayTime(DateUtil.getFormatDate(p.getPayTime(), DateUtil.DATE_TYPE7));
                }
                if (p.getCancelTime() != null) {
                    cardPurchaseRecordDto.setCancelTime(DateUtil.getFormatDate(p.getCancelTime(), DateUtil.DATE_TYPE7));
                }
                if (p.getPaymentStatus() == SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus()) {
                    Date createTime = p.getCreateTime();
                    Date planCancelTime = DateUtil.addMin(createTime, 5);
                    cardPurchaseRecordDto.setPlanCancelTime(DateUtil.getFormatDate(planCancelTime, DateUtil.simple));
                }
                cardPurchaseRecordDto.setActivityName(p.getCardName());
                cardPurchaseRecordDtos.add(cardPurchaseRecordDto);
            } catch (Exception e) {
                log.error("queryCardPurchaseInfoByUserId 查询用户随享卡购卡账单异常e[{}],p[{}]", e, p);
            }
        });
        return cardPurchaseRecordDtos;
    }

    @Override
    public QueryUserCardStatusDto queryUserSuiXiangCardStatus(Long userId) {

        QueryUserCardStatusDto queryUserCardStatusDto = new QueryUserCardStatusDto();
        try {
            //获取待支付数量
            SuixiangCardPurchaseRecordExample example = new SuixiangCardPurchaseRecordExample();
            example.createCriteria().andUserIdEqualTo(userId).andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete())
                    .andIssueTypeEqualTo(SuiXiangCardIssueTypeEnum.PURCHASE.getType())
                    .andPaymentStatusEqualTo(SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus());
            List<SuixiangCardPurchaseRecord> list = cardPurchaseRecordMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(list)) {
                queryUserCardStatusDto.setWaitPayCardNum(0);
            } else {
                queryUserCardStatusDto.setWaitPayCardNum(list.size());
            }

            SuiXiangCardUseQueryDto queryDto = new SuiXiangCardUseQueryDto();
            List<SuixiangCardUse> cardUsesList = suixiangCardUseMapper.selectCardListByUserId(userId, queryDto);

            if (CollectionUtils.isEmpty(cardUsesList)) {
                queryUserCardStatusDto.setCardStatus(-1);
                return queryUserCardStatusDto;
            }

            final BigDecimal[] totalAmount = {BigDecimal.ZERO};
            boolean overTime = false;
            boolean hasEffectiveCard = false;

            for (SuixiangCardUse p : cardUsesList) {
                if (p.getCardStatus().equals(SuiXiangCardStatusEnum.EFFECTIVE.getStatus())) {
                    //有效的随想卡
                    if (p.getExpiresTime().getTime() - System.currentTimeMillis() < 1000 * 60 * 60 * 24 * 7
                            && p.getExpiresTime().getTime() - System.currentTimeMillis() >= 0) {
                        overTime = true;
                    }
                    hasEffectiveCard = true;
                }
                totalAmount[0] = totalAmount[0].add(p.getTotalDiscountAmount());
            }

            if (hasEffectiveCard) {
                //存在有效卡
                queryUserCardStatusDto.setCardStatus(2);
            }
            if (overTime) {
                //过期
                queryUserCardStatusDto.setCardStatus(1);
            }
            queryUserCardStatusDto.setTotalDiscountAmount(totalAmount[0]);

        } catch (Exception e) {
            log.error("queryUserSuiXiangCardStatus查询用户使用卡状态时异常,userId[{}],e[{}]", userId, e);
        }
        return queryUserCardStatusDto;
    }

    @Resource
    private IMemberCardService memberCardService;

    @Override
    public QueryUserCardInfoDto queryUserAllCardInfo(Long userId) {
        //查询 之前会员卡信息的逻辑
        QueryUserCardInfoDto vipUserCardInfoDto = memberCardService.queryUserVipCardInfo(userId);
        if (vipUserCardInfoDto == null) {
            vipUserCardInfoDto = new QueryUserCardInfoDto();
        }

        //获取待支付数量
        SuixiangCardPurchaseRecordExample example = new SuixiangCardPurchaseRecordExample();
        example.createCriteria().andUserIdEqualTo(userId).andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete())
                .andIssueTypeEqualTo(SuiXiangCardIssueTypeEnum.PURCHASE.getType())
                .andPaymentStatusEqualTo(SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus());
        List<SuixiangCardPurchaseRecord> list = cardPurchaseRecordMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            vipUserCardInfoDto.setWaitPayCardNum(vipUserCardInfoDto.getWaitPayCardNum() + list.size());
        }

        // 查询随享卡
        SuiXiangCardUseQueryDto queryDto = new SuiXiangCardUseQueryDto();
        List<SuixiangCardUse> cardUsesList = suixiangCardUseMapper.selectCardListByUserId(userId, queryDto);
        if (CollectionUtils.isEmpty(cardUsesList)) {
            // 设置所有卡片
            setAllCardInfo(vipUserCardInfoDto);
            return vipUserCardInfoDto;
        }

        final BigDecimal[] totalAmount = {BigDecimal.ZERO};
        List<QueryMyCardInfo> suiXiangCardInfo = new ArrayList<>();
        for (SuixiangCardUse s : cardUsesList) {
            try {
                Long purchaseId = s.getPurchaseId();
                Date expiresTime = s.getExpiresTime();
                QueryMyCardInfo info = new QueryMyCardInfo();
                info.setCardGroup(4);
                info.setCardId(s.getId());
                info.setPurchaseId(purchaseId);
                info.setCardName(s.getCardName());
                info.setLimitCondition("");
                info.setCardBaseId(s.getCardBaseId());

                Integer cardStatus = s.getCardStatus();
                info.setSuiXiangCardUseStatus(cardStatus);
                if (cardStatus.equals(SuiXiangCardStatusEnum.DISCARD.getStatus()) || cardStatus.equals(SuiXiangCardStatusEnum.RETURN.getStatus())) {
                    info.setExpiresTime(DateUtil.getFormatDate(s.getUpdateTime(), DateUtil.DATE_TYPE7));
                } else {
                    info.setExpiresTime(DateUtil.getFormatDate(expiresTime, DateUtil.DATE_TYPE7));
                }

                if (cardStatus.equals(SuiXiangCardStatusEnum.EFFECTIVE.getStatus())) {
                    if (expiresTime.getTime() - System.currentTimeMillis() < 1000 * 60 * 60 * 24 * 7
                            && expiresTime.getTime() - System.currentTimeMillis() >= 0) {
                        info.setOverTime(1);
                    }
                }

                SuixiangCardPurchaseRecord purchaseRecord = cardPurchaseRecordMapper.selectByPrimaryKey(purchaseId);
                if (purchaseRecord != null) {
                    info.setSendStatus(purchaseRecord.getIssueType() == 2 ? 1 : 0);
                    SuixiangCardInfoDto suixiangCardInfoDto = cardPriceMapper.selectInfoByPriceId(purchaseRecord.getCardPriceId());
                    if (suixiangCardInfoDto != null) {
                        // 设置我的卡片卡面
                        Integer styleType = suixiangCardInfoDto.getStyleType();
                        String grayUrlByStyleNo = SuiXiangCardStyleImageEnum.getMyCardUrlByStyleNo(styleType);
                        suixiangCardInfoDto.setBackUrl(grayUrlByStyleNo);
                        info.setBackUrl(suixiangCardInfoDto.getBackUrl());
                    }
                }

                // 购买时间
                info.setPurchaseTime(s.getCreateTime());
                // 剩余可用天数
                info.setAvailableDays(s.getAvailableDays());
                SuixiangCardBase suixiangCardBase = suixiangCardBaseMapper.selectByPrimaryKey(s.getCardBaseId());
                if (suixiangCardBase != null && suixiangCardBase.getLandingPageFlag() != null) {
                    info.setLandingPageFlag(suixiangCardBase.getLandingPageFlag());
                }
                suiXiangCardInfo.add(info);

                totalAmount[0] = totalAmount[0].add(s.getTotalDiscountAmount());
            } catch (Exception e) {
                log.error("queryUserAllCardInfo处理卡信息时异常,SuixiangCardUse[{}],e[{}]", JSON.toJSONString(s), e);
            }
        }
        vipUserCardInfoDto.setSuiXiangCardInfo(suiXiangCardInfo);
        // 总优惠
        BigDecimal totalDiscountAmount = vipUserCardInfoDto.getTotalDiscountAmount();
        if (totalDiscountAmount == null) {
            vipUserCardInfoDto.setTotalDiscountAmount(totalAmount[0]);
        } else {
            vipUserCardInfoDto.setTotalDiscountAmount(totalDiscountAmount.add(totalAmount[0]));
        }
        // 设置所有卡片
        setAllCardInfo(vipUserCardInfoDto);
        return vipUserCardInfoDto;
    }


    /**
     * 卡片排序规则：生效中>已冻结>已失效
     * 按照购买时间 降序
     *
     * @param vipUserCardInfoDto
     * @return
     */
    public void setAllCardInfo(QueryUserCardInfoDto vipUserCardInfoDto) {
        List<QueryMyCardInfo> result = Lists.newArrayList();
        try {
            List<VipCardInfo> orgCardInfo = vipUserCardInfoDto.getOrgCardInfo();
            List<VipCardInfo> orgUserInfo = vipUserCardInfoDto.getOrgUserInfo();
            List<VipCardInfo> vipCardInfo = vipUserCardInfoDto.getVipCardInfo();
            List<VipCardInfo> overTimeCardInfo = vipUserCardInfoDto.getOverTimeCardInfo();

            List<QueryMyCardInfo> suiXiangCardInfo = vipUserCardInfoDto.getSuiXiangCardInfo();

            List<QueryMyCardInfo> effectiveList = Lists.newArrayList();
            List<QueryMyCardInfo> frozeList = Lists.newArrayList();
            List<QueryMyCardInfo> invalidityList = Lists.newArrayList();

            if (CollectionUtils.isNotEmpty(suiXiangCardInfo)) {
                suiXiangCardInfo.stream().forEach(s -> {
                    Integer suiXiangCardUseStatus = s.getSuiXiangCardUseStatus();
                    if (ArrayUtils.contains(Constants.SUIXIANGCARD_STATUS_EFFECTIVE, suiXiangCardUseStatus)) {
                        effectiveList.add(s);
                    } else if (ArrayUtils.contains(Constants.SUIXIANGCARD_STATUS_FROZENED, suiXiangCardUseStatus)) {
                        frozeList.add(s);
                    } else if (ArrayUtils.contains(Constants.SUIXIANGCARD_STATUS_INVALIDITY, suiXiangCardUseStatus)) {
                        invalidityList.add(s);
                    }
                });
            }

            if (CollectionUtils.isNotEmpty(orgCardInfo)) {
                orgCardInfo.stream().forEach(o -> {
                    QueryMyCardInfo o2 = QueryMyCardInfo.getQueryUserSuiXiangCardInfo2(o);
                    effectiveList.add(o2);
                });
            }

            if (CollectionUtils.isNotEmpty(orgUserInfo)) {
                orgUserInfo.stream().forEach(o -> {
                    QueryMyCardInfo o2 = QueryMyCardInfo.getQueryUserSuiXiangCardInfo2(o);
                    effectiveList.add(o2);
                });
            }
            if (CollectionUtils.isNotEmpty(vipCardInfo)) {
                vipCardInfo.stream().forEach(o -> {
                    QueryMyCardInfo o2 = QueryMyCardInfo.getQueryUserSuiXiangCardInfo2(o);
                    effectiveList.add(o2);
                });
            }

            if (CollectionUtils.isNotEmpty(overTimeCardInfo)) {
                overTimeCardInfo.stream().forEach(o -> {
                    QueryMyCardInfo o2 = QueryMyCardInfo.getQueryUserSuiXiangCardInfo2(o);
                    invalidityList.add(o2);
                });
            }
            effectiveList.sort(new CardInfoComparator());
            frozeList.sort(new CardInfoComparator());
            invalidityList.sort(new CardInfoComparator());

            result.addAll(effectiveList);
            result.addAll(frozeList);
            result.addAll(invalidityList);
            vipUserCardInfoDto.setAllCardInfo(result);
        } catch (Exception e) {
            log.error("我的卡片，设置AllCardInfo异常，input[{}]", JSON.toJSONString(vipUserCardInfoDto), e);
        }
    }

    @Override
    public List<CardReMindDto> pullUnreadOfferedCards(Long userId, Integer size) {
        List<CardPurchaseListDetailDto> vipCards = getUnreadOfferedCards(userId, size);
        List<CardReMindDto> suiXiangCards = getUnreadSuiXiangCards(userId, size);
        List<CardReMindDto> allCard = new ArrayList<>();


        if (CollectionUtils.isNotEmpty(vipCards)) {
            vipCards.stream().forEach(v -> {
                CardReMindDto dto = new CardReMindDto();
                dto.setCardName(v.getCardName());
                dto.setCreateTime(v.getCreateTime());
                dto.setDiscount(v.getDiscount());
                dto.setCardType(v.getCardType());
                dto.setPurchaseId(v.getId());
                allCard.add(dto);
            });
        }
        if (CollectionUtils.isNotEmpty(suiXiangCards)) {
            allCard.addAll(suiXiangCards);
        }

        // 按照日期排序
        allCard.sort(new Comparator<CardReMindDto>() {
            @Override
            public int compare(CardReMindDto o1, CardReMindDto o2) {
                return o1.getCreateTime().compareTo(o2.getCreateTime());
            }
        });

        List<CardReMindDto> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allCard)) {
            if (allCard.size() > size) {
                result = allCard.subList(0, size);
            } else {
                result = allCard;
            }
        }

        setCardsRead(result);
        return result;
    }


    /**
     * 标记已读
     *
     * @param result
     */
    private void setCardsRead(List<CardReMindDto> result) {
        List<Long> suiXiangIds = new ArrayList<>();
        List<Long> vipIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            result.stream().forEach(c -> {
                if (c.getCardType() == 3) {
                    suiXiangIds.add(c.getPurchaseId());
                } else {
                    vipIds.add(c.getPurchaseId());
                }
            });
        }

        if (CollectionUtils.isNotEmpty(suiXiangIds)) {
            readOfferedCards(suiXiangIds);
        }

        if (CollectionUtils.isNotEmpty(vipIds)) {
            memberCardService.readOfferedCards(vipIds);
        }
    }


    public List<CardPurchaseListDetailDto> getUnreadOfferedCards(Long userId, Integer size) {
        List<CardPurchaseListDetailDto> result = new ArrayList<>();
        if (userId == null) {
            return result;
        }
        if (size == null || size > 30) {
            size = 10;
        }
        Date startDate = DateUtils.addMonths(new Date(), -3);
        List<CardPurchaseRecordInfo> list = mmpCardPurchaseRecordMapper.selectUnreadCardOfferList(userId, startDate, size);

        for (CardPurchaseRecordInfo record : list) {
            CardPurchaseListDetailDto dto = new CardPurchaseListDetailDto();
            MmpCardDef mmpCardDef = mmpCardDefMapper.selectByPrimaryKey(record.getCardId());
            if (mmpCardDef != null) {
                dto.setDiscount(mmpCardDef.getDiscount());
            }
            memberCardInnerService.buildCardPurchaseListView(record, dto);
            result.add(dto);
        }
        return result;
    }


    public List<CardReMindDto> getUnreadSuiXiangCards(Long userId, Integer size) {
        List<CardReMindDto> result = new ArrayList<>();
        if (userId == null) {
            return result;
        }

        if (size == null || size > 30) {
            size = 10;
        }

        Date startDate = DateUtils.addMonths(new Date(), -3);
        List<CardReMindDto> list = cardPurchaseRecordMapper.selectUnreadCardList(userId, startDate, size);
        // 合并卡需要反查
        list.addAll(cardPurchaseRecordMapper.selectUnreadCardMergeList(userId, startDate, 10-list.size()));
        for (CardReMindDto record : list) {
            record.setCardType(3);
        }

        return list;
    }

    @Override
    public int readOfferedCards(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            return cardPurchaseRecordMapper.readCards(ids);
        }
        return 0;
    }

    @Override
    public BaseResponse modifySuiXiangCardUseStatus(ModifySuiXiangCardUseStatusInput input) {
        OperatorDto operatorDto = input.getOperatorDto();
        Long userCardId = input.getUserCardId();
        Integer newCardStatus = input.getCardStatus();
        Integer oldCardStatus = input.getOldCardStatus();
        Integer opertionType = input.getOpertionType();
        if (userCardId == null || newCardStatus == null || oldCardStatus == null || opertionType == null) {
            return new BaseResponse(-1, "入参参数不正确");
        }

        SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(userCardId);
        if (suixiangCardUse == null) {
            return new BaseResponse(-1, "未找到对应的卡使用记录");
        }
        Integer cardStatus = suixiangCardUse.getCardStatus();
        if (cardStatus != oldCardStatus) {
            return new BaseResponse(-1, "使用卡状态不一致，修改失败");
        }

        try {
            SuixiangCardUse suixiangCardUseUpdate = new SuixiangCardUse();
            suixiangCardUseUpdate.setCardStatus(newCardStatus);
            SuixiangCardUseExample example = new SuixiangCardUseExample();
            example.createCriteria().andIdEqualTo(userCardId);

            SuixiangCardUseOpreationLog useLog = new SuixiangCardUseOpreationLog();
            useLog.setCardUseId(suixiangCardUse.getId());
            useLog.setCardPriceId(suixiangCardUse.getCardPriceId());
            useLog.setPurchaseId(suixiangCardUse.getPurchaseId());
            useLog.setCardGroup(1); // 卡类别：1-随享卡
            useLog.setOperationType(opertionType.longValue());
            useLog.setOriginSystem(operatorDto.getOriginSystem()); // 退款
            useLog.setInitDays(suixiangCardUse.getInitDays());
            useLog.setAvailableDays(suixiangCardUse.getAvailableDays());
            useLog.setUsedDays(suixiangCardUse.getUsedDays());
            useLog.setFrozenDays(suixiangCardUse.getFrozenDays());
            useLog.setCreateTime(new Date());
            useLog.setOrderSeq("");
            useLog.setCreateOperId(operatorDto.getOperatorId());
            useLog.setCreateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
            useLog.setMiscDesc(ComUtils.splitStr(operatorDto.getRemark(), 200));
            suixiangCardBaseManager.modifySuiXiangCardUseStatus(suixiangCardUseUpdate, example, useLog);
        } catch (BusinessException e) {
            log.error("modifySuiXiangCardUseStatus 处理数据业务异常，input[{}]", JSON.toJSONString(input), e);
            return new BaseResponse(e.getCode(), e.getMessage());
        }

        return new BaseResponse(0, "成功");
    }


    @Resource
    private OrgService orgService;

    @Override
    public CardPurchaseDetailOutput getCardOrgByPurchaseId(CardPurchaseDetailInput input) {
        Long id = input.getId();
        Integer type = input.getType();
        CardPurchaseDetailOutput output = new CardPurchaseDetailOutput();

        if (id == null || type == null) {
            return null;
        }

        try {
            output.setId(id);
            output.setType(type);
            if (type == 0) {
                CardPurchaseDetailDto c = memberCardService.getCardPurchaseDetail(id);
                if (c != null) {
                    output.setOrgId(c.getOrgId());
                }
            } else {
                SuixiangCardPurchaseRecord s = cardPurchaseRecordMapper.selectByPrimaryKey(id);
                if (s != null) {
                    output.setOrgId(s.getOrgId());
                }
            }
            if (StringUtils.isNotBlank(output.getOrgId())) {
                String orgName = orgService.getOrgNameByOrgId(output.getOrgId());
                output.setOrgName(orgName);
            }
        } catch (Exception e) {
            log.error("getCardPurchaseDetail异常e[{}]", e);
        }
        return output;
    }


    @Override
    public void freezeSuiXiangCardDays(UseSuiXiangCardDaysInput input) throws BusinessException {
        try {
            Integer days = input.getDays();
            Date applyTime = input.getApplyTime();
            String contractId = input.getContractId();
            Long userId = input.getUserId();
            Long userCardId = input.getUserCardId();
            OperatorDto operateDto = input.getOperateDto();
            if (days == null || applyTime == null || StringUtils.isBlank(contractId) || userCardId == null || userId == null || operateDto == null) {
                throw new BusinessException(-1, "入参异常");
            }

            if (days <= 0) {
                log.warn("freezeSuiXiangCardDays：申请天数必须大于0，input={}", JSON.toJSONString(input));
                throw new BusinessException(-1, "申请天数必须大于0");
            }
            SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(userCardId);

            if (suixiangCardUse == null) {
                throw new BusinessException(-1, "未查找到对应的使用记录");
            }
            if (!userId.equals(suixiangCardUse.getUserId())) {
                throw new BusinessException(-1, "用户不匹配");
            }

            if (suixiangCardUse.getIsDeleted() == IsDeleteEnum.DELETE.getIsDelete()) {
                log.error("freezeSuiXiangCardDays：申请天数使用周期无效，input={}，suixiangCardUse={}.", JSON.toJSONString(input), JSON.toJSONString(suixiangCardUse));
                throw new BusinessException(-1, "查找到对应的使用记录 -无效");
            }

            if (suixiangCardUse.getCardStatus() != SuiXiangCardStatusEnum.EFFECTIVE.getStatus()) {
                log.error("freezeSuiXiangCardDays：随享卡状态未生效，input={}，suixiangCardUse={}.", JSON.toJSONString(input), JSON.toJSONString(suixiangCardUse));
                throw new BusinessException(-1, "随享卡状态未生效");
            }

            Integer availableDays = suixiangCardUse.getAvailableDays();
            Integer frozenDays = suixiangCardUse.getFrozenDays();
            int newFrozenDays = frozenDays + days;
            int newavailableDays = availableDays - days;
            if (days > availableDays) {
                throw new BusinessException(-1, "申请冻结天数 超出随享卡 可用天数");
            }

            // 更新内容
            SuixiangCardUse suixiangCardUseUpdate = new SuixiangCardUse();
            suixiangCardUseUpdate.setAvailableDays(newavailableDays);
            suixiangCardUseUpdate.setFrozenDays(newFrozenDays);
            suixiangCardUseUpdate.setUpdateTime(applyTime);
            suixiangCardUseUpdate.setUpdateOperId(operateDto.getOperatorId());
            suixiangCardUseUpdate.setUpdateOperName(operateDto.getOperatorName());
            // where条件
            SuixiangCardUseExample useExample = new SuixiangCardUseExample();
            useExample.createCriteria().andIdEqualTo(suixiangCardUse.getId()).andFrozenDaysEqualTo(frozenDays);

            //操作日志
            SuixiangCardUseOpreationLog insertLog = SuixiangCardUseOpreationLog.getLogFromSuixiangCardUse2(suixiangCardUse, SuiXiangCardUseLogOperationTypeEnum.FREEZE.getOperationType(), input);
            suixiangCardBaseManager.freezeSuiXiangCardDays(suixiangCardUseUpdate, useExample, insertLog);
        } catch (BusinessException e) {
            log.error("申请冻结业务异常 input[{}]", JSON.toJSONString(input), e);
            throw e;
        } catch (Exception e1) {
            log.error("申请冻结异常 input[{}]", JSON.toJSONString(input), e1);
            throw new BusinessException(-1, "申请冻结异常");
        }
    }

    @Override
    public void unfreezeSuiXiangCardDays(UseSuiXiangCardDaysInput input) throws BusinessException {
        try {
            Integer unfreezeDays = input.getDays();
            Date applyTime = input.getApplyTime();
            String contractId = input.getContractId();
            Long userId = input.getUserId();
            Long userCardId = input.getUserCardId();
            OperatorDto operateDto = input.getOperateDto();
            if (unfreezeDays == null || applyTime == null || StringUtils.isBlank(contractId) || userCardId == null || userId == null || operateDto == null) {
                throw new BusinessException(-1, "入参异常");
            }

            if (unfreezeDays <= 0) {
                log.warn("unfreezeSuiXiangCardDays：申请天数必须大于0，input={}", JSON.toJSONString(input));
                throw new BusinessException(-1, "申请天数必须大于0");
            }

            SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(userCardId);
            if (suixiangCardUse == null) {
                throw new BusinessException(-1, "未查找到对应的使用记录");
            }

            if (!userId.equals(suixiangCardUse.getUserId())) {
                throw new BusinessException(-1, "用户不匹配");
            }

            if (suixiangCardUse.getIsDeleted() == IsDeleteEnum.DELETE.getIsDelete()) {
                log.error("unfreezeSuiXiangCardDays：申请天数使用周期无效，input={}，suixiangCardUse={}.", JSON.toJSONString(input), JSON.toJSONString(suixiangCardUse));
                throw new BusinessException(-1, "查找到对应的使用记录 -无效");
            }

            // 冻结记录 天数判断
            unfreezeAndDeductDaysJudge(input);

           /* if (suixiangCardUse.getCardStatus() != SuiXiangCardStatusEnum.EFFECTIVE.getStatus()) {
                log.error("unfreezeSuiXiangCardDays：随享卡状态未生效，input={}，suixiangCardUse={}.", JSON.toJSONString(input), JSON.toJSONString(suixiangCardUse));
                throw new BusinessException(-1, "随享卡状态未生效");
            }*/
            Integer initDays = suixiangCardUse.getInitDays();
            Integer availableDays = suixiangCardUse.getAvailableDays();
            Integer frozenDays = suixiangCardUse.getFrozenDays();
            int newFrozenDays = frozenDays - unfreezeDays;
            int newavailableDays = availableDays + unfreezeDays;
            if (unfreezeDays > frozenDays) {
                throw new BusinessException(-1, "申请解冻天数 超出随享卡 冻结天数");
            }

            if (newavailableDays > initDays) {
                throw new BusinessException(-1, "申请解冻天数后可用天数 大于 卡初始天数");
            }

            // 更新内容
            SuixiangCardUse suixiangCardUseUpdate = new SuixiangCardUse();
            suixiangCardUseUpdate.setAvailableDays(newavailableDays);
            suixiangCardUseUpdate.setFrozenDays(newFrozenDays);
            suixiangCardUseUpdate.setUpdateTime(input.getApplyTime());
            suixiangCardUseUpdate.setUpdateOperId(operateDto.getOperatorId());
            suixiangCardUseUpdate.setUpdateOperName(operateDto.getOperatorName());
            // where条件
            SuixiangCardUseExample useExample = new SuixiangCardUseExample();
            useExample.createCriteria().andIdEqualTo(suixiangCardUse.getId()).andFrozenDaysEqualTo(frozenDays);

            //操作日志
            SuixiangCardUseOpreationLog insertLog = SuixiangCardUseOpreationLog.getLogFromSuixiangCardUse2(suixiangCardUse, SuiXiangCardUseLogOperationTypeEnum.UNFREEZE.getOperationType(), input);
            suixiangCardBaseManager.unfreezeSuiXiangCardDays(suixiangCardUseUpdate, useExample, insertLog);

            //TODO 待作废状态下且解冻金额清0，则作废此卡片。
            //tryCompleteCancelCard(mmpUserCardInfo, userCardDiscountInfo.getId(), operateDto);
        } catch (BusinessException e) {
            log.error("申请解冻业务异常 input[{}]", JSON.toJSONString(input), e);
            throw e;
        } catch (Exception e1) {
            log.error("申请解冻异常 input[{}]", JSON.toJSONString(input), e1);
            throw new BusinessException(-1, "申请解冻异常");
        }
    }

    /**
     * 解冻 扣除 天数判断
     * <p>
     * 历史冻结天数不为 0；
     * 历史冻结天数 大于 历史扣除和解冻天数
     * 历史冻结天数  大于等于 历史扣除和解冻天数 +此次操作天数
     *
     * @param input
     * @throws BusinessException
     */
    private void unfreezeAndDeductDaysJudge(UseSuiXiangCardDaysInput input) throws BusinessException {
        Long userCardId = input.getUserCardId();
        String contractId = input.getContractId();
        Integer days = input.getDays();

        List<Long> operationType = Lists.newArrayList(SuiXiangCardUseLogOperationTypeEnum.FREEZE.getOperationType(), SuiXiangCardUseLogOperationTypeEnum.CARD_CONSUME.getOperationType(), SuiXiangCardUseLogOperationTypeEnum.UNFREEZE.getOperationType());
        SuixiangCardUseOpreationLogExample example = new SuixiangCardUseOpreationLogExample();
        example.createCriteria().andCardUseIdEqualTo(userCardId)
                .andOrderSeqEqualTo(contractId)
                .andOperationTypeIn(operationType);
        List<SuixiangCardUseOpreationLog> suixiangCardUseOpreationLogs = suixiangCardUseOpreationLogMapper.selectByExample(example);
        boolean hasFreezeFlag = true;
        boolean freezeOverUnfreezeFlag = true;
        if (CollectionUtils.isEmpty(suixiangCardUseOpreationLogs)) {
            log.error("unfreezeSuiXiangCardDays：订单号没有冻结、解冻、扣除记录，input={}.", JSON.toJSONString(input));
            hasFreezeFlag = false;
        } else {
            int freezeLogDays = 0;
            int unfreezeAndDecutionLogDays = 0;
            for (SuixiangCardUseOpreationLog s : suixiangCardUseOpreationLogs) {
                if (SuiXiangCardUseLogOperationTypeEnum.FREEZE.getOperationType() == s.getOperationType()) {
                    freezeLogDays = freezeLogDays + s.getOrderOperationDays();
                } else if (SuiXiangCardUseLogOperationTypeEnum.CARD_CONSUME.getOperationType() == s.getOperationType() || SuiXiangCardUseLogOperationTypeEnum.UNFREEZE.getOperationType() == s.getOperationType()) {
                    unfreezeAndDecutionLogDays = unfreezeAndDecutionLogDays + s.getOrderOperationDays();
                }
            }

            if (freezeLogDays == 0) {
                hasFreezeFlag = false;
            }

            if (freezeLogDays <= unfreezeAndDecutionLogDays) {
                log.error("unfreezeSuiXiangCardDays：订单号对应历史记录 冻结天数小于等于解冻扣除天数，不允许接口操作，input={}，freezeLogDays[{}],unfreezeAndDecutionLogDays[{}].", JSON.toJSONString(input), freezeLogDays, unfreezeAndDecutionLogDays);
                freezeOverUnfreezeFlag = false;
            } else if ((freezeLogDays - unfreezeAndDecutionLogDays - days) < 0) {
                log.error("unfreezeSuiXiangCardDays：这笔订单扣除或解冻天数不允许接口操作，input={}，freezeLogDays[{}],unfreezeAndDecutionLogDays[{}].", JSON.toJSONString(input), freezeLogDays, unfreezeAndDecutionLogDays);
                freezeOverUnfreezeFlag = false;
            }
        }

        if (!hasFreezeFlag) {
            log.error("unfreezeSuiXiangCardDays：订单号没有冻结记录，input={}.", JSON.toJSONString(input));
            throw new BusinessException(-1, "该笔订单未有对应的冻结记录");
        }

        if (!freezeOverUnfreezeFlag) {
            log.error("unfreezeSuiXiangCardDays：订单号对应冻结记录天数小于等于解冻扣除天数，不允许接口扣除，input={}.", JSON.toJSONString(input));
            throw new BusinessException(-1, "该笔订单对应冻结记录天数不够");
        }
    }

    @Override
    public void deductFrozenSuiXiangCardDays(UseSuiXiangCardDaysInput input) throws BusinessException {
        try {
            Integer deductDays = input.getDays();
            Date applyTime = input.getApplyTime();
            String contractId = input.getContractId();
            Long userId = input.getUserId();
            Long userCardId = input.getUserCardId();
            OperatorDto operateDto = input.getOperateDto();
            if (deductDays == null || applyTime == null || StringUtils.isBlank(contractId) || userCardId == null || userId == null || operateDto == null) {
                throw new BusinessException(-1, "入参异常");
            }

            if (deductDays <= 0) {
                log.warn("deductFrozenSuiXiangCardDays：申请天数必须大于0，input={}", JSON.toJSONString(input));
                throw new BusinessException(-1, "申请天数必须大于0");
            }

            SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(userCardId);
            if (suixiangCardUse == null) {
                throw new BusinessException(-1, "未查找到对应的使用记录");
            }

            if (!userId.equals(suixiangCardUse.getUserId())) {
                throw new BusinessException(-1, "用户不匹配");
            }

            if (suixiangCardUse.getIsDeleted() == IsDeleteEnum.DELETE.getIsDelete()) {
                log.error("deductFrozenSuiXiangCardDays：使用记录无效，input={}，suixiangCardUse={}.", JSON.toJSONString(input), JSON.toJSONString(suixiangCardUse));
                throw new BusinessException(-1, "查找到对应的使用记录 -无效");
            }

            // 冻结记录 天数判断
            unfreezeAndDeductDaysJudge(input);

            /*if (suixiangCardUse.getCardStatus() != SuiXiangCardStatusEnum.EFFECTIVE.getStatus()) {
                log.error("deductFrozenSuiXiangCardDays：随享卡状态未生效，input={}，suixiangCardUse={}.", JSON.toJSONString(input), JSON.toJSONString(suixiangCardUse));
                throw new BusinessException(-1, "随享卡状态不是生效中");
            }*/

            Integer frozenDays = suixiangCardUse.getFrozenDays();
            Integer usedDays = suixiangCardUse.getUsedDays();
            Integer initDays = suixiangCardUse.getInitDays();
            int newFrozenDays = frozenDays - deductDays;
            int newUsedDays = usedDays + deductDays;
            if (deductDays > frozenDays) {
                throw new BusinessException(-1, "申请扣除天数 超出随享卡 冻结天数");
            }

            boolean overFlag = false;
            // 用完天数
            if (initDays == newUsedDays) {
                log.info("随享卡天数用完。userCardId[{}]", userCardId);
                overFlag = true;
            }

            // 更新内容
            SuixiangCardUse suixiangCardUseUpdate = new SuixiangCardUse();
            suixiangCardUseUpdate.setUsedDays(newUsedDays);
            suixiangCardUseUpdate.setFrozenDays(newFrozenDays);
            suixiangCardUseUpdate.setUpdateTime(input.getApplyTime());
            suixiangCardUseUpdate.setUpdateOperId(operateDto.getOperatorId());
            suixiangCardUseUpdate.setUpdateOperName(operateDto.getOperatorName());
            suixiangCardUseUpdate.setTotalOrder(suixiangCardUse.getTotalOrder() + 1);
            suixiangCardUseUpdate.setTotalDiscountAmount(suixiangCardUse.getTotalDiscountAmount().add(input.getTotalAmount()));
            // 天数用完，状态由 生效中 变成 已使用
            if (overFlag) {
                suixiangCardUseUpdate.setCardStatus(SuiXiangCardStatusEnum.USED.getStatus());
            }
            // where条件
            SuixiangCardUseExample useExample = new SuixiangCardUseExample();
            useExample.createCriteria().andIdEqualTo(suixiangCardUse.getId()).andFrozenDaysEqualTo(frozenDays);

            //操作日志
            SuixiangCardUseOpreationLog insertLog = SuixiangCardUseOpreationLog.getLogFromSuixiangCardUse2(suixiangCardUse, SuiXiangCardUseLogOperationTypeEnum.CARD_CONSUME.getOperationType(), input);

            suixiangCardBaseManager.deductFreezeSuiXiangCardDays(suixiangCardUseUpdate, useExample, insertLog);

            //TODO 待作废状态下且解冻金额清0，则作废此卡片。
            //tryCompleteCancelCard(mmpUserCardInfo, userCardDiscountInfo.getId(), operateDto);
        } catch (BusinessException e) {
            log.error("申请扣除冻结天数异常 input[{}]", JSON.toJSONString(input), e);
            throw e;
        } catch (Exception e1) {
            log.error("申请扣除冻结天数业务异常 input[{}]", JSON.toJSONString(input), e1);
            throw new BusinessException(-1, "扣除冻结天数异常");
        }

    }

    @Override
    public void unFreezeAndFreezeSuiXiangCardDays(UnFreezeAndFreezeSuiXiangCardDaysInput input) throws BusinessException {
        try {
            Integer unfreezeDays = input.getUnFreezeDays();
            Integer freezeDays = input.getFreezeDays();
            Date applyTime = input.getApplyTime();
            String oldContractId = input.getOldContractId();
            String newContractId = input.getNewContractId();
            Long userId = input.getUserId();
            Long userCardId = input.getUserCardId();
            OperatorDto operateDto = input.getOperateDto();
            if (unfreezeDays == null || freezeDays == null || applyTime == null || StringUtils.isBlank(oldContractId) || StringUtils.isBlank(newContractId) || userCardId == null || userId == null || operateDto == null) {
                throw new BusinessException(-1, "入参异常");
            }

            if (unfreezeDays <= 0 || freezeDays <= 0) {
                log.warn("unFreezeAndFreezeSuiXiangCardDays：申请天数必须大于0，input={}", JSON.toJSONString(input));
                throw new BusinessException(-1, "冻结或解冻天数必须大于0");
            }

            SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(userCardId);
            if (suixiangCardUse == null) {
                throw new BusinessException(-1, "未查找到对应的使用记录");
            }

            if (!userId.equals(suixiangCardUse.getUserId())) {
                throw new BusinessException(-1, "用户不匹配");
            }

            if (suixiangCardUse.getIsDeleted() == IsDeleteEnum.DELETE.getIsDelete()) {
                log.error("unfreezeSuiXiangCardDays：申请天数使用周期无效，input={}，suixiangCardUse={}.", JSON.toJSONString(input), JSON.toJSONString(suixiangCardUse));
                throw new BusinessException(-1, "查找到对应的使用记录 -无效");
            }

            // 1:解冻；冻结记录 天数判断
            UseSuiXiangCardDaysInput unfreezeInput = getUnfreezeInput(input);
            unfreezeAndDeductDaysJudge(unfreezeInput);

            Integer initDays = suixiangCardUse.getInitDays();
            Integer availableDays = suixiangCardUse.getAvailableDays();
            Integer oldFrozenDays = suixiangCardUse.getFrozenDays();
            int newFrozenDays = oldFrozenDays - unfreezeDays;
            int newavailableDays = availableDays + unfreezeDays;
            if (unfreezeDays > oldFrozenDays) {
                throw new BusinessException(-1, "申请解冻天数 超出随享卡 冻结天数");
            }

            if (newavailableDays > initDays) {
                throw new BusinessException(-1, "申请解冻天数后可用天数 大于 卡初始天数");
            }

            // 更新内容
            SuixiangCardUse suixiangCardUseUpdate = new SuixiangCardUse();
            suixiangCardUseUpdate.setAvailableDays(newavailableDays);
            suixiangCardUseUpdate.setFrozenDays(newFrozenDays);
            suixiangCardUseUpdate.setUpdateTime(applyTime);
            suixiangCardUseUpdate.setUpdateOperId(operateDto.getOperatorId());
            suixiangCardUseUpdate.setUpdateOperName(operateDto.getOperatorName());
            // where条件
            SuixiangCardUseExample useExample = new SuixiangCardUseExample();
            useExample.createCriteria().andIdEqualTo(suixiangCardUse.getId()).andFrozenDaysEqualTo(oldFrozenDays);

            //操作日志
            SuixiangCardUseOpreationLog insertLog = SuixiangCardUseOpreationLog.getLogFromSuixiangCardUse2(suixiangCardUse, SuiXiangCardUseLogOperationTypeEnum.UNFREEZE.getOperationType(), unfreezeInput);


            // 2:冻结天数
            UseSuiXiangCardDaysInput freezeInput = getFreezeInput(input);
            int new2FrozenDays = newFrozenDays + freezeDays;
            int new2availableDays = newavailableDays - freezeDays;
            if (freezeDays > new2availableDays) {
                throw new BusinessException(-1, "申请冻结天数 超出随享卡 可用天数");
            }

            // 更新内容
            SuixiangCardUse suixiangCardUseUpdate2 = new SuixiangCardUse();
            suixiangCardUseUpdate2.setAvailableDays(new2availableDays);
            suixiangCardUseUpdate2.setFrozenDays(new2FrozenDays);
            suixiangCardUseUpdate2.setUpdateTime(applyTime);
            suixiangCardUseUpdate2.setUpdateOperId(operateDto.getOperatorId());
            suixiangCardUseUpdate2.setUpdateOperName(operateDto.getOperatorName());
            // where条件
            SuixiangCardUseExample useExample2 = new SuixiangCardUseExample();
            useExample2.createCriteria().andIdEqualTo(suixiangCardUse.getId()).andFrozenDaysEqualTo(newFrozenDays);

            //操作日志
            suixiangCardBaseManager.modifyOrderOperateSuiXiangCardDays(suixiangCardUseUpdate,useExample,insertLog,suixiangCardUseUpdate2, useExample2, freezeInput);

        } catch (BusinessException e) {
            log.error("modifyOrderOperateSuiXiangCardDays业务异常 input[{}]", JSON.toJSONString(input), e);
            throw e;
        } catch (Exception e1) {
            log.error("modifyOrderOperateSuiXiangCardDays异常 input[{}]", JSON.toJSONString(input), e1);
            throw new BusinessException(-1, "操作失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deductAndUnfreezeSuiXiangCardDays(DeductAndUnfreezeSuiXiangCardDaysInput input) throws BusinessException {
        try {
            Integer unfreezeDays = input.getUnFreezeDays();
            Integer deductDays = input.getDeductDays();
            Date applyTime = input.getApplyTime();
            String deductContractId = input.getDeductContractId();
            String unFreezeContractId = input.getUnFreezeContractId();
            Long userId = input.getUserId();
            Long userCardId = input.getUserCardId();
            OperatorDto operateDto = input.getOperateDto();
            if (unfreezeDays == null || deductDays == null || applyTime == null || StringUtils.isBlank(deductContractId) || StringUtils.isBlank(unFreezeContractId) || userCardId == null || userId == null || operateDto == null) {
                throw new BusinessException(-1, "入参异常");
            }

            if (unfreezeDays <= 0 || deductDays <= 0) {
                log.warn("deductAndUnfreezeSuiXiangCardDays：申请天数必须大于0，input={}", JSON.toJSONString(input));
                throw new BusinessException(-1, "扣除或解冻天数必须大于0");
            }

            // 1:扣除；扣除天数
            UseSuiXiangCardDaysInput deductInput = getDeductInput(input);
            suiXiangCardService.deductSuiXiangCardDays(operateDto, deductInput);

            // 2:解冻 天数
            UseSuiXiangCardDaysInput unfreezeInput = getUnfreezeInput(input);
            suiXiangCardService.unFreezeSuiXiangCardDays(operateDto, unfreezeInput);
        } catch (BusinessException e) {
            log.error("deductAndUnfreezeSuiXiangCardDays业务异常 input[{}]", JSON.toJSONString(input), e);
            throw e;
        } catch (Exception e1) {
            log.error("deductAndUnfreezeSuiXiangCardDays异常 input[{}]", JSON.toJSONString(input), e1);
            throw new BusinessException(-1, "操作失败");
        }
    }

    public void unFreezeSuiXiangCardDays(OperatorDto operateDto, UseSuiXiangCardDaysInput unfreezeInput) throws BusinessException {
        Integer unfreezeDays = unfreezeInput.getDays();
        Long userCardId = unfreezeInput.getUserCardId();
        Long userId = unfreezeInput.getUserId();
        unfreezeAndDeductDaysJudge(unfreezeInput);

        SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(userCardId);
        if (suixiangCardUse == null) {
            throw new BusinessException(-1, "未查找到对应的使用记录");
        }

        if (!userId.equals(suixiangCardUse.getUserId())) {
            throw new BusinessException(-1, "用户不匹配");
        }

        if (suixiangCardUse.getIsDeleted() == IsDeleteEnum.DELETE.getIsDelete()) {
            log.error("unFreezeSuiXiangCardDays：申请天数使用周期无效，input={}，suixiangCardUse={}.", JSON.toJSONString(unfreezeInput), JSON.toJSONString(suixiangCardUse));
            throw new BusinessException(-1, "查找到对应的使用记录 -无效");
        }

        Integer initDays = suixiangCardUse.getInitDays();
        Integer availableDays = suixiangCardUse.getAvailableDays();
        Integer oldFrozenDays = suixiangCardUse.getFrozenDays();
        int newFrozenDays = oldFrozenDays - unfreezeDays;
        int newavailableDays = availableDays + unfreezeDays;
        if (unfreezeDays > oldFrozenDays) {
            throw new BusinessException(-1, "申请解冻天数 超出随享卡 冻结天数");
        }

        if (newavailableDays > initDays) {
            throw new BusinessException(-1, "申请解冻天数后可用天数 大于 卡初始天数");
        }

        // 更新内容
        SuixiangCardUse suixiangCardUseUpdate = new SuixiangCardUse();
        suixiangCardUseUpdate.setAvailableDays(newavailableDays);
        suixiangCardUseUpdate.setFrozenDays(newFrozenDays);
        suixiangCardUseUpdate.setUpdateTime(unfreezeInput.getApplyTime());
        suixiangCardUseUpdate.setUpdateOperId(operateDto.getOperatorId());
        suixiangCardUseUpdate.setUpdateOperName(operateDto.getOperatorName());
        // where条件
        SuixiangCardUseExample useExample = new SuixiangCardUseExample();
        useExample.createCriteria().andIdEqualTo(suixiangCardUse.getId()).andFrozenDaysEqualTo(oldFrozenDays);

        //操作日志
        SuixiangCardUseOpreationLog insertUseLog = SuixiangCardUseOpreationLog.getLogFromSuixiangCardUse2(suixiangCardUse,
                SuiXiangCardUseLogOperationTypeEnum.UNFREEZE.getOperationType(), unfreezeInput);
        try {
            int ret = suixiangCardUseMapper.updateByExampleSelective(suixiangCardUseUpdate, useExample);
            if (ret != 1) {
                log.error("解冻随享卡天数，更新失败,suixiangCardUseUpdate[{}]，insertUseLog[{}]", JSON.toJSONString(suixiangCardUseUpdate), JSON.toJSONString(insertUseLog));
                throw new BusinessException(-1, "解冻失败");
            }
            suixiangCardUseOpreationLogMapper.insertSelective(insertUseLog);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e1) {
            throw new BusinessException(-1, "解冻失败");
        }
    }

    public void deductSuiXiangCardDays(OperatorDto operateDto, UseSuiXiangCardDaysInput deductInput) throws BusinessException {
        Integer deductDays = deductInput.getDays();
        Long userCardId = deductInput.getUserCardId();
        Long userId = deductInput.getUserId();

        SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(userCardId);
        if (suixiangCardUse == null) {
            throw new BusinessException(-1, "未查找到对应的使用记录");
        }

        if (!userId.equals(suixiangCardUse.getUserId())) {
            throw new BusinessException(-1, "用户不匹配");
        }

        if (suixiangCardUse.getIsDeleted() == IsDeleteEnum.DELETE.getIsDelete()) {
            log.error("deductAndUnfreezeSuiXiangCardDays：申请天数使用周期无效，input={}，suixiangCardUse={}.", JSON.toJSONString(deductInput), JSON.toJSONString(suixiangCardUse));
            throw new BusinessException(-1, "查找到对应的使用记录 -无效");
        }

        // 冻结记录 天数判断
        unfreezeAndDeductDaysJudge(deductInput);

        Integer frozenDays = suixiangCardUse.getFrozenDays();
        Integer usedDays = suixiangCardUse.getUsedDays();
        Integer initDays = suixiangCardUse.getInitDays();
        int newFrozenDays = frozenDays - deductDays;
        int newUsedDays = usedDays + deductDays;
        if (deductDays > frozenDays) {
            throw new BusinessException(-1, "申请扣除天数 超出随享卡 冻结天数");
        }

        boolean overFlag = false;
        // 用完天数
        if (initDays == newUsedDays) {
            log.info("随享卡天数用完。userCardId[{}]", userCardId);
            overFlag = true;
        }

        // 更新内容
        SuixiangCardUse suixiangCardUseUpdate = new SuixiangCardUse();
        suixiangCardUseUpdate.setUsedDays(newUsedDays);
        suixiangCardUseUpdate.setFrozenDays(newFrozenDays);
        suixiangCardUseUpdate.setUpdateTime(deductInput.getApplyTime());
        suixiangCardUseUpdate.setUpdateOperId(operateDto.getOperatorId());
        suixiangCardUseUpdate.setUpdateOperName(operateDto.getOperatorName());
        suixiangCardUseUpdate.setTotalOrder(suixiangCardUse.getTotalOrder() + 1);
        suixiangCardUseUpdate.setTotalDiscountAmount(suixiangCardUse.getTotalDiscountAmount().add(deductInput.getTotalAmount()));
        // 天数用完，状态由 生效中 变成 已使用
        if (overFlag) {
            suixiangCardUseUpdate.setCardStatus(SuiXiangCardStatusEnum.USED.getStatus());
        }
        // where条件
        SuixiangCardUseExample useExample = new SuixiangCardUseExample();
        useExample.createCriteria().andIdEqualTo(suixiangCardUse.getId()).andFrozenDaysEqualTo(frozenDays);

        //操作日志
        SuixiangCardUseOpreationLog insertLog = SuixiangCardUseOpreationLog.getLogFromSuixiangCardUse2(suixiangCardUse, SuiXiangCardUseLogOperationTypeEnum.CARD_CONSUME.getOperationType(), deductInput);
        //db
        try {
            int ret = suixiangCardUseMapper.updateByExampleSelective(suixiangCardUseUpdate, useExample);
            if (ret != 1) {
                log.error("扣除随享卡冻结天数，更新失败,suixiangCardUseUpdate[{}]，insertUseLog[{}]", JSON.toJSONString(suixiangCardUseUpdate), JSON.toJSONString(insertLog));
                throw new BusinessException(-1, "扣除失败");
            }
            suixiangCardUseOpreationLogMapper.insertSelective(insertLog);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e1) {
            throw new BusinessException(-1, "扣除失败");
        }
    }

    private UseSuiXiangCardDaysInput getUnfreezeInput(UnFreezeAndFreezeSuiXiangCardDaysInput input) {
        UseSuiXiangCardDaysInput result = new UseSuiXiangCardDaysInput();
        BeanUtils.copyProperties(input,result);
        result.setContractId(input.getOldContractId());
        result.setDays(input.getUnFreezeDays());
        result.setTotalAmount(BigDecimal.ZERO);
        return result;
    }

    private UseSuiXiangCardDaysInput getFreezeInput(UnFreezeAndFreezeSuiXiangCardDaysInput input) {
        UseSuiXiangCardDaysInput result = new UseSuiXiangCardDaysInput();
        BeanUtils.copyProperties(input,result);
        result.setContractId(input.getNewContractId());
        result.setDays(input.getFreezeDays());
        return result;
    }

    private UseSuiXiangCardDaysInput getUnfreezeInput(DeductAndUnfreezeSuiXiangCardDaysInput input) {
        UseSuiXiangCardDaysInput result = new UseSuiXiangCardDaysInput();
        BeanUtils.copyProperties(input,result);
        result.setContractId(input.getUnFreezeContractId());
        result.setDays(input.getUnFreezeDays());
        result.setTotalAmount(BigDecimal.ZERO);
        return result;
    }

    private UseSuiXiangCardDaysInput getDeductInput(DeductAndUnfreezeSuiXiangCardDaysInput input) {
        UseSuiXiangCardDaysInput result = new UseSuiXiangCardDaysInput();
        BeanUtils.copyProperties(input,result);
        result.setContractId(input.getDeductContractId());
        result.setDays(input.getUnFreezeDays());
        result.setTotalAmount(input.getTotalAmount());
        return result;
    }


    @Override
    public List<SuiXiangCardPurchaseRecordDto> queryCardPurchaseInfoListByCondition(Long userId,int paymentStatus) {
        try {
            SuixiangCardPurchaseRecordExample example = new SuixiangCardPurchaseRecordExample();
            example.createCriteria()
                    .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete())
                    .andUserIdEqualTo(userId)
            .andPaymentStatusEqualTo(paymentStatus);

            List<SuixiangCardPurchaseRecord> list = cardPurchaseRecordMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }else{
                return list.stream().map(SuixiangCardPurchaseRecord::toDto).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("queryCardPurchaseInfoListByCondition,userId<{}>,paymentStatus<{}>,异常",userId,paymentStatus,e);
            return null;
        }
    }

    @Override
    public QuerySuiXiangCardRentDayOutput querySuiXiangCardRentDay(QuerySuiXiangCardRentDayInput querySuiXiangCardRentDayInput) {
        SuixiangCardRentDaysExample rentDaysExample = new SuixiangCardRentDaysExample();
        rentDaysExample.createCriteria()
                .andCardBaseIdEqualTo(querySuiXiangCardRentDayInput.getCardBaseId())
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardRentDays> rentDaysList = suixiangCardRentDaysMapper.selectByExample(rentDaysExample);
        log.info("查询suixiang_card_rent_days,rentDaysList:{}", JSON.toJSON(rentDaysList));
        List<SuiXiangCardRentDayDto> suiXiangCardRentDayDtoList = new ArrayList<>();
        for (SuixiangCardRentDays suixiangCardRentDays : rentDaysList) {
            suiXiangCardRentDayDtoList.add(suixiangCardRentDays.toDto());
        }
        QuerySuiXiangCardRentDayOutput querySuiXiangCardRentDayOutput = new QuerySuiXiangCardRentDayOutput();
        querySuiXiangCardRentDayOutput.setSuiXiangCardRentDayDtoList(suiXiangCardRentDayDtoList);
        return querySuiXiangCardRentDayOutput;
    }

    @Override
    public QuerySuiXiangCardPriceOutput querySuiXiangCardPrice(QuerySuiXiangCardPriceInput querySuiXiangCardPriceInput) {
        SuixiangCardPriceExample priceExample = new SuixiangCardPriceExample();
        priceExample.createCriteria()
                .andCardRentIdEqualTo(querySuiXiangCardPriceInput.getCardRentId())
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardPrice> suixiangCardPrices = suixiangCardPriceMapper.selectByExample(priceExample);
        log.info("查询suixiang_card_price, suixiangCardPrices:{}", suixiangCardPrices);
        List<SuiXiangCardPriceDto> suiXiangCardPriceDtoList = new ArrayList<>();
        for (SuixiangCardPrice suixiangCardPrice : suixiangCardPrices) {
            suiXiangCardPriceDtoList.add(suixiangCardPrice.toDto());
        }
        QuerySuiXiangCardPriceOutput querySuiXiangCardPriceOutput = new QuerySuiXiangCardPriceOutput();
        querySuiXiangCardPriceOutput.setSuixiangCardPriceList(suiXiangCardPriceDtoList);
        return querySuiXiangCardPriceOutput;
    }

}
