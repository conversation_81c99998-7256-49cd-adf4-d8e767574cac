<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.MmpUserCardInfoMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.MmpUserCardInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:00:12 CST 2020.
    -->
    <id column="user_card_no" property="userCardNo" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="card_group" property="cardGroup" jdbcType="INTEGER" />
    <result column="card_name" property="cardName" jdbcType="VARCHAR" />
    <result column="card_status" property="cardStatus" jdbcType="INTEGER" />
    <result column="agency_id" property="agencyId" jdbcType="VARCHAR" />
    <result column="card_id" property="cardId" jdbcType="BIGINT" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="expires_time" property="expiresTime" jdbcType="TIMESTAMP" />
    <result column="total_order" property="totalOrder" jdbcType="DECIMAL" />
    <result column="total_discount_amount" property="totalDiscountAmount" jdbcType="DECIMAL" />
    <result column="active_flag" property="activeFlag" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:00:12 CST 2020.
    -->
    user_card_no, user_id, card_group, card_name, card_status, agency_id, card_id, start_time, 
    expires_time, total_order, total_discount_amount, active_flag, status, create_time, create_oper_id,
    create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:00:12 CST 2020.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_card_info
    where user_card_no = #{userCardNo,jdbcType=BIGINT}
  </select>
    <select id="queryUserVipCardById" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from ${issSchema}.mmp_user_card_info
      where user_id = #{id} and status = 0
    </select>
    <select id="queryUserVipCardByIdAndCard"
            resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from ${issSchema}.mmp_user_card_info
      where user_id = #{id} and card_id = #{cardId}
      limit 1
    </select>
  <select id="queryUserOverTimeCardInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_card_info
    where status = 0 and card_status = 1 and card_group = 3
    and DATEDIFF(date_format(expires_time, '%Y-%c-%d' ),date_format(NOW(), '%Y-%c-%d' ))=3
  </select>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:00:12 CST 2020.
    -->
    insert into ${issSchema}.mmp_user_card_info (user_card_no, user_id, card_group,
      card_name, card_status, agency_id, 
      card_id, start_time, expires_time, 
      total_order, total_discount_amount, active_flag, status,
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name
      )
    values (#{userCardNo,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{cardGroup,jdbcType=INTEGER}, 
      #{cardName,jdbcType=VARCHAR}, #{cardStatus,jdbcType=INTEGER}, #{agencyId,jdbcType=VARCHAR}, 
      #{cardId,jdbcType=BIGINT}, #{startTime,jdbcType=TIMESTAMP}, #{expiresTime,jdbcType=TIMESTAMP}, 
      #{totalOrder,jdbcType=DECIMAL}, #{totalDiscountAmount,jdbcType=DECIMAL}, #{activeFlag}, #{status,jdbcType=INTEGER},
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="userCardNo" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:00:12 CST 2020.
    -->
    insert into ${issSchema}.mmp_user_card_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="userCardNo != null" >
        user_card_no,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="cardGroup != null" >
        card_group,
      </if>
      <if test="cardName != null" >
        card_name,
      </if>
      <if test="cardStatus != null" >
        card_status,
      </if>
      <if test="agencyId != null" >
        agency_id,
      </if>
      <if test="cardId != null" >
        card_id,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="expiresTime != null" >
        expires_time,
      </if>
      <if test="totalOrder != null" >
        total_order,
      </if>
      <if test="totalDiscountAmount != null" >
        total_discount_amount,
      </if>
      <if test="activeFlag != null" >
        active_flag,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="userCardNo != null" >
        #{userCardNo,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="cardGroup != null" >
        #{cardGroup,jdbcType=INTEGER},
      </if>
      <if test="cardName != null" >
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="cardStatus != null" >
        #{cardStatus,jdbcType=INTEGER},
      </if>
      <if test="agencyId != null" >
        #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="cardId != null" >
        #{cardId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expiresTime != null" >
        #{expiresTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalOrder != null" >
        #{totalOrder,jdbcType=DECIMAL},
      </if>
      <if test="totalDiscountAmount != null" >
        #{totalDiscountAmount,jdbcType=DECIMAL},
      </if>
      <if test="activeFlag != null" >
        #{activeFlag,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:00:12 CST 2020.
    -->
    update ${issSchema}.mmp_user_card_info
    <set >
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="cardGroup != null" >
        card_group = #{cardGroup,jdbcType=INTEGER},
      </if>
      <if test="cardName != null" >
        card_name = #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="cardStatus != null" >
        card_status = #{cardStatus,jdbcType=INTEGER},
      </if>
      <if test="agencyId != null" >
        agency_id = #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="cardId != null" >
        card_id = #{cardId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expiresTime != null" >
        expires_time = #{expiresTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalOrder != null" >
        total_order = #{totalOrder,jdbcType=DECIMAL},
      </if>
      <if test="totalDiscountAmount != null" >
        total_discount_amount = #{totalDiscountAmount,jdbcType=DECIMAL},
      </if>
      <if test="activeFlag != null" >
        active_flag = #{activeFlag,jdbcType=DECIMAL},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where user_card_no = #{userCardNo,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:00:12 CST 2020.
    -->
    update ${issSchema}.mmp_user_card_info
    set user_id = #{userId,jdbcType=BIGINT},
      card_group = #{cardGroup,jdbcType=INTEGER},
      card_name = #{cardName,jdbcType=VARCHAR},
      card_status = #{cardStatus,jdbcType=INTEGER},
      agency_id = #{agencyId,jdbcType=VARCHAR},
      card_id = #{cardId,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      expires_time = #{expiresTime,jdbcType=TIMESTAMP},
      total_order = #{totalOrder,jdbcType=DECIMAL},
      total_discount_amount = #{totalDiscountAmount,jdbcType=DECIMAL},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where user_card_no = #{userCardNo,jdbcType=BIGINT}
  </update>
  <select id="queryEffectiveUserVipCardById" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_card_info
    where card_status = 1
    and user_id = #{userId} and status = 0
  </select>

  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="userCardNo">
    insert into ${issSchema}.mmp_user_card_info (user_id, card_group,
      card_name, card_status, agency_id,
      card_id, start_time, expires_time,
      create_time, create_oper_id, create_oper_name
    )
    values
    <foreach collection="list" item="item" index="index" separator=",">
     (#{item.userId,jdbcType=BIGINT}, #{item.cardGroup,jdbcType=INTEGER},
      #{item.cardName,jdbcType=VARCHAR}, #{item.cardStatus,jdbcType=INTEGER}, #{item.agencyId,jdbcType=VARCHAR},
      #{item.cardId,jdbcType=BIGINT}, #{item.startTime,jdbcType=TIMESTAMP}, #{item.expiresTime,jdbcType=TIMESTAMP},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.createOperId,jdbcType=BIGINT}, #{item.createOperName,jdbcType=VARCHAR}
    )
    </foreach>
  </insert>


  <update id="batchDisable">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:00:12 CST 2020.
    -->
    update ${issSchema}.mmp_user_card_info
    set
    status = 1,
    update_time = now(),
    update_oper_id = #{updateOperId,jdbcType=BIGINT},
    update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where status = 0 and card_group in (1, 2)
    <if test="agencyId!=null and agencyId!=''">
      and agency_id = #{agencyId,jdbcType=VARCHAR}
    </if>
    <if test="userIds!=null and userIds.size > 0">
      and user_id in
      <foreach collection="userIds" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </update>

  <select id="queryUserCorporateCards" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_card_info
    where agency_id = #{agencyId,jdbcType=VARCHAR} and status = 0
    and card_group = 1 and card_status = 1
    <if test="userIds!=null and userIds.size > 0">
      and user_id in
      <foreach collection="userIds" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

  <resultMap id="fullResultMap" type="com.extracme.evcard.rpc.vipcard.model.MemberCardListInfo" extends="BaseResultMap">
    <result column="card_type" property="cardType" jdbcType="INTEGER" />
    <result column="purchase_type" property="purchaseType" jdbcType="INTEGER" />
    <result column="discount" property="discount" jdbcType="INTEGER" />
    <result column="max_value" property="maxValue" jdbcType="DECIMAL" />
    <result column="duration_limit" property="durationLimit" jdbcType="INTEGER" />
    <result column="city_limit" property="cityLimit" jdbcType="VARCHAR" />
    <result column="vehicle_model_limit" property="vehicleModelLimit" jdbcType="VARCHAR" />
    <result column="goods_model_id" property="goodsModelId" jdbcType="VARCHAR" />
    <result column="store_ids" property="storeIds" jdbcType="VARCHAR" />
    <result column="rent_method" property="rentMethod" jdbcType="VARCHAR" />
    <result column="rent_method_group" property="rentMethodGroup" jdbcType="VARCHAR" />
  </resultMap>


  <select id="selectUserCardPage" resultMap="fullResultMap"
          parameterType="com.extracme.evcard.rpc.vipcard.dto.MemberCardQueryDto" >
    select
    uc.user_card_no, uc.user_id, uc.card_group, uc.card_name,
    uc.card_status,
    uc.start_time, uc.expires_time,
    uc.agency_id, uc.card_id,
    uc.total_order, uc.total_discount_amount,
    uc.active_flag,
    uc.create_time, uc.create_oper_id,
    uc.create_oper_name,
    uc.update_time,
    uc.update_oper_id,
    uc.update_oper_name,
    c.card_type, c.purchase_type,
    c.discount, c.max_value, c.duration_limit, c.city_limit,
    c.vehicle_model_limit, c.store_ids, c.rent_method
    from ${issSchema}.mmp_user_card_info uc
    LEFT JOIN ${issSchema}.mmp_card_def c ON c.card_id = uc.card_id
    where 1 = 1 and uc.status = 0
    and uc.user_id = #{userId}
    <if test="condition.cardId != null">
      and uc.card_id = #{condition.cardId}
    </if>
    <if test="condition.cardName != null and condition.cardName != '' ">
      and uc.card_name like concat('%',#{condition.cardName},'%')
    </if>
    <if test="condition.cardGroup != null">
      and uc.card_group = #{condition.cardGroup}
    </if>
    <if test="condition.cardType != null">
      and c.card_type = #{condition.cardType}
    </if>
    <if test="condition.purchaseType != null">
      and c.purchase_type = #{condition.purchaseType}
    </if>
<!--    <if test="condition.userCardStatus != null">-->
<!--      and uc.card_status = #{condition.userCardStatus}-->
<!--    </if>-->
    <if test="condition.userCardStatus != null">
      <!-- 非已过期 -->
      <if test=" condition.userCardStatus!=3 "> and uc.card_status = #{condition.userCardStatus}  AND uc.expires_time &gt;= now() </if>
      <!-- 已过期 -->
      <if test=" condition.userCardStatus==3 "> and uc.card_status in (1,3) AND uc.expires_time &lt; now() </if>
    </if>

    <if test="condition.startDate != null and condition.startDate != '' ">
      and uc.start_time &gt;= #{condition.startDate}
    </if>
    <if test="condition.endDate != null and condition.endDate != '' ">
      and (uc.expires_time is null or uc.expires_time &lt;= #{condition.endDate})
    </if>
    order by uc.expires_time desc
  </select>


  <update id="cancelUserCard">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:00:12 CST 2020.
    -->
    update ${issSchema}.mmp_user_card_info
    set
    card_status = 5,
    active_flag = 0,
    update_time = now(),
    update_oper_id = #{updateOperId,jdbcType=BIGINT},
    update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where status = 0 and card_status in (0, 1)
    <if test="userId!=null">
      and user_id = #{userId,jdbcType=BIGINT}
    </if>
    <if test="userCardNo!=null">
      and user_card_no = #{userCardNo,jdbcType=BIGINT}
    </if>
    <if test="cardId!=null">
      and card_id = #{cardId,jdbcType=BIGINT}
    </if>
  </update>


  <update id="updateUseCounts" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:00:12 CST 2020.
    -->
    update ${issSchema}.mmp_user_card_info
    <set >
        total_order = total_order + 1,
      <if test="totalDiscountAmount != null" >
        total_discount_amount = #{totalDiscountAmount,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where user_card_no = #{userCardNo,jdbcType=BIGINT}
  </update>


  <update id="expireUserCards" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardInfo" >
      <!--
        WARNING - @mbggenerated
        This element is automatically generated by MyBatis Generator, do not modify.
        This element was generated on Thu Dec 24 14:00:12 CST 2020.
      -->
      update ${issSchema}.mmp_user_card_info
      set card_status = 3
      where card_status = 1 and status = 0 and card_group = 3
      and (expires_time is null or expires_time &lt; now())
  </update>

  <select id="selectOneUserActiveCardByCardId"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_card_info
    where user_id = #{id} and card_id = #{cardId}
    and card_status in (0,1) and status = 0
    and (expires_time is null or expires_time &lt;= now())
    and start_time &lt;= now()
    limit 1
  </select>

</mapper>