package com.extracme.evcard.rpc.vipcard.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SuixiangCardCdkExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public SuixiangCardCdkExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdIsNull() {
            addCriterion("card_cdk_config_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdIsNotNull() {
            addCriterion("card_cdk_config_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdEqualTo(Long value) {
            addCriterion("card_cdk_config_detail_id =", value, "cardCdkConfigDetailId");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdNotEqualTo(Long value) {
            addCriterion("card_cdk_config_detail_id <>", value, "cardCdkConfigDetailId");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdGreaterThan(Long value) {
            addCriterion("card_cdk_config_detail_id >", value, "cardCdkConfigDetailId");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_cdk_config_detail_id >=", value, "cardCdkConfigDetailId");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdLessThan(Long value) {
            addCriterion("card_cdk_config_detail_id <", value, "cardCdkConfigDetailId");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("card_cdk_config_detail_id <=", value, "cardCdkConfigDetailId");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdIn(List<Long> values) {
            addCriterion("card_cdk_config_detail_id in", values, "cardCdkConfigDetailId");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdNotIn(List<Long> values) {
            addCriterion("card_cdk_config_detail_id not in", values, "cardCdkConfigDetailId");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdBetween(Long value1, Long value2) {
            addCriterion("card_cdk_config_detail_id between", value1, value2, "cardCdkConfigDetailId");
            return (Criteria) this;
        }

        public Criteria andCardCdkConfigDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("card_cdk_config_detail_id not between", value1, value2, "cardCdkConfigDetailId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdIsNull() {
            addCriterion("card_base_id is null");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdIsNotNull() {
            addCriterion("card_base_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdEqualTo(Long value) {
            addCriterion("card_base_id =", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdNotEqualTo(Long value) {
            addCriterion("card_base_id <>", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdGreaterThan(Long value) {
            addCriterion("card_base_id >", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_base_id >=", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdLessThan(Long value) {
            addCriterion("card_base_id <", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("card_base_id <=", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdIn(List<Long> values) {
            addCriterion("card_base_id in", values, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdNotIn(List<Long> values) {
            addCriterion("card_base_id not in", values, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdBetween(Long value1, Long value2) {
            addCriterion("card_base_id between", value1, value2, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("card_base_id not between", value1, value2, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdIsNull() {
            addCriterion("card_rent_id is null");
            return (Criteria) this;
        }

        public Criteria andCardRentIdIsNotNull() {
            addCriterion("card_rent_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardRentIdEqualTo(Long value) {
            addCriterion("card_rent_id =", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdNotEqualTo(Long value) {
            addCriterion("card_rent_id <>", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdGreaterThan(Long value) {
            addCriterion("card_rent_id >", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_rent_id >=", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdLessThan(Long value) {
            addCriterion("card_rent_id <", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdLessThanOrEqualTo(Long value) {
            addCriterion("card_rent_id <=", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdIn(List<Long> values) {
            addCriterion("card_rent_id in", values, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdNotIn(List<Long> values) {
            addCriterion("card_rent_id not in", values, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdBetween(Long value1, Long value2) {
            addCriterion("card_rent_id between", value1, value2, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdNotBetween(Long value1, Long value2) {
            addCriterion("card_rent_id not between", value1, value2, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIsNull() {
            addCriterion("card_price_id is null");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIsNotNull() {
            addCriterion("card_price_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdEqualTo(Long value) {
            addCriterion("card_price_id =", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotEqualTo(Long value) {
            addCriterion("card_price_id <>", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdGreaterThan(Long value) {
            addCriterion("card_price_id >", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_price_id >=", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdLessThan(Long value) {
            addCriterion("card_price_id <", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdLessThanOrEqualTo(Long value) {
            addCriterion("card_price_id <=", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIn(List<Long> values) {
            addCriterion("card_price_id in", values, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotIn(List<Long> values) {
            addCriterion("card_price_id not in", values, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdBetween(Long value1, Long value2) {
            addCriterion("card_price_id between", value1, value2, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotBetween(Long value1, Long value2) {
            addCriterion("card_price_id not between", value1, value2, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCdkeyIsNull() {
            addCriterion("cdkey is null");
            return (Criteria) this;
        }

        public Criteria andCdkeyIsNotNull() {
            addCriterion("cdkey is not null");
            return (Criteria) this;
        }

        public Criteria andCdkeyEqualTo(String value) {
            addCriterion("cdkey =", value, "cdkey");
            return (Criteria) this;
        }

        public Criteria andCdkeyNotEqualTo(String value) {
            addCriterion("cdkey <>", value, "cdkey");
            return (Criteria) this;
        }

        public Criteria andCdkeyGreaterThan(String value) {
            addCriterion("cdkey >", value, "cdkey");
            return (Criteria) this;
        }

        public Criteria andCdkeyGreaterThanOrEqualTo(String value) {
            addCriterion("cdkey >=", value, "cdkey");
            return (Criteria) this;
        }

        public Criteria andCdkeyLessThan(String value) {
            addCriterion("cdkey <", value, "cdkey");
            return (Criteria) this;
        }

        public Criteria andCdkeyLessThanOrEqualTo(String value) {
            addCriterion("cdkey <=", value, "cdkey");
            return (Criteria) this;
        }

        public Criteria andCdkeyLike(String value) {
            addCriterion("cdkey like", value, "cdkey");
            return (Criteria) this;
        }

        public Criteria andCdkeyNotLike(String value) {
            addCriterion("cdkey not like", value, "cdkey");
            return (Criteria) this;
        }

        public Criteria andCdkeyIn(List<String> values) {
            addCriterion("cdkey in", values, "cdkey");
            return (Criteria) this;
        }

        public Criteria andCdkeyNotIn(List<String> values) {
            addCriterion("cdkey not in", values, "cdkey");
            return (Criteria) this;
        }

        public Criteria andCdkeyBetween(String value1, String value2) {
            addCriterion("cdkey between", value1, value2, "cdkey");
            return (Criteria) this;
        }

        public Criteria andCdkeyNotBetween(String value1, String value2) {
            addCriterion("cdkey not between", value1, value2, "cdkey");
            return (Criteria) this;
        }

        public Criteria andIsActivatedIsNull() {
            addCriterion("is_activated is null");
            return (Criteria) this;
        }

        public Criteria andIsActivatedIsNotNull() {
            addCriterion("is_activated is not null");
            return (Criteria) this;
        }

        public Criteria andIsActivatedEqualTo(Integer value) {
            addCriterion("is_activated =", value, "isActivated");
            return (Criteria) this;
        }

        public Criteria andIsActivatedNotEqualTo(Integer value) {
            addCriterion("is_activated <>", value, "isActivated");
            return (Criteria) this;
        }

        public Criteria andIsActivatedGreaterThan(Integer value) {
            addCriterion("is_activated >", value, "isActivated");
            return (Criteria) this;
        }

        public Criteria andIsActivatedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_activated >=", value, "isActivated");
            return (Criteria) this;
        }

        public Criteria andIsActivatedLessThan(Integer value) {
            addCriterion("is_activated <", value, "isActivated");
            return (Criteria) this;
        }

        public Criteria andIsActivatedLessThanOrEqualTo(Integer value) {
            addCriterion("is_activated <=", value, "isActivated");
            return (Criteria) this;
        }

        public Criteria andIsActivatedIn(List<Integer> values) {
            addCriterion("is_activated in", values, "isActivated");
            return (Criteria) this;
        }

        public Criteria andIsActivatedNotIn(List<Integer> values) {
            addCriterion("is_activated not in", values, "isActivated");
            return (Criteria) this;
        }

        public Criteria andIsActivatedBetween(Integer value1, Integer value2) {
            addCriterion("is_activated between", value1, value2, "isActivated");
            return (Criteria) this;
        }

        public Criteria andIsActivatedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_activated not between", value1, value2, "isActivated");
            return (Criteria) this;
        }

        public Criteria andActivatedMidIsNull() {
            addCriterion("activated_mid is null");
            return (Criteria) this;
        }

        public Criteria andActivatedMidIsNotNull() {
            addCriterion("activated_mid is not null");
            return (Criteria) this;
        }

        public Criteria andActivatedMidEqualTo(String value) {
            addCriterion("activated_mid =", value, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedMidNotEqualTo(String value) {
            addCriterion("activated_mid <>", value, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedMidGreaterThan(String value) {
            addCriterion("activated_mid >", value, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedMidGreaterThanOrEqualTo(String value) {
            addCriterion("activated_mid >=", value, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedMidLessThan(String value) {
            addCriterion("activated_mid <", value, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedMidLessThanOrEqualTo(String value) {
            addCriterion("activated_mid <=", value, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedMidLike(String value) {
            addCriterion("activated_mid like", value, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedMidNotLike(String value) {
            addCriterion("activated_mid not like", value, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedMidIn(List<String> values) {
            addCriterion("activated_mid in", values, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedMidNotIn(List<String> values) {
            addCriterion("activated_mid not in", values, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedMidBetween(String value1, String value2) {
            addCriterion("activated_mid between", value1, value2, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedMidNotBetween(String value1, String value2) {
            addCriterion("activated_mid not between", value1, value2, "activatedMid");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameIsNull() {
            addCriterion("activated_user_name is null");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameIsNotNull() {
            addCriterion("activated_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameEqualTo(String value) {
            addCriterion("activated_user_name =", value, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameNotEqualTo(String value) {
            addCriterion("activated_user_name <>", value, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameGreaterThan(String value) {
            addCriterion("activated_user_name >", value, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("activated_user_name >=", value, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameLessThan(String value) {
            addCriterion("activated_user_name <", value, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameLessThanOrEqualTo(String value) {
            addCriterion("activated_user_name <=", value, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameLike(String value) {
            addCriterion("activated_user_name like", value, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameNotLike(String value) {
            addCriterion("activated_user_name not like", value, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameIn(List<String> values) {
            addCriterion("activated_user_name in", values, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameNotIn(List<String> values) {
            addCriterion("activated_user_name not in", values, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameBetween(String value1, String value2) {
            addCriterion("activated_user_name between", value1, value2, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserNameNotBetween(String value1, String value2) {
            addCriterion("activated_user_name not between", value1, value2, "activatedUserName");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileIsNull() {
            addCriterion("activated_user_mobile is null");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileIsNotNull() {
            addCriterion("activated_user_mobile is not null");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileEqualTo(String value) {
            addCriterion("activated_user_mobile =", value, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileNotEqualTo(String value) {
            addCriterion("activated_user_mobile <>", value, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileGreaterThan(String value) {
            addCriterion("activated_user_mobile >", value, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileGreaterThanOrEqualTo(String value) {
            addCriterion("activated_user_mobile >=", value, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileLessThan(String value) {
            addCriterion("activated_user_mobile <", value, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileLessThanOrEqualTo(String value) {
            addCriterion("activated_user_mobile <=", value, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileLike(String value) {
            addCriterion("activated_user_mobile like", value, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileNotLike(String value) {
            addCriterion("activated_user_mobile not like", value, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileIn(List<String> values) {
            addCriterion("activated_user_mobile in", values, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileNotIn(List<String> values) {
            addCriterion("activated_user_mobile not in", values, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileBetween(String value1, String value2) {
            addCriterion("activated_user_mobile between", value1, value2, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedUserMobileNotBetween(String value1, String value2) {
            addCriterion("activated_user_mobile not between", value1, value2, "activatedUserMobile");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeIsNull() {
            addCriterion("activated_time is null");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeIsNotNull() {
            addCriterion("activated_time is not null");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeEqualTo(Date value) {
            addCriterion("activated_time =", value, "activatedTime");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeNotEqualTo(Date value) {
            addCriterion("activated_time <>", value, "activatedTime");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeGreaterThan(Date value) {
            addCriterion("activated_time >", value, "activatedTime");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("activated_time >=", value, "activatedTime");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeLessThan(Date value) {
            addCriterion("activated_time <", value, "activatedTime");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeLessThanOrEqualTo(Date value) {
            addCriterion("activated_time <=", value, "activatedTime");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeIn(List<Date> values) {
            addCriterion("activated_time in", values, "activatedTime");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeNotIn(List<Date> values) {
            addCriterion("activated_time not in", values, "activatedTime");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeBetween(Date value1, Date value2) {
            addCriterion("activated_time between", value1, value2, "activatedTime");
            return (Criteria) this;
        }

        public Criteria andActivatedTimeNotBetween(Date value1, Date value2) {
            addCriterion("activated_time not between", value1, value2, "activatedTime");
            return (Criteria) this;
        }

        public Criteria andCardUseIdIsNull() {
            addCriterion("card_use_id is null");
            return (Criteria) this;
        }

        public Criteria andCardUseIdIsNotNull() {
            addCriterion("card_use_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardUseIdEqualTo(Long value) {
            addCriterion("card_use_id =", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdNotEqualTo(Long value) {
            addCriterion("card_use_id <>", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdGreaterThan(Long value) {
            addCriterion("card_use_id >", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_use_id >=", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdLessThan(Long value) {
            addCriterion("card_use_id <", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdLessThanOrEqualTo(Long value) {
            addCriterion("card_use_id <=", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdIn(List<Long> values) {
            addCriterion("card_use_id in", values, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdNotIn(List<Long> values) {
            addCriterion("card_use_id not in", values, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdBetween(Long value1, Long value2) {
            addCriterion("card_use_id between", value1, value2, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdNotBetween(Long value1, Long value2) {
            addCriterion("card_use_id not between", value1, value2, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlIsNull() {
            addCriterion("wechat_cdk_qr_url is null");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlIsNotNull() {
            addCriterion("wechat_cdk_qr_url is not null");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlEqualTo(String value) {
            addCriterion("wechat_cdk_qr_url =", value, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlNotEqualTo(String value) {
            addCriterion("wechat_cdk_qr_url <>", value, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlGreaterThan(String value) {
            addCriterion("wechat_cdk_qr_url >", value, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlGreaterThanOrEqualTo(String value) {
            addCriterion("wechat_cdk_qr_url >=", value, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlLessThan(String value) {
            addCriterion("wechat_cdk_qr_url <", value, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlLessThanOrEqualTo(String value) {
            addCriterion("wechat_cdk_qr_url <=", value, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlLike(String value) {
            addCriterion("wechat_cdk_qr_url like", value, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlNotLike(String value) {
            addCriterion("wechat_cdk_qr_url not like", value, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlIn(List<String> values) {
            addCriterion("wechat_cdk_qr_url in", values, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlNotIn(List<String> values) {
            addCriterion("wechat_cdk_qr_url not in", values, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlBetween(String value1, String value2) {
            addCriterion("wechat_cdk_qr_url between", value1, value2, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andWechatCdkQrUrlNotBetween(String value1, String value2) {
            addCriterion("wechat_cdk_qr_url not between", value1, value2, "wechatCdkQrUrl");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNull() {
            addCriterion("create_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNotNull() {
            addCriterion("create_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdEqualTo(Long value) {
            addCriterion("create_oper_id =", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotEqualTo(Long value) {
            addCriterion("create_oper_id <>", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThan(Long value) {
            addCriterion("create_oper_id >", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_oper_id >=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThan(Long value) {
            addCriterion("create_oper_id <", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("create_oper_id <=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIn(List<Long> values) {
            addCriterion("create_oper_id in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotIn(List<Long> values) {
            addCriterion("create_oper_id not in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdBetween(Long value1, Long value2) {
            addCriterion("create_oper_id between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("create_oper_id not between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNull() {
            addCriterion("create_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNotNull() {
            addCriterion("create_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameEqualTo(String value) {
            addCriterion("create_oper_name =", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotEqualTo(String value) {
            addCriterion("create_oper_name <>", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThan(String value) {
            addCriterion("create_oper_name >", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_name >=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThan(String value) {
            addCriterion("create_oper_name <", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThanOrEqualTo(String value) {
            addCriterion("create_oper_name <=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLike(String value) {
            addCriterion("create_oper_name like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotLike(String value) {
            addCriterion("create_oper_name not like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIn(List<String> values) {
            addCriterion("create_oper_name in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotIn(List<String> values) {
            addCriterion("create_oper_name not in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameBetween(String value1, String value2) {
            addCriterion("create_oper_name between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotBetween(String value1, String value2) {
            addCriterion("create_oper_name not between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNull() {
            addCriterion("update_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNotNull() {
            addCriterion("update_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdEqualTo(Long value) {
            addCriterion("update_oper_id =", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotEqualTo(Long value) {
            addCriterion("update_oper_id <>", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThan(Long value) {
            addCriterion("update_oper_id >", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_oper_id >=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThan(Long value) {
            addCriterion("update_oper_id <", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("update_oper_id <=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIn(List<Long> values) {
            addCriterion("update_oper_id in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotIn(List<Long> values) {
            addCriterion("update_oper_id not in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdBetween(Long value1, Long value2) {
            addCriterion("update_oper_id between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("update_oper_id not between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNull() {
            addCriterion("update_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNotNull() {
            addCriterion("update_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameEqualTo(String value) {
            addCriterion("update_oper_name =", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotEqualTo(String value) {
            addCriterion("update_oper_name <>", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThan(String value) {
            addCriterion("update_oper_name >", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_oper_name >=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThan(String value) {
            addCriterion("update_oper_name <", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThanOrEqualTo(String value) {
            addCriterion("update_oper_name <=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLike(String value) {
            addCriterion("update_oper_name like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotLike(String value) {
            addCriterion("update_oper_name not like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIn(List<String> values) {
            addCriterion("update_oper_name in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotIn(List<String> values) {
            addCriterion("update_oper_name not in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameBetween(String value1, String value2) {
            addCriterion("update_oper_name between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotBetween(String value1, String value2) {
            addCriterion("update_oper_name not between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated do_not_delete_during_merge Mon Nov 18 10:20:00 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}