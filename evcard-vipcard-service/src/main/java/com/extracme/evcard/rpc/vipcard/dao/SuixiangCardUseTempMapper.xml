<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.SuixiangCardUseTempMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseTemp">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_base_id" jdbcType="BIGINT" property="cardBaseId" />
    <result column="card_price_id" jdbcType="BIGINT" property="cardPriceId" />
    <result column="card_type" jdbcType="INTEGER" property="cardType" />
    <result column="card_name" jdbcType="VARCHAR" property="cardName" />
    <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="cdk_id" jdbcType="BIGINT" property="cdkId" />
    <result column="cdkey" jdbcType="VARCHAR" property="cdkey" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="expires_time" jdbcType="TIMESTAMP" property="expiresTime" />
    <result column="is_offer" jdbcType="INTEGER" property="isOffer" />
    <result column="issue_type" jdbcType="INTEGER" property="issueType" />
    <result column="obtain_type" jdbcType="INTEGER" property="obtainType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    id, card_base_id, card_price_id, card_type, card_name, mobile_phone, quantity, cdk_id, 
    cdkey, start_time, expires_time, is_offer, issue_type, obtain_type, create_time, 
    create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseTempExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from suixiang_card_use_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from suixiang_card_use_temp
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    delete from suixiang_card_use_temp
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseTemp">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    insert into suixiang_card_use_temp (id, card_base_id, card_price_id, 
      card_type, card_name, mobile_phone, 
      quantity, cdk_id, cdkey, 
      start_time, expires_time, is_offer, 
      issue_type, obtain_type, create_time, 
      create_oper_id, create_oper_name, update_time, 
      update_oper_id, update_oper_name, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{cardBaseId,jdbcType=BIGINT}, #{cardPriceId,jdbcType=BIGINT}, 
      #{cardType,jdbcType=INTEGER}, #{cardName,jdbcType=VARCHAR}, #{mobilePhone,jdbcType=VARCHAR}, 
      #{quantity,jdbcType=INTEGER}, #{cdkId,jdbcType=BIGINT}, #{cdkey,jdbcType=VARCHAR}, 
      #{startTime,jdbcType=TIMESTAMP}, #{expiresTime,jdbcType=TIMESTAMP}, #{isOffer,jdbcType=INTEGER}, 
      #{issueType,jdbcType=INTEGER}, #{obtainType,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseTemp">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    insert into suixiang_card_use_temp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cardBaseId != null">
        card_base_id,
      </if>
      <if test="cardPriceId != null">
        card_price_id,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="cardName != null">
        card_name,
      </if>
      <if test="mobilePhone != null">
        mobile_phone,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="cdkId != null">
        cdk_id,
      </if>
      <if test="cdkey != null">
        cdkey,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="expiresTime != null">
        expires_time,
      </if>
      <if test="isOffer != null">
        is_offer,
      </if>
      <if test="issueType != null">
        issue_type,
      </if>
      <if test="obtainType != null">
        obtain_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cardBaseId != null">
        #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null">
        #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=INTEGER},
      </if>
      <if test="cardName != null">
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null">
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="cdkId != null">
        #{cdkId,jdbcType=BIGINT},
      </if>
      <if test="cdkey != null">
        #{cdkey,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expiresTime != null">
        #{expiresTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isOffer != null">
        #{isOffer,jdbcType=INTEGER},
      </if>
      <if test="issueType != null">
        #{issueType,jdbcType=INTEGER},
      </if>
      <if test="obtainType != null">
        #{obtainType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseTempExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    select count(*) from suixiang_card_use_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    update suixiang_card_use_temp
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardBaseId != null">
        card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.cardPriceId != null">
        card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="record.cardType != null">
        card_type = #{record.cardType,jdbcType=INTEGER},
      </if>
      <if test="record.cardName != null">
        card_name = #{record.cardName,jdbcType=VARCHAR},
      </if>
      <if test="record.mobilePhone != null">
        mobile_phone = #{record.mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.cdkId != null">
        cdk_id = #{record.cdkId,jdbcType=BIGINT},
      </if>
      <if test="record.cdkey != null">
        cdkey = #{record.cdkey,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expiresTime != null">
        expires_time = #{record.expiresTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isOffer != null">
        is_offer = #{record.isOffer,jdbcType=INTEGER},
      </if>
      <if test="record.issueType != null">
        issue_type = #{record.issueType,jdbcType=INTEGER},
      </if>
      <if test="record.obtainType != null">
        obtain_type = #{record.obtainType,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null">
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null">
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null">
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null">
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    update suixiang_card_use_temp
    set id = #{record.id,jdbcType=BIGINT},
      card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      card_type = #{record.cardType,jdbcType=INTEGER},
      card_name = #{record.cardName,jdbcType=VARCHAR},
      mobile_phone = #{record.mobilePhone,jdbcType=VARCHAR},
      quantity = #{record.quantity,jdbcType=INTEGER},
      cdk_id = #{record.cdkId,jdbcType=BIGINT},
      cdkey = #{record.cdkey,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      expires_time = #{record.expiresTime,jdbcType=TIMESTAMP},
      is_offer = #{record.isOffer,jdbcType=INTEGER},
      issue_type = #{record.issueType,jdbcType=INTEGER},
      obtain_type = #{record.obtainType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseTemp">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    update suixiang_card_use_temp
    <set>
      <if test="cardBaseId != null">
        card_base_id = #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null">
        card_price_id = #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="cardType != null">
        card_type = #{cardType,jdbcType=INTEGER},
      </if>
      <if test="cardName != null">
        card_name = #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null">
        mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="cdkId != null">
        cdk_id = #{cdkId,jdbcType=BIGINT},
      </if>
      <if test="cdkey != null">
        cdkey = #{cdkey,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expiresTime != null">
        expires_time = #{expiresTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isOffer != null">
        is_offer = #{isOffer,jdbcType=INTEGER},
      </if>
      <if test="issueType != null">
        issue_type = #{issueType,jdbcType=INTEGER},
      </if>
      <if test="obtainType != null">
        obtain_type = #{obtainType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseTemp">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 10:32:49 CST 2024.
    -->
    update suixiang_card_use_temp
    set card_base_id = #{cardBaseId,jdbcType=BIGINT},
      card_price_id = #{cardPriceId,jdbcType=BIGINT},
      card_type = #{cardType,jdbcType=INTEGER},
      card_name = #{cardName,jdbcType=VARCHAR},
      mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=INTEGER},
      cdk_id = #{cdkId,jdbcType=BIGINT},
      cdkey = #{cdkey,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      expires_time = #{expiresTime,jdbcType=TIMESTAMP},
      is_offer = #{isOffer,jdbcType=INTEGER},
      issue_type = #{issueType,jdbcType=INTEGER},
      obtain_type = #{obtainType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>