package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.dto.MemberCardQueryDto;
import com.extracme.evcard.rpc.vipcard.dto.SuiXiangCardMergeDto;
import com.extracme.evcard.rpc.vipcard.dto.SuixiangCardUseCountDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuiXiangCardUseQueryDto;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUse;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SuixiangCardUseMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    int countByExample(SuixiangCardUseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    int insert(SuixiangCardUse record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    int insertSelective(SuixiangCardUse record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    List<SuixiangCardUse> selectByExample(SuixiangCardUseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    SuixiangCardUse selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    int updateByExampleSelective(@Param("record") SuixiangCardUse record, @Param("example") SuixiangCardUseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    int updateByExample(@Param("record") SuixiangCardUse record, @Param("example") SuixiangCardUseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    int updateByPrimaryKeySelective(SuixiangCardUse record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    int updateByPrimaryKey(SuixiangCardUse record);


    List<SuixiangCardUse> selectListByUserId(@Param("userId")Integer userId,@Param("cardStatus")Integer cardStatus);

    List<SuixiangCardUse> selectListByCondition(@Param("userId")Long userId,@Param("cardStatus")Integer cardStatus,
                                                @Param("cityId")Long cityId, @Param("carModelId")int carModelId);

    /**
     * 查询用户的随享卡列表
     * @param userId
     * @param queryDto
     * @return
     */
    List<SuixiangCardUse> selectCardListByUserId(@Param("userId") Long userId, @Param("condition") SuiXiangCardUseQueryDto queryDto);

    /**
     * 过期的随享卡更新状态
     * @return
     */
    int expireSuiXiangCardUse();

    /**
     * 会员管理系统 -> 个人会员 -> 会员信息 -> 用户权益（tab） 查询用户的随享卡列表
     * @param userId
     * @param queryDto
     * @return
     */
    List<SuixiangCardUse> selectCardListByUserIdForMmp(@Param("userId") Long userId, @Param("condition") MemberCardQueryDto queryDto);

    List<SuixiangCardUse> selectForPage(@Param("minId")Long minId);

    List<SuixiangCardUseCountDto> selectCountByUserId(@Param("userIdList") List<Long> userIdList);


    /**
     * 查询可合并的随享卡列表
     * @param userId
     * @param cardBaseId
     * @return
     */
    List<SuixiangCardUse> selectCanMergeCardList(@Param("userId") Long userId, @Param("cardBaseId") Long cardBaseId);


    /**
     * 合并随享卡
     * @param suiXiangCardMergeDto
     * @return
     */
    int mergeSuiXiangCardUse(SuiXiangCardMergeDto suiXiangCardMergeDto);

    /**
     * 根据cdkId查询随享卡
     * @param cdkId
     * @return
     */
    SuixiangCardUse selectByCdkId(@Param("cdkId") Long cdkId);
}