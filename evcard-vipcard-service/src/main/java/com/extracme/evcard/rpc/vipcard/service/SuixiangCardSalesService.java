package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dao.*;
import com.extracme.evcard.rpc.vipcard.dto.CardActivityQueryDto;
import com.extracme.evcard.rpc.vipcard.dto.GetAvailableCardByUseConditionInputDto;
import com.extracme.evcard.rpc.vipcard.dto.GetEffectiveActivityCardInput;
import com.extracme.evcard.rpc.vipcard.dto.ServiceFeeCfgDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;
import com.extracme.evcard.rpc.vipcard.enums.*;
import com.extracme.evcard.rpc.vipcard.manager.SuixiangCardBaseManager;
import com.extracme.evcard.rpc.vipcard.model.*;
import com.extracme.evcard.rpc.vipcard.rest.entity.GoodsVehicleModelInfo;
import com.extracme.evcard.rpc.vipcard.service.inner.CityServ;
import com.extracme.evcard.rpc.vipcard.service.inner.MemberCardInnerService;
import com.extracme.evcard.rpc.vipcard.service.inner.OrgService;
import com.extracme.evcard.rpc.vipcard.service.inner.SuiXiangCardInnerService;
import com.extracme.evcard.rpc.vipcard.service.store.ConfigLoader;
import com.extracme.evcard.rpc.vipcard.service.store.GoodsModelService;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import com.extracme.evcard.rpc.vipcard.util.RestfulHttpClientUtils;
import com.google.common.collect.Lists;
import com.extracme.evcard.rpc.vipcard.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SuixiangCardSalesService implements ISuixiangCardSalesService {

    @Resource
    private SuixiangCardBaseMapper cardBaseMapper;

    @Resource
    private SuixiangCardRentDaysMapper cardRentDaysMapper;

    @Resource
    private SuixiangCardPriceMapper cardPriceMapper;

    @Resource
    private SuixiangCardUseMapper cardUseMapper;

    @Resource
    private ICardSalesActivityConfigService cardSalesActivityConfigService;

    @Resource
    private MmpCardSalesActivityMapper mmpCardSalesActivityMapper;

    @Resource
    private SuixiangCardBaseManager suixiangCardBaseManager;

    @Resource
    private GoodsModelService goodsModelService;

    @Resource
    private SuixiangCardPurchaseRecordMapper cardPurchaseRecordMapper;

    @Resource
    private SuixiangCardCdkMapper suixiangCardCdkMapper;

    @Value("${md.rest.api.baseUrl}")
    private String requestUrl;


    @Autowired
    private ConfigLoader configLoader;

    @Override
    public SuixiangCardInfoBo queryCardInfoById(Long cardPriceId) {
        SuixiangCardInfoBo result = new SuixiangCardInfoBo();
        SuixiangCardPrice cardPrice = cardPriceMapper.selectByPrimaryKey(cardPriceId);
        if (cardPrice != null) {
            result.setCardPriceDto(cardPrice.toDto());

            Long cardBaseId = cardPrice.getCardBaseId();
            Long cardRentId = cardPrice.getCardRentId();
            SuixiangCardBase cardBase = cardBaseMapper.selectByPrimaryKey(cardBaseId);
            if (cardBase != null) {
                result.setCardBaseDto(cardBase.toDto());
            }

            SuixiangCardRentDays cardRentDays = cardRentDaysMapper.selectByPrimaryKey(cardRentId);
            if (cardRentDays != null) {
                result.setCardRentDayDto(cardRentDays.toDto());
            }
        }
        return result;
    }

    @Override
    public SuixiangCardInfoBo queryCardInfoByUserCardId(Long userCardId) {
        SuixiangCardUse cardUse = cardUseMapper.selectByPrimaryKey(userCardId);
        if (cardUse != null) {
            Long cardPriceId = cardUse.getCardPriceId();

            SuixiangCardInfoBo cardInfo = queryCardInfoById(cardPriceId);
            if (cardInfo != null) {
                cardInfo.setCardUseDto(cardUse.toDto());
                return cardInfo;
            }
        }
        return null;
    }

    @Override
    public List<SuixiangCardInfoDto> getCanBuyCardList(GetCanBuyCardListInput input) {
        List<SuixiangCardInfoDto> suixiangCardInfoDtoList = new ArrayList<>();
        Integer cityId = input.getCityId();
        Integer carModelId = input.getCarModelId();
        if (cityId == null && carModelId == null) {
            return null;
        }
        List<UnavailableDate> holidayConfigurationList =new ArrayList<>();
        //查询节假日
        JSONObject req = new JSONObject();
        req.put("particularYear", "");
        JSONObject contractRes = RestfulHttpClientUtils.sendRestHttp(requestUrl + "mdadmin/store/inner/searchHolidayConfiguration", "POST", req.toJSONString());
        if (contractRes != null && contractRes.getString("code").equals("0")) {
            JSONObject data = contractRes.getJSONObject("data");
            JSONArray holidayConfiguration = data.getJSONArray("cfg");
            List<UnavailableDate> unavailableDateList = JSONObject.parseArray(holidayConfiguration.toJSONString(), UnavailableDate.class);
            if(CollectionUtils.isNotEmpty(unavailableDateList)){
                for(UnavailableDate unavailableDate : unavailableDateList){
                    UnavailableDate unavailableDate1 = new UnavailableDate();
                    unavailableDate1.setStartDate(unavailableDate.getStartDate().replaceAll("-",""));
                    unavailableDate1.setEndDate(unavailableDate.getEndDate().replaceAll("-",""));
                    holidayConfigurationList.add(unavailableDate1);
                }
            }
        }

        List<SuixiangCardInfoDto> list = cardPriceMapper.getListByCityIdAndCarModelId(cityId, carModelId);
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(suixiangCardInfoDto ->{
                List<UnavailableDate> holidayDateList = new ArrayList<>();
                if(StringUtils.isNotEmpty(suixiangCardInfoDto.getUnavailableDate())){
                    List<UnavailableDate> unavailableDateList = JSON.parseArray(suixiangCardInfoDto.getUnavailableDate(),UnavailableDate.class);
                    holidayDateList.addAll(unavailableDateList);
                }

                if(suixiangCardInfoDto.getHolidayAvailable()==2 && CollectionUtils.isNotEmpty(holidayConfigurationList)){
                    //节假日不可用
                    holidayDateList.addAll(holidayConfigurationList);
                }
                suixiangCardInfoDto.setUnavailableDateList(holidayDateList);
                suixiangCardInfoDtoList.add(suixiangCardInfoDto);
            });
        }
        return suixiangCardInfoDtoList;
    }

    @Autowired
    private MemberCardInnerService memberCardInnerService;
    @Resource
    private IMemberShipService memberShipService;
    @Resource
    private SuiXiangCardInnerService suiXiangCardInnerService;

    @Override
    public List<SuiXiangQueryAvailableCardInfoDto> getAvailableCardByUseCondition(GetAvailableCardByUseConditionInputDto inputDto) {
        List<SuiXiangQueryAvailableCardInfoDto> resultList = new ArrayList<>();
        /**
         * 预处理：  取车时间、用车时长、用车区域、车型、产品线
         */
        try {
            GetEffectiveActivityCardInput input = new GetEffectiveActivityCardInput();
            BeanUtils.copyProperties(inputDto,input);
            memberCardInnerService.preProcessOrderCondition(input);

            MembershipBasicInfo memberInfo = memberShipService.getUserBasicInfo(inputDto.getAuthId(), (short) 0);
            if (memberInfo == null) {
                log.error("getAvailableCardByUseCondition,未查找到对应用户inputDto[{}]", JSON.toJSONString(inputDto));
                return null;
            }

            Long userId = memberInfo.getPkId();
            Integer goodsModelId = input.getVehicleModelSeq();
            Long pickUpCity = input.getPickUpCity();

            List<SuixiangCardUseBo> list = getEffectiveCardInfoListByUserId(userId.intValue());
            if (CollectionUtils.isNotEmpty(list)) {
                for(SuixiangCardUseBo bo : list){
//                    SuixiangCardUseBo bo = list.get(0);
                    SuixiangCardUseDto cardUseDto = bo.getCardUseDto();
                    SuixiangCardInfoDto cardInfoDto = bo.getCardInfoDto();
                    String cityLimit = cardInfoDto.getCityId();
                    Long cardBaseId = cardInfoDto.getCardBaseId();
                    SuixiangCardBase base = cardBaseMapper.selectByPrimaryKey(cardBaseId);

                    List<UnavailableDate> unavailableDateAllList = new ArrayList<>();
                    List<UnavailableDate> unavailableDateList = new ArrayList<>();
                    boolean cityFlag = false;
                    boolean carFlag = false;
                    boolean orderFlag = true;
                    boolean modelFlag = true;
                    boolean unavailableDateFlag = true;//随享卡是否可用（在不可用日期内） false：不可用
                    try {
                        if (StringUtils.isNotBlank(cityLimit)) {
                            // 全部城市
                            if (cityLimit.contains("-1")) {
                                cityFlag = true;
                            }else{
                                List<Integer> cityIdList = ComUtils.turnToList(cityLimit, ",");
                                if (pickUpCity != null && (cityIdList.contains(pickUpCity.intValue())) ) {
                                    cityFlag = true;
                                }
                            }
                        }else{
                            cityFlag = true;
                        }

                        String carModelIds = cardInfoDto.getCarModelIds();
                        if (StringUtils.isNotBlank(carModelIds)) {
                            if (carModelIds.contains("-1")) {
                                carFlag = true;
                            }else{
                                if (goodsModelId != null) {
                                    List<Integer> carList = ComUtils.turnToList(carModelIds, ",");
                                    if (carList.contains(goodsModelId)) {
                                        carFlag = true;
                                    }
                                }
                            }
                        }else {
                            carFlag = true;
                        }

                        Integer initDays = cardUseDto.getInitDays();
                        Integer frozenDays = cardUseDto.getFrozenDays();

                        // app5.7 修改订单,恢复冻结天数
                        String oldOrderSeq = input.getOldOrderSeq();
                        Pair<Boolean, Integer> frozenPair = suiXiangCardInnerService.getFreezeDays(cardUseDto.getId(), oldOrderSeq);
                        if (frozenPair.getKey()) {
                            frozenDays = frozenDays - frozenPair.getValue();
                        }

                        Integer usedDays = cardUseDto.getUsedDays();
                        if (initDays == usedDays + frozenDays) {
                            orderFlag = false;
                        }

                        // 订单活动类型为2 代表随享卡不可用
                        if (inputDto.getActivityType() != null && inputDto.getActivityType() == 2) {
                            modelFlag = false;
                        }

                        //是否在不可用日期范围内
                        if(base!=null){
                            String unavailableDate = base.getUnavailableDate();
                            if(StringUtils.isNotEmpty(unavailableDate)){
                                unavailableDateList = JSON.parseArray(base.getUnavailableDate(),UnavailableDate.class);
                                unavailableDateAllList.addAll(unavailableDateList);
                            }
                            if(base.getHolidayAvailable()==2){
                                //配置节假日不可用，查询节假日配置
                                JSONObject req = new JSONObject();
                                req.put("particularYear", "");
                                JSONObject contractRes = RestfulHttpClientUtils.sendRestHttp(requestUrl + "mdadmin/store/inner/searchHolidayConfiguration", "POST", req.toJSONString());
                                if (contractRes != null && contractRes.getString("code").equals("0")) {
                                    JSONObject data = contractRes.getJSONObject("data");
                                    JSONArray holidayConfiguration = data.getJSONArray("cfg");
                                    List<UnavailableDate> holidayConfigurationList = JSONObject.parseArray(holidayConfiguration.toJSONString(), UnavailableDate.class);
                                    if (!CollectionUtils.isEmpty(holidayConfigurationList)) {
                                        unavailableDateAllList.addAll(holidayConfigurationList);
                                    }
                                }

                            }
                            int availableDay =0;//不可用天数
                            if(CollectionUtils.isNotEmpty(unavailableDateAllList)){
                           /* inputDto.setOrderPickUpDate(DateUtil.getDateFromStr("20230424130000",DateUtil.dtLong));
                            inputDto.setOrderReturnDate(DateUtil.getDateFromStr("20230425140000",DateUtil.dtLong));*/
                                LocalDateTime planPickUpDate =  DateUtil.getLocalDateFromStr(DateUtil.dateToString(inputDto.getOrderPickUpDate(),DateUtil.dtLong),DateUtil.DATE_TYPE14);
                                LocalDateTime planReturnDate =  DateUtil.getLocalDateFromStr(DateUtil.dateToString(inputDto.getOrderReturnDate(),DateUtil.dtLong),DateUtil.DATE_TYPE14);
                        /*    LocalDateTime planPickUpDate =  DateUtil.getLocalDateFromStr("20230424130000",DateUtil.DATE_TYPE14);
                            LocalDateTime planReturnDate =  DateUtil.getLocalDateFromStr("20230425140000",DateUtil.DATE_TYPE14);*/
                                out:for (;planPickUpDate.compareTo(planReturnDate)<0 ;planPickUpDate = planPickUpDate.plusDays(1) ){
                                    for(UnavailableDate unavailableDate1 : unavailableDateAllList){
                                        LocalDateTime startDate = DateUtil.getLocalDateFromStr(unavailableDate1.getStartDate().replaceAll("-","")+"000000",DateUtil.DATE_TYPE14);
                                        LocalDateTime endDate =  DateUtil.getLocalDateFromStr(unavailableDate1.getEndDate().replaceAll("-","")+"235959",DateUtil.DATE_TYPE14);
                                        if(planPickUpDate.compareTo(startDate) >=0 && planPickUpDate.compareTo(endDate)<=0){
                                            availableDay++;
                                            continue out;
                                        }

                                    }
                                }

                                long num=inputDto.getOrderReturnDate().getTime()-inputDto.getOrderPickUpDate().getTime();
                                long durationDay=0;
                                if(num%86400000==0){
                                    durationDay =(num/86400000);
                                }else{
                                    durationDay =(num/86400000)+1;
                                }

                                log.warn("getAvailableCardByUseCondition ----planPickUpDate :"+inputDto.getOrderReturnDate()+"----planReturnDate :"
                                        +inputDto.getOrderPickUpDate()+"----availableDay :"+availableDay+"----durationDay :"+durationDay);

                                unavailableDateFlag=durationDay > availableDay;
                            }
                        }
                    } catch (Exception e) {
                        log.error("getEffectiveActivityCard 遍历SuixiangCardUseBo 异常bo[{}]",JSON.toJSONString(bo),e);
                    }

                    SuiXiangQueryAvailableCardInfoDto result = new SuiXiangQueryAvailableCardInfoDto();

                    //result.setDays(cardInfoDto.getRentDays());
                    result.setDays(cardUseDto.getInitDays());
                    result.setCardGroup(4);
                    result.setCardName(cardInfoDto.getCardName());
                    result.setUserCardNo(cardUseDto.getId());
                    result.setBackUrl(cardInfoDto.getBackUrl());
                    result.setUsedDays(cardUseDto.getUsedDays());

                    //根据cardId 获取不可用日期
                    if(base!=null){
                        result.setHolidayAvailable(base.getHolidayAvailable());
                    }

                    result.setUnavailableDate(unavailableDateList);


                    if (carFlag && cityFlag && orderFlag && modelFlag && unavailableDateFlag) {
                        result.setAvailabilityFlag(1);
                    }else{
                        StringBuffer  unAvailableBuffer  = new StringBuffer();
                        if(!carFlag){
                            //unAvailableBuffer.append("车型不可用/");
                            unAvailableBuffer.append("下单车型不可用/");
                        }
                        if(!cityFlag){
                            //unAvailableBuffer.append("用车区域不可用/");
                            unAvailableBuffer.append("当前城市不可用/");
                        }

                        if(!orderFlag){
                            unAvailableBuffer.append("存在进行中的用卡订单/");
                        }

                        if(!modelFlag){
                            unAvailableBuffer.append("和套餐不可同享/");
                        }
                        if(!unavailableDateFlag){
                            unAvailableBuffer.append("本次租车时间段暂不可用此随享卡/");
                        }

                        String unAvailableDesc = ComUtils.formatEndChar(unAvailableBuffer.toString(),"/");
                        result.setAvailabilityFlag(0);
                        result.setUnAvailableDesc(unAvailableDesc);
                        Integer styleType = cardInfoDto.getStyleType();
                        String grayUrlByStyleNo = SuiXiangCardStyleImageEnum.getGrayUrlByStyleNo(styleType);
                        result.setBackUrl(grayUrlByStyleNo);
                    }
                    resultList.add(result);
                }

                return resultList;
            }
        } catch (Exception e) {
            log.error("我的出行卡，查询可用随享卡时异常 input{}",JSON.toJSONString(inputDto),e);
        }
        return null;
    }




    @Override
    public List<SuixiangCardUseBo> getEffectiveCardInfoListByUserId(Integer userPkId) {
        List<SuixiangCardUseBo> result = new ArrayList<>();

        List<SuixiangCardUse> cardUsesList = cardUseMapper.selectListByUserId(userPkId, SuiXiangCardStatusEnum.EFFECTIVE.getStatus());
        if (CollectionUtils.isNotEmpty(cardUsesList)) {
            List<Long> cardPriceIdList = cardUsesList.stream().map(SuixiangCardUse::getCardPriceId).collect(Collectors.toList());
            List<SuixiangCardInfoDto> cardInfoDtoList = cardPriceMapper.selectInfoListByPriceIds(cardPriceIdList);

            Map<Long, List<SuixiangCardInfoDto>> map = cardInfoDtoList.stream().collect(Collectors.groupingBy(SuixiangCardInfoDto::getCardPriceId));

            cardUsesList.stream().forEach(c -> {
                SuixiangCardUseBo bo = new SuixiangCardUseBo();
                bo.setCardInfoDto(map.get(c.getCardPriceId()).get(0));
                bo.setCardUseDto(c.toDto());
                result.add(bo);
            });
        }
        return result;
    }

    @Override
    public Set<Long> getSaleCardCityIds(int saleType) {
        Set<Long> result = new HashSet<>();
        List<String> suiXiangcityIds = new ArrayList<>();
        List<String> disCountcityIds = new ArrayList<>();
        List<String> allCountcityIds = new ArrayList<>();
        // 查询折扣卡
        if (saleType == 1 || saleType == 3) {
            try {
                List<MmpCardSalesActivityInfo> allDisCountCardList = new ArrayList<>();
                //查询所有预售 折扣卡 城市
                CardActivityQueryDto queryDto = new CardActivityQueryDto();
                queryDto.setActivityStatus(CardActivityStatusEnum.PUBLISHED.getStatus());
                queryDto.setStockMin(0L);
                queryDto.setPlatformType(0);

                List<MmpCardSalesActivityInfo> disCountCardList = mmpCardSalesActivityMapper.selectList(queryDto);
                if (CollectionUtils.isNotEmpty(disCountCardList)) {
                    allDisCountCardList.addAll(disCountCardList);
                }

                //查询所有在售 折扣卡 城市
                queryDto.setActivityStatus(CardActivityStatusEnum.RUNNING.getStatus());
                List<MmpCardSalesActivityInfo> disCountCardList2 = mmpCardSalesActivityMapper.selectList(queryDto);
                if (CollectionUtils.isNotEmpty(disCountCardList2)) {
                    allDisCountCardList.addAll(disCountCardList2);
                }

                disCountcityIds = allDisCountCardList.stream().map(MmpCardSalesActivityInfo::getCityLimit).collect(Collectors.toList());
                disCountCardList2 = null;
            } catch (Exception e) {
                log.error("查询所有 预售和在售 折扣卡城市异常 [{}]", e);
            }
        }

        if (saleType == 2 || saleType == 3) {
            try {
                SuixiangCardBaseExample baseExample = new SuixiangCardBaseExample();
                //卡片状态：1-待提交、2-待上架、3-已上架(开放购买)、4-已下架
                List<Integer> cardStatusList = Arrays.asList(2, 3);
                baseExample.createCriteria().andCardStatusIn(cardStatusList)
                        .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete())
                        .andDisplayFlagEqualTo(1).andStockGreaterThanOrEqualTo(0);
                List<SuixiangCardBase> baseList = cardBaseMapper.selectByExample(baseExample);

                if (CollectionUtils.isNotEmpty(baseList)) {
                    suiXiangcityIds = baseList.stream().map(SuixiangCardBase::getCityId).collect(Collectors.toList());
                }
                baseList = null;
            } catch (Exception e) {
                log.error("查询所有 预售和在售 随想卡城市异常 [{}]", e);
            }
        }

        // -1 代表全部城市
        if (suiXiangcityIds.contains("-1") || disCountcityIds.contains("-1")) {
            result.add(-1L);
            return result;
        }

        allCountcityIds.addAll(disCountcityIds);
        allCountcityIds.addAll(suiXiangcityIds);

        //释放内存
        disCountcityIds = null;
        suiXiangcityIds = null;

        String[] idsArray = null;
        for (String ids : allCountcityIds) {
            try {
                if (StringUtils.isNotBlank(ids)) {
                    idsArray = ids.split(",");
                    for (String s : idsArray) {
                        result.add(Long.parseLong(s));
                    }
                }
            } catch (Exception e) {
                log.error("查询所有预售和在售卡城市时，处理数据异常ids[{}],e[{}]", ids, e);
            }
        }
        return result;
    }

    @Resource
    private CityServ cityServ;

    @Resource
    private ISuiXiangCardService suiXiangCardService;

    @Resource
    private SuixiangCardPriceMapper suixiangCardPriceMapper;

    @Override
    public List<EffectiveSuiXiangCardInfoDto> getSuiXiangCardListByCity(Long userId, String cityName) {
        /**
         * 1.根据城市名称，获取城市id
         */
        Long cityId = cityServ.getCityIdByCityName(cityName);
        if (cityId == null) {
            return null;
        }

        List<EffectiveSuiXiangCardInfoDto> reslut = new ArrayList<>();
        // 获取售卡城市基础表信息
        List<SuixiangCardBase> effectiveSuiXiangCardList = cardBaseMapper.getEffectiveSuiXiangCardList(cityId);
        if (CollectionUtils.isEmpty(effectiveSuiXiangCardList)) {
            return null;
        }

        List<Long> baseCardIds = effectiveSuiXiangCardList.stream().map(SuixiangCardBase::getId).collect(Collectors.toList());

        effectiveSuiXiangCardList.stream().forEach(baseCard -> {
            try {
                Long baseCardId = baseCard.getId();
                EffectiveSuiXiangCardInfoDto dto = new EffectiveSuiXiangCardInfoDto();
                dto.setCardId(baseCardId);
                dto.setCardName(baseCard.getCardName());
                dto.setStock(baseCard.getStock());
                dto.setBackUrl(baseCard.getBackUrl());
                dto.setValidDaysType(baseCard.getValidDaysType());
                dto.setStartTime(DateUtil.getFormatDate(baseCard.getSaleStartTime(), DateUtil.dtLong));
                // 全部城市
                String cityIds = baseCard.getCityId();
                if (StringUtils.isNotBlank(cityIds) && cityIds.contains("-1")) {
                    dto.setAllCityFlag(1);
                }else{
                    dto.setAllCityFlag(0);
                }

                // 查询出所有价格记录
                List<SuixiangCardInfoDto> suixiangCardInfoDtos = suixiangCardPriceMapper.selectInfoByBaseId(baseCardId);
                if (CollectionUtils.isNotEmpty(suixiangCardInfoDtos)) {
                    //这里 取天数最少、价格最低的 购卡页面展示
                    SuixiangCardInfoDto suixiangCardInfoDto = suixiangCardInfoDtos.get(0);
                    dto.setDays(suixiangCardInfoDto.getRentDays());
                    dto.setSalesPrice(suixiangCardInfoDto.getSalesPrice());
                    // 是否有全部车型
                    boolean hasAllCarModelFlag = suixiangCardInfoDtos.stream().anyMatch(info -> {
                        String carModelIds = info.getCarModelIds();
                        if (StringUtils.isNotBlank(carModelIds) && carModelIds.contains("-1")) {
                            return true;
                        }
                        return false;
                    });
                    if (hasAllCarModelFlag) {
                        dto.setAllCarModelFlag(1);
                    }
                }
                //1：可以购买 2：用户有生效的随享卡不可以买 3：未设置开售提醒 4：已设置开售提醒 5:已达购买上限
                dto.setBuyCardStatus(getBuyCardStatus(baseCardIds, userId, baseCard.getCardStatus(), baseCardId,baseCard.getSaleStartTime(),baseCard.getSaleEndTime(),baseCard.getPurchaseLimitNum()));

                reslut.add(dto);
            } catch (Exception e) {
                log.error("getSuiXiangCardListByCity，处理卡时异常，baseCard[{}],e[{}]", JSON.toJSONString(baseCard), e);
            }
        });
        return reslut;
    }

    /**
     * 获取 购卡状态 1：可以购买 2：用户有生效的随享卡不可以买 3：未设置开售提醒 4：已设置开售提醒 5:已达购买上限
     *  未登录时：返回1或者3
     *  已登录时：返回1或者2或者4或者5
     *
     * @param baseCardIds
     * @param userId
     * @param cardStatus  卡活动状态 2待上架 3 已上架
     * @param baseCardId  当前卡基础id
     * @param saleStartDate  活动上架时间
     * @param purchaseLimitNum   购买上限数，0代表没有购买张数限制
     * @return
     */
    public Integer getBuyCardStatus(List<Long> baseCardIds, Long userId, Integer cardStatus, Long baseCardId, Date saleStartDate, Date saleEndDate, Integer purchaseLimitNum) {
        // 用户已经 设置过售卡提醒的 cardBaseId集合
        List<Long> hasRemindBaseIds = suiXiangCardService.queryCardHasRemind(userId, baseCardIds);
        // 1：可以购买 2：用户有生效的随享卡不可以买 3：未设置开售提醒 4：已设置开售提醒
        if (SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus() == cardStatus) {
            // 已经可以售卖
            Date now = new Date();
            if ((now.before(saleEndDate) || now.equals(saleEndDate)) && (now.after(saleStartDate) || now.equals(saleStartDate))) {
                // 用户 已购买的随享卡数
                Integer suiXiangCardNum = suiXiangCardService.getPurchaseSuiXiangCardNum(userId, baseCardId);
                if (suiXiangCardNum == null) {
                    return -1;
                }

                if (purchaseLimitNum > 0 && suiXiangCardNum >= purchaseLimitNum) {
                    //已达购买上限
                    return 5;
                } else {
                    // 1：可以购买
                    return 1;
                }
            }
            // 预售
            else {
                if (CollectionUtils.isNotEmpty(hasRemindBaseIds) && hasRemindBaseIds.contains(baseCardId)) {
                    // 4：已设置开售提醒
                    return 4;
                } else {
                    // 3：未设置开售提醒
                    return 3;
                }
            }
        }
        return -1;
    }


    @Override
    public EffectiveSuiXiangCardDetailDto getSuiXiangCardDetail(Long userId, Long cardBaseId) {
        if (cardBaseId == null) {
            return null;
        }
        EffectiveSuiXiangCardDetailDto result = new EffectiveSuiXiangCardDetailDto();
        List<SuixiangCardInfoDto> suixiangCardInfoDtoList = suixiangCardPriceMapper.selectInfoByBaseId(cardBaseId);

        if (CollectionUtils.isEmpty(suixiangCardInfoDtoList)) {
            return null;
        }

        List<String> carModelIds = new ArrayList<>();
        // 基础信息
        SuiXiangCardBaseInfoForSalesDto suiXiangCardBaseInfoForSales = getSuiXiangCardBaseInfoForSales(userId, suixiangCardInfoDtoList.get(0));
        if (suiXiangCardBaseInfoForSales == null) {
            return null;
        }
        result.setCardBaseInfo(suiXiangCardBaseInfoForSales);


        List<String> modelGroupList = Lists.newArrayList();
        Set<Integer> daysSet = new HashSet<>();
        List<Integer> daysList = Lists.newArrayList();
        result.setDaysList(daysList);
        result.setModelGroupList(modelGroupList);

        List<SuiXiangCardSalesInfoDto> cardInfoList = new ArrayList<>();
        result.setCardInfoList(cardInfoList);

        Long tempRentId = suixiangCardInfoDtoList.get(0).getCardRentId();

        suixiangCardInfoDtoList.stream().forEach(dto -> {
            daysSet.add(dto.getRentDays());
            if(tempRentId.equals(dto.getCardRentId())){
                modelGroupList.add(dto.getCarModelGroup());
            }
            carModelIds.add(dto.getCarModelIds());
            SuiXiangCardSalesInfoDto salesInfoDto = getSuiXiangCardSalesInfoDto(dto);
            cardInfoList.add(salesInfoDto);
        });

        daysList.addAll(daysSet);
        Collections.sort(daysList);

        // 设置车型
        List<CarModelInfoDto> carListInfo = getCarListInfo(carModelIds);
        suiXiangCardBaseInfoForSales.setCarList(carListInfo);
        return result;
    }

    @Resource
    private OrgService orgService;

    @Override
    public GetSuiXiangCardDetailForHasBuyBo querySuiXiangCardDetailForHasBuy(GetSuiXiangCardDetailForHasBuyInput input) throws BusinessException {
        Integer type = input.getType();
        Long purchaseId = input.getPurchaseId();
        Long useCardId = input.getUseCardId();
        if (type == null || (purchaseId == null && useCardId == null)) {
            throw new BusinessException(-2, "入参格式不正确");
        }

        if (type == 1 && purchaseId == null) {
            throw new BusinessException(-2, "入参格式不正确");
        }

        if (type == 2 && useCardId == null) {
            throw new BusinessException(-2, "入参格式不正确");
        }

        SuixiangCardUseDto suixiangCardUseDto = new SuixiangCardUseDto();
        SuixiangCardUse suixiangCardUse = new SuixiangCardUse();
        if (type == 2) {
            suixiangCardUse = cardUseMapper.selectByPrimaryKey(useCardId);
            if (suixiangCardUse == null || suixiangCardUse.getId() == null) {
                throw new BusinessException(-1, "使用记录不存在");
            }
            suixiangCardUseDto = suixiangCardUse.toDto();
            purchaseId = suixiangCardUseDto.getPurchaseId();
        }

        SuixiangCardPurchaseRecord purchaseRecord = cardPurchaseRecordMapper.selectByPrimaryKey(purchaseId);
        if (purchaseRecord == null) {
            throw new BusinessException(-1, "交易记录不存在");
        }

        SuixiangCardInfoDto suixiangCardInfoDto = suixiangCardPriceMapper.selectInfoByPriceId(purchaseRecord.getCardPriceId());
        if (suixiangCardInfoDto == null) {
            throw new BusinessException(-1, "交易记录不存在");
        }

        //组织名称
        String orgName = orgService.getOrgNameByOrgId(suixiangCardInfoDto.getOrgId());
        suixiangCardInfoDto.setOrgName(orgName);

        GetSuiXiangCardDetailForHasBuyBo bo = new GetSuiXiangCardDetailForHasBuyBo();
        SuiXiangCardSalesInfoDto salesInfoDto = getSuiXiangCardSalesInfoDto(suixiangCardInfoDto);
        SuiXiangCardBaseInfoHasBuyDto baseInfoHasBuyDto = new SuiXiangCardBaseInfoHasBuyDto();

        bo.setCardSaleInfo(salesInfoDto);
        bo.setCardBaseInfo(baseInfoHasBuyDto);
        bo.setCardInfoDto(suixiangCardInfoDto);
        bo.setCardUseDto(suixiangCardUseDto);


        // 基础信息
        SuiXiangCardBaseInfoForSalesDto forSalesDto = getSuiXiangCardBaseInfoForSales(purchaseRecord.getUserId(), suixiangCardInfoDto);

        if(suixiangCardInfoDto.getHolidayAvailable() ==2){
            List<UnavailableDate> unavailableDateForServer = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(forSalesDto.getUnavailableDate())){
                unavailableDateForServer.addAll(forSalesDto.getUnavailableDate());
            }

            //节假日不可用
            JSONObject req = new JSONObject();
            req.put("particularYear", "");
            JSONObject contractRes = RestfulHttpClientUtils.sendRestHttp(requestUrl + "mdadmin/store/inner/searchHolidayConfiguration", "POST", req.toJSONString());
            if (contractRes != null && contractRes.getString("code").equals("0")) {
                JSONObject data = contractRes.getJSONObject("data");
                JSONArray holidayConfiguration = data.getJSONArray("cfg");
                List<UnavailableDate> unavailableDateList = JSONObject.parseArray(holidayConfiguration.toJSONString(), UnavailableDate.class);
                if(CollectionUtils.isNotEmpty(unavailableDateList)){
                    for(UnavailableDate unavailableDate : unavailableDateList){
                        UnavailableDate unavailableDate1 = new UnavailableDate();
                        unavailableDate1.setStartDate(unavailableDate.getStartDate().replaceAll("-",""));
                        unavailableDate1.setEndDate(unavailableDate.getEndDate().replaceAll("-",""));
                        unavailableDateForServer.add(unavailableDate1);
                    }
                }
            }

            forSalesDto.setUnavailableDateForServer(unavailableDateForServer);
        }

        BeanUtils.copyProperties(forSalesDto, baseInfoHasBuyDto);

        // 车型信息
        String carModelIdsString = suixiangCardInfoDto.getCarModelIds();
        String[] carModelIds = carModelIdsString.split(",");
        List<CarModelInfoDto> carListInfo = getCarListInfo(Arrays.asList(carModelIds));
        baseInfoHasBuyDto.setCarList(carListInfo);

        //购买的随享卡数量
        baseInfoHasBuyDto.setCardNum(purchaseRecord.getQuantity());

        if (purchaseRecord.getPaymentStatus() == SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus()) {
            baseInfoHasBuyDto.setPayFlag(1);
            if (suixiangCardUseDto != null) {
                Integer cardStatus = suixiangCardUseDto.getCardStatus();
                baseInfoHasBuyDto.setCardUseStatus(cardStatus);
                //随享卡有效期
                baseInfoHasBuyDto.setCardDateDesc(getCardDateDesc(purchaseRecord, suixiangCardUse, null));
                // 设置失效卡面
                if (!(cardStatus == SuiXiangCardStatusEnum.EFFECTIVE.getStatus())) {
                    Integer styleType = suixiangCardInfoDto.getStyleType();
                    String grayUrlByStyleNo = SuiXiangCardStyleImageEnum.getGrayUrlByStyleNo(styleType);
                    suixiangCardInfoDto.setBackUrl(grayUrlByStyleNo);
                }
            }
        } else if (purchaseRecord.getPaymentStatus() == SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus()) {
            baseInfoHasBuyDto.setPayFlag(2);
            //随享卡有效期
            baseInfoHasBuyDto.setCardDateDesc(getCardDateDesc(purchaseRecord, null, suixiangCardInfoDto.getValidDaysType()));

            // 剩余支付时间 mmss
            try {
                Date createTime = purchaseRecord.getCreateTime();
                Date planCancleTime = DateUtil.addMin(createTime, 5);
                Date now = new Date();
                String mmssBetween = DateUtil.mmssBetween(now, planCancleTime);
                baseInfoHasBuyDto.setRemainPayTime(mmssBetween);
            } catch (Exception e) {
                log.error("随享卡计算 剩余支付时间异常,purchaseRecord[{}]，e[{}]", JSON.toJSONString(purchaseRecord), e);
            }
        }

        // 赠送标记
        baseInfoHasBuyDto.setSendStatus(purchaseRecord.getIssueType() == 2 ? 1 : 2);
        //支付订单号
        baseInfoHasBuyDto.setPayOrderNO(purchaseRecord.getPayOrderNo());
        // 购卡方式
        baseInfoHasBuyDto.setIssueType(purchaseRecord.getIssueType());

        // 购买方式
        if (purchaseRecord.getIssueType().equals(SuiXiangCardIssueTypeEnum.PURCHASE.getType())) {
            BigDecimal realAmount = purchaseRecord.getRealAmount();
            Integer quantity = (purchaseRecord.getQuantity() == null || purchaseRecord.getQuantity() == 0) ? 1 : purchaseRecord.getQuantity();
            BigDecimal unitPrice = realAmount.divide(new BigDecimal(quantity), 2, RoundingMode.HALF_DOWN);
            baseInfoHasBuyDto.setSingleCardPrice(unitPrice.toPlainString());
        } else if (purchaseRecord.getIssueType().equals(SuiXiangCardIssueTypeEnum.EXCHANGE.getType())) {
            Long cdkId = purchaseRecord.getCdkId();
            SuixiangCardCdkOtherDto ckdOtherDto = suixiangCardCdkMapper.getCkdOtherDtoByCkdId(cdkId);
            if (ckdOtherDto != null) {
                baseInfoHasBuyDto.setCdkPurpose(ckdOtherDto.getPurpose());
                baseInfoHasBuyDto.setThirdSalesPrice(ckdOtherDto.getThirdSalesPrice());
            }
        }
        return bo;
    }

    /**
     * 随享卡有效期
     * <p>
     * 随享卡卡面 有效期描述
     * 例（退卡时间 2022年12月1日 12:00、作废时间 2022年12月1日 12:00、有效期至 2022年12月1日 12:00、购买后6个月有效）
     *
     * @param purchaseRecord
     * @param suixiangCardUse
     * @param validDaysType
     * @return
     */
    public String getCardDateDesc(SuixiangCardPurchaseRecord purchaseRecord, SuixiangCardUse suixiangCardUse, Integer validDaysType) {
        StringBuffer sb = new StringBuffer();
        if (purchaseRecord.getPaymentStatus() == SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus()) {
            if (suixiangCardUse != null) {
                Integer cardStatus = suixiangCardUse.getCardStatus();
                String cardValidDesc1 = null;
                String cardValidDescDate = null;
                if (SuiXiangCardStatusEnum.RETURN.getStatus().equals(cardStatus)) {
                    cardValidDesc1 = "退卡时间 ";
                    cardValidDescDate = DateUtil.getFormatDate(suixiangCardUse.getUpdateTime(), DateUtil.DATE_TYPE13);
                } else if (SuiXiangCardStatusEnum.DISCARD.getStatus().equals(cardStatus)) {
                    cardValidDesc1 = "作废时间 ";
                    cardValidDescDate = DateUtil.getFormatDate(suixiangCardUse.getUpdateTime(), DateUtil.DATE_TYPE13);
                } else {
                    cardValidDesc1 = "有效期至 ";
                    cardValidDescDate = DateUtil.getFormatDate(suixiangCardUse.getExpiresTime(), DateUtil.DATE_TYPE13);
                }

                sb.append(cardValidDesc1);
                sb.append(cardValidDescDate);
            }
        } else if (purchaseRecord.getPaymentStatus() == SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus()) {
            //随享卡有效期
            sb.append("购买后");
            sb.append(validDaysType);
            sb.append("个月有效");
        }
        return sb.toString();
    }


    /**
     * 获取 车型 信息
     *
     * @param carModelIds
     * @return
     */
    private List<CarModelInfoDto> getCarListInfo(List<String> carModelIds) {
        if (CollectionUtils.isEmpty(carModelIds)) {
            return null;
        }

        List<CarModelInfoDto> carList = new ArrayList<>();
        Boolean isAllCarModelFlag = false;
        List<Long> carModelIdsLong = new ArrayList<>();
        for (String id : carModelIds) {
            if (StringUtils.isNotBlank(id)) {
                String[] idsArray = id.split(",");
                for (String idString : idsArray) {
                    carModelIdsLong.add(Long.valueOf(idString));
                }
            }
        }

        //Map<Long, GoodsVehicleModelInfo> goodsDetailModelMap = goodsModelService.getGoodsDetailModelMap(carModelIdsLong, isAllCarModelFlag);
        //Collection<GoodsVehicleModelInfo> carCollection = goodsDetailModelMap.values();
        List<GoodsVehicleModelInfo> carCollection = goodsModelService.getGoodsDetailModelList(carModelIdsLong, isAllCarModelFlag);
        carCollection.stream().forEach(cc -> {
            CarModelInfoDto carModelInfoDto = new CarModelInfoDto();
            carModelInfoDto.setCarModelId(cc.getGoodsModelId().intValue());
            carModelInfoDto.setCarName(cc.getGoodsModelName());
            carModelInfoDto.setAndroidPicUrl(cc.getAndroidPicUrl());
            carModelInfoDto.setIosPicUrl(cc.getIosPicUrl());
            carList.add(carModelInfoDto);
        });
        return carList;
    }

    /**
     * 封装SuiXiangCardSalesInfoDto
     *
     * @param dto
     * @return
     */
    private SuiXiangCardSalesInfoDto getSuiXiangCardSalesInfoDto(SuixiangCardInfoDto dto) {
        SuiXiangCardSalesInfoDto salesInfoDto = null;
        try {
            salesInfoDto = new SuiXiangCardSalesInfoDto();
            salesInfoDto.setCardId(dto.getCardBaseId());
            salesInfoDto.setPriceId(dto.getCardPriceId());
            salesInfoDto.setRentDayId(dto.getCardRentId());
            salesInfoDto.setDays(dto.getRentDays());
            salesInfoDto.setModelGroup(dto.getCarModelGroup());
            String carModelIds = dto.getCarModelIds();
            if (StringUtils.isNotBlank(carModelIds) && carModelIds.contains("-1")) {
                salesInfoDto.setAllCarModelFlag(1);
                // 设置全部车型
                Set<Long> goodsModelIds = configLoader.getGoodsModelIds();
                String goodsModelIdsStr = StringUtils.join(goodsModelIds, ',');
                salesInfoDto.setCarModelIds(goodsModelIdsStr);
            }else{
                salesInfoDto.setCarModelIds(carModelIds);
            }

            // 服务费-总免
            BigDecimal totalServiceFeesAmout = BigDecimal.ZERO;
            String serviceFees = dto.getServiceFees();
            if (StringUtils.isNotBlank(serviceFees)) {
                List<ServiceFeeCfgDto> feeList = JSON.parseArray(serviceFees, ServiceFeeCfgDto.class);
                salesInfoDto.setServiceFeeCfg(feeList);
                for (ServiceFeeCfgDto f : feeList) {
                    totalServiceFeesAmout = totalServiceFeesAmout.add(new BigDecimal(f.getCrossedPrice()));
                }
            }

            // 售价
            BigDecimal salesPrice = dto.getSalesPrice();
            // 汽车租金划线价格
            BigDecimal carUnderlinePrice = dto.getUnderlinePrice();
            // 划线价格总=  汽车租金划线价格 + 服务费-总免
            BigDecimal totalUnderlinePrice = totalServiceFeesAmout.add(carUnderlinePrice);
            // 节省
            BigDecimal savePrice = totalUnderlinePrice.subtract(salesPrice);

            salesInfoDto.setSalesPrice(salesPrice);
            salesInfoDto.setCarUnderlinePrice(carUnderlinePrice);
            salesInfoDto.setSavePrice(BigDecimal.ZERO.compareTo(savePrice) > 0 ? BigDecimal.ZERO : savePrice);
            salesInfoDto.setTotalUnderlinePrice(totalUnderlinePrice);
        } catch (Exception e) {
            log.error("getSuiXiangCardSalesInfoDto异常,dto[{}],e[{}]", JSON.toJSONString(dto), e);
        }
        return salesInfoDto;
    }

    /**
     * 封装 SuiXiangCardBaseInfoForSalesDto
     *
     * @param userId
     * @param firstDto
     * @return
     */
    public SuiXiangCardBaseInfoForSalesDto getSuiXiangCardBaseInfoForSales(Long userId, SuixiangCardInfoDto firstDto) {
        try {
            SuiXiangCardBaseInfoForSalesDto baseInfoForSalesDto = new SuiXiangCardBaseInfoForSalesDto();
            baseInfoForSalesDto.setCardId(firstDto.getCardBaseId());
            baseInfoForSalesDto.setValidDaysType(firstDto.getValidDaysType());
            baseInfoForSalesDto.setCardName(firstDto.getCardName());
            baseInfoForSalesDto.setSaleNum(firstDto.getTotalSales()+90);//增加默认已售卖张数90
            baseInfoForSalesDto.setBackUrl(firstDto.getBackUrl());
            baseInfoForSalesDto.setPurchaseLimitNum(firstDto.getPurchaseLimitNum());
            baseInfoForSalesDto.setStock(firstDto.getStock());
            baseInfoForSalesDto.setHolidayAvailable(firstDto.getHolidayAvailable());
            if(StringUtils.isNotEmpty(firstDto.getUnavailableDate())){
                List<UnavailableDate> unavailableDateList =  JSON.parseArray(firstDto.getUnavailableDate(),UnavailableDate.class);
                baseInfoForSalesDto.setUnavailableDate(unavailableDateList);
            }
            // 使用限制 城市
            String cityIds = firstDto.getCityId();
            StringBuffer cityLimitDesc = new StringBuffer();
            cityLimitDesc.append("可用城市：");
            if (StringUtils.isNotBlank(cityIds)) {
                //全部城市
                if (cityIds.contains("-1")) {
                    baseInfoForSalesDto.setAllCityFlag(1);
                    cityLimitDesc.append("EVCARD业务覆盖的全部城市");
                } else {
                    String cityNames = suixiangCardBaseManager.getCityNamesByCityId(cityIds);
                    cityLimitDesc.append(cityNames);
                }
            } else {
                baseInfoForSalesDto.setAllCityFlag(1);
                // 没有配置 不限制城市
                cityLimitDesc.append("EVCARD业务覆盖的全部城市");
            }
            baseInfoForSalesDto.setCityLimitDesc(cityLimitDesc.toString());

            // 活动有效期
            StringBuffer activityValidDesc = new StringBuffer();
            activityValidDesc.append(DateUtil.getFormatDate(firstDto.getSaleStartTime(), DateUtil.DATE_TYPE12));
            activityValidDesc.append("至");
            activityValidDesc.append(DateUtil.getFormatDate(firstDto.getSaleEndTime(), DateUtil.DATE_TYPE12));
            baseInfoForSalesDto.setActivityValidDesc(activityValidDesc.toString());

            //随享卡有效期
            StringBuffer cardValidDesc = new StringBuffer();
            cardValidDesc.append("购买立即生效，生效后");
            cardValidDesc.append(SuixiangCardValidDaysTypeEnum.getDescByType(firstDto.getValidDaysType()));
            cardValidDesc.append("过期");
            baseInfoForSalesDto.setCardValidDesc(cardValidDesc.toString());

            baseInfoForSalesDto.setStartTime(DateUtil.getFormatDate(firstDto.getSaleStartTime(), DateUtil.dtLong));
            baseInfoForSalesDto.setRules(firstDto.getRules());

            // 1：可以购买 2：用户有生效的随享卡不可以买 3：未设置开售提醒 4：已设置开售提醒 5:已达购买上限
            Integer buyCardStatus = getBuyCardStatus(Arrays.asList(firstDto.getCardBaseId()), userId, firstDto.getCardStatus(), firstDto.getCardBaseId(),firstDto.getSaleStartTime(),firstDto.getSaleEndTime(),firstDto.getPurchaseLimitNum());
            baseInfoForSalesDto.setBuyCardStatus(buyCardStatus);
            if (buyCardStatus == 4) {
                baseInfoForSalesDto.setSaleRemind(1);
            } else {
                baseInfoForSalesDto.setSaleRemind(2);
            }

            return baseInfoForSalesDto;
        } catch (Exception e) {
            log.error("getSuiXiangCardBaseInfoForSales异常，firstDto[{}],userId[{}]e[{}]", JSON.toJSONString(firstDto), userId, e);
        }
        return null;
    }
}
