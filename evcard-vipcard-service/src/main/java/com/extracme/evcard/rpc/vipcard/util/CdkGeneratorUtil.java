package com.extracme.evcard.rpc.vipcard.util;

import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardCdkMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class CdkGeneratorUtil {
    @Autowired
    private SuixiangCardCdkMapper suixiangCardCdkMapper;

    private static final char[] r = new char[]{'j','g','z','q','n','b','v','w','t','k','s','x','r','7','8','f','i','0','l','d','5','m','h','9','u','4','p','6','2','y','3','e'};

    /** 区分符，(不能与自定义进制有重复) */
    private static final char b = 'o';

    /** 进制长度 */
    private static final int binLen = r.length;

    /** 序列最小长度 */
    private static final int s = 16;

    /**
     * 进制换算（加密）.<br>
     * @param id
     * @return
     */
    public static String encode(long id) {
        char[] buf=new char[32];
        int charPos=32;
        while((id / binLen) > 0) {
            int ind=(int)(id % binLen);
            buf[--charPos]=r[ind];
            id /= binLen;
        }
        buf[--charPos]=r[(int)(id % binLen)];
        String str = new String(buf, charPos, (32 - charPos));
        // 不够长度的自动随机补全
        if(str.length() < s) {
            StringBuilder sb=new StringBuilder();
            sb.append(str);
            sb.append(b);
            Random rnd=new Random();
            for(int i=1; i < s - str.length(); i++) {
                sb.append(r[rnd.nextInt(binLen)]);
            }
            str = sb.toString();
        }
        return changePlaces(str);
    }

    /**
     * 进制换算（解密）.<br>
     * @param code
     * @return
     */
    public static long decode(String code) {
        code = changePlaces(code);
        char chs[]=code.toCharArray();
        long res=0L;
        for(int i=0; i < chs.length; i++) {
            int ind=0;
            for(int j=0; j < binLen; j++) {
                if(chs[i] == r[j]) {
                    ind=j;
                    break;
                }
            }
            if(chs[i] == b) {
                break;
            }
            if(i > 0) {
                res=res * binLen + ind;
            } else {
                res=ind;
            }
        }
        return res;
    }

    private static String changePlaces(String code) {
        char[] chars = code.toCharArray();
        char char0 = chars[0];
        char char1 = chars[1];
        char char3 = chars[3];
        char char6 = chars[6];
        char char8 = chars[8];
        char char10 = chars[10];
        char char12 = chars[12];
        char char13 = chars[13];
        chars[0] = char10;
        chars[10] = char0;
        chars[1] = char8;
        chars[8] = char1;
        chars[3] = char12;
        chars[12] = char3;
        chars[6] = char13;
        chars[13] = char6;
        return new String(chars);
    }

    public Map<Long, String> batchEncode(List<Long> ids) {
        Map<Long, String> keys = new HashMap<>();
        for (Long id : ids) {
            keys.put(id, encode(id));
        }
        return keys;
    }
}
