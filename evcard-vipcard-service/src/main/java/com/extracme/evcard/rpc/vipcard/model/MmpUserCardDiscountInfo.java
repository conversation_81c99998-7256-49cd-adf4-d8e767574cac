package com.extracme.evcard.rpc.vipcard.model;

import java.math.BigDecimal;
import java.util.Date;

public class MmpUserCardDiscountInfo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.id
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.user_card_no
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private Long userCardNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.start_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private Date startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.end_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private Date endTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.total_discount_amount
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private BigDecimal totalDiscountAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.discount_amount
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private BigDecimal discountAmount;


    private BigDecimal frozenAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.status
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.create_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.create_oper_id
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.create_oper_name
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.update_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.update_oper_id
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_discount_info.update_oper_name
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.id
     *
     * @return the value of mmp_user_card_discount_info.id
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.id
     *
     * @param id the value for mmp_user_card_discount_info.id
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.user_card_no
     *
     * @return the value of mmp_user_card_discount_info.user_card_no
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public Long getUserCardNo() {
        return userCardNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.user_card_no
     *
     * @param userCardNo the value for mmp_user_card_discount_info.user_card_no
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setUserCardNo(Long userCardNo) {
        this.userCardNo = userCardNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.start_time
     *
     * @return the value of mmp_user_card_discount_info.start_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.start_time
     *
     * @param startTime the value for mmp_user_card_discount_info.start_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.end_time
     *
     * @return the value of mmp_user_card_discount_info.end_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.end_time
     *
     * @param endTime the value for mmp_user_card_discount_info.end_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.total_discount_amount
     *
     * @return the value of mmp_user_card_discount_info.total_discount_amount
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.total_discount_amount
     *
     * @param totalDiscountAmount the value for mmp_user_card_discount_info.total_discount_amount
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.discount_amount
     *
     * @return the value of mmp_user_card_discount_info.discount_amount
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.discount_amount
     *
     * @param discountAmount the value for mmp_user_card_discount_info.discount_amount
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.status
     *
     * @return the value of mmp_user_card_discount_info.status
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.status
     *
     * @param status the value for mmp_user_card_discount_info.status
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.create_time
     *
     * @return the value of mmp_user_card_discount_info.create_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.create_time
     *
     * @param createTime the value for mmp_user_card_discount_info.create_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.create_oper_id
     *
     * @return the value of mmp_user_card_discount_info.create_oper_id
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.create_oper_id
     *
     * @param createOperId the value for mmp_user_card_discount_info.create_oper_id
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.create_oper_name
     *
     * @return the value of mmp_user_card_discount_info.create_oper_name
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.create_oper_name
     *
     * @param createOperName the value for mmp_user_card_discount_info.create_oper_name
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.update_time
     *
     * @return the value of mmp_user_card_discount_info.update_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.update_time
     *
     * @param updateTime the value for mmp_user_card_discount_info.update_time
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.update_oper_id
     *
     * @return the value of mmp_user_card_discount_info.update_oper_id
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.update_oper_id
     *
     * @param updateOperId the value for mmp_user_card_discount_info.update_oper_id
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_discount_info.update_oper_name
     *
     * @return the value of mmp_user_card_discount_info.update_oper_name
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_discount_info.update_oper_name
     *
     * @param updateOperName the value for mmp_user_card_discount_info.update_oper_name
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    public BigDecimal getFrozenAmount() {
        return frozenAmount;
    }

    public void setFrozenAmount(BigDecimal frozenAmount) {
        this.frozenAmount = frozenAmount;
    }
}