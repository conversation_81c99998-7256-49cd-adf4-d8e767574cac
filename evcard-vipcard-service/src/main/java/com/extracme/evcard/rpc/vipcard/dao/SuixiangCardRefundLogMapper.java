package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLog;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardRefundLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_refund_log
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    int countByExample(SuixiangCardRefundLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_refund_log
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_refund_log
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    int insert(SuixiangCardRefundLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_refund_log
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    int insertSelective(SuixiangCardRefundLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_refund_log
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    List<SuixiangCardRefundLog> selectByExample(SuixiangCardRefundLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_refund_log
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    SuixiangCardRefundLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_refund_log
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    int updateByExampleSelective(@Param("record") SuixiangCardRefundLog record, @Param("example") SuixiangCardRefundLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_refund_log
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    int updateByExample(@Param("record") SuixiangCardRefundLog record, @Param("example") SuixiangCardRefundLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_refund_log
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    int updateByPrimaryKeySelective(SuixiangCardRefundLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_refund_log
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    int updateByPrimaryKey(SuixiangCardRefundLog record);
}