package com.extracme.evcard.rpc.vipcard.service.inner;

import com.extracme.evcard.rpc.pay.service.IEvcardPayService;
import com.extracme.evcard.rpc.vipcard.dto.PurchaseRecordInfo;
import com.extracme.evcard.rpc.vipcard.service.store.StorePayService;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class OrderPayCheckService {

    @Resource
    IEvcardPayService evcardPayService;
    @Resource
    private StorePayService storePayService;

    public int checkPurchasePayingOrNot(PurchaseRecordInfo purchaseRecord){
        if(StringUtils.isNotBlank(purchaseRecord.getOutTradeSeq()) && purchaseRecord.getStatus() == 0) {
            boolean isPaying = false;
            try {
                if(StringUtils.startsWith(purchaseRecord.getOutTradeSeq(), Constants.PREFIX_PAY_ORDER_NO)) {
                    isPaying = storePayService.isOrderPayingOrNot(purchaseRecord.getOutTradeSeq());
                } else {
                    isPaying = evcardPayService.payOrderIsPayingOrNot(purchaseRecord.getOutTradeSeq());
                }
                log.info("检查购卡订单支付状态：purchaseId={}, outTradeSeq={}, result={}", purchaseRecord.getId(), purchaseRecord.getOutTradeSeq(), isPaying);
            } catch (Exception e) {
                log.warn("检查购卡订单支付状态：check异常，状态未知，purchaseId=" + purchaseRecord.getId() + "outTradeSeq="
                        + purchaseRecord.getOutTradeSeq(), e);
                return -1;
            }
            if(isPaying) {
                return 1;
            }
        }
        return 0;
    }
}
