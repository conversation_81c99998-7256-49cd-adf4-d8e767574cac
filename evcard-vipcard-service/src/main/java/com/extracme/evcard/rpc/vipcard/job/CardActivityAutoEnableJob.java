package com.extracme.evcard.rpc.vipcard.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.rpc.vipcard.dao.MmpCardSalesActivityMapper;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.enums.CardActivityOperateEnum;
import com.extracme.evcard.rpc.vipcard.enums.CardActivityStatusEnum;
import com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity;
import com.extracme.evcard.rpc.vipcard.service.inner.MemberCardInnerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卡片售卖活动自动上下线，每个整点执行一次
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "vipcard-rpc-cardActivityAutoEnableJob",
		cron = "0 0 0/1 * * ?", description = "卡片售卖活动自动上下线", overwrite = true)
public class CardActivityAutoEnableJob implements SimpleJob {

	@Resource
	private MemberCardInnerService memberCardInnerService;

	@Autowired
	private MmpCardSalesActivityMapper mmpCardSalesActivityMapper;

	@Override
	public void execute(ShardingContext shardingContext) {
		try{
			autoUpdateOnOffActivitys();
		}catch (Exception ex) {
			log.error("----ActivityAutoOnOfflineJob 定时上下线失败...", ex);
			throw ex;
		}
	}

	public void autoUpdateOnOffActivitys() {
		log.info("autoUpdateTaskStatus start ...");
		Date date = new Date();
		log.debug("定时任务: date={}", date);
		//指定类型，待上线活动列表
		List<MmpCardSalesActivity> onlineList = mmpCardSalesActivityMapper.selectReadyToStartActivityList(date);
		//指定类型，待下线活动列表
		List<MmpCardSalesActivity> offlineList = mmpCardSalesActivityMapper.selectReadyToStopActivityList(date);
		//活动上线
		if (CollectionUtils.isNotEmpty(onlineList)) {
			List<Long> activityIds = onlineList.stream().map(MmpCardSalesActivity::getId).collect(Collectors.toList());
			mmpCardSalesActivityMapper.batchUpdateActivityStatusStart(activityIds, CardActivityStatusEnum.RUNNING.getStatus(),
					CardActivityStatusEnum.PUBLISHED.getStatus(), -1L, "vipcard");
			memberCardInnerService.batchSaveActivityConfigLogs("自动上架", activityIds, CardActivityOperateEnum.START, OperatorDto.getSystemOp());
		}
		log.info("auto online end, size={} ...", (onlineList == null) ? 0 : onlineList.size());

		//活动下线
		if (CollectionUtils.isNotEmpty(offlineList)) {
			List<Long> activityIds = offlineList.stream().map(MmpCardSalesActivity::getId).collect(Collectors.toList());
			mmpCardSalesActivityMapper.batchUpdateActivityStatusStop(activityIds, CardActivityStatusEnum.STOPPED.getStatus(),
					CardActivityStatusEnum.RUNNING.getStatus(), -1L, "vipcard");
			memberCardInnerService.batchSaveActivityConfigLogs("自动下架", activityIds, CardActivityOperateEnum.STOP, OperatorDto.getSystemOp());
		}
		log.info("auto offline end, size={} ...", (offlineList == null) ? 0 : offlineList.size());

		//更新缓存
		for (MmpCardSalesActivity activity : offlineList) {
			//this.updateCache(activity.getId(), activity, CardActivityOperateEnum.STOP);
		}
		//更新缓存
		for (MmpCardSalesActivity activity : onlineList) {
			//this.updateCache(activity.getId(), activity, CardActivityOperateEnum.START);
		}
		log.info("autoUpdateTaskStatus end ...");
	}

}
