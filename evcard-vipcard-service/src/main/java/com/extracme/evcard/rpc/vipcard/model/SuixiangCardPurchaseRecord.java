package com.extracme.evcard.rpc.vipcard.model;

import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuiXiangCardPurchaseRecordDto;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardPaymentStatusEnum;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;

import java.math.BigDecimal;
import java.util.Date;

public class SuixiangCardPurchaseRecord {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.org_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private String orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.user_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Long userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.user_mobile
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private String userMobile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.card_base_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Long cardBaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.card_name
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private String cardName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.card_rent_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Long cardRentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.card_price_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Long cardPriceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.cdk_id
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    private Long cdkId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.issue_type
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Integer issueType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.payment_status
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Integer paymentStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.quantity
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Integer quantity;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.pay_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Date payTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.real_amount
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private BigDecimal realAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.out_trade_seq
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private String outTradeSeq;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.card_use_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Long cardUseId;

    private String cardUseIds;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.start_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Date startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.end_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Date endTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.order_seq
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private String orderSeq;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.pay_order_no
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private String payOrderNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.remind_status
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Integer remindStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.cancel_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Date cancelTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.merge_pay_origin
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Integer mergePayOrigin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.create_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.create_oper_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.create_oper_name
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.update_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.update_oper_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.update_oper_name
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record.is_deleted
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    private Integer isDeleted;

    private Date createTimeStart;

    private Date createTimeEnd;

    private Integer obtainType;

    public Integer getObtainType() {
        return obtainType;
    }

    public void setObtainType(Integer obtainType) {
        this.obtainType = obtainType;
    }

    public Date getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(Date createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public Date getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(Date createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.id
     *
     * @return the value of suixiang_card_purchase_record.id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.id
     *
     * @param id the value for suixiang_card_purchase_record.id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.org_id
     *
     * @return the value of suixiang_card_purchase_record.org_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.org_id
     *
     * @param orgId the value for suixiang_card_purchase_record.org_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.user_id
     *
     * @return the value of suixiang_card_purchase_record.user_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.user_id
     *
     * @param userId the value for suixiang_card_purchase_record.user_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.user_mobile
     *
     * @return the value of suixiang_card_purchase_record.user_mobile
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public String getUserMobile() {
        return userMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.user_mobile
     *
     * @param userMobile the value for suixiang_card_purchase_record.user_mobile
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.card_base_id
     *
     * @return the value of suixiang_card_purchase_record.card_base_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Long getCardBaseId() {
        return cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.card_base_id
     *
     * @param cardBaseId the value for suixiang_card_purchase_record.card_base_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setCardBaseId(Long cardBaseId) {
        this.cardBaseId = cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.card_name
     *
     * @return the value of suixiang_card_purchase_record.card_name
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public String getCardName() {
        return cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.card_name
     *
     * @param cardName the value for suixiang_card_purchase_record.card_name
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.card_rent_id
     *
     * @return the value of suixiang_card_purchase_record.card_rent_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Long getCardRentId() {
        return cardRentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.card_rent_id
     *
     * @param cardRentId the value for suixiang_card_purchase_record.card_rent_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setCardRentId(Long cardRentId) {
        this.cardRentId = cardRentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.card_price_id
     *
     * @return the value of suixiang_card_purchase_record.card_price_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Long getCardPriceId() {
        return cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.card_price_id
     *
     * @param cardPriceId the value for suixiang_card_purchase_record.card_price_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setCardPriceId(Long cardPriceId) {
        this.cardPriceId = cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.cdk_id
     *
     * @return the value of suixiang_card_purchase_record.cdk_id
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public Long getCdkId() {
        return cdkId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.cdk_id
     *
     * @param cdkId the value for suixiang_card_purchase_record.cdk_id
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public void setCdkId(Long cdkId) {
        this.cdkId = cdkId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.issue_type
     *
     * @return the value of suixiang_card_purchase_record.issue_type
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Integer getIssueType() {
        return issueType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.issue_type
     *
     * @param issueType the value for suixiang_card_purchase_record.issue_type
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setIssueType(Integer issueType) {
        this.issueType = issueType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.payment_status
     *
     * @return the value of suixiang_card_purchase_record.payment_status
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Integer getPaymentStatus() {
        return paymentStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.payment_status
     *
     * @param paymentStatus the value for suixiang_card_purchase_record.payment_status
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setPaymentStatus(Integer paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.quantity
     *
     * @return the value of suixiang_card_purchase_record.quantity
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.quantity
     *
     * @param quantity the value for suixiang_card_purchase_record.quantity
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.pay_time
     *
     * @return the value of suixiang_card_purchase_record.pay_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Date getPayTime() {
        return payTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.pay_time
     *
     * @param payTime the value for suixiang_card_purchase_record.pay_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.real_amount
     *
     * @return the value of suixiang_card_purchase_record.real_amount
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public BigDecimal getRealAmount() {
        return realAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.real_amount
     *
     * @param realAmount the value for suixiang_card_purchase_record.real_amount
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.out_trade_seq
     *
     * @return the value of suixiang_card_purchase_record.out_trade_seq
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public String getOutTradeSeq() {
        return outTradeSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.out_trade_seq
     *
     * @param outTradeSeq the value for suixiang_card_purchase_record.out_trade_seq
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setOutTradeSeq(String outTradeSeq) {
        this.outTradeSeq = outTradeSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.card_use_id
     *
     * @return the value of suixiang_card_purchase_record.card_use_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Long getCardUseId() {
        return cardUseId;
    }

    public String getCardUseIds() {
        return cardUseIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.card_use_id
     *
     * @param cardUseId the value for suixiang_card_purchase_record.card_use_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setCardUseId(Long cardUseId) {
        this.cardUseId = cardUseId;
    }

    public void setCardUseIds(String cardUseIds) {
        this.cardUseIds = cardUseIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.start_time
     *
     * @return the value of suixiang_card_purchase_record.start_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.start_time
     *
     * @param startTime the value for suixiang_card_purchase_record.start_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.end_time
     *
     * @return the value of suixiang_card_purchase_record.end_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.end_time
     *
     * @param endTime the value for suixiang_card_purchase_record.end_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.order_seq
     *
     * @return the value of suixiang_card_purchase_record.order_seq
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public String getOrderSeq() {
        return orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.order_seq
     *
     * @param orderSeq the value for suixiang_card_purchase_record.order_seq
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.pay_order_no
     *
     * @return the value of suixiang_card_purchase_record.pay_order_no
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public String getPayOrderNo() {
        return payOrderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.pay_order_no
     *
     * @param payOrderNo the value for suixiang_card_purchase_record.pay_order_no
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.remind_status
     *
     * @return the value of suixiang_card_purchase_record.remind_status
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Integer getRemindStatus() {
        return remindStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.remind_status
     *
     * @param remindStatus the value for suixiang_card_purchase_record.remind_status
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setRemindStatus(Integer remindStatus) {
        this.remindStatus = remindStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.cancel_time
     *
     * @return the value of suixiang_card_purchase_record.cancel_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Date getCancelTime() {
        return cancelTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.cancel_time
     *
     * @param cancelTime the value for suixiang_card_purchase_record.cancel_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.merge_pay_origin
     *
     * @return the value of suixiang_card_purchase_record.merge_pay_origin
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Integer getMergePayOrigin() {
        return mergePayOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.merge_pay_origin
     *
     * @param mergePayOrigin the value for suixiang_card_purchase_record.merge_pay_origin
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setMergePayOrigin(Integer mergePayOrigin) {
        this.mergePayOrigin = mergePayOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.create_time
     *
     * @return the value of suixiang_card_purchase_record.create_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.create_time
     *
     * @param createTime the value for suixiang_card_purchase_record.create_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.create_oper_id
     *
     * @return the value of suixiang_card_purchase_record.create_oper_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.create_oper_id
     *
     * @param createOperId the value for suixiang_card_purchase_record.create_oper_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.create_oper_name
     *
     * @return the value of suixiang_card_purchase_record.create_oper_name
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.create_oper_name
     *
     * @param createOperName the value for suixiang_card_purchase_record.create_oper_name
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.update_time
     *
     * @return the value of suixiang_card_purchase_record.update_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.update_time
     *
     * @param updateTime the value for suixiang_card_purchase_record.update_time
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.update_oper_id
     *
     * @return the value of suixiang_card_purchase_record.update_oper_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_purchase_record.update_oper_id
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.update_oper_name
     *
     * @return the value of suixiang_card_purchase_record.update_oper_name
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_purchase_record.update_oper_name
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record.is_deleted
     *
     * @return the value of suixiang_card_purchase_record.is_deleted
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record.is_deleted
     *
     * @param isDeleted the value for suixiang_card_purchase_record.is_deleted
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }


    public static SuiXiangCardPurchaseRecordDto toDto(SuixiangCardPurchaseRecord record) {
        SuiXiangCardPurchaseRecordDto cardPurchaseRecordDto = new SuiXiangCardPurchaseRecordDto();
        try {
            BeanCopyUtils.copyProperties(record, cardPurchaseRecordDto);
            cardPurchaseRecordDto.setPurchaseId(record.getId());
            cardPurchaseRecordDto.setCardType(2);
            cardPurchaseRecordDto.setRealAmount(record.getRealAmount().doubleValue());
            if (record.getPayTime() != null) {
                cardPurchaseRecordDto.setPayTime(DateUtil.getFormatDate(record.getPayTime(), DateUtil.DATE_TYPE7));
            }
            if (record.getCancelTime() != null) {
                cardPurchaseRecordDto.setCancelTime(DateUtil.getFormatDate(record.getCancelTime(), DateUtil.DATE_TYPE7));
            }
            if (record.getPaymentStatus() == SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus()) {
                Date createTime = record.getCreateTime();
                Date planCancelTime = DateUtil.addMin(createTime, 5);
                cardPurchaseRecordDto.setPlanCancelTime(DateUtil.getFormatDate(planCancelTime, DateUtil.simple));
            }
            cardPurchaseRecordDto.setActivityName(record.getCardName());
        } catch (Exception e) {
        }
        return cardPurchaseRecordDto;
    }
}