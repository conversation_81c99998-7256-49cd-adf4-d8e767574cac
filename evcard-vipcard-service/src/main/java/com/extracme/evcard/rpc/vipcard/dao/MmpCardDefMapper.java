package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.dto.CardModelQueryDto;
import com.extracme.evcard.rpc.vipcard.dto.CardModelQueryParamDto;
import com.extracme.evcard.rpc.vipcard.model.MmpCardDef;
import com.extracme.evcard.rpc.vipcard.model.MmpCardDefInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface MmpCardDefMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_def
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    int deleteByPrimaryKey(Long cardId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_def
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    int insert(MmpCardDef record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_def
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    int insertSelective(MmpCardDef record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_def
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    MmpCardDef selectByPrimaryKey(Long cardId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_def
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    int updateByPrimaryKeySelective(MmpCardDef record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_def
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    int updateByPrimaryKey(MmpCardDef record);

    /**
     * 查询相同优惠券限制的卡片模板
     * @param cardId
     * @return
     */
    MmpCardDef selectMatchCardModel(MmpCardDef cardId);

    /**
     * 添加卡片
     * @param record
     * @return
     */
    int add(MmpCardDef record);

    /**
     * 根据卡片ID查询当前被使用的卡片
     * @param cardId
     * @return
     */
    Long selectOneActivityByCard(Long cardId);

    /**
     * 查询引用了指定卡片的活动信息， 配置使用
     * @param cardIds, 最多50
     * @return
     */
    List<Long> batchSelectActivityByCardIds(@Param("cardIds") Set<Long> cardIds);

    /**
     * 列表查询卡片配置信息
     * @param queryDto
     * @return
     * @remark (包含模糊查询，配置系统使用), 交易不可用
     */
    List<MmpCardDefInfo> selectList(CardModelQueryParamDto queryDto);

    int updateStatusStop(@Param("cardId") Long cardId,
                         @Param("operId") Long operId,
                         @Param("operUser") String operUser);

    int updateStatusStart(@Param("cardId") Long cardId,
                          @Param("operId") Long operId,
                          @Param("operUser") String operUser);

    /**
     * 根据id列表批量查询卡信息
     * @param cardIdList
     * @return
     */
    List<MmpCardDef> batchQueryByIdList(@Param("cardIdList") List<Long> cardIdList);
}