<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.MmpCardPurchaseRecordLogMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecordLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 22 16:27:09 CST 2021.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="purchase_id" property="purchaseId" jdbcType="BIGINT" />
    <result column="operation_type" property="operationType" jdbcType="INTEGER" />
    <result column="out_trade_seq" property="outTradeSeq" jdbcType="VARCHAR" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 22 16:27:09 CST 2021.
    -->
    id, purchase_id, operation_type, out_trade_seq, content, misc_desc, status, create_time, 
    create_oper_id, create_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 22 16:27:09 CST 2021.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_purchase_record_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 22 16:27:09 CST 2021.
    -->
    delete from ${issSchema}.mmp_card_purchase_record_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecordLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 22 16:27:09 CST 2021.
    -->
    insert into ${issSchema}.mmp_card_purchase_record_log (id, purchase_id, operation_type,
      out_trade_seq, content, misc_desc, 
      status, create_time, create_oper_id, 
      create_oper_name)
    values (#{id,jdbcType=BIGINT}, #{purchaseId,jdbcType=BIGINT}, #{operationType,jdbcType=INTEGER}, 
      #{outTradeSeq,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{miscDesc,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecordLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 22 16:27:09 CST 2021.
    -->
    insert into ${issSchema}.mmp_card_purchase_record_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="purchaseId != null" >
        purchase_id,
      </if>
      <if test="operationType != null" >
        operation_type,
      </if>
      <if test="outTradeSeq != null" >
        out_trade_seq,
      </if>
      <if test="content != null" >
        content,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="purchaseId != null" >
        #{purchaseId,jdbcType=BIGINT},
      </if>
      <if test="operationType != null" >
        #{operationType,jdbcType=INTEGER},
      </if>
      <if test="outTradeSeq != null" >
        #{outTradeSeq,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecordLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 22 16:27:09 CST 2021.
    -->
    update ${issSchema}.mmp_card_purchase_record_log
    <set >
      <if test="purchaseId != null" >
        purchase_id = #{purchaseId,jdbcType=BIGINT},
      </if>
      <if test="operationType != null" >
        operation_type = #{operationType,jdbcType=INTEGER},
      </if>
      <if test="outTradeSeq != null" >
        out_trade_seq = #{outTradeSeq,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecordLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 22 16:27:09 CST 2021.
    -->
    update ${issSchema}.mmp_card_purchase_record_log
    set purchase_id = #{purchaseId,jdbcType=BIGINT},
      operation_type = #{operationType,jdbcType=INTEGER},
      out_trade_seq = #{outTradeSeq,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>