<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.MmpUserCardDiscountInfoMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.MmpUserCardDiscountInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 17 16:12:05 CST 2021.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_card_no" property="userCardNo" jdbcType="BIGINT" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="total_discount_amount" property="totalDiscountAmount" jdbcType="DECIMAL" />
    <result column="discount_amount" property="discountAmount" jdbcType="DECIMAL" />
    <result column="frozen_amount" property="frozenAmount" jdbcType="DECIMAL" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 17 16:12:05 CST 2021.
    -->
    id, user_card_no, start_time, end_time, total_discount_amount, discount_amount, frozen_amount, status,
    create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 17 16:12:05 CST 2021.
    -->
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_card_discount_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="userCardDiscount" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_card_discount_info
    where user_card_no = #{userCardNo}
    and start_time &lt;= now()
    and end_time &gt; now()
    and status = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 17 16:12:05 CST 2021.
    -->
    delete from ${issSchema}.mmp_user_card_discount_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <update id="cancelUserCard">
    update ${issSchema}.mmp_user_card_discount_info
    set status = 1
    where user_card_no = #{userCardNo}
    and end_time &gt; now()
  </update>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardDiscountInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 17 16:12:05 CST 2021.
    -->
    insert into ${issSchema}.mmp_user_card_discount_info (id, user_card_no, start_time,
      end_time, total_discount_amount, discount_amount, 
      status, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{userCardNo,jdbcType=BIGINT}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{totalDiscountAmount,jdbcType=DECIMAL}, #{discountAmount,jdbcType=DECIMAL}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardDiscountInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 17 16:12:05 CST 2021.
    -->
    insert into ${issSchema}.mmp_user_card_discount_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userCardNo != null" >
        user_card_no,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="totalDiscountAmount != null" >
        total_discount_amount,
      </if>
      <if test="discountAmount != null" >
        discount_amount,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userCardNo != null" >
        #{userCardNo,jdbcType=BIGINT},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalDiscountAmount != null" >
        #{totalDiscountAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null" >
        #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardDiscountInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 17 16:12:05 CST 2021.
    -->
    update ${issSchema}.mmp_user_card_discount_info
    <set >
      <if test="userCardNo != null" >
        user_card_no = #{userCardNo,jdbcType=BIGINT},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalDiscountAmount != null" >
        total_discount_amount = #{totalDiscountAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null" >
        discount_amount = #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardDiscountInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 17 16:12:05 CST 2021.
    -->
    update ${issSchema}.mmp_user_card_discount_info
    set user_card_no = #{userCardNo,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      total_discount_amount = #{totalDiscountAmount,jdbcType=DECIMAL},
      discount_amount = #{discountAmount,jdbcType=DECIMAL},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectPage" resultType="com.extracme.evcard.rpc.vipcard.dto.UserCardDiscountViewDto">
    select
    id as Id, user_card_no as userCardNo, start_time as startTime, end_time as endTime,
    total_discount_amount as totalDiscountAmount,
    discount_amount - frozen_amount as discountAmount,
    frozen_amount as frozenAmount,
    total_discount_amount - discount_amount as balanceDiscount,
    status as status,
    create_time as createTime, create_oper_id as createOperId,
    create_oper_name as createOperName, update_time as updateTime,
    update_oper_id as updateOperId, update_oper_name as updateOperName
    from ${issSchema}.mmp_user_card_discount_info
    where user_card_no = #{userCardNo}
    <if test="endTime != null" >
      and start_time &lt;= #{endTime}
    </if>
    <if test="startTime != null" >
      and end_time &gt; #{startTime}
    </if>
    order by id desc
  </select>

  <select id="getUserDiscountByTime" resultMap="BaseResultMap">
    select *
    from ${issSchema}.mmp_user_card_discount_info
    <where>
      start_time &lt;= #{date}
      and end_time &gt; #{date}
      and user_card_no = #{userCardNo}
    </where>
  </select>

  <update id="freezeDiscountAmount">
    update ${issSchema}.mmp_user_card_discount_info
    set
    discount_amount = discount_amount + #{frozenAmount,jdbcType=DECIMAL},
    frozen_amount = frozen_amount + #{frozenAmount,jdbcType=DECIMAL},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    update_oper_id = #{userId},
    update_oper_name = #{userName}
    <where>
      id = #{id,jdbcType=BIGINT}
      and discount_amount = #{discountAmount,jdbcType=DECIMAL}
    </where>
  </update>

  <update id="unfreezeDiscountAmount">
    update ${issSchema}.mmp_user_card_discount_info
    set
    discount_amount = discount_amount - #{unfreezeAmount,jdbcType=DECIMAL},
    frozen_amount = frozen_amount - #{unfreezeAmount,jdbcType=DECIMAL},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    update_oper_id = #{userId},
    update_oper_name = #{userName}
    <where>
    id = #{id,jdbcType=BIGINT}
    and discount_amount = #{discountAmount,jdbcType=DECIMAL}
    </where>
  </update>

  <update id="deductFrozenDiscountAmount">
    update ${issSchema}.mmp_user_card_discount_info
    set
    frozen_amount = frozen_amount - #{deductAmount,jdbcType=DECIMAL},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    update_oper_id = #{userId},
    update_oper_name = #{userName}
    <where>
      id = #{id,jdbcType=BIGINT}
      and frozen_amount = #{frozenAmount,jdbcType=DECIMAL}
    </where>
  </update>

  <select id="userCardFrozenDiscounts" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_card_discount_info
    where user_card_no = #{userCardNo}
    and start_time &lt;= now()
    and end_time &gt; now()
    and frozen_amount &gt; 0
    and status = 0
    limit 1
  </select>

</mapper>