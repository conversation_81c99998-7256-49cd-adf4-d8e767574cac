package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.vipcard.dao.MmpCardConfigOperationLogMapper;
import com.extracme.evcard.rpc.vipcard.dao.MmpCardDefMapper;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.enums.CardActivityOperateEnum;
import com.extracme.evcard.rpc.vipcard.enums.CardTypeEnum;
import com.extracme.evcard.rpc.vipcard.model.MmpCardDef;
import com.extracme.evcard.rpc.vipcard.model.MmpCardDefInfo;
import com.extracme.evcard.rpc.vipcard.service.inner.*;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/23
 *
 * TODO 缓存/日志
 */
@Slf4j
@Service
public class CardModelService implements ICardModelService {

    @Resource
    private MemberCardInnerService memberCardInnerService;

    @Resource
    private OrgService orgService;

    @Resource
    private MmpCardDefMapper mmpCardDefMapper;

    @Resource
    private MmpCardConfigOperationLogMapper mmpCardConfigOperationLogMapper;

    private static final Integer STATUS_ENABLE = 0;
    private static final Integer STATUS_DISABLE = 1;

    @Override
    public BaseResponse add(CardConfigDto configDTO, OperatorDto operateDTO) {
        //1.0 检查卡片配置参数
        BaseResponse checkResp = checkConfigInfo(configDTO, true);
        if(!Constants.STATUS_OK.equals(checkResp.getCode())) {
            return checkResp;
        }
        BaseResponse resp = new BaseResponse();
        //2.1 活动数据保存
        MmpCardDef mmpCardDef = new MmpCardDef();
        BeanCopyUtils.copyProperties(configDTO, mmpCardDef);
        createCustomSettings(mmpCardDef, configDTO);
        //1.1 检查相同卡片
        checkResp = checkSameCardExist(mmpCardDef);
        if(!Constants.STATUS_OK.equals(checkResp.getCode())) {
            return checkResp;
        }
        mmpCardDef.setCreateOperId(operateDTO.getOperatorId());
        mmpCardDef.setCreateOperName(operateDTO.getOperatorName());
        mmpCardDef.setCreateTime(new Date());
        mmpCardDef.setUpdateOperId(operateDTO.getOperatorId());
        mmpCardDef.setUpdateOperName(operateDTO.getOperatorName());
        mmpCardDef.setUpdateTime(mmpCardDef.getCreateTime());
        mmpCardDef.setStatus(STATUS_ENABLE);
        mmpCardDefMapper.add(mmpCardDef);
        //4 后处理
        operateDTO.setRemark("创建并启用");
        //createPostProcess(taskCreateInputDTO, taskInfo, taskConfig);
        postProcess(mmpCardDef.getCardId(), mmpCardDef, CardActivityOperateEnum.CREATE, operateDTO);
        resp.setMessage("提交成功");
        return resp;
    }

    @Override
    public BaseResponse update(CardConfigDto configDTO, OperatorDto operateDTO) {
        BaseResponse resp = new BaseResponse();
        //1.1 校验卡片状态
        Long cardId = configDTO.getCardId();
        MmpCardDef oldCardDef = mmpCardDefMapper.selectByPrimaryKey(cardId);
        if (oldCardDef == null) {
            resp.setCode(-1);
            resp.setMessage("卡片不存在");
            return resp;
        }
        //1.2. 校验卡片是否被活动所引用，若被引用则不可编辑
        Long activityId = mmpCardDefMapper.selectOneActivityByCard(cardId);
        if(activityId != null) {
            resp.setCode(-1);
            resp.setMessage("卡片被销售活动[" + activityId + "]引用，不可编辑");
            return resp;
        }
        //1.3. 检查活动配置参数
        BaseResponse checkResp = checkConfigInfo(configDTO, false);
        if(!Constants.STATUS_OK.equals(checkResp.getCode())) {
            return checkResp;
        }
        //时间冲突暂不检查
        //2.1 卡片数据保存
        MmpCardDef mmpCardDef = new MmpCardDef();
        BeanCopyUtils.copyProperties(configDTO, mmpCardDef);
        createCustomSettings(mmpCardDef, configDTO);
        mmpCardDef.setUpdateOperId(operateDTO.getOperatorId());
        mmpCardDef.setUpdateOperName(operateDTO.getOperatorName());
        mmpCardDef.setUpdateTime(new Date());
        //1.4 检查相同卡片
        checkResp = checkSameCardExist(mmpCardDef);
        if(!Constants.STATUS_OK.equals(checkResp.getCode())) {
            return checkResp;
        }
        mmpCardDefMapper.updateByPrimaryKeySelective(mmpCardDef);

        //4 后处理
        operateDTO.setRemark("修改");
        //createPostProcess(taskCreateInputDTO, taskInfo, taskConfig);
        postProcess(mmpCardDef.getCardId(), mmpCardDef, CardActivityOperateEnum.UPDATE, operateDTO);
        resp.setMessage("提交成功");
        return resp;
    }

    private String getValidWeekDays(List<Integer> days){
        String weekDayStr = StringUtils.EMPTY;
        if(CollectionUtils.isNotEmpty(days)) {
            Collections.sort(days);
            if(days.size() > 7 || days.get(days.size() - 1) > 7 || days.get(0) < 1) {
                return null;
            }
            weekDayStr = ComUtils.sortJoin(days, Constants.STR_COMMA, true);
            if(weekDayStr.contains("1,2,3,4,5,6,7")){
                weekDayStr = StringUtils.EMPTY;
            }
        }
        return weekDayStr;
    }


    public BaseResponse postProcess(Long id, MmpCardDef cardDef, CardActivityOperateEnum opType, OperatorDto operateDTO) {
        //1 记录操作日志
        memberCardInnerService.saveCardConfigLog(operateDTO.getRemark(), id, opType, operateDTO);
        //2 后处理-各活动自定义
        //customPostProcess(taskInfo, operator);
        //3 更新活动redis缓存
        //updateCache(id, taskInfo, operator);
        return new BaseResponse(0, operateDTO.getRemark() + "成功");
    }

    private BaseResponse checkSameCardExist(MmpCardDef cardDef) {
        BaseResponse resp = new BaseResponse();
        MmpCardDef card = mmpCardDefMapper.selectMatchCardModel(cardDef);
        if(card != null) {
            resp.setCode(-1);
            resp.setMessage("已存在相同限制条件的卡片【" + card.getCardId() + card.getCardName() + "】");
            return resp;
        }
        return resp;
    }

    @Override
    public BaseResponse disable(Long id, OperatorDto operateDTO) {
        /**
         * 增加发布逻辑
         */
        Long operatorId = operateDTO.getOperatorId();
        String operatorName = operateDTO.getOperatorName();
        String remark = "禁用";
        if(StringUtils.isBlank(operateDTO.getRemark())) {
            operateDTO.setRemark(remark);
        }
        //1. 检查活动状态
        MmpCardDef cardDef = mmpCardDefMapper.selectByPrimaryKey(id);
        if (cardDef == null) {
            return new BaseResponse(-1,"卡片不存在，无法禁用");
        }
        if (!STATUS_ENABLE.equals(cardDef.getStatus())) {
            return new BaseResponse(-1,"卡片状态已变更，请刷新后重试");
        }
        //2. 卡片禁用
        int count = mmpCardDefMapper.updateStatusStop(id, operatorId, operatorName);
        if (count < 1) {
            return new BaseResponse(-1,remark + "失败");
        }
        //4. 后处理
        return postProcess(id, cardDef, CardActivityOperateEnum.STOP, operateDTO);
    }

    @Override
    public BaseResponse enable(Long id, OperatorDto operateDTO) {
        /**
         * 增加发布逻辑
         */
        Long operatorId = operateDTO.getOperatorId();
        String operatorName = operateDTO.getOperatorName();
        String remark = "启用";
        if(StringUtils.isBlank(operateDTO.getRemark())) {
            operateDTO.setRemark(remark);
        }
        //1. 检查活动状态
        MmpCardDef cardDef = mmpCardDefMapper.selectByPrimaryKey(id);
        if (cardDef == null) {
            return new BaseResponse(-1,"卡片不存在，无法启用");
        }
        if (!STATUS_DISABLE.equals(cardDef.getStatus())) {
            return new BaseResponse(-1,"卡片状态已变更，请刷新后重试");
        }
        //1.1 检查相同卡片是否存在
        BaseResponse checkResp = checkSameCardExist(cardDef);
        if(!Constants.STATUS_OK.equals(checkResp.getCode())) {
            return checkResp;
        }
        //2. 卡片启用
        int count = mmpCardDefMapper.updateStatusStart(id, operatorId, operatorName);
        if (count < 1) {
            return new BaseResponse(-1,remark + "失败");
        }
        //4. 后处理
        return postProcess(id, cardDef, CardActivityOperateEnum.START, operateDTO);
    }

    @Override
    public CardModelDetailDto getCardModelById(Long id) {
        MmpCardDef cardDef = mmpCardDefMapper.selectByPrimaryKey(id);
        if(cardDef == null) {
            return null;
        }
        CardModelDetailDto cardDetailDto = new CardModelDetailDto();
        BeanCopyUtils.copyProperties(cardDef, cardDetailDto);
        //0. 机构名称
        String orgName = orgService.getOrgNameByOrgId(cardDef.getOrgId());
        cardDetailDto.setOrgName(orgName);
        defToCardModelDetailDto(cardDef, cardDetailDto);
        return cardDetailDto;
    }

    @Override
    public CardModelListViewDto getCardModelDetailById(Long id) {
        CardModelDetailDto cardDef = getCardModelById(id);
        if(cardDef == null) {
            return null;
        }
        CardModelListViewDto dto = new CardModelListViewDto();
        BeanCopyUtils.copyProperties(cardDef, dto);
        dto.setCityNames(memberCardInnerService.getCityDesc(cardDef.getCityLimit()));
        //更换为商品车型
        //dto.setVehicleModelNames(memberCardInnerService.getVehicleModelDesc(cardDef.getVehicleModelLimit()));
        dto.setVehicleModelNames(memberCardInnerService.getGoodsVehicleModelDesc(cardDef.getGoodsModelId()));
        dto.setRentMethodNames(memberCardInnerService.getRentMethodDesc(cardDef.getRentMethod()));
        dto.setStoreNames(memberCardInnerService.getStoreDesc(cardDef.getStoreIds()));
        dto.setRentMethods(ComUtils.getIntList(cardDef.getRentMethod()));
        dto.setRentMethodGroups(ComUtils.getIntList(cardDef.getRentMethodGroup()));
        /**
         * 历史数据，包含具体产品线，则详情时转换为对应的大类数据
         */
        if(CollectionUtils.isNotEmpty(dto.getRentMethods()) && CollectionUtils.isEmpty(dto.getRentMethodGroups())) {
            dto.setRentMethodGroups(memberCardInnerService.convertRentMethodGroup(dto.getRentMethods()));
        }
        dto.setRentMethodGroupNames(memberCardInnerService.getRentMethodGroupDesc(cardDef.getRentMethodGroup()));
        return dto;
    }

    @Override
    public PageBeanBO<CardModelListViewDto> queryPage(CardModelQueryDto queryDto) {
        PageBeanBO<CardModelListViewDto> pageBeanBO = new PageBeanBO<>();
        if(queryDto.getPageNum() == null) {
            queryDto.setPageNum(1);
        }
        if(queryDto.getPageSize() == null) {
            queryDto.setPageSize(10);
        }
        Page page;
        if (queryDto.getIsAll() == null || queryDto.getIsAll() == 0) {
            page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize(), false);
        } else {
            page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        }
        /*if(CollectionUtils.isNotEmpty(queryDto.getRentMethods())) {
            Collections.sort(queryDto.getRentMethods());
        }*/
        CardModelQueryParamDto queryParamDto = new CardModelQueryParamDto();
        BeanCopyUtils.copyProperties(queryDto, queryParamDto);
        if(CollectionUtils.isNotEmpty(queryDto.getRentMethods())) {
            String rentMethodStr = ComUtils.sortJoin(queryDto.getRentMethods(), Constants.STR_COMMA, true);
            queryParamDto.setRentMethodStr(rentMethodStr);
        }
        if(CollectionUtils.isNotEmpty(queryDto.getRentMethodGroups())) {
            String rentMethodGroupStr = ComUtils.sortJoin(queryDto.getRentMethodGroups(), Constants.STR_COMMA, true);
            queryParamDto.setRentMethodGroupStr(rentMethodGroupStr);
        }
        List<MmpCardDefInfo> list = mmpCardDefMapper.selectList(queryParamDto);
        /**
         * 查询已被引用的卡
         */
        List<Long> activityCards = null;
        Set<Long> cardIds = new HashSet<>();
        for(MmpCardDefInfo cardDef : list) {
            cardIds.add(cardDef.getCardId());
        }
        if(CollectionUtils.isNotEmpty(cardIds)) {
            activityCards = mmpCardDefMapper.batchSelectActivityByCardIds(cardIds);
        }
        List<CardModelListViewDto> result = new ArrayList<>();
        for(MmpCardDefInfo cardDef : list) {
            CardModelListViewDto dto = defToViewDto(cardDef);
            //是否可编辑
            if(activityCards != null && activityCards.contains(cardDef.getCardId())) {
                dto.setEditAvailable(0);
            }
            result.add(dto);
        }
        PageBO pageBO = new PageBO();
        pageBO.setTotal(page.getTotal());
        pageBO.setPageNum(page.getPageNum());
        pageBO.setPageSize(page.getPageSize());
        pageBeanBO.setPage(pageBO);
        pageBeanBO.setList(result);
        return pageBeanBO;
    }

    @Override
    public CardModelViewDto getCardViewById(Long id) {
        return null;
    }

    private CardModelListViewDto defToViewDto(MmpCardDef cardDef){
        CardModelListViewDto dto = new CardModelListViewDto();
        BeanCopyUtils.copyProperties(cardDef, dto);
        defToCardModelDetailDto(cardDef, dto);
        dto.setCreateTime(DateUtil.getFormatDate(cardDef.getCreateTime(), DateUtil.simple));
        dto.setUpdateTime(DateUtil.getFormatDate(cardDef.getUpdateTime(), DateUtil.simple));
        dto.setCityNames(memberCardInnerService.getCityDesc(cardDef.getCityLimit()));
        //更换为商品车型
        //dto.setVehicleModelNames(memberCardInnerService.getVehicleModelDesc(cardDef.getVehicleModelLimit()));
        dto.setVehicleModelNames(memberCardInnerService.getGoodsVehicleModelDesc(cardDef.getGoodsModelId()));
        dto.setStoreNames(memberCardInnerService.getStoreDesc(cardDef.getStoreIds()));
        dto.setRentMethodNames(memberCardInnerService.getRentMethodDesc(cardDef.getRentMethod()));
        dto.setRentMethodGroupNames(memberCardInnerService.getRentMethodGroupDesc(cardDef.getRentMethodGroup()));
        return dto;
    }

    public void defToCardModelDetailDto(MmpCardDef cardDef, CardModelDetailDto cardDetailDto) {
        /**
         * 1. 可用天
         */
        List<Integer> weekDays = ComUtils.getIntList(cardDef.getAvailableDaysOfWeek());
        if(CollectionUtils.isEmpty(weekDays)) {
            weekDays = Arrays.asList(1,2,3,4,5,6,7);
        }
        cardDetailDto.setAvailableDaysOfWeekLimit(ComUtils.sortJoin(weekDays, Constants.STR_COMMA, true));
        cardDetailDto.setAvailableDaysOfWeek(weekDays);
        /**
         * 2. 可用时段
         */
        if(StringUtils.isBlank(cardDef.getStartTime()) && StringUtils.isBlank(cardDef.getEndTime())) {
            cardDetailDto.setStartTime("000000");
            cardDetailDto.setEndTime("240000");
        }
        // 3. 可用区域
        cardDetailDto.setCityIds(ComUtils.getLongList(cardDef.getCityLimit()));
        // 4. 可用车型
        cardDetailDto.setVehicleModels(ComUtils.getLongList(cardDef.getVehicleModelLimit()));
        // 5. 可用门店
        cardDetailDto.setStoreIdList(ComUtils.getLongList(cardDef.getStoreIds()));
        cardDetailDto.setCreateTime(DateUtil.getFormatDate(cardDef.getCreateTime(), DateUtil.simple));
        cardDetailDto.setUpdateTime(DateUtil.getFormatDate(cardDef.getUpdateTime(), DateUtil.simple));
        //最小用车时长限制
        if(NumberUtils.INTEGER_ZERO.equals(cardDetailDto.getDurationLimit())) {
            cardDetailDto.setDurationLimit(null);
        }
    }



    private void createCustomSettings(MmpCardDef mmpCardDef, CardConfigDto configDTO) {
        /**
         * 1. 处理有效期
         */
        if(configDTO.getCardType() == null) {
            //缺省为月卡
            configDTO.setCardType(1);
        }
        Integer validDays = 0;
        CardTypeEnum cardTypeEnum = CardTypeEnum.getCardType(configDTO.getCardType());
        validDays = cardTypeEnum.getDays();
        mmpCardDef.setValidTimeType(0);
        mmpCardDef.setEffectiveDays(0);
        mmpCardDef.setValidDays(validDays);
        /**
         * 2. 处理产品线限制
         */
        String rentMethods = ComUtils.sortJoin(configDTO.getRentMethods(), Constants.STR_COMMA, true);
        mmpCardDef.setRentMethod(rentMethods);
        String rentMethodGroups = ComUtils.sortJoin(configDTO.getRentMethodGroups(), Constants.STR_COMMA, true);
        mmpCardDef.setRentMethodGroup(rentMethodGroups);
        //处理历史卡片，若原本指定具体产品线模式，修改后指定了具体产品线大类，则以产品线大类为准。
        if(rentMethodGroups != null) {
            mmpCardDef.setRentMethod(StringUtils.EMPTY);
        }

        /**
         * 3. 处理可用区域
         */
        String cityLimit = ComUtils.sortJoin(configDTO.getCityIds(), Constants.STR_COMMA, true);
        mmpCardDef.setCityLimit(cityLimit);
        /**
         * 4. 处理车型限制
         */
        String vehicleModelLimit = ComUtils.sortJoin(configDTO.getVehicleModels(), Constants.STR_COMMA, true);
        mmpCardDef.setVehicleModelLimit(vehicleModelLimit);
        //处理商品车型
        String goodsVehicleModelLimit = ComUtils.sortJoin(configDTO.getGoodsModelIds(), Constants.STR_COMMA, true);
        mmpCardDef.setGoodsModelId(goodsVehicleModelLimit);

        /**
         * 5. 处理门店限制
         */
        String storeIdsLimit = ComUtils.sortJoin(configDTO.getStoreIds(), Constants.STR_COMMA, true);
        mmpCardDef.setStoreIds(storeIdsLimit);

        //5. 可用天限制
        String weekDaysLimit = getValidWeekDays(configDTO.getAvailableDaysOfWeek());
        mmpCardDef.setAvailableDaysOfWeek(weekDaysLimit);
        //5. 可用时段限制
        if(StringUtils.equals(mmpCardDef.getStartTime(), "000000")
                && StringUtils.equals(mmpCardDef.getStartTime(), "240000")) {
            mmpCardDef.setStartTime(StringUtils.EMPTY);
            mmpCardDef.setEndTime(StringUtils.EMPTY);
        }
    }

    private int[] CARD_TYPES = {0, 1, 2};
    private BaseResponse checkConfigInfo(CardConfigDto configDTO, boolean isCreate) {
        BaseResponse vo = new BaseResponse();
        if (null == configDTO) {
            vo.setCode(-1);
            vo.setMessage("卡片配置参数不完整");
            return vo;
        }
        if (StringUtils.isBlank(configDTO.getCardName())) {
            vo.setCode(-1);
            vo.setMessage("请填写卡片名称");
            return vo;
        }
        if (StringUtils.isNotBlank(configDTO.getCardName()) &&
                configDTO.getCardName().length() > 14) {
            vo.setCode(-1);
            vo.setMessage("卡片名称长度不满足要求，长度不能超过14个字符");
            return vo;
        }
        //卡片使用说明
        if (isCreate && StringUtils.isBlank(configDTO.getRules())) {
            vo.setCode(-1);
            vo.setMessage("请填写卡片规则");
            return vo;
        }
        //卡片使用说明
        if (StringUtils.isNotBlank(configDTO.getRules())
                && configDTO.getRules().length() > 100) {
            vo.setCode(-1);
            vo.setMessage("卡片使用说明不满足要求，长度不能超过100个字符");
            return vo;
        }
        if (!ArrayUtils.contains(CARD_TYPES, configDTO.getCardType())) {
            vo.setCode(-1);
            vo.setMessage("请选择卡片类型为周卡、月卡、季卡");
            return vo;
        }
        if(configDTO.getPurchaseType() == null) {
            configDTO.setPurchaseType(0);
        }
        if(configDTO.getStyleType() == null) {
            configDTO.setStyleType(1);
        }
        if(isCreate && configDTO.getStyleType() == 0 && StringUtils.isBlank(configDTO.getBackUrl())) {
            vo.setCode(-1);
            vo.setMessage("请上传自定义卡面背景图");
            return vo;
        }
        if(isCreate && StringUtils.isBlank(configDTO.getBackUrl())) {
            vo.setCode(-1);
            vo.setMessage("卡面样式图片不能为空");
            return vo;
        }
        if(configDTO.getBackUrl() == null) {
            configDTO.setBackUrl(StringUtils.EMPTY);
        }
        if(StringUtils.isNotBlank(configDTO.getBackUrl()) && !StringUtils.startsWith("/", configDTO.getBackUrl())) {
            configDTO.setBackUrl("/" + configDTO.getBackUrl());
        }
        if (StringUtils.isBlank(configDTO.getOrgId())) {
            vo.setCode(-1);
            vo.setMessage("请选择运营公司");
            return vo;
        }
        //最小用车市场，缺省值为0
        if(configDTO.getDurationLimit() == null) {
            configDTO.setDurationLimit(0);
        }
        if (configDTO.getDurationLimit() < 0) {
            vo.setCode(-1);
            vo.setMessage("请指定用车时长限制");
            return vo;
        }
        if (configDTO.getDurationLimit() >= 100000) {
            vo.setCode(-1);
            vo.setMessage("指定用车时长限制不应超过5位整数");
            return vo;
        }
        if (configDTO.getDiscount() == null || configDTO.getDiscount() < 1 || configDTO.getDiscount() >= 100) {
            vo.setCode(-1);
            vo.setMessage("请指定享有折扣，范围为1~99的整数");
            return vo;
        }
        if (configDTO.getTotalDiscountAmount() != null && (configDTO.getTotalDiscountAmount() < 1
                || configDTO.getTotalDiscountAmount() >= 100000)) {
            vo.setCode(-1);
            vo.setMessage("请指定累计折扣上限，范围为1~99999的整数");
            return vo;
        }
        if (!"00".equals(configDTO.getOrgId()) && CollectionUtils.isEmpty(configDTO.getCityIds())) {
            vo.setCode(-1);
            vo.setMessage("请选择可用区域");
            return vo;
        }
        if (configDTO.getMaxValue() == null || BigDecimal.ZERO.compareTo(configDTO.getMaxValue()) > 0) {
            vo.setCode(-1);
            vo.setMessage("请指定单笔订单最高可抵扣金额");
            return vo;
        }
        if (new BigDecimal(100000).compareTo(configDTO.getMaxValue()) <= 0) {
            vo.setCode(-1);
            vo.setMessage("单笔订单最高可抵扣金额不应超过5位整数");
            return vo;
        }
        //移除可用时段限制条件
        configDTO.setStartTime(StringUtils.EMPTY);
        configDTO.setEndTime(StringUtils.EMPTY);
//        if (StringUtils.isBlank(configDTO.getStartTime()) || StringUtils.isBlank(configDTO.getEndTime())) {
//            vo.setCode(-1);
//            vo.setMessage("请选择取车可用时段");
//            return vo;
//        }
//        Date startTime = DateUtil.getDateFromStr(configDTO.getStartTime(), DateUtil.timeShort);
//        Date endTime = DateUtil.getDateFromStr(configDTO.getStartTime(), DateUtil.timeShort);
//        if (startTime == null || endTime == null) {
//            vo.setCode(-1);
//            vo.setMessage("请选择取车可用时段");
//            return vo;
//        }
        String weekDaysLimit = getValidWeekDays(configDTO.getAvailableDaysOfWeek());
        if(weekDaysLimit == null) {
            vo.setCode(-1);
            vo.setMessage("可用天限制不合法");
            return vo;
        }
        return vo;
    }
}
