package com.extracme.evcard.rpc.vipcard.model;

import java.util.Date;

// 这种表记录的是 已经设置过提醒的人
public class SuixiangCardRemind {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_remind.id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_remind.user_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    private Long userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_remind.card_base_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    private Long cardBaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_remind.remind_status
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    private Integer remindStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_remind.create_time
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_remind.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_remind.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_remind.update_time
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_remind.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_remind.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_remind.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    private Integer isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_remind.id
     *
     * @return the value of suixiang_card_remind.id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_remind.id
     *
     * @param id the value for suixiang_card_remind.id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_remind.user_id
     *
     * @return the value of suixiang_card_remind.user_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_remind.user_id
     *
     * @param userId the value for suixiang_card_remind.user_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_remind.card_base_id
     *
     * @return the value of suixiang_card_remind.card_base_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public Long getCardBaseId() {
        return cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_remind.card_base_id
     *
     * @param cardBaseId the value for suixiang_card_remind.card_base_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public void setCardBaseId(Long cardBaseId) {
        this.cardBaseId = cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_remind.remind_status
     *
     * @return the value of suixiang_card_remind.remind_status
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public Integer getRemindStatus() {
        return remindStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_remind.remind_status
     *
     * @param remindStatus the value for suixiang_card_remind.remind_status
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public void setRemindStatus(Integer remindStatus) {
        this.remindStatus = remindStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_remind.create_time
     *
     * @return the value of suixiang_card_remind.create_time
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_remind.create_time
     *
     * @param createTime the value for suixiang_card_remind.create_time
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_remind.create_oper_id
     *
     * @return the value of suixiang_card_remind.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_remind.create_oper_id
     *
     * @param createOperId the value for suixiang_card_remind.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_remind.create_oper_name
     *
     * @return the value of suixiang_card_remind.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_remind.create_oper_name
     *
     * @param createOperName the value for suixiang_card_remind.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_remind.update_time
     *
     * @return the value of suixiang_card_remind.update_time
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_remind.update_time
     *
     * @param updateTime the value for suixiang_card_remind.update_time
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_remind.update_oper_id
     *
     * @return the value of suixiang_card_remind.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_remind.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_remind.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_remind.update_oper_name
     *
     * @return the value of suixiang_card_remind.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_remind.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_remind.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_remind.is_deleted
     *
     * @return the value of suixiang_card_remind.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_remind.is_deleted
     *
     * @param isDeleted the value for suixiang_card_remind.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}