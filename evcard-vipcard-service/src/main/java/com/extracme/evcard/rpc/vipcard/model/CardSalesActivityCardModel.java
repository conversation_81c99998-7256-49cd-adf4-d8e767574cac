package com.extracme.evcard.rpc.vipcard.model;

import java.math.BigDecimal;
import java.util.Date;

public class CardSalesActivityCardModel {

    /**
     * 卡片销售活动id
     */
    private Long activityId;

    /**
     * 卡片id
     */
    private Long cardId;

    /**
     * 实际售价，单位：元
     */
    private BigDecimal salesPrice;

    /**
     * 划线价格，单位：元
     */
    private BigDecimal underlinePrice;

    /**
     * 活动 参与次数上限
     */
    private Integer activityPersonPurchasesLimit;

    /**
     * 卡片上架时间, 精确到小时
     */
    private Date startTime;

    /**
     * 卡片下架时间
     */
    private Date endTime;

    /**
     * 活动状态  0待审核 1待上架 2已上架(开放购买) 3已下架(不影响已购卡片)
     */
    private Integer activityStatus;

    /**
     * 当前库存
     */
    private Integer stock;

    /**
     * 活动平台： 0：EVCARD 1：公众不可见
     */
    private Integer platformType;

    /**
     * 预售开始时间
     */
    private Date advanceNoticeTime;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 付费会员卡类型（0周卡 1月卡 2季卡）
     */
    private Integer cardType;

    /**
     * 享有折扣，1~99整
     */
    private Integer discount;

    /**
     * 单笔订单最大抵扣金额，单位：元
     */
    private BigDecimal maxValue;

    /**
     * 订单时长限制，单位分钟
     */
    private Integer durationLimit;

    /**
     * 可用城市限制，最多10个
     */
    private String cityLimit;

    /**
     * 可用车型（多个车型id以,分割）
     */
    private String vehicleModelLimit;

    private String goodsModelId;

    private String storeIds;

    /**
     * 租车模式 0 即时分时 1 预约分时(废弃) 2即时日租 3预约日租, 空表示不限，多个以逗号分隔
     */
    private String rentMethod;

    private String rentMethodGroup;

    /**
     * 可使用时段限制(取车时间)，开始时间点(hhmmss)
     */
    private String pickUpStartTime;

    /**
     * 可使用时段限制(取车时间)，结束时间点(hhmmss)
     */
    private String pickUpEndTime;

    /**
     * 可用天限制(取车时间)，逗号分隔，1,3表示周一和周三可用
     */
    private String availableDaysOfWeek;

    /**
     * 生效后有效天数
     */
    private Integer validDays;

    /**
     * 卡片使用说明
     */
    private String rules;

    /**
     * 购买条件 0：无  1：学生卡
     */
    private Integer purchaseType;

    /**
     * 卡面背景图片
     */
    private String backUrl;

    /**
     * 周期内累计折扣上限, 单位元
     */
    private BigDecimal totalDiscountAmount;

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public BigDecimal getSalesPrice() {
        return salesPrice;
    }

    public void setSalesPrice(BigDecimal salesPrice) {
        this.salesPrice = salesPrice;
    }

    public BigDecimal getUnderlinePrice() {
        return underlinePrice;
    }

    public void setUnderlinePrice(BigDecimal underlinePrice) {
        this.underlinePrice = underlinePrice;
    }

    public Integer getActivityPersonPurchasesLimit() {
        return activityPersonPurchasesLimit;
    }

    public void setActivityPersonPurchasesLimit(Integer activityPersonPurchasesLimit) {
        this.activityPersonPurchasesLimit = activityPersonPurchasesLimit;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(Integer activityStatus) {
        this.activityStatus = activityStatus;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Date getAdvanceNoticeTime() {
        return advanceNoticeTime;
    }

    public void setAdvanceNoticeTime(Date advanceNoticeTime) {
        this.advanceNoticeTime = advanceNoticeTime;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    public BigDecimal getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(BigDecimal maxValue) {
        this.maxValue = maxValue;
    }

    public Integer getDurationLimit() {
        return durationLimit;
    }

    public void setDurationLimit(Integer durationLimit) {
        this.durationLimit = durationLimit;
    }

    public String getCityLimit() {
        return cityLimit;
    }

    public void setCityLimit(String cityLimit) {
        this.cityLimit = cityLimit;
    }

    public String getVehicleModelLimit() {
        return vehicleModelLimit;
    }

    public void setVehicleModelLimit(String vehicleModelLimit) {
        this.vehicleModelLimit = vehicleModelLimit;
    }

    public String getRentMethod() {
        return rentMethod;
    }

    public void setRentMethod(String rentMethod) {
        this.rentMethod = rentMethod;
    }

    public String getPickUpStartTime() {
        return pickUpStartTime;
    }

    public void setPickUpStartTime(String pickUpStartTime) {
        this.pickUpStartTime = pickUpStartTime;
    }

    public String getPickUpEndTime() {
        return pickUpEndTime;
    }

    public void setPickUpEndTime(String pickUpEndTime) {
        this.pickUpEndTime = pickUpEndTime;
    }

    public String getAvailableDaysOfWeek() {
        return availableDaysOfWeek;
    }

    public void setAvailableDaysOfWeek(String availableDaysOfWeek) {
        this.availableDaysOfWeek = availableDaysOfWeek;
    }

    public Integer getValidDays() {
        return validDays;
    }

    public void setValidDays(Integer validDays) {
        this.validDays = validDays;
    }

    public String getRules() {
        return rules;
    }

    public void setRules(String rules) {
        this.rules = rules;
    }

    public Integer getPlatformType() {
        return platformType;
    }

    public void setPlatformType(Integer platformType) {
        this.platformType = platformType;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    public String getRentMethodGroup() {
        return rentMethodGroup;
    }

    public void setRentMethodGroup(String rentMethodGroup) {
        this.rentMethodGroup = rentMethodGroup;
    }

    public String getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(String storeIds) {
        this.storeIds = storeIds;
    }

    public String getGoodsModelId() {
        return goodsModelId;
    }

    public void setGoodsModelId(String goodsModelId) {
        this.goodsModelId = goodsModelId;
    }
}