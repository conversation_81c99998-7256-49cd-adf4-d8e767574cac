package com.extracme.evcard.rpc.vipcard.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.rpc.vipcard.dao.MmpCardSalesActivityMapper;
import com.extracme.evcard.rpc.vipcard.dao.MmpUserCardInfoMapper;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.enums.CardActivityOperateEnum;
import com.extracme.evcard.rpc.vipcard.enums.CardActivityStatusEnum;
import com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity;
import com.extracme.evcard.rpc.vipcard.service.inner.MemberCardInnerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员卡过期job
 * 非即时，每天执行一次
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "vipcard-rpc-userCardExpireJob",
		cron = "0 0 0/1 * * ?", description = "用户卡自动过期JOB", overwrite = true)
public class UserCardExpireJob implements SimpleJob {

	@Resource
	private MemberCardInnerService memberCardInnerService;

	@Autowired
	private MmpUserCardInfoMapper mmpUserCardInfoMapper;

	@Override
	public void execute(ShardingContext shardingContext) {
		try{
			autoExpireUserCards();
		}catch (Exception ex) {
			log.error("----UserCardExpireJob 用户卡片自动过期失败...", ex);
			throw ex;
		}
	}

	public void autoExpireUserCards() {
		log.info("autoExpireUserCards start ...");
		int total = mmpUserCardInfoMapper.expireUserCards();
		log.info("autoExpireUserCards end, size={}", total);
	}

}
