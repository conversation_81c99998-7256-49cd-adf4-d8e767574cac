package com.extracme.evcard.rpc.vipcard.model;

import java.util.Date;

public class SuixiangCardUseTemp {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.card_base_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Long cardBaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.card_price_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Long cardPriceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.card_type
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Integer cardType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.card_name
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private String cardName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.mobile_phone
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private String mobilePhone;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.quantity
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Integer quantity;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.cdk_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Long cdkId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.cdkey
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private String cdkey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.start_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Date startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.expires_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Date expiresTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.is_offer
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Integer isOffer;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.issue_type
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Integer issueType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.obtain_type
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Integer obtainType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.create_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.create_oper_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.create_oper_name
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.update_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.update_oper_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.update_oper_name
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use_temp.is_deleted
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    private Integer isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.id
     *
     * @return the value of suixiang_card_use_temp.id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.id
     *
     * @param id the value for suixiang_card_use_temp.id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.card_base_id
     *
     * @return the value of suixiang_card_use_temp.card_base_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Long getCardBaseId() {
        return cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.card_base_id
     *
     * @param cardBaseId the value for suixiang_card_use_temp.card_base_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setCardBaseId(Long cardBaseId) {
        this.cardBaseId = cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.card_price_id
     *
     * @return the value of suixiang_card_use_temp.card_price_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Long getCardPriceId() {
        return cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.card_price_id
     *
     * @param cardPriceId the value for suixiang_card_use_temp.card_price_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setCardPriceId(Long cardPriceId) {
        this.cardPriceId = cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.card_type
     *
     * @return the value of suixiang_card_use_temp.card_type
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Integer getCardType() {
        return cardType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.card_type
     *
     * @param cardType the value for suixiang_card_use_temp.card_type
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.card_name
     *
     * @return the value of suixiang_card_use_temp.card_name
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public String getCardName() {
        return cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.card_name
     *
     * @param cardName the value for suixiang_card_use_temp.card_name
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.mobile_phone
     *
     * @return the value of suixiang_card_use_temp.mobile_phone
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public String getMobilePhone() {
        return mobilePhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.mobile_phone
     *
     * @param mobilePhone the value for suixiang_card_use_temp.mobile_phone
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.quantity
     *
     * @return the value of suixiang_card_use_temp.quantity
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.quantity
     *
     * @param quantity the value for suixiang_card_use_temp.quantity
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.cdk_id
     *
     * @return the value of suixiang_card_use_temp.cdk_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Long getCdkId() {
        return cdkId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.cdk_id
     *
     * @param cdkId the value for suixiang_card_use_temp.cdk_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setCdkId(Long cdkId) {
        this.cdkId = cdkId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.cdkey
     *
     * @return the value of suixiang_card_use_temp.cdkey
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public String getCdkey() {
        return cdkey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.cdkey
     *
     * @param cdkey the value for suixiang_card_use_temp.cdkey
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setCdkey(String cdkey) {
        this.cdkey = cdkey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.start_time
     *
     * @return the value of suixiang_card_use_temp.start_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.start_time
     *
     * @param startTime the value for suixiang_card_use_temp.start_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.expires_time
     *
     * @return the value of suixiang_card_use_temp.expires_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Date getExpiresTime() {
        return expiresTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.expires_time
     *
     * @param expiresTime the value for suixiang_card_use_temp.expires_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setExpiresTime(Date expiresTime) {
        this.expiresTime = expiresTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.is_offer
     *
     * @return the value of suixiang_card_use_temp.is_offer
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Integer getIsOffer() {
        return isOffer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.is_offer
     *
     * @param isOffer the value for suixiang_card_use_temp.is_offer
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setIsOffer(Integer isOffer) {
        this.isOffer = isOffer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.issue_type
     *
     * @return the value of suixiang_card_use_temp.issue_type
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Integer getIssueType() {
        return issueType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.issue_type
     *
     * @param issueType the value for suixiang_card_use_temp.issue_type
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setIssueType(Integer issueType) {
        this.issueType = issueType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.obtain_type
     *
     * @return the value of suixiang_card_use_temp.obtain_type
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Integer getObtainType() {
        return obtainType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.obtain_type
     *
     * @param obtainType the value for suixiang_card_use_temp.obtain_type
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setObtainType(Integer obtainType) {
        this.obtainType = obtainType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.create_time
     *
     * @return the value of suixiang_card_use_temp.create_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.create_time
     *
     * @param createTime the value for suixiang_card_use_temp.create_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.create_oper_id
     *
     * @return the value of suixiang_card_use_temp.create_oper_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.create_oper_id
     *
     * @param createOperId the value for suixiang_card_use_temp.create_oper_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.create_oper_name
     *
     * @return the value of suixiang_card_use_temp.create_oper_name
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.create_oper_name
     *
     * @param createOperName the value for suixiang_card_use_temp.create_oper_name
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.update_time
     *
     * @return the value of suixiang_card_use_temp.update_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.update_time
     *
     * @param updateTime the value for suixiang_card_use_temp.update_time
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.update_oper_id
     *
     * @return the value of suixiang_card_use_temp.update_oper_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_use_temp.update_oper_id
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.update_oper_name
     *
     * @return the value of suixiang_card_use_temp.update_oper_name
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_use_temp.update_oper_name
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use_temp.is_deleted
     *
     * @return the value of suixiang_card_use_temp.is_deleted
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use_temp.is_deleted
     *
     * @param isDeleted the value for suixiang_card_use_temp.is_deleted
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}