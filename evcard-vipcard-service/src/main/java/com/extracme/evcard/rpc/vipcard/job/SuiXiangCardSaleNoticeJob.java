package com.extracme.evcard.rpc.vipcard.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardBaseMapper;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardRemindMapper;
import com.extracme.evcard.rpc.vipcard.enums.IsDeleteEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardBaseCardStatusEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardRemindStatusEnum;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardBase;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardBaseExample;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardRemind;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardRemindExample;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/28
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "vipcard-rpc-suiXiangCardSaleNoticeJob",
        cron = "0 * * * * ?", description = "随享卡开售提醒任务", overwrite = true)
public class SuiXiangCardSaleNoticeJob implements SimpleJob {

    @Resource
    private SuixiangCardBaseMapper suixiangCardBaseMapper;

    @Resource
    private SuixiangCardRemindMapper suixiangCardRemindMapper;

    @Resource
    private IMemberShipService memberShipService;

    @Resource
    private IMessagepushServ messageServ;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("随享卡开售提醒任务begin");

        // 如果有预告时间的，在售卖时间到之前已经状态为 已上架；如果没有预告时间的，在售卖时间到之前状态还是 待上架
        List<Integer> cardStatusList = Arrays.asList(SuiXiangCardBaseCardStatusEnum.TO_BE_PUT_ON_SHELF.getStatus(), SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus());

        // 售卖开始时间为3分钟后的0秒
        Date salesStartTime = DateUtil.localDateTimeToDate(LocalDateTime.now().withSecond(0).withNano(0).plusMinutes(3));

        // 查询3分钟后即将开售的随享卡
        SuixiangCardBaseExample baseExample = new SuixiangCardBaseExample();
        baseExample.createCriteria()
                .andSaleStartTimeEqualTo(salesStartTime)
                .andCardStatusIn(cardStatusList)
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardBase> baseList = suixiangCardBaseMapper.selectByExample(baseExample);
        if (CollectionUtils.isEmpty(baseList)) {
            log.info("没有3分钟后开售的随享卡");
            return;
        }
        List<Long> baseIdList = baseList.stream().map(item -> item.getId()).collect(Collectors.toList());
        log.info("3分钟后开售的随享卡有：{}", JSON.toJSONString(baseList));

        // 查询需要通知的用户列表
        SuixiangCardRemindExample remindExample = new SuixiangCardRemindExample();
        remindExample.createCriteria()
                .andCardBaseIdIn(baseIdList)
                .andRemindStatusEqualTo(SuiXiangCardRemindStatusEnum.NO.getRemindStatus())
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardRemind> remindList = suixiangCardRemindMapper.selectByExample(remindExample);
        if (CollectionUtils.isEmpty(remindList)) {
            log.info("3分钟后开售的随享卡，没有用户设置需要通知");
            return;
        }
        log.info("3分钟后开售的随享卡，需要通知的记录数为：{}", remindList.size());

        // 【出行卡开售提醒】消息中心+push（开售前3分钟发送提醒；文案：【EVCARD租车】尊敬的<名字>先生/女士，您所预约的随享出行卡即将开售，可前往个人中心特惠购卡选购！）
        // 尊敬的<name>先生/女士，您所预约的随享出行卡即将开售，可前往个人中心特惠购卡选购！
        for (SuixiangCardRemind item : remindList) {
            try {
                // 消息中心+push
                Map<String, String> param = new HashMap<>();
                MembershipBasicInfo member = memberShipService.getUserBasicInfoByPkId(item.getUserId());
                if (member == null) {
                    log.info("根据userId[{}]查询不到会员信息", item.getUserId());
                    continue;
                }
                param.put("name", member.getName());
                log.info("给用户发送随享卡开售提醒！userId[{}]baseId[{}]", item.getUserId(), item.getCardBaseId());
                messageServ.push(member.getAuthId(), member.getMembershipType(), 203, 2, param, "vipCard-rpc");

                // 更新suixiang_card_remind表的状态
                SuixiangCardRemind updateCond = new SuixiangCardRemind();
                updateCond.setId(item.getId());
                updateCond.setRemindStatus(SuiXiangCardRemindStatusEnum.YES.getRemindStatus());
                updateCond.setUpdateOperId(Constants.OPER_ID_SYSTEM);
                updateCond.setUpdateOperName(Constants.OPER_NAME_SYSTEM);
                int ret = suixiangCardRemindMapper.updateByPrimaryKeySelective(updateCond);
                if (ret != 1) {
                    log.error("更新suixiang_card_remind表的状态失败！id[{}]", item.getId());
                }
            } catch (Exception e) {
                log.error("随享卡开售提醒，单笔处理异常！remindId[{}]userId[{}]", item.getId(), item.getUserId(), e);
                continue;
            }
        }

        log.info("随享卡开售提醒任务end");
    }

}
