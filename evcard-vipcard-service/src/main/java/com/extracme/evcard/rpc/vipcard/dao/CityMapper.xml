<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.CityMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.City">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="CITYID" jdbcType="BIGINT" property="cityid" />
    <result column="CITY" jdbcType="VARCHAR" property="city" />
    <result column="FATHERID" jdbcType="BIGINT" property="fatherid" />
    <result column="LON" jdbcType="DECIMAL" property="lon" />
    <result column="LAT" jdbcType="DECIMAL" property="lat" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, CITYID, CITY, FATHERID, LON, LAT, STATUS, org_id
  </sql>
  <select id="selectByCityid" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.city
    where CITYID = #{cityId,jdbcType=BIGINT}
  </select>
  
  <select id="selectByAreaid" resultMap="BaseResultMap">
    select 
     c.ID, c.CITYID, c.CITY, c.FATHERID, c.LON, c.LAT, c.STATUS, c.org_id
    from ${siacSchema}.city c JOIN ${siacSchema}.area a ON a.FATHERID=c.CITYID WHERE a.AREAID = #{areaId,jdbcType=VARCHAR};
  </select>

  <select id="selectByCityName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from ${siacSchema}.city
    where CITY = #{city,jdbcType=VARCHAR}
  </select>

  <select id="selectByCityIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.city
    where 1=1
    <if test="cityIds!=null and cityIds.size > 0">
      and CITYID in
      <foreach item="item" collection="cityIds" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

</mapper>