package com.extracme.evcard.rpc.vipcard.model;

import java.math.BigDecimal;
import java.util.Date;

public class MmpCardSalesActivity {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.card_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Long cardId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.activity_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private String activityName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.org_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private String orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.sales_price
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private BigDecimal salesPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.underline_price
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private BigDecimal underlinePrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.activity_status
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Integer activityStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.person_purchases_limit
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Integer personPurchasesLimit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.start_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Date startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.end_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Date endTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.advance_notice_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Date advanceNoticeTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.rules
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private String rules;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.opening_stock
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Long openingStock;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.stock
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Long stock;

    /**
     * 销售量
     */
    private Long salesVolume;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.misc_desc
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.status
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.create_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.update_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_sales_activity.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private String updateOperName;

    /**
     * 活动平台 0：EVCARD  1：公众不可见
     */
    private Integer platformType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.id
     *
     * @return the value of mmp_card_sales_activity.id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.id
     *
     * @param id the value for mmp_card_sales_activity.id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.card_id
     *
     * @return the value of mmp_card_sales_activity.card_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Long getCardId() {
        return cardId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.card_id
     *
     * @param cardId the value for mmp_card_sales_activity.card_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.activity_name
     *
     * @return the value of mmp_card_sales_activity.activity_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public String getActivityName() {
        return activityName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.activity_name
     *
     * @param activityName the value for mmp_card_sales_activity.activity_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.org_id
     *
     * @return the value of mmp_card_sales_activity.org_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.org_id
     *
     * @param orgId the value for mmp_card_sales_activity.org_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.sales_price
     *
     * @return the value of mmp_card_sales_activity.sales_price
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public BigDecimal getSalesPrice() {
        return salesPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.sales_price
     *
     * @param salesPrice the value for mmp_card_sales_activity.sales_price
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setSalesPrice(BigDecimal salesPrice) {
        this.salesPrice = salesPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.underline_price
     *
     * @return the value of mmp_card_sales_activity.underline_price
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public BigDecimal getUnderlinePrice() {
        return underlinePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.underline_price
     *
     * @param underlinePrice the value for mmp_card_sales_activity.underline_price
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setUnderlinePrice(BigDecimal underlinePrice) {
        this.underlinePrice = underlinePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.activity_status
     *
     * @return the value of mmp_card_sales_activity.activity_status
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Integer getActivityStatus() {
        return activityStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.activity_status
     *
     * @param activityStatus the value for mmp_card_sales_activity.activity_status
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setActivityStatus(Integer activityStatus) {
        this.activityStatus = activityStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.person_purchases_limit
     *
     * @return the value of mmp_card_sales_activity.person_purchases_limit
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Integer getPersonPurchasesLimit() {
        return personPurchasesLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.person_purchases_limit
     *
     * @param personPurchasesLimit the value for mmp_card_sales_activity.person_purchases_limit
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setPersonPurchasesLimit(Integer personPurchasesLimit) {
        this.personPurchasesLimit = personPurchasesLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.start_time
     *
     * @return the value of mmp_card_sales_activity.start_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.start_time
     *
     * @param startTime the value for mmp_card_sales_activity.start_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.end_time
     *
     * @return the value of mmp_card_sales_activity.end_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.end_time
     *
     * @param endTime the value for mmp_card_sales_activity.end_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.advance_notice_time
     *
     * @return the value of mmp_card_sales_activity.advance_notice_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Date getAdvanceNoticeTime() {
        return advanceNoticeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.advance_notice_time
     *
     * @param advanceNoticeTime the value for mmp_card_sales_activity.advance_notice_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setAdvanceNoticeTime(Date advanceNoticeTime) {
        this.advanceNoticeTime = advanceNoticeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.rules
     *
     * @return the value of mmp_card_sales_activity.rules
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public String getRules() {
        return rules;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.rules
     *
     * @param rules the value for mmp_card_sales_activity.rules
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setRules(String rules) {
        this.rules = rules;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.opening_stock
     *
     * @return the value of mmp_card_sales_activity.opening_stock
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Long getOpeningStock() {
        return openingStock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.opening_stock
     *
     * @param openingStock the value for mmp_card_sales_activity.opening_stock
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setOpeningStock(Long openingStock) {
        this.openingStock = openingStock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.stock
     *
     * @return the value of mmp_card_sales_activity.stock
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Long getStock() {
        return stock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.stock
     *
     * @param stock the value for mmp_card_sales_activity.stock
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setStock(Long stock) {
        this.stock = stock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.misc_desc
     *
     * @return the value of mmp_card_sales_activity.misc_desc
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.misc_desc
     *
     * @param miscDesc the value for mmp_card_sales_activity.misc_desc
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.status
     *
     * @return the value of mmp_card_sales_activity.status
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.status
     *
     * @param status the value for mmp_card_sales_activity.status
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.create_time
     *
     * @return the value of mmp_card_sales_activity.create_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.create_time
     *
     * @param createTime the value for mmp_card_sales_activity.create_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.create_oper_id
     *
     * @return the value of mmp_card_sales_activity.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.create_oper_id
     *
     * @param createOperId the value for mmp_card_sales_activity.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.create_oper_name
     *
     * @return the value of mmp_card_sales_activity.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.create_oper_name
     *
     * @param createOperName the value for mmp_card_sales_activity.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.update_time
     *
     * @return the value of mmp_card_sales_activity.update_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.update_time
     *
     * @param updateTime the value for mmp_card_sales_activity.update_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.update_oper_id
     *
     * @return the value of mmp_card_sales_activity.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.update_oper_id
     *
     * @param updateOperId the value for mmp_card_sales_activity.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_sales_activity.update_oper_name
     *
     * @return the value of mmp_card_sales_activity.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_sales_activity.update_oper_name
     *
     * @param updateOperName the value for mmp_card_sales_activity.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    public Integer getPlatformType() {
        return platformType;
    }

    public void setPlatformType(Integer platformType) {
        this.platformType = platformType;
    }

    public Long getSalesVolume() {
        return salesVolume;
    }

    public void setSalesVolume(Long salesVolume) {
        this.salesVolume = salesVolume;
    }
}