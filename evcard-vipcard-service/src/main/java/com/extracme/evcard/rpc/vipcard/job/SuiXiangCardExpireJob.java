package com.extracme.evcard.rpc.vipcard.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.redis.JedisLock;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardPurchaseRecordMapper;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardUseMapper;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardRefundLogDto;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardIssueTypeEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardStatusEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardUseLogOperationTypeEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuixiangCardRefundSuccessEnum;
import com.extracme.evcard.rpc.vipcard.manager.SuixiangCardBaseManager;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecord;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUse;
import com.extracme.evcard.rpc.vipcard.rest.MdRestClient;
import com.extracme.evcard.rpc.vipcard.rest.entity.ReturnCardFeeReq;
import com.extracme.evcard.rpc.vipcard.rest.entity.ReturnCardFeeRes;
import com.extracme.evcard.rpc.vipcard.service.SuixiangCardRefundService;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 随享卡过期job
 * 非即时，每天执行一次
 *
 * <AUTHOR>
 * @date 2023/1/28
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "vipcard-rpc-suiXiangCardExpireJob",
        cron = "1 0 0 * * ?", description = "随享卡自动过期任务", overwrite = true)
public class SuiXiangCardExpireJob implements SimpleJob {

    @Resource
    private SuixiangCardPurchaseRecordMapper suixiangCardPurchaseRecordMapper;

    @Resource
    private SuixiangCardUseMapper suixiangCardUseMapper;

    @Resource
    private SuixiangCardBaseManager suixiangCardBaseManager;

    @Resource
    private MdRestClient mdRestClient;

    @Resource
    private SuixiangCardRefundService suixiangCardRefundService;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("随享卡自动过期任务begin");

        Long minId = 1L;
        while (true) {
            List<SuixiangCardUse> list = suixiangCardUseMapper.selectForPage(minId);
            if (CollectionUtils.isEmpty(list)) {
                log.info("随享卡自动过期任务！数据查询完毕，跳出循环");
                break;
            }
            log.info("随享卡自动过期任务！本分页查询完成，minId={}，size={}", minId, list.size());
            for (SuixiangCardUse item : list) {
                try (Jedis jedis = JedisUtil.getJedis()) {
                    int expireMillis = 60000;
                    String key = "lock_refund_cardUseId_" + item.getId();
                    JedisLock lock = new JedisLock(key, expireMillis);
                    if (lock.acquire(jedis)) {
                        try {
                            processSingleItem(item);
                        } catch (Exception e) {
                            log.error("随享卡自动过期任务！单笔处理异常！item[{}]", JSON.toJSONString(item));
                        } finally {
                            lock.releaseLua(jedis);
                        }
                    } else {
                        log.warn("随享卡自动过期任务，获取redis锁失败，key:{}", key);
                    }
                } catch (Exception e) {
                    log.error("随享卡自动过期任务，redis异常:{}", e.getMessage());
                }
            }
            minId = list.get(list.size() - 1).getId() + 1;
        }

        log.info("随享卡自动过期任务end");
    }

    private void processSingleItem(SuixiangCardUse item) {
        log.info("随享卡自动过期任务！单笔处理开始！id[{}]", item.getId());

        // 1-生效中
        if (SuiXiangCardStatusEnum.EFFECTIVE.getStatus().equals(item.getCardStatus())) {
            // 已使用或有冻结天数（就是有进行中的订单）
            if ((item.getUsedDays() != null && item.getUsedDays() > 0) || (item.getFrozenDays() != null && item.getFrozenDays() > 0)) {
                // 变成 2-已过期，记录log
                suixiangCardBaseManager.updateCardUseAndLog(item, SuiXiangCardStatusEnum.EFFECTIVE, SuiXiangCardStatusEnum.EXPIRED,
                        SuiXiangCardUseLogOperationTypeEnum.EXPIRE);
            }
            // 未使用
            else {
                // 查询购买表
                SuixiangCardPurchaseRecord purchaseRecord = suixiangCardPurchaseRecordMapper.selectByPrimaryKey(item.getPurchaseId());
                if (purchaseRecord == null) {
                    log.error("查询不到suixiang_card_purchase_record表记录！id[{}]purchaseId[{}]", item.getId(), item.getPurchaseId());
                    return;
                }

                // 如果是购买的卡
                if (SuiXiangCardIssueTypeEnum.PURCHASE.getType().equals(purchaseRecord.getIssueType())) {
                    if (StringUtils.isBlank(purchaseRecord.getPayOrderNo())) {
                        log.error("查到suixiang_card_purchase_record表记录的payOrderNo为空！id[{}]purchaseId[{}]", item.getId(), item.getPurchaseId());
                        return;
                    }

                    // 变成 8-自动已冻结，记录log
                    suixiangCardBaseManager.updateCardUseAndLog(item, SuiXiangCardStatusEnum.EFFECTIVE, SuiXiangCardStatusEnum.AUTO_FROZENED,
                            SuiXiangCardUseLogOperationTypeEnum.AUTO_FREEZED);

                    // 调用支付的returnCardFee退卡费（同步应答只是受理），然后接收异步的mq应答，变成 4-已退卡；
                    ReturnCardFeeReq req = new ReturnCardFeeReq();
                    req.setPayOrderNo(purchaseRecord.getPayOrderNo());

                    // 获得单价 兼容的是 一次购买多张随享卡，每次退卡，退单张卡
                    BigDecimal realAmount = purchaseRecord.getRealAmount();
                    if (realAmount == null || BigDecimal.ZERO.compareTo(realAmount) == 0) {
                        log.info("自动退卡，金额为0，item={}",JSON.toJSONString(item));
                        return;
                    }

                    Integer quantity = (purchaseRecord.getQuantity() == null || purchaseRecord.getQuantity() == 0) ? 1 : purchaseRecord.getQuantity();
                    BigDecimal unitPrice = realAmount.divide(new BigDecimal(quantity), 2, RoundingMode.HALF_DOWN);
                    req.setAmount(unitPrice);
                    req.setSxkUseId("TYPE_2_" + Constants.SXK_USEID_PREFIX + item.getId());

                    log.info("调用支付的returnCardFee退卡费，请求参数：{}", JSON.toJSONString(req));
                    try {
                        // 记录一条退款中的操作日志
                        suixiangCardRefundService.saveSuixiangCardRefundLog(item.getId(), 2, SuixiangCardRefundSuccessEnum.REFUNDING.getStatus(), null);
                        ReturnCardFeeRes resp = mdRestClient.returnCardFee(req);
                        log.info("调用支付的returnCardFee退卡费，应答参数：{}", JSON.toJSONString(resp));
                        if (resp == null) {
                            log.error("调用支付的returnCardFee退卡费，同步受理返回为空！id[{}]payOrderNo[{}]", item.getId(), req.getPayOrderNo());
                            throw new BusinessException(-1,"退卡接口返回为空");
                        }
                        if (resp.getCode() != 0) {
                            log.error("调用支付的returnCardFee退卡费，同步受理失败！id[{}]payOrderNo[{}]retCode[{}]", item.getId(), req.getPayOrderNo(), resp.getCode());
                            throw new BusinessException(resp.getCode(),resp.getMessage());
                        }
                    } catch (Exception e) {
                        log.error("调用支付的returnCardFee退卡费，同步受理有异常！id[{}]payOrderNo[{}]", item.getId(), purchaseRecord.getPayOrderNo(), e);
                        // 状态回滚 刷成正常，记录log
                        suixiangCardBaseManager.updateCardUseAndLog(item,  SuiXiangCardStatusEnum.AUTO_FROZENED,SuiXiangCardStatusEnum.EFFECTIVE,
                                SuiXiangCardUseLogOperationTypeEnum.RETURNED_FAIL);
                        // 记录退款失败的日志
                        suixiangCardRefundService.saveSuixiangCardRefundLog(item.getId(), 2, SuixiangCardRefundSuccessEnum.REFUND_FAILURE.getStatus(), null);
                        return;
                    }
                }
                // 如果是赠送的卡 或者 兑换的卡
                else {
                    // 变成 2-已过期，记录log
                    suixiangCardBaseManager.updateCardUseAndLog(item, SuiXiangCardStatusEnum.EFFECTIVE, SuiXiangCardStatusEnum.EXPIRED,
                            SuiXiangCardUseLogOperationTypeEnum.EXPIRE);
                }
            }
        }
        // 3-生效中-已冻结
        else if (SuiXiangCardStatusEnum.FROZENED.getStatus().equals(item.getCardStatus())) {
            // 变成 6-过期已冻结，记录log
            suixiangCardBaseManager.updateCardUseAndLog(item, SuiXiangCardStatusEnum.FROZENED, SuiXiangCardStatusEnum.EXPIRED_FROZENED,
                    SuiXiangCardUseLogOperationTypeEnum.EXPIRE_FREEZED);
        }

        log.info("随享卡自动过期任务！单笔处理结束！id[{}]", item.getId());
    }

}
