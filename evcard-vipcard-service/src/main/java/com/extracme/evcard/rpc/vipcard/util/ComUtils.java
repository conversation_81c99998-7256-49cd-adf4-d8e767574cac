package com.extracme.evcard.rpc.vipcard.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import utils.HqEncryptUtils;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class ComUtils {

    public static String buildNstRequestBody(Map<String, Object> reqParams,String outPublicKey,String innerPrivateKey) throws Exception {
        return HqEncryptUtils.buildRequest(reqParams, outPublicKey,innerPrivateKey, 0).toJSONString();
    }

    /**
     * BigDecimal比较
     * @param a
     * @param b
     * @return
     */
    public static boolean equalDecimal(BigDecimal a, BigDecimal b) {
        if(null == a) {
            return (null == b);
        }
        return a.equals(b);
    }

    public static Integer getInteger(String str){
        try{
            return NumberUtils.createInteger(str);
        }catch (Exception ex) {
        }
        return null;
    }

    public static Long getLong(String str){
        try{
            return NumberUtils.createLong(str);
        }catch (Exception ex) {
        }
        return null;
    }

    public static final List getIntList(String str){
        if(StringUtils.isBlank(str)) {
            return null;
        }
        String[] array = StringUtils.split(str, Constants.STR_COMMA);
        List<Integer> result = new ArrayList<>();
        for(String s : array) {
            Integer value = ComUtils.getInteger(s);
            if(value != null) {
                result.add(value);
            }
        }
        return result;
    }

    public static final List getLongList(String str){
        if(StringUtils.isBlank(str)) {
            return null;
        }
        String[] array = StringUtils.split(str, Constants.STR_COMMA);
        List<Long> result = new ArrayList<>();
        for(String s : array) {
            Long value = ComUtils.getLong(s);
            if(value != null) {
                result.add(value);
            }
        }
        return result;
    }

    /**
     * 字符串 去掉以endChar结尾
     * @param object
     * @param endChar
     * @return
     */
    public static String formatEndChar(String object, String endChar) {
        if (object.endsWith(endChar)) {
            object = object.substring(0, object.length() - 1);
        }
        return object;
    }

    /**
     * 将regex分隔的字符串转为int的集合
     * @param object
     * @param regex
     * @return
     */
    public static List<Integer> turnToList(String object, String regex) {
        String[] objectArray = object.split(regex);
        Integer[] objectIntArray = (Integer[]) ConvertUtils.convert(objectArray, Integer.class);
        List<Integer> objectList = new ArrayList<>(Arrays.asList(objectIntArray));
        return objectList;
    }

    /**
     * 排序后拼接为字符串
     * @param list
     * @param separator
     * @return
     */
    public static String sortJoin(List<?> list, String separator, boolean end) {
        String value = StringUtils.EMPTY;
        if(CollectionUtils.isNotEmpty(list)){
            List result = list.stream().distinct().collect(Collectors.toList());
            Collections.sort(result);
            value = StringUtils.join(result, separator);
            if(end) {
                value += separator;
            }
        }
        return value;
    }

    public static String decimalFormat(BigDecimal value, int scale) {
        if(value == null) {
            value = BigDecimal.ZERO;
        }
        String fm = "%." + scale + "f";
        return String.format(fm, value.doubleValue());
    }

    public static Integer add(Integer a, Integer b) {
        return a + b;
    }

    public static String createOrderSeq(Long pkId){
        return System.currentTimeMillis() + pkId + "";
    }

    public static String getValidSeq(String seq) {
        if(StringUtils.isBlank(seq) || "0".equals(seq)) {
            return StringUtils.EMPTY;
        }
        return seq;
    }

    public static Integer getValidSeq(Integer seq) {
        if("0".equals(String.valueOf(seq))) {
            return null;
        }
        return seq;
    }

    public static Long getValidSeq(Long seq) {
        if("0".equals(String.valueOf(seq))) {
            return null;
        }
        return seq;
    }

    public static boolean getIsMdOrder(Integer rentMethod) {
        return Constants.RENT_METHOD_MD.equals(rentMethod);
    }

    /**
     * 如果str的长度超过len，那么截取len位，否则返回原字符串
     *
     * @param str 原字符串
     * @param len 长度限制
     * @return
     */
    public static String splitStr(String str, int len) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        return str.length() > len ? str.substring(0, len) : str;
    }

    public static final String SECRET_MSG = "****";
    public static final String SECRET_MARK = "*";

    public static String getCommSecretMsg(String str, int left, int right) {
        return getCommSecretMsg(str, left, right, -1);
    }

    /**
     * 通用关键信息混淆方法
     * 保留前left位，后right位 其他部分使用*隐藏
     * @param str
     * @param left
     * @param right
     * @return
     */
    public static String getCommSecretMsg(String str, int left, int right, int maxMark) {
        int len = left + right;
        //保留前left位，后right位 其他部分使用*隐藏
        if(StringUtils.isBlank(str)) {
            return StringUtils.EMPTY;
        }
        if(str.length() <= len) {
            return SECRET_MSG;
        }
        int markNum = str.length() - len;
        if(maxMark > 0 && maxMark < markNum) {
            markNum = maxMark;
        }
        String secret = StringUtils.repeat(SECRET_MARK, markNum);
        String encodeStr = str.substring(0, left)
                .concat(secret)
                .concat(str.substring(str.length() - right));
        return encodeStr;
    }

    public static void checkAndMkDir(String dir) {
        File baseDir = new File(dir);
        if(!baseDir.exists() || !baseDir.isDirectory()) {
            log.info("临时目录不存在，初次创建, dir={}", dir);
            baseDir.mkdirs();
        }
    }

    public static boolean isValidLongStr(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        try {
            Long.parseLong(str);
        } catch (NumberFormatException e) {
            return false;
        }
        return true;
    }

    public static boolean isValidIntegerStr(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        try {
            Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return false;
        }
        return true;
    }

    public static boolean isNumber(String str){
        if(StringUtils.isBlank(str)){
            return false;
        }
        Pattern pattern = Pattern.compile("[0-9]*");
        return pattern.matcher(str).matches();
    }

    public static int getNumberDecimalDigits(double number) {
        String moneyStr = String.valueOf(number);
        String[] num = moneyStr.split("\\.");
        if (num.length == 2) {
            for (; ; ) {
                if (num[1].endsWith("0")) {
                    num[1] = num[1].substring(0, num[1].length() - 1);
                } else {
                    break;
                }
            }
            return num[1].length();
        } else {
            return 0;
        }
    }

    /**
     * 分隔
     * 判断2个字符串是否可以 完全相等
     * @param
     * @param string2
     * @return
     */
    public static boolean stringIsEquals(String string1, String string2) {
        String[] stringArray1 = string1.split(",");
        Set<String> set1 = new HashSet<>();
        for (String id : stringArray1) {
            set1.add(id);
        }
        String[] stringArray2 = string2.split(",");
        Set<String> set2 = new HashSet<>();
        for (String id : stringArray2) {
            set2.add(id);
        }

        if (set1.size() != set2.size()) {
            return false;
        }

        // 比较2个数组 元素是否完全相等
        if (set1.containsAll(set2) && set2.containsAll(set1)) {
            return true;
        }

        return false;
    }

}
