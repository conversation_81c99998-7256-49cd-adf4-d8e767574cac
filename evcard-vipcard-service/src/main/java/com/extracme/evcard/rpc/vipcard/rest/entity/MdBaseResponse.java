package com.extracme.evcard.rpc.vipcard.rest.entity;

import java.io.Serializable;

public class MdBaseResponse<T> implements Serializable {
    private int code = 0;
    private String message;
    private T data;
    public static final MdBaseResponse SUCCESS = new MdBaseResponse();

    public MdBaseResponse() {
    }

    public MdBaseResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static <T> MdBaseResponse<T> getSuccessVO(T data) {
        MdBaseResponse<T> defaultWebRespVO = new MdBaseResponse();
        defaultWebRespVO.setData(data);
        return defaultWebRespVO;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}