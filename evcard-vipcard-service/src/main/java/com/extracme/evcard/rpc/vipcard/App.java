package com.extracme.evcard.rpc.vipcard;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.extracme.evcard.elasticjob.EnableElasticJob;
import com.extracme.evcard.redis.spring.EnableRedisUtil;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@SpringBootApplication
@EnableAsync
@EnableApolloConfig
//@PropertySources({
//		@PropertySource("classpath:config.properties"),
//		@PropertySource("classpath:ons.properties"),
//		@PropertySource("classpath:redis.properties")
//})
@EnableDubbo
@EnableRedisUtil
@EnableElasticJob
@MapperScan(value = {"com.extracme.evcard.rpc.vipcard.dao"})
@ImportResource(locations = {"classpath:dubbo.xml"})
@EnableTransactionManagement
public class App {
	
	public static void main(String[] args) {
		SpringApplication.run(App.class, args);
	}

}
