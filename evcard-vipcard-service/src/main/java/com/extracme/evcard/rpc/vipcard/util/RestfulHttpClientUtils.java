package com.extracme.evcard.rpc.vipcard.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

/**
 * restfulHttp请求工具类
 *
 * <AUTHOR>
 */
public class RestfulHttpClientUtils {
    private static Logger log = LoggerFactory.getLogger( com.extracme.evcard.rpc.vipcard.util.RestfulHttpClientUtils.class);

    /**
     * ms
     */
    public static final int HTTP_CONNECT_TIMEOUT = 50000;
    /**
     * ms
     */
    public static final int HTTP_READ_TIMEOUT = 50000;

    public static String sendRestHttp_String(String url, String method, String parameter) {
        OutputStream out = null;
        OutputStreamWriter dos = null;
        InputStream is = null;
        InputStreamReader reader = null;
        HttpURLConnection http_conn = null;
        try {
            URL urlObj = new URL(url);
            http_conn = (HttpURLConnection) urlObj.openConnection();
            http_conn.setDoOutput(true);
            http_conn.setDoInput(true);
            http_conn.setUseCaches(false);
            http_conn.setRequestMethod(method);
            http_conn.setRequestProperty("Content-Type", "application/json");
            http_conn.setConnectTimeout(HTTP_CONNECT_TIMEOUT);
            http_conn.setReadTimeout(HTTP_READ_TIMEOUT);
            http_conn.connect();
            out = http_conn.getOutputStream();
            dos = new OutputStreamWriter(out, "UTF-8");
            IOUtils.write(parameter, dos);
            dos.flush();

            String returnData = StringUtils.EMPTY;
            int responseCode = http_conn.getResponseCode();
            if (HttpURLConnection.HTTP_OK == responseCode || HttpURLConnection.HTTP_CREATED == responseCode) {
                is = http_conn.getInputStream();
            } else if (HttpURLConnection.HTTP_BAD_REQUEST == responseCode) {
                is = http_conn.getErrorStream();
            }
            reader = new InputStreamReader(is, "UTF-8");
            char[] buffer = new char[1024];
            int len = 0;
            while ((len = reader.read(buffer)) != -1) {
                returnData += new String(buffer, 0, len);
            }
            return returnData;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("java.net.SocketTimeoutException: connect timed out");
        } finally {
            IOUtils.closeQuietly(dos);
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(is);
            IOUtils.close(http_conn);
        }
        return "";
    }

    public static JSONObject sendRestHttp(String url, String method, String parameter) {
        OutputStream out = null;
        OutputStreamWriter dos = null;
        InputStream is = null;
        InputStreamReader reader = null;
        HttpURLConnection http_conn = null;
        try {
            URL urlObj = new URL(url);
            http_conn = (HttpURLConnection) urlObj.openConnection();
            http_conn.setDoOutput(true);
            http_conn.setDoInput(true);
            http_conn.setUseCaches(false);
            http_conn.setRequestMethod(method);
            http_conn.setRequestProperty("Content-Type", "application/json");
            http_conn.setConnectTimeout(HTTP_CONNECT_TIMEOUT);
            http_conn.setReadTimeout(HTTP_READ_TIMEOUT);
            http_conn.connect();
            out = http_conn.getOutputStream();
            dos = new OutputStreamWriter(out, "UTF-8");
            IOUtils.write(parameter, dos);
            dos.flush();

            String returnData = StringUtils.EMPTY;
            int responseCode = http_conn.getResponseCode();
            log.info("httpResponseCode ===>" + http_conn.getResponseCode());
            if (HttpURLConnection.HTTP_OK == responseCode || HttpURLConnection.HTTP_CREATED == responseCode) {
                is = http_conn.getInputStream();
            } else if (HttpURLConnection.HTTP_BAD_REQUEST == responseCode) {
                is = http_conn.getErrorStream();
            }
            reader = new InputStreamReader(is, "UTF-8");
            char[] buffer = new char[1024];
            int len = 0;
            while ((len = reader.read(buffer)) != -1) {
                returnData += new String(buffer, 0, len);
            }
            JSONObject response = null;
            if (StringUtils.isNoneBlank(returnData)) {
                response = JSONObject.parseObject(returnData);
            }
            return response;
        } catch (Exception e) {
            log.error("sendRestHttpException", e);
            log.error("java.net.SocketTimeoutException: connect timed out");
        } finally {
            IOUtils.closeQuietly(dos);
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(is);
            IOUtils.close(http_conn);
        }
        return null;
    }

    public static JSONObject sendRestHttp(String url, String method, String parameter, Map<String, String> header) {
        OutputStream out = null;
        OutputStreamWriter dos = null;
        InputStream is = null;
        InputStreamReader reader = null;
        HttpURLConnection http_conn = null;
        try {
            URL urlObj = new URL(url);
            http_conn = (HttpURLConnection) urlObj.openConnection();
            http_conn.setDoOutput(true);
            http_conn.setDoInput(true);
            http_conn.setUseCaches(false);
            http_conn.setRequestMethod(method);
            http_conn.setRequestProperty("Content-Type", "application/json");
            http_conn.setConnectTimeout(HTTP_CONNECT_TIMEOUT);
            http_conn.setReadTimeout(HTTP_READ_TIMEOUT);
            if (MapUtils.isNotEmpty(header)) {
                for (String key : header.keySet()) {
                    http_conn.setRequestProperty(key, header.get(key));
                }
            }
            http_conn.connect();
            if (StringUtils.isNotEmpty(parameter)) {
                out = http_conn.getOutputStream();
                dos = new OutputStreamWriter(out, "UTF-8");
                IOUtils.write(parameter, dos);
                dos.flush();
            }

            String returnData = StringUtils.EMPTY;
            int responseCode = http_conn.getResponseCode();
            log.info("responseCode => " + String.valueOf(responseCode));
            if (HttpURLConnection.HTTP_OK == responseCode || HttpURLConnection.HTTP_CREATED == responseCode) {
                is = http_conn.getInputStream();
            } else if (HttpURLConnection.HTTP_BAD_REQUEST == responseCode) {
                is = http_conn.getErrorStream();
            }
            reader = new InputStreamReader(is, "UTF-8");
            char[] buffer = new char[1024];
            int len = 0;
            while ((len = reader.read(buffer)) != -1) {
                returnData += new String(buffer, 0, len);
            }
            JSONObject response = null;
            if (StringUtils.isNoneBlank(returnData)) {
                response = JSONObject.parseObject(returnData);
            }
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("java.net.SocketTimeoutException: connect timed out");
        } finally {
            IOUtils.closeQuietly(dos);
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(is);
            IOUtils.close(http_conn);
        }
        return null;
    }

    /**
     * get请求(有header参数)
     *
     * @param url
     * @param header
     * @return
     */
    public static JSONObject sendRestHttp_get(String url, Map<String, String> header) {
        InputStream is = null;
        InputStreamReader reader = null;
        HttpURLConnection http_conn = null;
        try {
            URL urlObj = new URL(url);
            http_conn = (HttpURLConnection) urlObj.openConnection();
            http_conn.setDoOutput(true);
            http_conn.setDoInput(true);
            http_conn.setUseCaches(false);
            http_conn.setRequestMethod("GET");
            http_conn.setRequestProperty("Content-Type", "application/json");
            http_conn.setConnectTimeout(HTTP_CONNECT_TIMEOUT);
            http_conn.setReadTimeout(HTTP_READ_TIMEOUT);
            if (MapUtils.isNotEmpty(header)) {
                for (String key : header.keySet()) {
                    http_conn.setRequestProperty(key, header.get(key));
                }
            }
            http_conn.connect();
            String returnData = StringUtils.EMPTY;
            int responseCode = http_conn.getResponseCode();
            if (HttpURLConnection.HTTP_OK == responseCode || HttpURLConnection.HTTP_CREATED == responseCode) {
                is = http_conn.getInputStream();
            } else if (HttpURLConnection.HTTP_BAD_REQUEST == responseCode) {
                is = http_conn.getErrorStream();
            }
            reader = new InputStreamReader(is, "UTF-8");
            char[] buffer = new char[1024];
            int len = 0;
            while ((len = reader.read(buffer)) != -1) {
                returnData += new String(buffer, 0, len);
            }
            JSONObject response = null;
            if (StringUtils.isNoneBlank(returnData)) {
                response = JSONObject.parseObject(returnData);
            }
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("java.net.SocketTimeoutException: connect timed out");
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(reader);
            IOUtils.close(http_conn);
        }
        return null;
    }

    public static JSONObject sendRestHttp_get(String url) {
        InputStream is = null;
        InputStreamReader reader = null;
        HttpURLConnection http_conn = null;
        try {
            URL urlObj = new URL(url);
            http_conn = (HttpURLConnection) urlObj.openConnection();
            http_conn.setDoOutput(true);
            http_conn.setDoInput(true);
            http_conn.setUseCaches(false);
            http_conn.setRequestMethod("GET");
            http_conn.setConnectTimeout(HTTP_CONNECT_TIMEOUT);
            http_conn.setReadTimeout(HTTP_READ_TIMEOUT);
            http_conn.connect();
            String returnData = StringUtils.EMPTY;
            int responseCode = http_conn.getResponseCode();
            if (HttpURLConnection.HTTP_OK == responseCode || HttpURLConnection.HTTP_CREATED == responseCode) {
                is = http_conn.getInputStream();
            } else if (HttpURLConnection.HTTP_BAD_REQUEST == responseCode) {
                is = http_conn.getErrorStream();
            }
            reader = new InputStreamReader(is, "UTF-8");
            char[] buffer = new char[1024];
            int len = 0;
            while ((len = reader.read(buffer)) != -1) {
                returnData += new String(buffer, 0, len);
            }
            JSONObject response = null;
            if (StringUtils.isNoneBlank(returnData)) {
                response = JSONObject.parseObject(returnData);
            }
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("java.net.SocketTimeoutException: connect timed out");
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(reader);
            IOUtils.close(http_conn);
        }
        return null;
    }

    public static String sendRestHttp_get_String(String url) {
        InputStream is = null;
        InputStreamReader reader = null;
        HttpURLConnection http_conn = null;
        try {
            URL urlObj = new URL(url);
            http_conn = (HttpURLConnection) urlObj.openConnection();
            http_conn.setDoOutput(true);
            http_conn.setDoInput(true);
            http_conn.setUseCaches(false);
            http_conn.setRequestMethod("GET");
            http_conn.setConnectTimeout(HTTP_CONNECT_TIMEOUT);
            http_conn.setReadTimeout(HTTP_READ_TIMEOUT);
            http_conn.connect();
            String returnData = StringUtils.EMPTY;
            int responseCode = http_conn.getResponseCode();
            if (HttpURLConnection.HTTP_OK == responseCode || HttpURLConnection.HTTP_CREATED == responseCode) {
                is = http_conn.getInputStream();
            } else if (HttpURLConnection.HTTP_BAD_REQUEST == responseCode) {
                is = http_conn.getErrorStream();
            }
            reader = new InputStreamReader(is, "UTF-8");
            char[] buffer = new char[1024];
            int len = 0;
            while ((len = reader.read(buffer)) != -1) {
                returnData += new String(buffer, 0, len);
            }
            return returnData;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("java.net.SocketTimeoutException: connect timed out");
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(reader);
            IOUtils.close(http_conn);
        }
        return null;
    }

    public static void main(String[] args) {
    }
}
