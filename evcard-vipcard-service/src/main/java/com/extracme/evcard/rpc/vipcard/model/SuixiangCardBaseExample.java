package com.extracme.evcard.rpc.vipcard.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SuixiangCardBaseExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public SuixiangCardBaseExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCardNameIsNull() {
            addCriterion("card_name is null");
            return (Criteria) this;
        }

        public Criteria andCardNameIsNotNull() {
            addCriterion("card_name is not null");
            return (Criteria) this;
        }

        public Criteria andCardNameEqualTo(String value) {
            addCriterion("card_name =", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotEqualTo(String value) {
            addCriterion("card_name <>", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameGreaterThan(String value) {
            addCriterion("card_name >", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameGreaterThanOrEqualTo(String value) {
            addCriterion("card_name >=", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameLessThan(String value) {
            addCriterion("card_name <", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameLessThanOrEqualTo(String value) {
            addCriterion("card_name <=", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameLike(String value) {
            addCriterion("card_name like", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotLike(String value) {
            addCriterion("card_name not like", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameIn(List<String> values) {
            addCriterion("card_name in", values, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotIn(List<String> values) {
            addCriterion("card_name not in", values, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameBetween(String value1, String value2) {
            addCriterion("card_name between", value1, value2, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotBetween(String value1, String value2) {
            addCriterion("card_name not between", value1, value2, "cardName");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNull() {
            addCriterion("org_id is null");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNotNull() {
            addCriterion("org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualTo(String value) {
            addCriterion("org_id =", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualTo(String value) {
            addCriterion("org_id <>", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThan(String value) {
            addCriterion("org_id >", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("org_id >=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThan(String value) {
            addCriterion("org_id <", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualTo(String value) {
            addCriterion("org_id <=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLike(String value) {
            addCriterion("org_id like", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotLike(String value) {
            addCriterion("org_id not like", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIn(List<String> values) {
            addCriterion("org_id in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotIn(List<String> values) {
            addCriterion("org_id not in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdBetween(String value1, String value2) {
            addCriterion("org_id between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotBetween(String value1, String value2) {
            addCriterion("org_id not between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andCityIdIsNull() {
            addCriterion("city_id is null");
            return (Criteria) this;
        }

        public Criteria andCityIdIsNotNull() {
            addCriterion("city_id is not null");
            return (Criteria) this;
        }

        public Criteria andCityIdEqualTo(String value) {
            addCriterion("city_id =", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotEqualTo(String value) {
            addCriterion("city_id <>", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdGreaterThan(String value) {
            addCriterion("city_id >", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdGreaterThanOrEqualTo(String value) {
            addCriterion("city_id >=", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdLessThan(String value) {
            addCriterion("city_id <", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdLessThanOrEqualTo(String value) {
            addCriterion("city_id <=", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdLike(String value) {
            addCriterion("city_id like", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotLike(String value) {
            addCriterion("city_id not like", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdIn(List<String> values) {
            addCriterion("city_id in", values, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotIn(List<String> values) {
            addCriterion("city_id not in", values, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdBetween(String value1, String value2) {
            addCriterion("city_id between", value1, value2, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotBetween(String value1, String value2) {
            addCriterion("city_id not between", value1, value2, "cityId");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeIsNull() {
            addCriterion("advance_notice_time is null");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeIsNotNull() {
            addCriterion("advance_notice_time is not null");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeEqualTo(Date value) {
            addCriterion("advance_notice_time =", value, "advanceNoticeTime");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeNotEqualTo(Date value) {
            addCriterion("advance_notice_time <>", value, "advanceNoticeTime");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeGreaterThan(Date value) {
            addCriterion("advance_notice_time >", value, "advanceNoticeTime");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("advance_notice_time >=", value, "advanceNoticeTime");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeLessThan(Date value) {
            addCriterion("advance_notice_time <", value, "advanceNoticeTime");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeLessThanOrEqualTo(Date value) {
            addCriterion("advance_notice_time <=", value, "advanceNoticeTime");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeIn(List<Date> values) {
            addCriterion("advance_notice_time in", values, "advanceNoticeTime");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeNotIn(List<Date> values) {
            addCriterion("advance_notice_time not in", values, "advanceNoticeTime");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeBetween(Date value1, Date value2) {
            addCriterion("advance_notice_time between", value1, value2, "advanceNoticeTime");
            return (Criteria) this;
        }

        public Criteria andAdvanceNoticeTimeNotBetween(Date value1, Date value2) {
            addCriterion("advance_notice_time not between", value1, value2, "advanceNoticeTime");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeIsNull() {
            addCriterion("sale_start_time is null");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeIsNotNull() {
            addCriterion("sale_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeEqualTo(Date value) {
            addCriterion("sale_start_time =", value, "saleStartTime");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeNotEqualTo(Date value) {
            addCriterion("sale_start_time <>", value, "saleStartTime");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeGreaterThan(Date value) {
            addCriterion("sale_start_time >", value, "saleStartTime");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sale_start_time >=", value, "saleStartTime");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeLessThan(Date value) {
            addCriterion("sale_start_time <", value, "saleStartTime");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("sale_start_time <=", value, "saleStartTime");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeIn(List<Date> values) {
            addCriterion("sale_start_time in", values, "saleStartTime");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeNotIn(List<Date> values) {
            addCriterion("sale_start_time not in", values, "saleStartTime");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeBetween(Date value1, Date value2) {
            addCriterion("sale_start_time between", value1, value2, "saleStartTime");
            return (Criteria) this;
        }

        public Criteria andSaleStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("sale_start_time not between", value1, value2, "saleStartTime");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeIsNull() {
            addCriterion("sale_end_time is null");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeIsNotNull() {
            addCriterion("sale_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeEqualTo(Date value) {
            addCriterion("sale_end_time =", value, "saleEndTime");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeNotEqualTo(Date value) {
            addCriterion("sale_end_time <>", value, "saleEndTime");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeGreaterThan(Date value) {
            addCriterion("sale_end_time >", value, "saleEndTime");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sale_end_time >=", value, "saleEndTime");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeLessThan(Date value) {
            addCriterion("sale_end_time <", value, "saleEndTime");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("sale_end_time <=", value, "saleEndTime");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeIn(List<Date> values) {
            addCriterion("sale_end_time in", values, "saleEndTime");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeNotIn(List<Date> values) {
            addCriterion("sale_end_time not in", values, "saleEndTime");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeBetween(Date value1, Date value2) {
            addCriterion("sale_end_time between", value1, value2, "saleEndTime");
            return (Criteria) this;
        }

        public Criteria andSaleEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("sale_end_time not between", value1, value2, "saleEndTime");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysIsNull() {
            addCriterion("effective_days is null");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysIsNotNull() {
            addCriterion("effective_days is not null");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysEqualTo(Integer value) {
            addCriterion("effective_days =", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysNotEqualTo(Integer value) {
            addCriterion("effective_days <>", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysGreaterThan(Integer value) {
            addCriterion("effective_days >", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("effective_days >=", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysLessThan(Integer value) {
            addCriterion("effective_days <", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysLessThanOrEqualTo(Integer value) {
            addCriterion("effective_days <=", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysIn(List<Integer> values) {
            addCriterion("effective_days in", values, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysNotIn(List<Integer> values) {
            addCriterion("effective_days not in", values, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysBetween(Integer value1, Integer value2) {
            addCriterion("effective_days between", value1, value2, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("effective_days not between", value1, value2, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeIsNull() {
            addCriterion("valid_days_type is null");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeIsNotNull() {
            addCriterion("valid_days_type is not null");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeEqualTo(Integer value) {
            addCriterion("valid_days_type =", value, "validDaysType");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeNotEqualTo(Integer value) {
            addCriterion("valid_days_type <>", value, "validDaysType");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeGreaterThan(Integer value) {
            addCriterion("valid_days_type >", value, "validDaysType");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("valid_days_type >=", value, "validDaysType");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeLessThan(Integer value) {
            addCriterion("valid_days_type <", value, "validDaysType");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeLessThanOrEqualTo(Integer value) {
            addCriterion("valid_days_type <=", value, "validDaysType");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeIn(List<Integer> values) {
            addCriterion("valid_days_type in", values, "validDaysType");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeNotIn(List<Integer> values) {
            addCriterion("valid_days_type not in", values, "validDaysType");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeBetween(Integer value1, Integer value2) {
            addCriterion("valid_days_type between", value1, value2, "validDaysType");
            return (Criteria) this;
        }

        public Criteria andValidDaysTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("valid_days_type not between", value1, value2, "validDaysType");
            return (Criteria) this;
        }

        public Criteria andInitStockIsNull() {
            addCriterion("init_stock is null");
            return (Criteria) this;
        }

        public Criteria andInitStockIsNotNull() {
            addCriterion("init_stock is not null");
            return (Criteria) this;
        }

        public Criteria andInitStockEqualTo(Integer value) {
            addCriterion("init_stock =", value, "initStock");
            return (Criteria) this;
        }

        public Criteria andInitStockNotEqualTo(Integer value) {
            addCriterion("init_stock <>", value, "initStock");
            return (Criteria) this;
        }

        public Criteria andInitStockGreaterThan(Integer value) {
            addCriterion("init_stock >", value, "initStock");
            return (Criteria) this;
        }

        public Criteria andInitStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("init_stock >=", value, "initStock");
            return (Criteria) this;
        }

        public Criteria andInitStockLessThan(Integer value) {
            addCriterion("init_stock <", value, "initStock");
            return (Criteria) this;
        }

        public Criteria andInitStockLessThanOrEqualTo(Integer value) {
            addCriterion("init_stock <=", value, "initStock");
            return (Criteria) this;
        }

        public Criteria andInitStockIn(List<Integer> values) {
            addCriterion("init_stock in", values, "initStock");
            return (Criteria) this;
        }

        public Criteria andInitStockNotIn(List<Integer> values) {
            addCriterion("init_stock not in", values, "initStock");
            return (Criteria) this;
        }

        public Criteria andInitStockBetween(Integer value1, Integer value2) {
            addCriterion("init_stock between", value1, value2, "initStock");
            return (Criteria) this;
        }

        public Criteria andInitStockNotBetween(Integer value1, Integer value2) {
            addCriterion("init_stock not between", value1, value2, "initStock");
            return (Criteria) this;
        }

        public Criteria andStockIsNull() {
            addCriterion("stock is null");
            return (Criteria) this;
        }

        public Criteria andStockIsNotNull() {
            addCriterion("stock is not null");
            return (Criteria) this;
        }

        public Criteria andStockEqualTo(Integer value) {
            addCriterion("stock =", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotEqualTo(Integer value) {
            addCriterion("stock <>", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockGreaterThan(Integer value) {
            addCriterion("stock >", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("stock >=", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockLessThan(Integer value) {
            addCriterion("stock <", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockLessThanOrEqualTo(Integer value) {
            addCriterion("stock <=", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockIn(List<Integer> values) {
            addCriterion("stock in", values, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotIn(List<Integer> values) {
            addCriterion("stock not in", values, "stock");
            return (Criteria) this;
        }

        public Criteria andStockBetween(Integer value1, Integer value2) {
            addCriterion("stock between", value1, value2, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotBetween(Integer value1, Integer value2) {
            addCriterion("stock not between", value1, value2, "stock");
            return (Criteria) this;
        }

        public Criteria andSalesIsNull() {
            addCriterion("sales is null");
            return (Criteria) this;
        }

        public Criteria andSalesIsNotNull() {
            addCriterion("sales is not null");
            return (Criteria) this;
        }

        public Criteria andSalesEqualTo(Integer value) {
            addCriterion("sales =", value, "sales");
            return (Criteria) this;
        }

        public Criteria andSalesNotEqualTo(Integer value) {
            addCriterion("sales <>", value, "sales");
            return (Criteria) this;
        }

        public Criteria andSalesGreaterThan(Integer value) {
            addCriterion("sales >", value, "sales");
            return (Criteria) this;
        }

        public Criteria andSalesGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales >=", value, "sales");
            return (Criteria) this;
        }

        public Criteria andSalesLessThan(Integer value) {
            addCriterion("sales <", value, "sales");
            return (Criteria) this;
        }

        public Criteria andSalesLessThanOrEqualTo(Integer value) {
            addCriterion("sales <=", value, "sales");
            return (Criteria) this;
        }

        public Criteria andSalesIn(List<Integer> values) {
            addCriterion("sales in", values, "sales");
            return (Criteria) this;
        }

        public Criteria andSalesNotIn(List<Integer> values) {
            addCriterion("sales not in", values, "sales");
            return (Criteria) this;
        }

        public Criteria andSalesBetween(Integer value1, Integer value2) {
            addCriterion("sales between", value1, value2, "sales");
            return (Criteria) this;
        }

        public Criteria andSalesNotBetween(Integer value1, Integer value2) {
            addCriterion("sales not between", value1, value2, "sales");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagIsNull() {
            addCriterion("display_flag is null");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagIsNotNull() {
            addCriterion("display_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagEqualTo(Integer value) {
            addCriterion("display_flag =", value, "displayFlag");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagNotEqualTo(Integer value) {
            addCriterion("display_flag <>", value, "displayFlag");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagGreaterThan(Integer value) {
            addCriterion("display_flag >", value, "displayFlag");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("display_flag >=", value, "displayFlag");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagLessThan(Integer value) {
            addCriterion("display_flag <", value, "displayFlag");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagLessThanOrEqualTo(Integer value) {
            addCriterion("display_flag <=", value, "displayFlag");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagIn(List<Integer> values) {
            addCriterion("display_flag in", values, "displayFlag");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagNotIn(List<Integer> values) {
            addCriterion("display_flag not in", values, "displayFlag");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagBetween(Integer value1, Integer value2) {
            addCriterion("display_flag between", value1, value2, "displayFlag");
            return (Criteria) this;
        }

        public Criteria andDisplayFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("display_flag not between", value1, value2, "displayFlag");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationIsNull() {
            addCriterion("single_order_duration is null");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationIsNotNull() {
            addCriterion("single_order_duration is not null");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationEqualTo(BigDecimal value) {
            addCriterion("single_order_duration =", value, "singleOrderDuration");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationNotEqualTo(BigDecimal value) {
            addCriterion("single_order_duration <>", value, "singleOrderDuration");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationGreaterThan(BigDecimal value) {
            addCriterion("single_order_duration >", value, "singleOrderDuration");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("single_order_duration >=", value, "singleOrderDuration");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationLessThan(BigDecimal value) {
            addCriterion("single_order_duration <", value, "singleOrderDuration");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationLessThanOrEqualTo(BigDecimal value) {
            addCriterion("single_order_duration <=", value, "singleOrderDuration");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationIn(List<BigDecimal> values) {
            addCriterion("single_order_duration in", values, "singleOrderDuration");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationNotIn(List<BigDecimal> values) {
            addCriterion("single_order_duration not in", values, "singleOrderDuration");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("single_order_duration between", value1, value2, "singleOrderDuration");
            return (Criteria) this;
        }

        public Criteria andSingleOrderDurationNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("single_order_duration not between", value1, value2, "singleOrderDuration");
            return (Criteria) this;
        }

        public Criteria andStyleTypeIsNull() {
            addCriterion("style_type is null");
            return (Criteria) this;
        }

        public Criteria andStyleTypeIsNotNull() {
            addCriterion("style_type is not null");
            return (Criteria) this;
        }

        public Criteria andStyleTypeEqualTo(Integer value) {
            addCriterion("style_type =", value, "styleType");
            return (Criteria) this;
        }

        public Criteria andStyleTypeNotEqualTo(Integer value) {
            addCriterion("style_type <>", value, "styleType");
            return (Criteria) this;
        }

        public Criteria andStyleTypeGreaterThan(Integer value) {
            addCriterion("style_type >", value, "styleType");
            return (Criteria) this;
        }

        public Criteria andStyleTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("style_type >=", value, "styleType");
            return (Criteria) this;
        }

        public Criteria andStyleTypeLessThan(Integer value) {
            addCriterion("style_type <", value, "styleType");
            return (Criteria) this;
        }

        public Criteria andStyleTypeLessThanOrEqualTo(Integer value) {
            addCriterion("style_type <=", value, "styleType");
            return (Criteria) this;
        }

        public Criteria andStyleTypeIn(List<Integer> values) {
            addCriterion("style_type in", values, "styleType");
            return (Criteria) this;
        }

        public Criteria andStyleTypeNotIn(List<Integer> values) {
            addCriterion("style_type not in", values, "styleType");
            return (Criteria) this;
        }

        public Criteria andStyleTypeBetween(Integer value1, Integer value2) {
            addCriterion("style_type between", value1, value2, "styleType");
            return (Criteria) this;
        }

        public Criteria andStyleTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("style_type not between", value1, value2, "styleType");
            return (Criteria) this;
        }

        public Criteria andBackUrlIsNull() {
            addCriterion("back_url is null");
            return (Criteria) this;
        }

        public Criteria andBackUrlIsNotNull() {
            addCriterion("back_url is not null");
            return (Criteria) this;
        }

        public Criteria andBackUrlEqualTo(String value) {
            addCriterion("back_url =", value, "backUrl");
            return (Criteria) this;
        }

        public Criteria andBackUrlNotEqualTo(String value) {
            addCriterion("back_url <>", value, "backUrl");
            return (Criteria) this;
        }

        public Criteria andBackUrlGreaterThan(String value) {
            addCriterion("back_url >", value, "backUrl");
            return (Criteria) this;
        }

        public Criteria andBackUrlGreaterThanOrEqualTo(String value) {
            addCriterion("back_url >=", value, "backUrl");
            return (Criteria) this;
        }

        public Criteria andBackUrlLessThan(String value) {
            addCriterion("back_url <", value, "backUrl");
            return (Criteria) this;
        }

        public Criteria andBackUrlLessThanOrEqualTo(String value) {
            addCriterion("back_url <=", value, "backUrl");
            return (Criteria) this;
        }

        public Criteria andBackUrlLike(String value) {
            addCriterion("back_url like", value, "backUrl");
            return (Criteria) this;
        }

        public Criteria andBackUrlNotLike(String value) {
            addCriterion("back_url not like", value, "backUrl");
            return (Criteria) this;
        }

        public Criteria andBackUrlIn(List<String> values) {
            addCriterion("back_url in", values, "backUrl");
            return (Criteria) this;
        }

        public Criteria andBackUrlNotIn(List<String> values) {
            addCriterion("back_url not in", values, "backUrl");
            return (Criteria) this;
        }

        public Criteria andBackUrlBetween(String value1, String value2) {
            addCriterion("back_url between", value1, value2, "backUrl");
            return (Criteria) this;
        }

        public Criteria andBackUrlNotBetween(String value1, String value2) {
            addCriterion("back_url not between", value1, value2, "backUrl");
            return (Criteria) this;
        }

        public Criteria andRulesIsNull() {
            addCriterion("rules is null");
            return (Criteria) this;
        }

        public Criteria andRulesIsNotNull() {
            addCriterion("rules is not null");
            return (Criteria) this;
        }

        public Criteria andRulesEqualTo(String value) {
            addCriterion("rules =", value, "rules");
            return (Criteria) this;
        }

        public Criteria andRulesNotEqualTo(String value) {
            addCriterion("rules <>", value, "rules");
            return (Criteria) this;
        }

        public Criteria andRulesGreaterThan(String value) {
            addCriterion("rules >", value, "rules");
            return (Criteria) this;
        }

        public Criteria andRulesGreaterThanOrEqualTo(String value) {
            addCriterion("rules >=", value, "rules");
            return (Criteria) this;
        }

        public Criteria andRulesLessThan(String value) {
            addCriterion("rules <", value, "rules");
            return (Criteria) this;
        }

        public Criteria andRulesLessThanOrEqualTo(String value) {
            addCriterion("rules <=", value, "rules");
            return (Criteria) this;
        }

        public Criteria andRulesLike(String value) {
            addCriterion("rules like", value, "rules");
            return (Criteria) this;
        }

        public Criteria andRulesNotLike(String value) {
            addCriterion("rules not like", value, "rules");
            return (Criteria) this;
        }

        public Criteria andRulesIn(List<String> values) {
            addCriterion("rules in", values, "rules");
            return (Criteria) this;
        }

        public Criteria andRulesNotIn(List<String> values) {
            addCriterion("rules not in", values, "rules");
            return (Criteria) this;
        }

        public Criteria andRulesBetween(String value1, String value2) {
            addCriterion("rules between", value1, value2, "rules");
            return (Criteria) this;
        }

        public Criteria andRulesNotBetween(String value1, String value2) {
            addCriterion("rules not between", value1, value2, "rules");
            return (Criteria) this;
        }

        public Criteria andCardStatusIsNull() {
            addCriterion("card_status is null");
            return (Criteria) this;
        }

        public Criteria andCardStatusIsNotNull() {
            addCriterion("card_status is not null");
            return (Criteria) this;
        }

        public Criteria andCardStatusEqualTo(Integer value) {
            addCriterion("card_status =", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusNotEqualTo(Integer value) {
            addCriterion("card_status <>", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusGreaterThan(Integer value) {
            addCriterion("card_status >", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("card_status >=", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusLessThan(Integer value) {
            addCriterion("card_status <", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusLessThanOrEqualTo(Integer value) {
            addCriterion("card_status <=", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusIn(List<Integer> values) {
            addCriterion("card_status in", values, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusNotIn(List<Integer> values) {
            addCriterion("card_status not in", values, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusBetween(Integer value1, Integer value2) {
            addCriterion("card_status between", value1, value2, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("card_status not between", value1, value2, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNull() {
            addCriterion("create_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNotNull() {
            addCriterion("create_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdEqualTo(Long value) {
            addCriterion("create_oper_id =", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotEqualTo(Long value) {
            addCriterion("create_oper_id <>", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThan(Long value) {
            addCriterion("create_oper_id >", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_oper_id >=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThan(Long value) {
            addCriterion("create_oper_id <", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("create_oper_id <=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIn(List<Long> values) {
            addCriterion("create_oper_id in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotIn(List<Long> values) {
            addCriterion("create_oper_id not in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdBetween(Long value1, Long value2) {
            addCriterion("create_oper_id between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("create_oper_id not between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNull() {
            addCriterion("create_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNotNull() {
            addCriterion("create_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameEqualTo(String value) {
            addCriterion("create_oper_name =", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotEqualTo(String value) {
            addCriterion("create_oper_name <>", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThan(String value) {
            addCriterion("create_oper_name >", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_name >=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThan(String value) {
            addCriterion("create_oper_name <", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThanOrEqualTo(String value) {
            addCriterion("create_oper_name <=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLike(String value) {
            addCriterion("create_oper_name like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotLike(String value) {
            addCriterion("create_oper_name not like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIn(List<String> values) {
            addCriterion("create_oper_name in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotIn(List<String> values) {
            addCriterion("create_oper_name not in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameBetween(String value1, String value2) {
            addCriterion("create_oper_name between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotBetween(String value1, String value2) {
            addCriterion("create_oper_name not between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNull() {
            addCriterion("update_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNotNull() {
            addCriterion("update_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdEqualTo(Long value) {
            addCriterion("update_oper_id =", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotEqualTo(Long value) {
            addCriterion("update_oper_id <>", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThan(Long value) {
            addCriterion("update_oper_id >", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_oper_id >=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThan(Long value) {
            addCriterion("update_oper_id <", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("update_oper_id <=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIn(List<Long> values) {
            addCriterion("update_oper_id in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotIn(List<Long> values) {
            addCriterion("update_oper_id not in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdBetween(Long value1, Long value2) {
            addCriterion("update_oper_id between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("update_oper_id not between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNull() {
            addCriterion("update_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNotNull() {
            addCriterion("update_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameEqualTo(String value) {
            addCriterion("update_oper_name =", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotEqualTo(String value) {
            addCriterion("update_oper_name <>", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThan(String value) {
            addCriterion("update_oper_name >", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_oper_name >=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThan(String value) {
            addCriterion("update_oper_name <", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThanOrEqualTo(String value) {
            addCriterion("update_oper_name <=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLike(String value) {
            addCriterion("update_oper_name like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotLike(String value) {
            addCriterion("update_oper_name not like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIn(List<String> values) {
            addCriterion("update_oper_name in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotIn(List<String> values) {
            addCriterion("update_oper_name not in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameBetween(String value1, String value2) {
            addCriterion("update_oper_name between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotBetween(String value1, String value2) {
            addCriterion("update_oper_name not between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_base
     *
     * @mbggenerated do_not_delete_during_merge Wed Jan 11 20:28:14 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}