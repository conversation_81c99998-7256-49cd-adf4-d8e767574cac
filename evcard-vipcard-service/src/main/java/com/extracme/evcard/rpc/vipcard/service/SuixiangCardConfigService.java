package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.csvreader.CsvWriter;
import com.extracme.evcard.membership.core.dto.MemberWrapInfoDto;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.MmpUserTagDto;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.IMembershipWrapService;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.vipcard.dao.*;
import com.extracme.evcard.rpc.vipcard.dto.CarRentalCfgDto;
import com.extracme.evcard.rpc.vipcard.dto.LeasePriceCfgDto;
import com.extracme.evcard.rpc.vipcard.dto.ServiceFeeCfgDto;
import com.extracme.evcard.rpc.vipcard.dto.SuixiangCardUseCountDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;
import com.extracme.evcard.rpc.vipcard.enums.IsDeleteEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardBaseCardStatusEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardConfigOperateTypeEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardEffectiveImmediatelyEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardFileTypeEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardIssueTypeEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardPaymentStatusEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardRecordLogOperationTypeEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardServiceFeeEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardStatusEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardStyleImageEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardUseLogOperationTypeEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuixiangCardValidDaysTypeEnum;
import com.extracme.evcard.rpc.vipcard.manager.SuixiangCardBaseManager;
import com.extracme.evcard.rpc.vipcard.model.*;
import com.extracme.evcard.rpc.vipcard.service.inner.OrgService;
import com.extracme.evcard.rpc.vipcard.service.store.ConfigLoader;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import com.extracme.evcard.rpc.vipcard.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Slf4j
@Service
public class SuixiangCardConfigService implements ISuixiangCardConfigService {

    @Resource
    private SuixiangCardBaseManager suixiangCardBaseManager;

    @Resource
    private SuixiangCardBaseMapper suixiangCardBaseMapper;

    @Resource
    private SuixiangCardRentDaysMapper suixiangCardRentDaysMapper;

    @Resource
    private SuixiangCardPriceMapper suixiangCardPriceMapper;

    @Resource
    private SuixiangCardUseMapper suixiangCardUseMapper;

    @Resource
    private SuixiangCardConfigOperationLogMapper suixiangCardConfigOperationLogMapper;

    @Resource
    private SuixiangCardPurchaseRecordMapper suixiangCardPurchaseRecordMapper;

    @Resource
    private SuixiangCardFileOperationLogMapper suixiangCardFileOperationLogMapper;

    @Resource
    private OrgService orgService;

    @Resource
    private ConfigLoader configLoader;

    @Resource
    private IMemberShipService memberShipService;

    @Resource
    private IMembershipWrapService membershipWrapService;

    @Resource
    private IMessagepushServ messageServ;

    @Resource
    private ISuiXiangCardService suiXiangCardService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private SuixiangCardCdkMapper suixiangCardCdkMapper;

    @Override
    public AddSuiXiangCardOutput addSuiXiangCard(AddSuiXiangCardInput req) {
        log.info("新增随享卡，请求参数：{}", JSON.toJSONString(req));
        AddSuiXiangCardOutput resp = new AddSuiXiangCardOutput();

        // 检查参数的必填及格式
        BaseResponse checkResp = checkInput(req);
        if (!Constants.STATUS_OK.equals(checkResp.getCode())) {
            resp.setBaseResponse(checkResp);
            log.info("新增随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        // 组装参数，准备插库
        // 随享卡基础表
        SuixiangCardBase base = new SuixiangCardBase();
        base.setCardName(ComUtils.splitStr(req.getCardName(), 64));
        base.setOrgId(req.getOrgCode());

        //节假日是否可用
        base.setHolidayAvailable(req.getHolidayAvailable());
        //不可用日期
        if(CollectionUtils.isNotEmpty(req.getUnavailableDate())){
            base.setUnavailableDate(JSONObject.toJSONString(req.getUnavailableDate()));
        }
        //购买上限数
        base.setPurchaseLimitNum(req.getPurchaseLimitNum());

        StringBuffer cityIdSb = new StringBuffer();
        if (CollectionUtils.isNotEmpty(req.getCityId())) {
            int size = req.getCityId().size();
            for (int i = 0; i < size; i++) {
                cityIdSb.append(req.getCityId().get(i));
                if (i < size - 1) {
                    cityIdSb.append(Constants.STR_COMMA);
                }
            }
        }
        base.setCityId(cityIdSb.toString());

        if (StringUtils.isNotBlank(req.getPreviewTime())) {
            base.setAdvanceNoticeTime(DateUtil.getDateFromStr(req.getPreviewTime(), DateUtil.DATE_TYPE8));
        }
        base.setSaleStartTime(DateUtil.getDateFromStr(req.getSaleStartTime(), DateUtil.DATE_TYPE8));
        base.setSaleEndTime(DateUtil.getDateFromStr(req.getSaleEndTime() + ":59", DateUtil.simple));
        base.setValidDaysType(req.getValidTime());
        base.setInitStock(req.getStock());
        base.setStock(req.getStock());
        base.setDisplayFlag(req.getIsDisplay());
        base.setSingleOrderDuration(BigDecimal.valueOf(req.getSingleOrderDuration()));
        base.setStyleType(req.getImageNo());
        base.setBackUrl(SuiXiangCardStyleImageEnum.getUrlByStyleNo(req.getImageNo()));
        base.setRules(ComUtils.splitStr(req.getPurchaseNotes(), 2000));

        if (req.getEffectiveImmediately() == SuiXiangCardEffectiveImmediatelyEnum.YES.getEffectiveImmediately()) {
            base.setCardStatus(SuiXiangCardBaseCardStatusEnum.TO_BE_PUT_ON_SHELF.getStatus());
        } else {
            base.setCardStatus(SuiXiangCardBaseCardStatusEnum.TO_BE_SUBMITTED.getStatus());
        }

        if (req.getOperatorDto() != null) {
            base.setCreateOperId(req.getOperatorDto().getOperatorId());
            base.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
            base.setUpdateOperId(req.getOperatorDto().getOperatorId());
            base.setUpdateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
        }

        // 随享卡充值功能配置
        int vehicleBrandId = req.getVehicleBrandId();
        if (vehicleBrandId > 0 ) {
            base.setVehicleBrandIds("" + vehicleBrandId);
        }
        if (req.getLandingPageFlag() == 1 || req.getLandingPageFlag() == 2) {
            base.setLandingPageFlag(req.getLandingPageFlag());
        }
        if (req.getMergeFlag() == 1 ||  req.getMergeFlag() == 2) {
            base.setMergeFlag(req.getMergeFlag());
        }

        // 随享卡租期表
        List<SuixiangCardRentDays> rentDaysList = new ArrayList<>();
        for (LeasePriceCfgDto leasePriceCfgDto : req.getLeasePriceCfgDtoList()) {
            SuixiangCardRentDays rentDays = new SuixiangCardRentDays();
            rentDays.setRentDays(leasePriceCfgDto.getLeaseTerm());
            if (req.getOperatorDto() != null) {
                rentDays.setCreateOperId(req.getOperatorDto().getOperatorId());
                rentDays.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
            }
            int totalServiceFeesAmout = 0;
            if (CollectionUtils.isNotEmpty(leasePriceCfgDto.getServiceFeeCfgDtoList())) {
                rentDays.setServiceFees(JSON.toJSONString(leasePriceCfgDto.getServiceFeeCfgDtoList()));
                for (ServiceFeeCfgDto serviceFeeCfgDto : leasePriceCfgDto.getServiceFeeCfgDtoList()) {
                    totalServiceFeesAmout += serviceFeeCfgDto.getCrossedPrice();
                }
            }
            rentDays.setTotalServiceFeesAmout(new BigDecimal(totalServiceFeesAmout));

            // 随享卡价格表
            List<SuixiangCardPrice> priceList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(leasePriceCfgDto.getCarRentalCfgDtoList())) {
                for (CarRentalCfgDto carRentalCfgDto : leasePriceCfgDto.getCarRentalCfgDtoList()) {
                    SuixiangCardPrice cardPrice = new SuixiangCardPrice();
                    cardPrice.setSalesPrice(new BigDecimal(carRentalCfgDto.getPrice()));
                    cardPrice.setUnderlinePrice(new BigDecimal(carRentalCfgDto.getCrossedPrice()));
                    cardPrice.setCarModelGroup(ComUtils.splitStr(carRentalCfgDto.getModelGroup(), 100));
                    if (StringUtils.isNotEmpty(carRentalCfgDto.getLandingPagePicUrl())) {
                        cardPrice.setLandingPagePicUrl(carRentalCfgDto.getLandingPagePicUrl());
                    }
                    if (StringUtils.isNotEmpty(carRentalCfgDto.getLandingPageHeadPicUrl())) {
                        cardPrice.setLandingPageHeadPicUrl(carRentalCfgDto.getLandingPageHeadPicUrl());
                    }

                    StringBuffer modelIdSb = new StringBuffer();
                    if (CollectionUtils.isNotEmpty(carRentalCfgDto.getModelIdList())) {
                        int size = carRentalCfgDto.getModelIdList().size();
                        for (int i = 0; i < size; i++) {
                            modelIdSb.append(carRentalCfgDto.getModelIdList().get(i));
                            if (i < size - 1) {
                                modelIdSb.append(Constants.STR_COMMA);
                            }
                        }
                    }
                    cardPrice.setCarModelIds(modelIdSb.toString());

                    if (req.getOperatorDto() != null) {
                        cardPrice.setCreateOperId(req.getOperatorDto().getOperatorId());
                        cardPrice.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                    }

                    priceList.add(cardPrice);
                }
            }
            rentDays.setPriceList(priceList);

            rentDaysList.add(rentDays);
        }

        // 随享卡配置操作日志表
        SuixiangCardConfigOperationLog operationLog = new SuixiangCardConfigOperationLog();
        operationLog.setOperateType(SuiXiangCardConfigOperateTypeEnum.ADD.getOperateType());
        operationLog.setOperationContent("新增随享卡（状态：待提交->待上架）");
        if (req.getOperatorDto() != null) {
            operationLog.setCreateOperId(req.getOperatorDto().getOperatorId());
            operationLog.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
        }

        // 开启事务，新增记录和操作日志
        Long id = null;
        try {
            id = suixiangCardBaseManager.addSuiXiangCard(base, rentDaysList, operationLog);
        } catch (Exception e) {
            log.error("新增随享卡数据库操作异常！", e);
            BaseResponse baseResponse = new BaseResponse(-2, "新增随享卡数据库操作异常");
            resp.setBaseResponse(baseResponse);
            log.info("新增随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        BaseResponse baseResponse = new BaseResponse(0, "成功");
        resp.setBaseResponse(baseResponse);
        resp.setId(id);
        log.info("新增随享卡，应答参数：{}", JSON.toJSONString(resp));
        return resp;
    }

    private BaseResponse checkInput(AddSuiXiangCardInput req) {
        BaseResponse checkResp = new BaseResponse();
        if (StringUtils.isBlank(req.getCardName())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写随享卡名称");
            return checkResp;
        } else if (req.getCardName().length() > 12) {
            checkResp.setCode(-1);
            checkResp.setMessage("随享卡名称的最大字数为12个");
            return checkResp;
        }
        if (StringUtils.isBlank(req.getOrgCode())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请选择运营公司");
            return checkResp;
        }
        if (CollectionUtils.isEmpty(req.getCityId())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请选择可用区域");
            return checkResp;
        }
        // 预告时间，如果送了，则需要校验格式 yyyy-MM-dd HH:mm
        if (StringUtils.isNotBlank(req.getPreviewTime())) {
            if (!DateUtil.isValidDateString(req.getPreviewTime(), DateUtil.DATE_TYPE8)) {
                checkResp.setCode(-1);
                checkResp.setMessage("预告时间格式非法，正确格式为 yyyy-MM-dd HH:mm");
                return checkResp;
            }
        }
        if (StringUtils.isBlank(req.getSaleStartTime())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写售卖开始时间");
            return checkResp;
        } else {
            if (!DateUtil.isValidDateString(req.getSaleStartTime(), DateUtil.DATE_TYPE8)) {
                checkResp.setCode(-1);
                checkResp.setMessage("售卖开始时间格式非法，正确格式为 yyyy-MM-dd HH:mm");
                return checkResp;
            }
        }
        if (StringUtils.isBlank(req.getSaleEndTime())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写售卖结束时间");
            return checkResp;
        } else {
            if (!DateUtil.isValidDateString(req.getSaleEndTime(), DateUtil.DATE_TYPE8)) {
                checkResp.setCode(-1);
                checkResp.setMessage("售卖结束时间格式非法，正确格式为 yyyy-MM-dd HH:mm");
                return checkResp;
            }
        }

        Date nowDate = new Date();
        Date saleStartTime = DateUtil.getDateFromStr(req.getSaleStartTime(), DateUtil.DATE_TYPE8);
        Date saleEndTime = DateUtil.getDateFromStr(req.getSaleEndTime() + ":59", DateUtil.simple);

        // 售卖开始时间不能早于当前系统日期
        if (saleStartTime.before(nowDate)) {
            checkResp.setCode(-1);
            checkResp.setMessage("售卖开始时间不能早于当前系统时间");
            return checkResp;
        }

        // 售卖开始时间不能晚于售卖结束时间
        if (saleStartTime.after(saleEndTime)) {
            checkResp.setCode(-1);
            checkResp.setMessage("售卖开始时间不能晚于售卖结束时间");
            return checkResp;
        }

        if (StringUtils.isNotBlank(req.getPreviewTime())) {
            Date previewTime = DateUtil.getDateFromStr(req.getPreviewTime(), DateUtil.DATE_TYPE8);

            // 预告时间不能早于当前系统日期
            if (previewTime.before(nowDate)) {
                checkResp.setCode(-1);
                checkResp.setMessage("预告时间不能早于当前系统时间");
                return checkResp;
            }

            // 预告时间不能晚于售卖开始时间
            if (previewTime.after(saleStartTime)) {
                checkResp.setCode(-1);
                checkResp.setMessage("预告时间不能晚于售卖开始时间");
                return checkResp;
            }
        }

        if (req.getValidTime() == 0) {
            checkResp.setCode(-1);
            checkResp.setMessage("请选择有效期");
            return checkResp;
        } else if (SuixiangCardValidDaysTypeEnum.isInValid(req.getValidTime())) {
            checkResp.setCode(-1);
            checkResp.setMessage("有效期类型非法");
            return checkResp;
        }
        if (req.getStock() <= 0 || req.getStock() > 99999) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写正确的库存数量");
            return checkResp;
        }
        if (req.getIsDisplay() != 1 && req.getIsDisplay() != 2) {
            checkResp.setCode(-1);
            checkResp.setMessage("isDisplay字段非法");
            return checkResp;
        }
        if (req.getSingleOrderDuration() <= 0
                || req.getSingleOrderDuration() > 999999999.9
                || ComUtils.getNumberDecimalDigits(req.getSingleOrderDuration()) > 1) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写正确的单订单时长");
            return checkResp;
        }
        if (CollectionUtils.isEmpty(req.getLeasePriceCfgDtoList())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写租期价格配置");
            return checkResp;
        } else {
            int firstRentCarRentalSize = 0; // 第一个租期的车辆租金配置个数，需求要求各个租期的租金配置数要相同
            Map<Integer, String> firstModelNameMap = new HashMap<>(); // 第一个租期的车型组名称的map，需求要求各个租期的对应位置的车型组名称要一致
            int rentIndex = 0;
            for (LeasePriceCfgDto leasePriceCfgDto : req.getLeasePriceCfgDtoList()) {
                if (leasePriceCfgDto.getLeaseTerm() <= 0) {
                    checkResp.setCode(-1);
                    checkResp.setMessage("请填写正确的租期天数");
                    return checkResp;
                }
                if (CollectionUtils.isEmpty(leasePriceCfgDto.getCarRentalCfgDtoList())) {
                    checkResp.setCode(-1);
                    checkResp.setMessage("请填写车辆租金信息");
                    return checkResp;
                } else {
                    // 如果是首次到这里，直接赋值
                    if (firstRentCarRentalSize == 0) {
                        firstRentCarRentalSize = leasePriceCfgDto.getCarRentalCfgDtoList().size();
                    } else { // 非首次，要进行对比，不同，则直接报错
                        if (firstRentCarRentalSize != leasePriceCfgDto.getCarRentalCfgDtoList().size()) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("保存失败，不同租期的车型组条数必须一致，请修改后重新保存");
                            return checkResp;
                        }
                    }
                    int index = 0;
                    for (CarRentalCfgDto carRentalCfgDto : leasePriceCfgDto.getCarRentalCfgDtoList()) {
                        if (carRentalCfgDto.getPrice() <= 0) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("请填写正确的售价");
                            return checkResp;
                        }
                        if (carRentalCfgDto.getCrossedPrice() < 0) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("请填写正确的划线价");
                            return checkResp;
                        }
                        if (StringUtils.isBlank(carRentalCfgDto.getModelGroup())) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("请填写车型组名称");
                            return checkResp;
                        }
                        List<String> modelIdList = carRentalCfgDto.getModelIdList();
                        if (CollectionUtils.isEmpty(modelIdList)) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("请填写可用车型");
                            return checkResp;
                        }
                        // 如果是首次进来，进行赋值
                        if (rentIndex == 0) {
                            firstModelNameMap.put(index++, carRentalCfgDto.getModelGroup());
                        } else {
                            if (!carRentalCfgDto.getModelGroup().equals(firstModelNameMap.get(index++))) {
                                checkResp.setCode(-1);
                                checkResp.setMessage("保存失败，不同租期的车型组名称必须一致，请修改后重新保存");
                                return checkResp;
                            }
                        }

                        // 验证车型配置
                        if (modelIdList.contains("-1") && modelIdList.size() > 1 ){
                            return new BaseResponse(-1, "全部车型不能与具体车型同时配置");
                        }

                    }
                }
                if (CollectionUtils.isNotEmpty(leasePriceCfgDto.getServiceFeeCfgDtoList())) {
                    for (ServiceFeeCfgDto serviceFeeCfgDto : leasePriceCfgDto.getServiceFeeCfgDtoList()) {
                        if (serviceFeeCfgDto.getFeeType() <= 0 || serviceFeeCfgDto.getFeeType() > 6) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("feeType字段非法");
                            return checkResp;
                        }
                        if (serviceFeeCfgDto.getCrossedPrice() < 0) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("请填写正确的划线价");
                            return checkResp;
                        }
                    }
                }
                rentIndex++;
            }
        }
        if (req.getImageNo() <= 0 || req.getImageNo() > 3) {
            checkResp.setCode(-1);
            checkResp.setMessage("imageNo字段非法");
            return checkResp;
        }
        if (StringUtils.isBlank(req.getPurchaseNotes())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写购买须知");
            return checkResp;
        } else if (req.getPurchaseNotes().length() > 2000) {
            checkResp.setCode(-1);
            checkResp.setMessage("购买须知的最大字数为2000个");
            return checkResp;
        }
        if (req.getEffectiveImmediately() <= 0 || req.getEffectiveImmediately() > 2) {
            checkResp.setCode(-1);
            checkResp.setMessage("effectiveImmediately字段非法");
            return checkResp;
        }
        if (req.getPurchaseLimitNum() < 0) {
            checkResp.setCode(-1);
            checkResp.setMessage("购买上限数小于0");
            return checkResp;
        }

        if (req.getLandingPageFlag() != 0 && req.getLandingPageFlag() != 1 && req.getLandingPageFlag() != 2) {
            checkResp.setCode(-1);
            checkResp.setMessage("是否有落地页字段非法");
            return checkResp;
        }

        if (req.getMergeFlag() != 0 && req.getMergeFlag() != 1 && req.getMergeFlag() != 2) {
            checkResp.setCode(-1);
            checkResp.setMessage("兑换卡是否可以合并字段非法");
            return checkResp;
        }

        // 验证城市和车型配置的合法性
        String cityIds = String.join(Constants.STR_COMMA, req.getCityId());
        if (StringUtils.isNotBlank(cityIds)) {
            if (cityIds.contains("-1") && cityIds.contains(Constants.STR_COMMA)) {
                return new BaseResponse(-1, "全部城市不能与具体城市同时配置");
            }
        }
        return checkResp;
    }

    @Override
    public BaseResponse modifySuiXiangCard(ModifySuiXiangCardInput req) {
        log.info("修改随享卡，请求参数：{}", JSON.toJSONString(req));
        BaseResponse resp = new BaseResponse();

        // 检查参数的必填及格式
        BaseResponse checkResp = checkInput(req);
        if (!Constants.STATUS_OK.equals(checkResp.getCode())) {
            log.info("修改随享卡，应答参数：{}", JSON.toJSONString(checkResp));
            return checkResp;
        }

        // 根据id查询随享卡基础表
        SuixiangCardBase baseOrg = suixiangCardBaseMapper.selectByPrimaryKey(req.getId());
        // 记录不存在，则报错
        if (baseOrg == null) {
            resp.setCode(-1);
            resp.setMessage("根据id[" + req.getId() + "]查询不到随享卡记录");
            log.info("修改随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }
        // 状态不是“1-待提交”，不能修改
        if (!baseOrg.getCardStatus().equals(SuiXiangCardBaseCardStatusEnum.TO_BE_SUBMITTED.getStatus())) {
            resp.setCode(-1);
            resp.setMessage("只有待提交状态的记录，才支持修改");
            log.info("修改随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        Date nowDate = new Date();

        // 随享卡基础表，直接更新
        SuixiangCardBase base = new SuixiangCardBase();
        base.setId(req.getId());
        base.setCardName(ComUtils.splitStr(req.getCardName(), 64));
        base.setOrgId(req.getOrgCode());
        base.setHolidayAvailable(req.getHolidayAvailable());

        // 是否有落地页
        if (req.getLandingPageFlag() == 1 || req.getLandingPageFlag() == 2) {
           base.setLandingPageFlag(req.getLandingPageFlag());
        }

        //不可用日期
        if(CollectionUtils.isNotEmpty(req.getUnavailableDate())){
            base.setUnavailableDate(JSONObject.toJSONString(req.getUnavailableDate()));
        }
        //购买上限数
        base.setPurchaseLimitNum(req.getPurchaseLimitNum());

        StringBuffer cityIdSb = new StringBuffer();
        if (CollectionUtils.isNotEmpty(req.getCityId())) {
            int size = req.getCityId().size();
            for (int i = 0; i < size; i++) {
                cityIdSb.append(req.getCityId().get(i));
                if (i < size - 1) {
                    cityIdSb.append(Constants.STR_COMMA);
                }
            }
        }
        base.setCityId(cityIdSb.toString());

        if (StringUtils.isNotBlank(req.getPreviewTime())) {
            base.setAdvanceNoticeTime(DateUtil.getDateFromStr(req.getPreviewTime(), DateUtil.DATE_TYPE8));
        }
        base.setSaleStartTime(DateUtil.getDateFromStr(req.getSaleStartTime(), DateUtil.DATE_TYPE8));
        base.setSaleEndTime(DateUtil.getDateFromStr(req.getSaleEndTime() + ":59", DateUtil.simple));
        base.setValidDaysType(req.getValidTime());
        base.setInitStock(req.getStock());
        base.setStock(req.getStock());
        base.setDisplayFlag(req.getIsDisplay());
        base.setSingleOrderDuration(BigDecimal.valueOf(req.getSingleOrderDuration()));
        base.setStyleType(req.getImageNo());
        base.setBackUrl(SuiXiangCardStyleImageEnum.getUrlByStyleNo(req.getImageNo()));
        base.setRules(ComUtils.splitStr(req.getPurchaseNotes(), 2000));
        if (req.getEffectiveImmediately() == SuiXiangCardEffectiveImmediatelyEnum.YES.getEffectiveImmediately()) {
            base.setCardStatus(SuiXiangCardBaseCardStatusEnum.TO_BE_PUT_ON_SHELF.getStatus());
        } else {
            base.setCardStatus(SuiXiangCardBaseCardStatusEnum.TO_BE_SUBMITTED.getStatus());
        }

        if (req.getOperatorDto() != null) {
            base.setUpdateOperId(req.getOperatorDto().getOperatorId());
            base.setUpdateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
        }

        // 随享卡租期表，老记录逻辑删除，再新增新纪录
        List<SuixiangCardRentDays> rentDaysList = new ArrayList<>();
        for (LeasePriceCfgDto leasePriceCfgDto : req.getLeasePriceCfgDtoList()) {
            SuixiangCardRentDays rentDays = new SuixiangCardRentDays();
            rentDays.setRentDays(leasePriceCfgDto.getLeaseTerm());
            if (req.getOperatorDto() != null) {
                rentDays.setUpdateOperId(req.getOperatorDto().getOperatorId());
                rentDays.setUpdateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
            }
            rentDays.setUpdateTime(nowDate);
            int totalServiceFeesAmout = 0;
            if (CollectionUtils.isNotEmpty(leasePriceCfgDto.getServiceFeeCfgDtoList())) {
                rentDays.setServiceFees(JSON.toJSONString(leasePriceCfgDto.getServiceFeeCfgDtoList()));
                for (ServiceFeeCfgDto serviceFeeCfgDto : leasePriceCfgDto.getServiceFeeCfgDtoList()) {
                    totalServiceFeesAmout += serviceFeeCfgDto.getCrossedPrice();
                }
            }
            rentDays.setTotalServiceFeesAmout(new BigDecimal(totalServiceFeesAmout));

            // 随享卡价格表，老记录逻辑删除，再新增新记录
            List<SuixiangCardPrice> priceList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(leasePriceCfgDto.getCarRentalCfgDtoList())) {
                for (CarRentalCfgDto carRentalCfgDto : leasePriceCfgDto.getCarRentalCfgDtoList()) {
                    SuixiangCardPrice cardPrice = new SuixiangCardPrice();
                    cardPrice.setSalesPrice(new BigDecimal(carRentalCfgDto.getPrice()));
                    cardPrice.setUnderlinePrice(new BigDecimal(carRentalCfgDto.getCrossedPrice()));
                    cardPrice.setCarModelGroup(ComUtils.splitStr(carRentalCfgDto.getModelGroup(), 100));
                    if (StringUtils.isNotEmpty(carRentalCfgDto.getLandingPagePicUrl())) {
                        cardPrice.setLandingPagePicUrl(carRentalCfgDto.getLandingPagePicUrl());
                    }
                    if (StringUtils.isNotEmpty(carRentalCfgDto.getLandingPageHeadPicUrl())) {
                        cardPrice.setLandingPageHeadPicUrl(carRentalCfgDto.getLandingPageHeadPicUrl());
                    }

                    StringBuffer modelIdSb = new StringBuffer();
                    if (CollectionUtils.isNotEmpty(carRentalCfgDto.getModelIdList())) {
                        int size = carRentalCfgDto.getModelIdList().size();
                        for (int i = 0; i < size; i++) {
                            modelIdSb.append(carRentalCfgDto.getModelIdList().get(i));
                            if (i < size - 1) {
                                modelIdSb.append(Constants.STR_COMMA);
                            }
                        }
                    }
                    cardPrice.setCarModelIds(modelIdSb.toString());

                    if (req.getOperatorDto() != null) {
                        cardPrice.setUpdateOperId(req.getOperatorDto().getOperatorId());
                        cardPrice.setUpdateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                    }
                    cardPrice.setUpdateTime(nowDate);

                    priceList.add(cardPrice);
                }
            }
            rentDays.setPriceList(priceList);

            rentDaysList.add(rentDays);
        }

        // 随享卡配置操作日志表
        SuixiangCardConfigOperationLog operationLog = new SuixiangCardConfigOperationLog();
        operationLog.setOperateType(SuiXiangCardConfigOperateTypeEnum.MODIFY.getOperateType());
        if (req.getOperatorDto() != null) {
            operationLog.setCreateOperId(req.getOperatorDto().getOperatorId());
            operationLog.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
        }

        // 开启事务，新增记录和操作日志
        try {
            suixiangCardBaseManager.modifySuiXiangCard(base, rentDaysList, operationLog, baseOrg);
        } catch (Exception e) {
            log.error("修改随享卡数据库操作异常！", e);
            resp.setCode(-2);
            resp.setMessage("修改随享卡数据库操作异常");
            log.info("修改随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        resp.setCode(0);
        resp.setMessage("成功");
        log.info("修改随享卡，应答参数：{}", JSON.toJSONString(resp));
        return resp;
    }

    private BaseResponse checkInput(ModifySuiXiangCardInput req) {
        BaseResponse checkResp = new BaseResponse();
        if (req.getId() <= 0) {
            checkResp.setCode(-1);
            checkResp.setMessage("请送正确的id");
            return checkResp;
        }
        if (StringUtils.isBlank(req.getCardName())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写随享卡名称");
            return checkResp;
        } else if (req.getCardName().length() > 12) {
            checkResp.setCode(-1);
            checkResp.setMessage("随享卡名称的最大字数为12个");
            return checkResp;
        }
        if (StringUtils.isBlank(req.getOrgCode())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请选择运营公司");
            return checkResp;
        }
        if (CollectionUtils.isEmpty(req.getCityId())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请选择可用区域");
            return checkResp;
        }
        // 预告时间，如果送了，则需要校验格式 yyyy-MM-dd HH:mm
        if (StringUtils.isNotBlank(req.getPreviewTime())) {
            if (!DateUtil.isValidDateString(req.getPreviewTime(), DateUtil.DATE_TYPE8)) {
                checkResp.setCode(-1);
                checkResp.setMessage("预告时间格式非法，正确格式为 yyyy-MM-dd HH:mm");
                return checkResp;
            }
        }
        if (StringUtils.isBlank(req.getSaleStartTime())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写售卖开始时间");
            return checkResp;
        } else {
            if (!DateUtil.isValidDateString(req.getSaleStartTime(), DateUtil.DATE_TYPE8)) {
                checkResp.setCode(-1);
                checkResp.setMessage("售卖开始时间格式非法，正确格式为 yyyy-MM-dd HH:mm");
                return checkResp;
            }
        }
        if (StringUtils.isBlank(req.getSaleEndTime())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写售卖结束时间");
            return checkResp;
        } else {
            if (!DateUtil.isValidDateString(req.getSaleEndTime(), DateUtil.DATE_TYPE8)) {
                checkResp.setCode(-1);
                checkResp.setMessage("售卖结束时间格式非法，正确格式为 yyyy-MM-dd HH:mm");
                return checkResp;
            }
        }

        Date nowDate = new Date();
        Date saleStartTime = DateUtil.getDateFromStr(req.getSaleStartTime(), DateUtil.DATE_TYPE8);
        Date saleEndTime = DateUtil.getDateFromStr(req.getSaleEndTime() + ":59", DateUtil.simple);

        // 售卖开始时间不能早于当前系统日期
        if (saleStartTime.before(nowDate)) {
            checkResp.setCode(-1);
            checkResp.setMessage("售卖开始时间不能早于当前系统时间");
            return checkResp;
        }

        // 售卖开始时间不能晚于售卖结束时间
        if (saleStartTime.after(saleEndTime)) {
            checkResp.setCode(-1);
            checkResp.setMessage("售卖开始时间不能晚于售卖结束时间");
            return checkResp;
        }

        if (StringUtils.isNotBlank(req.getPreviewTime())) {
            Date previewTime = DateUtil.getDateFromStr(req.getPreviewTime(), DateUtil.DATE_TYPE8);

            // 预告时间不能早于当前系统日期
            if (previewTime.before(nowDate)) {
                checkResp.setCode(-1);
                checkResp.setMessage("预告时间不能早于当前系统时间");
                return checkResp;
            }

            // 预告时间不能晚于售卖开始时间
            if (previewTime.after(saleStartTime)) {
                checkResp.setCode(-1);
                checkResp.setMessage("预告时间不能晚于售卖开始时间");
                return checkResp;
            }
        }

        if (req.getValidTime() == 0) {
            checkResp.setCode(-1);
            checkResp.setMessage("请选择有效期");
            return checkResp;
        } else if (SuixiangCardValidDaysTypeEnum.isInValid(req.getValidTime())) {
            checkResp.setCode(-1);
            checkResp.setMessage("有效期类型非法");
            return checkResp;
        }
        if (req.getStock() <= 0 || req.getStock() > 99999) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写正确的库存数量");
            return checkResp;
        }
        if (req.getIsDisplay() != 1 && req.getIsDisplay() != 2) {
            checkResp.setCode(-1);
            checkResp.setMessage("isDisplay字段非法");
            return checkResp;
        }
        if (req.getSingleOrderDuration() <= 0
                || req.getSingleOrderDuration() > 999999999.9
                || ComUtils.getNumberDecimalDigits(req.getSingleOrderDuration()) > 1) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写正确的单订单时长");
            return checkResp;
        }
        if (CollectionUtils.isEmpty(req.getLeasePriceCfgDtoList())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写租期价格配置");
            return checkResp;
        } else {
            int firstRentCarRentalSize = 0; // 第一个租期的车辆租金配置个数，需求要求各个租期的租金配置数要相同
            Map<Integer, String> firstModelNameMap = new HashMap<>(); // 第一个租期的车型组名称的map，需求要求各个租期的对应位置的车型组名称要一致
            int rentIndex = 0;
            for (LeasePriceCfgDto leasePriceCfgDto : req.getLeasePriceCfgDtoList()) {
                if (leasePriceCfgDto.getLeaseTerm() <= 0) {
                    checkResp.setCode(-1);
                    checkResp.setMessage("请填写正确的租期天数");
                    return checkResp;
                }
                if (CollectionUtils.isEmpty(leasePriceCfgDto.getCarRentalCfgDtoList())) {
                    checkResp.setCode(-1);
                    checkResp.setMessage("请填写车辆租金信息");
                    return checkResp;
                } else {
                    // 如果是首次到这里，直接赋值
                    if (firstRentCarRentalSize == 0) {
                        firstRentCarRentalSize = leasePriceCfgDto.getCarRentalCfgDtoList().size();
                    } else { // 非首次，要进行对比，不同，则直接报错
                        if (firstRentCarRentalSize != leasePriceCfgDto.getCarRentalCfgDtoList().size()) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("保存失败，不同租期的车型组条数必须一致，请修改后重新保存");
                            return checkResp;
                        }
                    }
                    int index = 0;
                    for (CarRentalCfgDto carRentalCfgDto : leasePriceCfgDto.getCarRentalCfgDtoList()) {
                        if (carRentalCfgDto.getPrice() <= 0) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("请填写正确的售价");
                            return checkResp;
                        }
                        if (carRentalCfgDto.getCrossedPrice() < 0) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("请填写正确的划线价");
                            return checkResp;
                        }
                        if (StringUtils.isBlank(carRentalCfgDto.getModelGroup())) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("请填写车型组名称");
                            return checkResp;
                        }
                        List<String> modelIdList = carRentalCfgDto.getModelIdList();
                        if (CollectionUtils.isEmpty(modelIdList)) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("请填写可用车型");
                            return checkResp;
                        }
                        // 如果是首次进来，进行赋值
                        if (rentIndex == 0) {
                            firstModelNameMap.put(index++, carRentalCfgDto.getModelGroup());
                        } else {
                            if (!carRentalCfgDto.getModelGroup().equals(firstModelNameMap.get(index++))) {
                                checkResp.setCode(-1);
                                checkResp.setMessage("保存失败，不同租期的车型组名称必须一致，请修改后重新保存");
                                return checkResp;
                            }
                        }
                        // 验证车型配置
                        if (modelIdList.contains("-1") && modelIdList.size() > 1 ){
                            return new BaseResponse(-1, "全部车型不能与具体车型同时配置");
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(leasePriceCfgDto.getServiceFeeCfgDtoList())) {
                    for (ServiceFeeCfgDto serviceFeeCfgDto : leasePriceCfgDto.getServiceFeeCfgDtoList()) {
                        if (serviceFeeCfgDto.getFeeType() <= 0 || serviceFeeCfgDto.getFeeType() > 6) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("feeType字段非法");
                            return checkResp;
                        }
                        if (serviceFeeCfgDto.getCrossedPrice() < 0) {
                            checkResp.setCode(-1);
                            checkResp.setMessage("请填写正确的划线价");
                            return checkResp;
                        }
                    }
                }
                rentIndex++;
            }
        }
        if (req.getImageNo() <= 0 || req.getImageNo() > 3) {
            checkResp.setCode(-1);
            checkResp.setMessage("imageNo字段非法");
            return checkResp;
        }
        if (StringUtils.isBlank(req.getPurchaseNotes())) {
            checkResp.setCode(-1);
            checkResp.setMessage("请填写购买须知");
            return checkResp;
        } else if (req.getPurchaseNotes().length() > 2000) {
            checkResp.setCode(-1);
            checkResp.setMessage("购买须知的最大字数为2000个");
            return checkResp;
        }
        if (req.getEffectiveImmediately() <= 0 || req.getEffectiveImmediately() > 2) {
            checkResp.setCode(-1);
            checkResp.setMessage("effectiveImmediately字段非法");
            return checkResp;
        }
        if (req.getPurchaseLimitNum() < 0) {
            checkResp.setCode(-1);
            checkResp.setMessage("购买上限数小于0");
            return checkResp;
        }

        if (req.getLandingPageFlag() != 0 && req.getLandingPageFlag() != 1 && req.getLandingPageFlag() != 2) {
            checkResp.setCode(-1);
            checkResp.setMessage("是否有落地页字段非法");
            return checkResp;
        }

        // 验证城市和车型配置的合法性
        String cityIds = String.join(Constants.STR_COMMA, req.getCityId());
        if (StringUtils.isNotBlank(cityIds)) {
            if (cityIds.contains("-1") && cityIds.contains(Constants.STR_COMMA)) {
                return new BaseResponse(-1, "全部城市不能与具体城市同时配置");
            }
        }

        return checkResp;
    }

    @Override
    public BaseResponse onlineSuiXiangCard(OnlineSuiXiangCardInput req) {
        log.info("上架随享卡，请求参数：{}", JSON.toJSONString(req));
        BaseResponse resp = new BaseResponse();

        if (req.getId() <= 0) {
            resp.setCode(-1);
            resp.setMessage("请送正确的id");
            log.info("上架随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        // 根据id查询随享卡基础表
        SuixiangCardBase baseOrg = suixiangCardBaseMapper.selectByPrimaryKey(req.getId());
        // 记录不存在，则报错
        if (baseOrg == null) {
            resp.setCode(-1);
            resp.setMessage("根据id[" + req.getId() + "]查询不到随享卡记录");
            log.info("上架随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }
        // 状态不是“1-待提交”，不能修改
        if (!baseOrg.getCardStatus().equals(SuiXiangCardBaseCardStatusEnum.TO_BE_SUBMITTED.getStatus())) {
            resp.setCode(-1);
            resp.setMessage("只有待提交状态的记录，才支持上架");
            log.info("上架随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }
        // 如果当前系统时间超过售卖结束时间，则报错“当前已超过售卖结束时间，请修改时间后再操作上架”
        Date nowDate = new Date();
        if (nowDate.after(baseOrg.getSaleEndTime())) {
            resp.setCode(-1);
            resp.setMessage("当前已超过售卖结束时间，请修改时间后再操作上架");
            log.info("上架随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        // 随享卡基础表，直接更新
        SuixiangCardBase base = new SuixiangCardBase();
        base.setId(req.getId());
        base.setCardStatus(SuiXiangCardBaseCardStatusEnum.TO_BE_PUT_ON_SHELF.getStatus());
        if (req.getOperatorDto() != null) {
            base.setUpdateOperId(req.getOperatorDto().getOperatorId());
            base.setUpdateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
        }

        // 随享卡配置操作日志表
        SuixiangCardConfigOperationLog operationLog = new SuixiangCardConfigOperationLog();
        operationLog.setOperateType(SuiXiangCardConfigOperateTypeEnum.RELEASE.getOperateType());
        operationLog.setOperationContent("上架随享卡");
        if (req.getOperatorDto() != null) {
            operationLog.setCreateOperId(req.getOperatorDto().getOperatorId());
            operationLog.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
        }

        // 开启事务，新增记录和操作日志
        try {
            suixiangCardBaseManager.onlineSuiXiangCard(base, operationLog);
        } catch (Exception e) {
            log.error("上架随享卡数据库操作异常！", e);
            resp.setCode(-2);
            resp.setMessage("上架随享卡数据库操作异常");
            log.info("上架随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        resp.setCode(0);
        resp.setMessage("成功");
        log.info("上架随享卡，应答参数：{}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public BaseResponse offlineSuiXiangCard(OfflineSuiXiangCardInput req) {
        log.info("下架随享卡，请求参数：{}", JSON.toJSONString(req));
        BaseResponse resp = new BaseResponse();

        if (req.getId() <= 0) {
            resp.setCode(-1);
            resp.setMessage("请送正确的id");
            log.info("下架随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        // 根据id查询随享卡基础表
        SuixiangCardBase baseOrg = suixiangCardBaseMapper.selectByPrimaryKey(req.getId());
        // 记录不存在，则报错
        if (baseOrg == null) {
            resp.setCode(-1);
            resp.setMessage("根据id[" + req.getId() + "]查询不到随享卡记录");
            log.info("下架随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }
        // 状态不是“2-待上架”或“3-已上架”，不能修改
        if (!baseOrg.getCardStatus().equals(SuiXiangCardBaseCardStatusEnum.TO_BE_PUT_ON_SHELF.getStatus())
                && !baseOrg.getCardStatus().equals(SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus())) {
            resp.setCode(-1);
            resp.setMessage("只有待上架或已上架状态的记录，才支持下架");
            log.info("下架随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        // 随享卡基础表，直接更新
        SuixiangCardBase base = new SuixiangCardBase();
        base.setId(req.getId());
        base.setCardStatus(SuiXiangCardBaseCardStatusEnum.OFF_SHELF.getStatus());
        if (req.getOperatorDto() != null) {
            base.setUpdateOperId(req.getOperatorDto().getOperatorId());
            base.setUpdateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
        }

        // 随享卡配置操作日志表
        SuixiangCardConfigOperationLog operationLog = new SuixiangCardConfigOperationLog();
        operationLog.setOperateType(SuiXiangCardConfigOperateTypeEnum.OFFLINE.getOperateType());
        operationLog.setOperationContent("下架随享卡");
        if (req.getOperatorDto() != null) {
            operationLog.setCreateOperId(req.getOperatorDto().getOperatorId());
            operationLog.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
        }

        // 开启事务，新增记录和操作日志
        try {
            suixiangCardBaseManager.offlineSuiXiangCard(base, operationLog);
        } catch (Exception e) {
            log.error("下架随享卡数据库操作异常！", e);
            resp.setCode(-2);
            resp.setMessage("下架随享卡数据库操作异常");
            log.info("下架随享卡，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        resp.setCode(0);
        resp.setMessage("成功");
        log.info("下架随享卡，应答参数：{}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public QuerySuiXiangCardListOutput querySuiXiangCardList(QuerySuiXiangCardListInput req) {
        log.info("查询随享卡列表，请求参数：{}", JSON.toJSONString(req));
        QuerySuiXiangCardListOutput resp = new QuerySuiXiangCardListOutput();
        BaseResponse baseResponse = new BaseResponse();
        resp.setBaseResponse(baseResponse);

        // 校验必填参数
        if (req.getPageNum() <= 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请送正确的pageNum");
            log.info("查询随享卡列表，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }
        if (req.getPageSize() <= 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请送正确的pageSize");
            log.info("查询随享卡列表，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        Page page = new Page(req.getPageNum(), req.getPageSize());

        SuixiangCardBase baseQuery = new SuixiangCardBase();
        if (StringUtils.isNotBlank(req.getCardName())) {
            baseQuery.setCardName(req.getCardName());
        }
        if (req.getCardId() > 0) {
            baseQuery.setId(req.getCardId());
        }
        if (req.getCardStatus() > 0) {
            baseQuery.setCardStatus(req.getCardStatus());
        }
        if (StringUtils.isNotBlank(req.getOrgCode())) {
            baseQuery.setOrgId(req.getOrgCode());
        }
        if (StringUtils.isNotBlank(req.getSaleStartTime())) {
            if (!DateUtil.isValidDateString(req.getSaleStartTime(), DateUtil.DATE_TYPE1)) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("售卖开始日期格式非法，正确格式为 yyyy-MM-dd");
                log.info("查询随享卡列表，应答参数：{}", JSON.toJSONString(resp));
                return resp;
            }
            Date saleStartTime = DateUtil.getDateFromStrEx(req.getSaleStartTime(), DateUtil.DATE_TYPE1);
            baseQuery.setSaleStartTime(saleStartTime);
        }
        if (StringUtils.isNotBlank(req.getSaleEndTime())) {
            if (!DateUtil.isValidDateString(req.getSaleEndTime(), DateUtil.DATE_TYPE1)) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("售卖结束日期格式非法，正确格式为 yyyy-MM-dd");
                log.info("查询随享卡列表，应答参数：{}", JSON.toJSONString(resp));
                return resp;
            }
            Date saleEndTime = DateUtil.getDateFromStrEx(req.getSaleEndTime() + " 23:59:59", DateUtil.simple);
            baseQuery.setSaleEndTime(saleEndTime);
        }
        if (StringUtils.isNotBlank(req.getPreviewTime())) {
            if (!DateUtil.isValidDateString(req.getPreviewTime(), DateUtil.DATE_TYPE1)) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("预告日期格式非法，正确格式为 yyyy-MM-dd");
                log.info("查询随享卡列表，应答参数：{}", JSON.toJSONString(resp));
                return resp;
            }
            Date advanceNoticeTimeStart = DateUtil.getDateFromStrEx(req.getPreviewTime(), DateUtil.DATE_TYPE1);
            Date advanceNoticeTimeEnd = DateUtil.getDateFromStrEx(req.getPreviewTime() + " 23:59:59", DateUtil.simple);
            baseQuery.setAdvanceNoticeTimeStart(advanceNoticeTimeStart);
            baseQuery.setAdvanceNoticeTimeEnd(advanceNoticeTimeEnd);
        }
        if (StringUtils.isNotBlank(req.getCityId())) {
            baseQuery.setCityId(req.getCityId());
        }
        if (req.getIsDisplay() > 0) {
            baseQuery.setDisplayFlag(req.getIsDisplay());
        }
        if (StringUtils.isNotBlank(req.getSaleMin())) {
            int saleMin;
            try {
                saleMin = Integer.parseInt(req.getSaleMin());
            } catch (NumberFormatException e) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("售出数量下限格式非法");
                log.info("查询随享卡列表，应答参数：{}", JSON.toJSONString(resp));
                return resp;
            }
            baseQuery.setSaleMin(saleMin);
        }
        if (StringUtils.isNotBlank(req.getSaleMax())) {
            int saleMax;
            try {
                saleMax = Integer.parseInt(req.getSaleMax());
            } catch (NumberFormatException e) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("售出数量上限格式非法");
                log.info("查询随享卡列表，应答参数：{}", JSON.toJSONString(resp));
                return resp;
            }
            baseQuery.setSaleMax(saleMax);
        }
        if (StringUtils.isNotBlank(req.getLeftStockMin())) {
            int leftStockMin;
            try {
                leftStockMin = Integer.parseInt(req.getLeftStockMin());
            } catch (NumberFormatException e) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("剩余库存下限格式非法");
                log.info("查询随享卡列表，应答参数：{}", JSON.toJSONString(resp));
                return resp;
            }
            baseQuery.setLeftStockMin(leftStockMin);
        }
        if (StringUtils.isNotBlank(req.getLeftStockMax())) {
            int leftStockMax;
            try {
                leftStockMax = Integer.parseInt(req.getLeftStockMax());
            } catch (NumberFormatException e) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("剩余库存上限格式非法");
                log.info("查询随享卡列表，应答参数：{}", JSON.toJSONString(resp));
                return resp;
            }
            baseQuery.setLeftStockMax(leftStockMax);
        }

        // 查询总条数
        int total = suixiangCardBaseMapper.countForPage(baseQuery);
        List<SuiXiangCardItemDto> suiXiangCardItemDtoList = new ArrayList<>();
        if (total > 0) {
            // 查询当前分页的数据
            List<SuixiangCardBase> list = suixiangCardBaseMapper.selectForPage(baseQuery, page);
            if (CollectionUtils.isNotEmpty(list)) {
                // 租期信息需要从 suixiang_card_rent_days表中统计出来
                List<Long> cardBaseIdList = list.stream().map(item -> item.getId()).collect(Collectors.toList());
                List<SuiXiangCardRentDaysMinDto> rentDaysMinList = suixiangCardRentDaysMapper.selectMinRentDaysGroupByCardBaseId(cardBaseIdList);
                Map<Long, Integer> rentDaysMinMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(rentDaysMinList)) {
                    rentDaysMinMap = rentDaysMinList.stream().collect(Collectors.toMap(SuiXiangCardRentDaysMinDto::getCardBaseId, SuiXiangCardRentDaysMinDto::getMinRentDays));
                }
                for (SuixiangCardBase base : list) {
                    SuiXiangCardItemDto dto = new SuiXiangCardItemDto();
                    dto.setId(base.getId());
                    dto.setCardName(base.getCardName());
                    dto.setCardStatus(base.getCardStatus());
                    dto.setIsDisplay(base.getDisplayFlag());
                    Integer minRentDays = rentDaysMinMap.get(base.getId());
                    dto.setMinLeaseTerm(minRentDays == null ? "" : (minRentDays.toString() + "天/起"));
                    dto.setLeftStock(base.getStock());
                    dto.setPreviewTime(base.getAdvanceNoticeTime() == null ? "" : DateUtil.getFormatDate(base.getAdvanceNoticeTime(), DateUtil.DATE_TYPE8));
                    dto.setSaleTimeRange((base.getSaleStartTime() == null ? "" : DateUtil.getFormatDate(base.getSaleStartTime(), DateUtil.DATE_TYPE8)) + "~" +
                            (base.getSaleEndTime() == null ? "" : DateUtil.getFormatDate(base.getSaleEndTime(), DateUtil.DATE_TYPE8)));
                    dto.setCityNames(suixiangCardBaseManager.getCityNamesByCityId(base.getCityId()));
                    dto.setOrgName(orgService.getOrgNameByOrgId(base.getOrgId()));
                    dto.setUpdateOperName(base.getUpdateOperName());

                    suiXiangCardItemDtoList.add(dto);
                }
            }
        }

        baseResponse.setCode(0);
        baseResponse.setMessage("成功");
        resp.setSuiXiangCardItemDtoList(suiXiangCardItemDtoList);
        resp.setTotal(total);
        log.info("查询随享卡列表，应答参数：{}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public QuerySuiXiangCardDetailOutput querySuiXiangCardDetail(QuerySuiXiangCardDetailInput req) {
        log.info("查询随享卡详情，请求参数：{}", JSON.toJSONString(req));
        QuerySuiXiangCardDetailOutput resp = new QuerySuiXiangCardDetailOutput();
        BaseResponse baseResponse = new BaseResponse();
        resp.setBaseResponse(baseResponse);

        if (req.getId() <= 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请送正确的id");
            log.info("查询随享卡详情，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        // 根据id查询随享卡基础表
        SuixiangCardBase base = suixiangCardBaseMapper.selectByPrimaryKey(req.getId());
        // 记录不存在，则报错
        if (base == null) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("根据id[" + req.getId() + "]查询不到随享卡记录");
            log.info("查询随享卡详情，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        resp.setId(base.getId());
        resp.setCardName(base.getCardName());
        resp.setOrgCode(base.getOrgId());
        resp.setCityId(base.getCityId());
        resp.setPreviewTime(base.getAdvanceNoticeTime() == null ? "" : DateUtil.getFormatDate(base.getAdvanceNoticeTime(), DateUtil.DATE_TYPE8));
        resp.setSaleStartTime(base.getSaleStartTime() == null ? "" : DateUtil.getFormatDate(base.getSaleStartTime(), DateUtil.DATE_TYPE8));
        resp.setSaleEndTime(base.getSaleEndTime() == null ? "" : DateUtil.getFormatDate(base.getSaleEndTime(), DateUtil.DATE_TYPE8));
        resp.setValidTime(base.getValidDaysType());
        resp.setStock(base.getInitStock());
        resp.setIsDisplay(base.getDisplayFlag());
        resp.setSingleOrderDuration(base.getSingleOrderDuration().doubleValue());
        resp.setImageUrl(base.getBackUrl());
        resp.setPurchaseNotes(base.getRules());
        resp.setImageNo(base.getStyleType());
        resp.setOrgName(orgService.getOrgNameByOrgId(base.getOrgId()));
        resp.setCityName(suixiangCardBaseManager.getCityNamesByCityId(base.getCityId()));
        resp.setCardStatus(base.getCardStatus());
        resp.setPurchaseLimitNum(base.getPurchaseLimitNum());
        resp.setHolidayAvailable(base.getHolidayAvailable());
        if(StringUtils.isNotEmpty(base.getUnavailableDate())){
            List<UnavailableDate> unavailableDateList = JSON.parseArray(base.getUnavailableDate(),UnavailableDate.class);
            resp.setUnavailableDate(unavailableDateList);
        }
        resp.setLandingPageFlag(base.getLandingPageFlag());
        resp.setMergeFlag(base.getMergeFlag());
        String vehicleBrandIds = base.getVehicleBrandIds();

        if (StringUtils.isNotBlank(vehicleBrandIds)) {
            String[] vehicleBrandIdArray = vehicleBrandIds.split(Constants.STR_COMMA);
            if (vehicleBrandIdArray != null && vehicleBrandIdArray.length > 0) {
                String vehicleBrandIdString = vehicleBrandIdArray[0];
                if (StringUtils.isNotBlank(vehicleBrandIdString)) {
                    resp.setVehicleBrandId(Integer.valueOf(vehicleBrandIdString));
                }
            }
        }

        List<LeasePriceCfgDto> leasePriceCfgDtoList = new ArrayList<>();
        resp.setLeasePriceCfgDtoList(leasePriceCfgDtoList);

        // 查询suixiang_card_rent_days表
        SuixiangCardRentDaysExample rentDaysExample = new SuixiangCardRentDaysExample();
        rentDaysExample.createCriteria()
                .andCardBaseIdEqualTo(base.getId())
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardRentDays> rentDaysList = suixiangCardRentDaysMapper.selectByExampleWithBLOBs(rentDaysExample);
        if (CollectionUtils.isNotEmpty(rentDaysList)) {
            List<Long> rentDaysIdList = rentDaysList.stream().map(item -> item.getId()).collect(Collectors.toList());

            // 查询suixiang_card_price表
            SuixiangCardPriceExample priceExample = new SuixiangCardPriceExample();
            priceExample.createCriteria()
                    .andCardBaseIdEqualTo(base.getId())
                    .andCardRentIdIn(rentDaysIdList)
                    .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
            List<SuixiangCardPrice> priceList = suixiangCardPriceMapper.selectByExample(priceExample);
            Map<Long, List<SuixiangCardPrice>> rentDaysPriceMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(priceList)) {
                rentDaysPriceMap = priceList.stream().collect(Collectors.groupingBy(SuixiangCardPrice::getCardRentId));
            }

            for (SuixiangCardRentDays rentDays : rentDaysList) {
                LeasePriceCfgDto leasePriceCfgDto = new LeasePriceCfgDto();
                leasePriceCfgDto.setLeaseTerm(rentDays.getRentDays());

                List<SuixiangCardPrice> thePriceList = rentDaysPriceMap.get(rentDays.getId());
                List<CarRentalCfgDto> carRentalCfgDtoList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(thePriceList)) {
                    for (SuixiangCardPrice price : thePriceList) {
                        CarRentalCfgDto carRentalCfgDto = new CarRentalCfgDto();
                        carRentalCfgDto.setPrice(price.getSalesPrice().intValue());
                        carRentalCfgDto.setCrossedPrice(price.getUnderlinePrice().intValue());
                        carRentalCfgDto.setModelGroup(price.getCarModelGroup());
                        carRentalCfgDto.setLandingPageHeadPicUrl(price.getLandingPageHeadPicUrl());
                        carRentalCfgDto.setLandingPagePicUrl(price.getLandingPagePicUrl());
                        List<String> modelIdList = new ArrayList<>();
                        StringBuffer modelNames = new StringBuffer();
                        String modelIdStr = price.getCarModelIds();
                        if (StringUtils.isNotBlank(modelIdStr)) {
                            if (modelIdStr.contains("-1")) {
                                modelIdList.add("-1");
                                modelNames.append("全部车型");
                            }else{
                                String[] modelIdArray = price.getCarModelIds().split(Constants.STR_COMMA);
                                int len = modelIdArray.length;
                                for (int j = 0; j < len; j++) {
                                    modelIdList.add(modelIdArray[j]);
                                    modelNames.append(configLoader.getGoodsModelInfo(Long.parseLong(modelIdArray[j])));
                                    if (j < len - 1) {
                                        modelNames.append(Constants.STR_SPLIT_ZH);
                                    }
                                }
                            }
                        }
                        carRentalCfgDto.setModelIdList(modelIdList);
                        carRentalCfgDto.setModelName(modelNames.toString());

                        carRentalCfgDtoList.add(carRentalCfgDto);
                    }
                }

                leasePriceCfgDto.setCarRentalCfgDtoList(carRentalCfgDtoList);
                leasePriceCfgDto.setServiceFeeCfgDtoList(JSON.parseArray(rentDays.getServiceFees(), ServiceFeeCfgDto.class));
                leasePriceCfgDto.setRentDayId(rentDays.getId());
                leasePriceCfgDtoList.add(leasePriceCfgDto);
            }
        }

        baseResponse.setCode(0);
        baseResponse.setMessage("成功");
        log.info("查询随享卡详情，应答参数：{}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public GiveSuiXiangCardOutput giveSuiXiangCard(GiveSuiXiangCardInput req) {
        log.info("赠送随享卡，请求参数：{}", JSON.toJSONString(req));
        GiveSuiXiangCardOutput resp = new GiveSuiXiangCardOutput();
        BaseResponse baseResponse = new BaseResponse();
        resp.setBaseResponse(baseResponse);

        // 校验入参
        if (validGiveSuiXiangCard(req, baseResponse)) {
            return resp;
        }

        // 根据id查询suixiang_card_base表
        SuixiangCardBase base = suixiangCardBaseMapper.selectByPrimaryKey(req.getId());
        if (base == null) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("根据随享卡id[" + req.getId() + "]查询不到随享卡的配置记录");
            return resp;
        }
        // 判断状态是否是 已上架
        if (!base.getCardStatus().equals(SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus())) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("随享卡id[" + req.getId() + "]不是上架状态，不能进行赠送操作");
            return resp;
        }
        // 没有到售卖开始时间，不能赠卡
        if (base.getSaleStartTime().after(new Date())) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("随享卡id[" + req.getId() + "]未到售卖开始时间，不能进行赠送操作");
            return resp;
        }


        List<SuiXiangCardGiveCardDto> dataList = new ArrayList<>(); // 输入的数据列表

        // 解析excel文件或输入框内容
        if (getData(req, baseResponse, dataList,base)) {
            return resp;
        }
        log.info("赠送随享卡，解析文件完毕");

        // 记录批量赠送文件
        SuixiangCardFileOperationLog suixiangCardFileOperationLog = new SuixiangCardFileOperationLog();
        suixiangCardFileOperationLog.setCardBaseId(req.getId());
        suixiangCardFileOperationLog.setSourceFile(req.getFileUrl());
        suixiangCardFileOperationLog.setTargetFile("");
        suixiangCardFileOperationLog.setCreateOperName(req.getOperatorDto().getOperatorName());
        suixiangCardFileOperationLog.setCreateOperId(req.getOperatorDto().getOperatorId());
        suixiangCardFileOperationLogMapper.insertSelective(suixiangCardFileOperationLog);
        // 赠送的业务逻辑
        taskExecutor.execute(() -> {
            commitBatchGiveSuiXiangCard(req, dataList, base, suixiangCardFileOperationLog);
        });
        resp.setFileId(suixiangCardFileOperationLog.getId());
        baseResponse.setMessage("正在赠送中，请稍后...");
        return resp;
    }

    @Override
    public QueryGiveSuiXiangCardOutput queryGiveSuiXiangCard(QueryGiveSuiXiangCardInput queryGiveSuiXiangCardInput) {
        long id = queryGiveSuiXiangCardInput.getId();
        SuixiangCardFileOperationLog suixiangCardFileOperationLog = suixiangCardFileOperationLogMapper.selectByPrimaryKey(id);

        QueryGiveSuiXiangCardOutput resp = new QueryGiveSuiXiangCardOutput();
        BaseResponse baseResponse = new BaseResponse();
        resp.setBaseResponse(baseResponse);
        if (StringUtils.isEmpty(suixiangCardFileOperationLog.getTargetFile())) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("正在赠送中，请稍后...");
            return resp;
        }
        Long totalCnt = suixiangCardFileOperationLog.getTotalCnt();
        Long successCnt = suixiangCardFileOperationLog.getSuccessCnt();
        Long failCnt = suixiangCardFileOperationLog.getFailCnt();
        String resultDesc = totalCnt + "个用户，其中" + successCnt + "个用户赠送成功，" + failCnt + "个用户赠送失败；";
        resp.setResultDesc(resultDesc);
        resp.setResultFileUrl(suixiangCardFileOperationLog.getTargetFile());
        log.info("赠送结果:{}", JSON.toJSONString(resp));

        return resp;
    }

    /**
     * 赠送业务
     */
    @Async
    public void commitBatchGiveSuiXiangCard(GiveSuiXiangCardInput req, List<SuiXiangCardGiveCardDto> dataList, SuixiangCardBase base, SuixiangCardFileOperationLog suixiangCardFileOperationLog) {

        // 赠送随享卡
        int listType = req.getListType(); // 名单类型：1-手机号码、2-会员ID
        long id = req.getId(); // 随享卡配置id
        // int failCnt = giveSuiXiangCardDataProcess(req, listType, id, dataList, base);
        int failCnt = processWithCompletableFuture(req, listType, id, dataList, base);
        // log.info("赠送随享卡，数据及处理结果：{}", JSON.toJSONString(dataList));

        // 生成结果excel文件，并上传阿里云
        // 检查并创建临时目录
        ComUtils.checkAndMkDir(TEMP_FILE_DIR);
        String fileName = "suiXiangCard_give_result_" + System.currentTimeMillis() + "_" + new Random().nextInt(10000) + ".xls";
        String filePath = TEMP_FILE_DIR + fileName;

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        int rowIndex = 0;

        // 写入标题行
        Row titleRow = sheet.createRow(rowIndex++);
        Cell titlcCell0 = titleRow.createCell(0);
        // 名单类型：1-手机号码、2-会员ID
        if (listType == SuiXiangCardFileTypeEnum.MOBILE_PHONE.getFileType()) {
            titlcCell0.setCellValue("会员手机号");
        } else {
            titlcCell0.setCellValue("会员ID");
        }
        titleRow.createCell(1).setCellValue("租期ID");
        titleRow.createCell(2).setCellValue("售价（元）");
        titleRow.createCell(3).setCellValue("赠送张数（1个用户仅支持发放1张，为空默认1张）");
        titleRow.createCell(4).setCellValue("赠送成功张数");
        titleRow.createCell(5).setCellValue("处理结果");

        // 写数据行
        for (SuiXiangCardGiveCardDto dto : dataList) {
            Row row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(dto.getUserInfo());
            row.createCell(1).setCellValue(dto.getRentDaysId());
            row.createCell(2).setCellValue(dto.getPrice());
            row.createCell(3).setCellValue(dto.getZhang());
            row.createCell(4).setCellValue(String.valueOf(dto.getSuccZhang()));
            row.createCell(5).setCellValue(dto.getResult());
        }

        // excel内容写到本次路径
        FileOutputStream fos = null;
        File resultFile = new File(filePath);
        try {
            fos = new FileOutputStream(resultFile);
            workbook.write(fos);
        } catch (Exception e) {
            log.error("赠送随享卡的结果文件，写入到本地失败！filePath={}", filePath, e);
//            baseResponse.setCode(-1);
//            baseResponse.setMessage("赠送随享卡的结果文件，写入到本地失败！");
//            return resp;
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error("close workbook error", e);
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    log.error("close fos error", e);
                }
            }
        }
        log.info("赠送随享卡，写本地结果文件完毕");

        // 上传阿里云
        String resultFileUrl = FileUtil.uploadSynByFilePath(filePath, "/giveResult/" + fileName);
        if (resultFileUrl == null) {
//            baseResponse.setCode(-1);
//            baseResponse.setMessage("赠送文件处理完毕！但是上传赠送结果文件到阿里云失败！");
//            return resp;
        }
        log.info("赠送随享卡，结果文件上传阿里云完毕");

        // 删除本地临时文件
        deleteTmpFile(resultFile);

        // 准备应答参数
        int totalCnt = dataList.size();
        int succCnt = totalCnt - failCnt;
        suixiangCardFileOperationLog.setTotalCnt((long) totalCnt);
        suixiangCardFileOperationLog.setSuccessCnt((long) succCnt);
        suixiangCardFileOperationLog.setFailCnt((long) failCnt);
        suixiangCardFileOperationLog.setTargetFile(resultFileUrl);

        String resultDesc = totalCnt + "个用户，其中" + succCnt + "个用户赠送成功，" + failCnt + "个用户赠送失败；";
        log.info("赠送随享卡情况：{}，阿里云地址：{}", resultDesc, resultFileUrl);
//        resp.setResultDesc(resultDesc);
//        resp.setResultFileUrl(resultFileUrl);

        // 记录操作员日志：（操作内容：成功送卡x张）
        try {
            SuixiangCardConfigOperationLog operationLog = new SuixiangCardConfigOperationLog();
            operationLog.setCardBaseId(req.getId());
            operationLog.setOperateType(SuiXiangCardConfigOperateTypeEnum.GIVE.getOperateType());
            operationLog.setOperationContent("赠卡成功" + succCnt + "张，失败" + failCnt + "张"); // 目前只支持一个用户赠送一张，如果以后放开多张，这个成功张数需要另外获得
            if (req.getOperatorDto() != null) {
                operationLog.setCreateOperId(req.getOperatorDto().getOperatorId());
                operationLog.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
            }
            suixiangCardFileOperationLogMapper.updateByPrimaryKeySelective(suixiangCardFileOperationLog);
            suixiangCardConfigOperationLogMapper.insertSelective(operationLog);
        } catch (Exception e) {
            log.error("新增suixiang_card_config_operation_log表异常！", e);
        }

        //log.info("赠送随享卡，应答参数：{}", JSON.toJSONString(resp));
//        return resp;
    }

    public int processWithCompletableFuture(GiveSuiXiangCardInput req, int listType, long id, List<SuiXiangCardGiveCardDto> dataList, SuixiangCardBase base) {
        // ForkJoinPool forkJoinPool = new ForkJoinPool(4);
        int totalFailCnt = 0;
        // List<CompletableFuture<Integer>> futures = new ArrayList<>();
        // int chunkSize = (int) Math.ceil((double) dataList.size() / ForkJoinPool.commonPool().getParallelism());
        int chunkSize = (int) Math.ceil((double) dataList.size() / 500);
        for (int i = 0; i < dataList.size(); i += chunkSize) {
            int end = Math.min(i + chunkSize, dataList.size());
            int finalI = i;
            // CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> giveSuiXiangCardDataProcess(req, listType, id, dataList.subList(finalI, end), base), forkJoinPool);
            totalFailCnt += giveSuiXiangCardDataProcess(req, listType, id, dataList.subList(finalI, end), base);
            // futures.add(future);
        }

//        int totalFailCnt = futures.stream()
//                .mapToInt(CompletableFuture::join)
//                .sum();
        return totalFailCnt;
    }

    private int giveSuiXiangCardDataProcess(GiveSuiXiangCardInput req, int listType, long id, List<SuiXiangCardGiveCardDto> dataList, SuixiangCardBase base) {
        int failCnt = 0; // 失败数
        Set<String> existUserSet = new HashSet<>(); // 已经出现过的用户集合，如果一个用户多次出现，只送第一个

        // 批量查询会员信息
        Set<String> userInfoSet = new HashSet<>();
        List<Long> rentDaysIdList = new ArrayList<>();
        for (SuiXiangCardGiveCardDto item : dataList) {
            userInfoSet.add(item.getUserInfo());
            rentDaysIdList.add(Long.parseLong(item.getRentDaysId()));
        }
        List<MembershipBasicInfo> memberList = null;
        Map<String, MembershipBasicInfo> memberMap = new HashMap<>(); // key:userInfo,value:member对象
        if (listType == SuiXiangCardFileTypeEnum.MOBILE_PHONE.getFileType()) {
            //memberList = membershipWrapService.getMembersByMobilePhones(userInfoSet);
            memberList = queryMemberInfo(new ArrayList<>(userInfoSet), listType);
            if (CollectionUtils.isNotEmpty(memberList)) {
                memberMap = memberList.stream().collect(Collectors.toMap(MembershipBasicInfo::getMobilePhone, Function.identity(), (a, b) -> a));
            }
        } else {
            //memberList = membershipWrapService.getMembersByAuthIds(userInfoSet);
            memberList = queryMemberInfo(new ArrayList<>(userInfoSet), listType);
            if (CollectionUtils.isNotEmpty(memberList)) {
                memberMap = memberList.stream().collect(Collectors.toMap(MembershipBasicInfo::getAuthId, Function.identity(), (a, b) -> a));
            }
        }

        // 批量查询会员标签
        List<String> authIdList = new ArrayList<>();
        List<Long> userIdList = new ArrayList<>();
        Map<String, MmpUserTagDto> tagMap = new HashMap<>(); // key:authId,value:标签对象
        if (CollectionUtils.isNotEmpty(memberList)) {
            for (MembershipBasicInfo member : memberList) {
                authIdList.add(member.getAuthId());
                userIdList.add(member.getPkId());
            }
        }
        if (CollectionUtils.isNotEmpty(authIdList)) {
            List<MmpUserTagDto> tagList = new ArrayList<>();
            List<List<String>> partitionAuthIdList = partitionList(authIdList, 1000);
            for (List<String> partitionAuthId : partitionAuthIdList) {
                List<MmpUserTagDto> list = memberShipService.queryUserTagListByAuthIds(partitionAuthId);
                if (CollectionUtils.isNotEmpty(list)){
                    tagList.addAll(list);
                }
            }
            if (CollectionUtils.isNotEmpty(tagList)) {
                tagMap = tagList.stream().distinct().collect(Collectors.toMap(MmpUserTagDto::getAuthId, Function.identity(), (a, b) -> a));
            }
        }
        log.info("赠送随享卡，批量查询会员信息完毕");

        /*// 批量查询suixiang_card_use表
        Map<Long, Integer> useCountMap = new HashMap<>(); // key:userId,value:count
        if (CollectionUtils.isNotEmpty(userIdList)) {
            List<List<Long>> groupedUserIdLists = partitionList(userIdList, 1000);
            List<SuixiangCardUseCountDto> useList = new ArrayList<>();
            for (List<Long> groupedUserIdList : groupedUserIdLists) {
                List<SuixiangCardUseCountDto> list = suixiangCardUseMapper.selectCountByUserId(groupedUserIdList);
                useList.addAll(list);
            }
            if (CollectionUtils.isNotEmpty(useList)) {
                useCountMap = useList.stream().distinct().collect(Collectors.toMap(SuixiangCardUseCountDto::getUserId, SuixiangCardUseCountDto::getCount));
            }
        }
        log.info("赠送随享卡，批量查询suixiang_card_use表完毕");*/

        Map<Long, SuixiangCardRentDays> rentDaysMap = new HashMap<>(); // key:rentDaysId,value:SuixiangCardRentDays
        Map<Long, List<SuixiangCardPrice>> priceMap = new HashMap<>(); // key:rentDaysId,value:List<SuixiangCardPrice>
        if (CollectionUtils.isNotEmpty(rentDaysIdList)) {
            // 批量查询suixiang_card_rent_days表
            SuixiangCardRentDaysExample rentDaysExample = new SuixiangCardRentDaysExample();
            rentDaysExample.createCriteria()
                    .andCardBaseIdEqualTo(id)
                    .andIdIn(rentDaysIdList)
                    .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
            List<SuixiangCardRentDays> rentDaysList = suixiangCardRentDaysMapper.selectByExample(rentDaysExample);
            if (CollectionUtils.isNotEmpty(rentDaysList)) {
                rentDaysMap = rentDaysList.stream().collect(Collectors.toMap(SuixiangCardRentDays::getId, Function.identity()));
            }
            log.info("赠送随享卡，批量查询suixiang_card_rent_days表完毕");

            // 批量查询suixiang_card_price表
            SuixiangCardPriceExample priceExample = new SuixiangCardPriceExample();
            priceExample.createCriteria()
                    .andCardRentIdIn(rentDaysIdList)
                    .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
            List<SuixiangCardPrice> priceList = suixiangCardPriceMapper.selectByExample(priceExample);
            if (CollectionUtils.isNotEmpty(priceList)) {
                priceMap = priceList.stream().collect(Collectors.groupingBy(SuixiangCardPrice::getCardRentId));
            }
            log.info("赠送随享卡，批量查询suixiang_card_price表完毕");
        }

        for (SuiXiangCardGiveCardDto giveCardDto : dataList) {
            // 判断该用户是否出现过
            if (existUserSet.contains(giveCardDto.getUserInfo())) {
                giveCardDto.setResult("同一次操作，同一个用户，不能多次赠送");
                failCnt++;
                continue;
            }
            existUserSet.add(giveCardDto.getUserInfo());

            // 根据userInfo和listType，查询会员系统，输出authId、userId和userMobile
            String authId = null;
            Long userId = null;
            String userMobile = null;
            if (listType == SuiXiangCardFileTypeEnum.MOBILE_PHONE.getFileType()) {
                userMobile = giveCardDto.getUserInfo();
                MembershipBasicInfo member = memberMap.get(userMobile);
                if (member == null || member.getPkId() == null || StringUtils.isBlank(member.getAuthId())) {
                    giveCardDto.setResult("根据会员手机号查询不到会员记录或会员编号或会员ID");
                    failCnt++;
                    continue;
                }
                userId = member.getPkId();
                authId = member.getAuthId();
            } else {
                authId = giveCardDto.getUserInfo();
                MembershipBasicInfo member = memberMap.get(authId);
                if (member == null || member.getPkId() == null || StringUtils.isBlank(member.getMobilePhone())) {
                    giveCardDto.setResult("根据会员ID查询不到会员记录或会员编号或会员手机号");
                    failCnt++;
                    continue;
                }
                userId = member.getPkId();
                userMobile = member.getMobilePhone();
            }

            // 查询用户个性化开关，如果是关闭，则不能做赠送，作为赠送失败处理
            MmpUserTagDto tag = tagMap.get(authId);
            // 个性化推荐 1:打开 2:关闭
            if (tag != null && "2".equals(tag.getSpare10())) {
                giveCardDto.setResult("该会员的个性化推荐开关为关闭，不做随享卡赠送");
                failCnt++;
                continue;
            }

//            // 查询该会员是否已经有 “1：生效中 或 3 生效中-已冻结” 的随享卡，如果有，不能再赠送
//            Integer count = useCountMap.get(userId);
//            if (count != null && count > 0) {
//                giveCardDto.setResult("该会员已有生效中的随享卡，不再赠送");
//                failCnt++;
//                continue;
//            }
            if (base.getStock() <= 0) {
                giveCardDto.setResult("该卡已售罄，请选择其他卡片购买");
                failCnt++;
                continue;
            }
            if(base.getStock() > 0 && giveCardDto.getZhangInt() > base.getStock()){
                giveCardDto.setResult("已达库存上限，最多能赠送"+base.getStock()+"张");
                failCnt++;
                continue;
            }
            // 用户 已购买的随享卡数
            Integer suiXiangCardNum = suiXiangCardService.getPurchaseSuiXiangCardNum(userId,id);
            if(suiXiangCardNum == null){
                giveCardDto.setResult("查询用户 已购买的随享卡数量异常");
                failCnt++;
                continue;
            }

            if(base.getPurchaseLimitNum() > 0 && suiXiangCardNum >= base.getPurchaseLimitNum()){
                giveCardDto.setResult("该会员已达购买上限，不再赠送");
                failCnt++;
                continue;
            }
            if(base.getPurchaseLimitNum() > 0 && base.getPurchaseLimitNum() < (giveCardDto.getZhangInt()+suiXiangCardNum)){
                giveCardDto.setResult("购买张数大于限购张数,最多能购买"+ (base.getPurchaseLimitNum() - suiXiangCardNum) + "张");
                failCnt++;
                continue;
            }

            // 准备数据库操作
            Date nowDate = new Date();

            // 准备更新suixiang_card_base剩余库存
            SuixiangCardBase baseUpdate = new SuixiangCardBase();
            baseUpdate.setId(id);
            baseUpdate.setStock(giveCardDto.getZhangInt()); // 这里借用stock字段作为需要赠送的数量，在sql中使用减法
            baseUpdate.setSales(giveCardDto.getZhangInt()); // 这里借用sales字段作为需要赠送的数量，在sql中使用加法
            if (req.getOperatorDto() != null) {
                baseUpdate.setUpdateOperId(req.getOperatorDto().getOperatorId());
                baseUpdate.setUpdateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
            }

            // 准备新增suixiang_card_purchase_record表
            SuixiangCardPurchaseRecord record = new SuixiangCardPurchaseRecord();
            record.setOrderSeq(ComUtils.createOrderSeq(userId));
            record.setOrgId(base.getOrgId());
            record.setUserId(userId);
            record.setUserMobile(userMobile);
            record.setCardBaseId(id);
            record.setCardName(base.getCardName());
            record.setCardRentId(Long.parseLong(giveCardDto.getRentDaysId()));
            record.setCardUseIds("-1");

            List<SuixiangCardPrice> priceList = priceMap.get(record.getCardRentId());
            if (CollectionUtils.isEmpty(priceList)) {
                giveCardDto.setResult("根据租期ID[" + giveCardDto.getRentDaysId() + "]售价[" + giveCardDto.getPrice() + "]查询不到随享卡价格表记录");
                failCnt++;
                continue;
            }
            boolean haveThePrice = false;
            long cardPriceId = 0;
            for (SuixiangCardPrice price : priceList) {
                if (price.getSalesPrice() != null
                        && price.getSalesPrice().compareTo(new BigDecimal(giveCardDto.getPrice())) == 0) {
                    haveThePrice = true;
                    cardPriceId = price.getId();
                    break;
                }
            }
            if (!haveThePrice) {
                giveCardDto.setResult("根据租期ID[" + giveCardDto.getRentDaysId() + "]售价[" + giveCardDto.getPrice() + "]查询不到随享卡价格表记录");
                failCnt++;
                continue;
            }

            record.setCardPriceId(cardPriceId);
            record.setIssueType(SuiXiangCardIssueTypeEnum.SEND.getType());
            record.setPaymentStatus(SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus());
            record.setQuantity(giveCardDto.getZhangInt());
            record.setPayTime(nowDate);
            record.setRealAmount(BigDecimal.ZERO);
            record.setCreateTime(nowDate);
            if (req.getOperatorDto() != null) {
                record.setCreateOperId(req.getOperatorDto().getOperatorId());
                record.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                record.setUpdateOperId(req.getOperatorDto().getOperatorId());
                record.setUpdateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
            }

            // 准备新增suixiang_card_purchase_record_log表
            SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
            recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.GIVE_OUT.getOperationType());
            recordLog.setContent("赠送随享卡");
            recordLog.setCreateTime(nowDate);
            if (req.getOperatorDto() != null) {
                recordLog.setCreateOperId(req.getOperatorDto().getOperatorId());
                recordLog.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                recordLog.setUpdateOperId(req.getOperatorDto().getOperatorId());
                recordLog.setUpdateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
            }

            List<SuixiangCardUse> useList = new ArrayList<>();
            List<SuixiangCardUseOpreationLog> useLogList = new ArrayList<>();
            for(int i=0; i<record.getQuantity();i++){
                // 准备新增suixiang_card_use表
                SuixiangCardUse use = new SuixiangCardUse();
                use.setCardBaseId(id);
                use.setCardPriceId(record.getCardPriceId());
                use.setUserId(userId);
                use.setCardType(1); // 1-随享卡
                use.setCardName(record.getCardName());
                use.setCardStatus(SuiXiangCardStatusEnum.EFFECTIVE.getStatus());
                use.setStartTime(nowDate);
                LocalDateTime startLocalDateTime = DateUtil.dateToLocalDateTime(nowDate);
                LocalDateTime expiresLocalDateTime = startLocalDateTime.plusMonths(base.getValidDaysType());
                use.setExpiresTime(DateUtil.localDateTimeToDate(expiresLocalDateTime));

                SuixiangCardRentDays rentDays = rentDaysMap.get(record.getCardRentId());
                if (rentDays == null || rentDays.getRentDays() == null) {
                    giveCardDto.setResult("根据租期ID[" + giveCardDto.getRentDaysId() + "]查询不到随享卡租期表记录");
                    failCnt++;
                    continue;
                }
                use.setInitDays(rentDays.getRentDays());
                use.setAvailableDays(rentDays.getRentDays());
                use.setUsedDays(0);
                use.setFrozenDays(0);
                use.setCreateTime(nowDate);
                if (req.getOperatorDto() != null) {
                    use.setCreateOperId(req.getOperatorDto().getOperatorId());
                    use.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                    use.setUpdateOperId(req.getOperatorDto().getOperatorId());
                    use.setUpdateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                }
                useList.add(use);

                // 准备新增suixiang_card_use_opreation_log表
                SuixiangCardUseOpreationLog useLog = new SuixiangCardUseOpreationLog();
                useLog.setCardPriceId(record.getCardPriceId());
                useLog.setCardGroup(1); // 卡类别：1-随享卡
                useLog.setOperationType(SuiXiangCardUseLogOperationTypeEnum.GIVE_OUT.getOperationType());
                useLog.setOriginSystem("storeManagementSystem"); // 门店管理系统
                useLog.setInitDays(rentDays.getRentDays());
                useLog.setAvailableDays(rentDays.getRentDays());
                useLog.setUsedDays(0);
                useLog.setFrozenDays(0);
                useLog.setCreateTime(nowDate);
                if (req.getOperatorDto() != null) {
                    useLog.setCreateOperId(req.getOperatorDto().getOperatorId());
                    useLog.setCreateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                    useLog.setUpdateOperId(req.getOperatorDto().getOperatorId());
                    useLog.setUpdateOperName(ComUtils.splitStr(req.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                }
                useLogList.add(useLog);
            }

            // 开启事务，新增记录
            try {
                suixiangCardBaseManager.giveSuiXiangCard(baseUpdate, record, recordLog, useList, useLogList);
            } catch (Exception e) {
                log.error("赠送随享卡数据库操作异常！", e);
                giveCardDto.setResult("赠送随享卡操作失败！" + e.getMessage());
                failCnt++;
                continue;
            }

            // 走到这里，就是赠送成功了
            giveCardDto.setSuccZhang(giveCardDto.getZhangInt());
            giveCardDto.setResult("成功");
            log.info("赠送随享卡，单个用户数据处理完毕！userInfo[{}]", giveCardDto.getUserInfo());
        }

        log.info("赠送随享卡，异步发送短信之前");
        // 异步发送短信
        Map<String, MembershipBasicInfo> finalMemberMap = memberMap;
        new Thread(() -> {
            for (SuiXiangCardGiveCardDto dto : dataList) {
                // 赠送成功的才需要发短信和消息
                if (dto.getSuccZhang() > 0) {
                    log.info("赠送随享卡，给userInfo[{}]发送短信和消息！", dto.getUserInfo());
                    MembershipBasicInfo member = finalMemberMap.get(dto.getUserInfo());
                    if (member == null) {
                        log.info("赠送随享卡，根据userInfo[{}]在memberMap中找不到记录！", dto.getUserInfo());
                        continue;
                    }
                    // push、消息中心，模板为：恭喜您获得一张随享出行卡，随时随地 享受自由出行～请前往查看使用规则
                    messageServ.push(member.getAuthId(), 0, 204, 2, 6, null, "vipCard-rpc");
                    // 短信，模板为：一张随享出行卡已经抵达您的账户，随时随地 享受自由出行，赶快打开APP查看并使用吧！
                    messageServ.asyncSendSMSTemplate(member.getMobilePhone(), 205, null, "vipCard-rpc");
                    log.info("赠送随享卡，userInfo[{}]发送短信和消息完毕！", dto.getUserInfo());
                }
            }
        }).start();

        log.info("赠送随享卡，异步发送短信之后");
        return failCnt;
    }

    /**
     * 将给定的ArrayList按照指定数量进行分组，返回一个包含多个子ArrayList的List。
     *
     * @param sourceList   待分组的ArrayList。
     * @param groupSize    每个子列表的目标元素数量。
     * @param <T>          ArrayList中元素的类型。
     * @return             分组后的List，每个元素是一个包含指定数量元素的ArrayList。
     */
    public static <T> List<List<T>> partitionList(List<T> sourceList, int groupSize) {
        List<List<T>> partitions = new ArrayList<>();
        if (sourceList == null || sourceList.isEmpty() || groupSize <= 0) {
            return partitions; // 返回空列表，或当分组大小无效时
        }

        int listSize = sourceList.size();
        for (int i = 0; i < listSize; i += groupSize) {
            int end = Math.min(i + groupSize, listSize); // 防止超出原列表范围
            partitions.add(new ArrayList<>(sourceList.subList(i, end)));
        }

        return partitions;
    }

    private List<MembershipBasicInfo> queryMemberInfo(List<String> userInfoList, int importType) {

        //最大数据量为1w， 防止内存溢出，分组查询
        List<ArrayList<String>> partitions = new ArrayList<>();
        int groupSize = 1000;
        if (CollectionUtils.isEmpty(userInfoList)) {
            return null; // 返回空列表，或当分组大小无效时
        }

        int listSize = userInfoList.size();
        for (int i = 0; i < listSize; i += groupSize) {
            int end = Math.min(i + groupSize, listSize); // 防止超出原列表范围
            partitions.add(new ArrayList<>(userInfoList.subList(i, end)));
        }

        ArrayList<MembershipBasicInfo> allMemberList = new ArrayList<>();
        //根据分组结果查询，后期需要优化，也可加入线程池
        if (importType == SuiXiangCardFileTypeEnum.MOBILE_PHONE.getFileType()) {
            for (ArrayList<String> partition : partitions) {
                List<MembershipBasicInfo> memberList = membershipWrapService.getMembersByMobilePhones(new HashSet<>(partition));
                if (CollectionUtils.isNotEmpty(memberList)) {
                    allMemberList.addAll(memberList);
                }
            }
        } else {
            for (ArrayList<String> partition : partitions) {
                List<MembershipBasicInfo> memberList  = membershipWrapService.getMembersByAuthIds(new HashSet<>(partition));
                if (CollectionUtils.isNotEmpty(memberList)) {
                    allMemberList.addAll(memberList);
                }
            }
        }
        return allMemberList.stream().distinct().collect(Collectors.toList());
    }

    private boolean getData(GiveSuiXiangCardInput req, BaseResponse baseResponse, List<SuiXiangCardGiveCardDto> dataList,SuixiangCardBase base) {
        String fileUrl = req.getFileUrl(); // 文件地址，阿里云oss的完整文件地址（是个excel）

        // 检查并创建临时目录
        ComUtils.checkAndMkDir(TEMP_FILE_DIR);
        String fileName = "suiXiangCard_give_" + System.currentTimeMillis() + "_" + new Random().nextInt(10000) + ".xls";
        String filePath = TEMP_FILE_DIR + fileName;
        File localFile = new File(filePath);

        // 下载阿里云url的文件
        boolean fileDownloadResult = FileUtil.downloadFromUrl(fileUrl, localFile);
        if (!fileDownloadResult) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("赠送文件下载失败");
            return true;
        }

        // 解析excel
        InputStream is = null;
        Workbook workbook = null;
        try {
            is = new FileInputStream(filePath);
            workbook = new XSSFWorkbook(is);
        } catch (IOException e) {
            log.error("创建XSSFWorkbook异常！", e);
            baseResponse.setCode(-1);
            baseResponse.setMessage("创建XSSFWorkbook异常");
            return true;
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("close is fail", e);
                }
            }
        }
        Sheet sheet = workbook.getSheetAt(0);
        int rowNo = sheet.getLastRowNum();
        // rowNo是从0开始，只有等于0，说明只有标题行
        if (rowNo == 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("上传的文件数据为空");
            return true;
        }
        // 一个处理最大支持100000条记录
        if (rowNo > 100000) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("一次最多支持100000条");
            return true;
        }

        for (int i = 1; i <= rowNo; i++) {
            Row row = sheet.getRow(i);
            if (row.getCell(0) == null || row.getCell(1) == null || row.getCell(2) == null) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("前三列不能为空");
                return true;
            }

            String userInfo = getValue(row.getCell(0));
            String rentDaysId = getValue(row.getCell(1));
            if (!ComUtils.isNumber(rentDaysId)) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("第" + (i + 1) + "行，租期ID非法");
                return true;
            }

            String price = getValue(row.getCell(2));
            if (!ComUtils.isNumber(price)) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("第" + (i + 1) + "行，售价非法");
                return true;
            }

            String zhang = null;
            int zhangInt = 1; // 默认是一张
            if (row.getCell(3) != null) {
                zhang = getValue(row.getCell(3));
                if (!ComUtils.isNumber(zhang)) {
                    baseResponse.setCode(-1);
                    baseResponse.setMessage("第" + (i + 1) + "行，赠送张数非法");
                    return true;
                }
                zhangInt = Integer.parseInt(zhang);
                if (base.getPurchaseLimitNum() > 0 && zhangInt > base.getPurchaseLimitNum()) {
                    baseResponse.setCode(-1);
                    baseResponse.setMessage("第" + (i + 1) + "行，赠送张数非法，目前只支持一个用户赠送"+base.getPurchaseLimitNum()+"张随享卡");
                    return true;
                }
            }

            SuiXiangCardGiveCardDto dto = new SuiXiangCardGiveCardDto();
            dto.setUserInfo(userInfo);
            dto.setRentDaysId(rentDaysId);
            dto.setPrice(price);
            dto.setZhang(zhang);
            dto.setZhangInt(zhangInt);
            dataList.add(dto);
        }
        if (workbook != null) {
            try {
                workbook.close();
            } catch (IOException e) {
                log.error("close workbook error", e);
            }
        }

        // 删除本地临时文件
        deleteTmpFile(localFile);

        return false;
    }

    public String getValue(Cell cell) {
        String ret = null;
        if (cell == null) {
            return ret;
        }
        try {
            switch (cell.getCellType()) {
                // 字符串
                case STRING:
                    ret = cell.getRichStringCellValue().getString();
                    break;
                // 日期
                case NUMERIC:
                    if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                        ret = cell.getDateCellValue().toLocaleString();
                    } else {
                        double temp = cell.getNumericCellValue();
                        Double dt = new Double(temp);
                        ret = String.format("%d", dt.longValue());
                    }
                    break;
            }
        } catch (Exception e) {
            return null;
        }
        return ret;
    }

    private boolean validGiveSuiXiangCard(GiveSuiXiangCardInput req, BaseResponse baseResponse) {
        if (req.getListType() != 1 && req.getListType() != 2) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请求参数listType非法");
            return true;
        }
        if (StringUtils.isBlank(req.getFileUrl())) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请求参数fileUrl不能为空");
            return true;
        } else if (!req.getFileUrl().endsWith(".xls") && !req.getFileUrl().endsWith(".xlsx")) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("目前只支持.xls和.xlsx文件");
            return true;
        }
        if (req.getId() <= 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("随享卡的配置id字段不能送空");
            return true;
        }
        return false;
    }

    @Override
    public QuerySuiXiangCardPurchaseRecordOutput querySuiXiangCardPurchaseRecord(QuerySuiXiangCardPurchaseRecordInput req) {
        log.info("查询随享卡购买记录，请求参数：{}", JSON.toJSONString(req));
        QuerySuiXiangCardPurchaseRecordOutput resp = new QuerySuiXiangCardPurchaseRecordOutput();
        BaseResponse baseResponse = new BaseResponse();
        resp.setBaseResponse(baseResponse);

        // 校验必填参数
        if (req.getPageNum() <= 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请送正确的pageNum");
            log.info("查询随享卡购买记录，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }
        if (req.getPageSize() <= 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请送正确的pageSize");
            log.info("查询随享卡购买记录，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        Page page = new Page(req.getPageNum(), req.getPageSize());

        SuixiangCardPurchaseRecord recordQuery = new SuixiangCardPurchaseRecord();
        if (StringUtils.isNotBlank(req.getOrgCode())) {
            recordQuery.setOrgId(req.getOrgCode());
        }
        if (StringUtils.isNotBlank(req.getCardName())) {
            recordQuery.setCardName(req.getCardName());
        }
        if (req.getCardId() > 0) {
            recordQuery.setCardBaseId(req.getCardId());
        }
        if (req.getPurchaseMethod() > 0) {
            recordQuery.setIssueType(req.getPurchaseMethod());
        }
        if (StringUtils.isNotBlank(req.getPurchaseStartTime())) {
            if (!DateUtil.isValidDateString(req.getPurchaseStartTime(), DateUtil.DATE_TYPE1)) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("购买周期开始日期格式非法，正确格式为 yyyy-MM-dd");
                log.info("查询随享卡购买记录，应答参数：{}", JSON.toJSONString(resp));
                return resp;
            }
            Date createTimeStart = DateUtil.getDateFromStrEx(req.getPurchaseStartTime(), DateUtil.DATE_TYPE1);
            recordQuery.setCreateTimeStart(createTimeStart);
        }
        if (StringUtils.isNotBlank(req.getPurchaseEndTime())) {
            if (!DateUtil.isValidDateString(req.getPurchaseEndTime(), DateUtil.DATE_TYPE1)) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("购买周期结束日期格式非法，正确格式为 yyyy-MM-dd");
                log.info("查询随享卡购买记录，应答参数：{}", JSON.toJSONString(resp));
                return resp;
            }
            Date createTimeEnd = DateUtil.getDateFromStrEx(req.getPurchaseEndTime() + " 23:59:59", DateUtil.simple);
            recordQuery.setCreateTimeEnd(createTimeEnd);
        }
        if (req.getOrderStatus() > 0) {
            recordQuery.setPaymentStatus(req.getOrderStatus());
        }
        if (StringUtils.isNotBlank(req.getMobilePhone())) {
            recordQuery.setUserMobile(req.getMobilePhone());
        }
        if (StringUtils.isNotBlank(req.getOrderId())) {
            recordQuery.setOrderSeq(req.getOrderId());
        }

        // 查询总条数
        int total = suixiangCardPurchaseRecordMapper.countForPage(recordQuery);
        List<SuiXiangCardPurchaseRecordItemDto> dtoList = new ArrayList<>();
        if (total > 0) {
            // 查询当前分页的数据
            List<SuixiangCardPurchaseRecord> list = suixiangCardPurchaseRecordMapper.selectForPage(recordQuery, page);
            if (CollectionUtils.isNotEmpty(list)) {
                List<Long> baseIdList = new ArrayList<>();
                List<Long> rentDaysIdList = new ArrayList<>();
                List<Long> priceIdList = new ArrayList<>();
                for (SuixiangCardPurchaseRecord record : list) {
                    baseIdList.add(record.getCardBaseId());
                    rentDaysIdList.add(record.getCardRentId());
                    priceIdList.add(record.getCardPriceId());
                }

                SuixiangCardBaseExample baseExample = new SuixiangCardBaseExample();
                baseExample.createCriteria().andIdIn(baseIdList).andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
                List<SuixiangCardBase> baseList = suixiangCardBaseMapper.selectByExample(baseExample);
                Map<Long, SuixiangCardBase> baseMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(baseList)) {
                    baseMap = baseList.stream().collect(Collectors.toMap(SuixiangCardBase::getId, Function.identity(), (a, b) -> a));
                }

                SuixiangCardRentDaysExample rentDaysExample = new SuixiangCardRentDaysExample();
                rentDaysExample.createCriteria().andIdIn(rentDaysIdList).andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
                List<SuixiangCardRentDays> rentDaysList = suixiangCardRentDaysMapper.selectByExampleWithBLOBs(rentDaysExample);
                Map<Long, SuixiangCardRentDays> rentDaysMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(rentDaysList)) {
                    rentDaysMap = rentDaysList.stream().collect(Collectors.toMap(SuixiangCardRentDays::getId, Function.identity(), (a, b) -> a));
                }

                SuixiangCardPriceExample priceExample = new SuixiangCardPriceExample();
                priceExample.createCriteria().andIdIn(priceIdList).andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
                List<SuixiangCardPrice> priceList = suixiangCardPriceMapper.selectByExample(priceExample);
                Map<Long, SuixiangCardPrice> priceMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(priceList)) {
                    priceMap = priceList.stream().collect(Collectors.toMap(SuixiangCardPrice::getId, Function.identity(), (a, b) -> a));
                }

                for (SuixiangCardPurchaseRecord record : list) {
                    SuiXiangCardPurchaseRecordItemDto dto = new SuiXiangCardPurchaseRecordItemDto();
                    dto.setId(record.getId());
                    MembershipBasicInfo member = memberShipService.getUserBasicInfoByPkId(record.getUserId());
                    if (member != null) {
                        dto.setUserName(member.getName());
                        if (StringUtils.isNotBlank(member.getName())) {
                            dto.setUserNameMask(ComUtils.getCommSecretMsg(member.getName(), 1, 0));
                        }
                    }
                    dto.setMobilePhone(record.getUserMobile());
                    dto.setMobilePhoneMask(ComUtils.getCommSecretMsg(record.getUserMobile(), 3, 4));
                    dto.setPurchaseMethod(record.getIssueType());
                    dto.setBuyCount(record.getQuantity());
                    dto.setOrderId(record.getOrderSeq());
                    dto.setPurchaseTime(DateUtil.getFormatDate(record.getCreateTime(), DateUtil.simple));
                    dto.setPayAmount(record.getRealAmount().toString());
                    dto.setOrderStatus(record.getPaymentStatus());
                    dto.setCardId(record.getCardBaseId());
                    dto.setCardName(record.getCardName());

                    SuixiangCardBase base = baseMap.get(record.getCardBaseId());
                    if (base != null && StringUtils.isNotBlank(base.getCityId())) {
                        dto.setCityNames(suixiangCardBaseManager.getCityNamesByCityId(base.getCityId()));
                    }

                    SuixiangCardRentDays rentDays = rentDaysMap.get(record.getCardRentId());
                    if (rentDays != null) {
                        if (rentDays.getRentDays() != null) {
                            dto.setLeaseTerm(rentDays.getRentDays() + "天");
                        }
                        if (StringUtils.isNotBlank(rentDays.getServiceFees())) {
                            List<ServiceFeeCfgDto> feeList = JSON.parseArray(rentDays.getServiceFees(), ServiceFeeCfgDto.class);
                            StringBuffer feeSb = new StringBuffer();
                            if (CollectionUtils.isNotEmpty(feeList)) {
                                int size = feeList.size();
                                for (int i = 0; i < size; i++) {
                                    feeSb.append(SuiXiangCardServiceFeeEnum.getShortDescByFeeType(feeList.get(i).getFeeType()));
                                    if (i < size - 1) {
                                        feeSb.append(Constants.STR_SPLIT_ZH);
                                    }
                                }
                            }
                            dto.setServiceFees(feeSb.toString());
                        }
                    }

                    SuixiangCardPrice price = priceMap.get(record.getCardPriceId());
                    if (price != null && StringUtils.isNotBlank(price.getCarModelIds())) {
                        String modelNames = suixiangCardBaseManager.getCarModelNamesByCarModelIds(price.getCarModelIds());
                        dto.setModelNames(modelNames);
                    }

                    dto.setOrgName(orgService.getOrgNameByOrgId(record.getOrgId()));
                    dto.setUpdateOperName(record.getUpdateOperName());
                    if (record.getCdkId() > 0) {
                        SuixiangCardCdk suixiangCardCdk = suixiangCardCdkMapper.selectByPrimaryKey(record.getCdkId());
                        if (suixiangCardCdk != null) {
                            dto.setCdkey(suixiangCardCdk.getCdkey());
                        }
                    }

                    dtoList.add(dto);
                }
            }
        }

        baseResponse.setCode(0);
        baseResponse.setMessage("成功");
        resp.setSuiXiangCardPurchaseRecordItemDtoList(dtoList);
        resp.setTotal(total);
        log.info("查询随享卡购买记录，应答参数：{}", JSON.toJSONString(resp));
        return resp;
    }

    private static final String TEMP_FILE_DIR = "/data/";
    //private static final String TEMP_FILE_DIR = "E:/";
    private static final String[] EXPORT_TITLES = {"会员编号", "authId", "mid", "购卡方式", "购买订单号", "购买时间", "支付金额", "订单状态", "卡片ID", "随享卡名称", "可用区域", "租期", "车型", "已选服务费", "运营公司", "操作人"};

    @Override
    public ExportSuiXiangCardPurchaseRecordOutput exportSuiXiangCardPurchaseRecord(ExportSuiXiangCardPurchaseRecordInput req) {
        log.info("导出随享卡购买记录，请求参数：{}", JSON.toJSONString(req));
        ExportSuiXiangCardPurchaseRecordOutput resp = new ExportSuiXiangCardPurchaseRecordOutput();
        BaseResponse baseResponse = new BaseResponse();
        resp.setBaseResponse(baseResponse);

        // 拼接查询条件
        SuixiangCardPurchaseRecord recordQuery = new SuixiangCardPurchaseRecord();
        if (StringUtils.isNotBlank(req.getOrgCode())) {
            recordQuery.setOrgId(req.getOrgCode());
        }
        if (StringUtils.isNotBlank(req.getCardName())) {
            recordQuery.setCardName(req.getCardName());
        }
        if (req.getCardId() > 0) {
            recordQuery.setCardBaseId(req.getCardId());
        }
        if (req.getPurchaseMethod() > 0) {
            recordQuery.setIssueType(req.getPurchaseMethod());
        }
        if (StringUtils.isNotBlank(req.getPurchaseStartTime())) {
            if (!DateUtil.isValidDateString(req.getPurchaseStartTime(), DateUtil.DATE_TYPE1)) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("购买周期开始日期格式非法，正确格式为 yyyy-MM-dd");
                log.info("导出随享卡购买记录，应答参数：{}", JSON.toJSONString(resp));
                return resp;
            }
            Date createTimeStart = DateUtil.getDateFromStrEx(req.getPurchaseStartTime(), DateUtil.DATE_TYPE1);
            recordQuery.setCreateTimeStart(createTimeStart);
        }
        if (StringUtils.isNotBlank(req.getPurchaseEndTime())) {
            if (!DateUtil.isValidDateString(req.getPurchaseEndTime(), DateUtil.DATE_TYPE1)) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("购买周期结束日期格式非法，正确格式为 yyyy-MM-dd");
                log.info("导出随享卡购买记录，应答参数：{}", JSON.toJSONString(resp));
                return resp;
            }
            Date createTimeEnd = DateUtil.getDateFromStrEx(req.getPurchaseEndTime() + " 23:59:59", DateUtil.simple);
            recordQuery.setCreateTimeEnd(createTimeEnd);
        }
        if (req.getOrderStatus() > 0) {
            recordQuery.setPaymentStatus(req.getOrderStatus());
        }
        if (StringUtils.isNotBlank(req.getMobilePhone())) {
            recordQuery.setUserMobile(req.getMobilePhone());
        }
        if (StringUtils.isNotBlank(req.getOrderId())) {
            recordQuery.setOrderSeq(req.getOrderId());
        }

        int pageNum = 1;
        int pageSize = 1000;

        // 检查并创建临时目录
        ComUtils.checkAndMkDir(TEMP_FILE_DIR);
        String fileName = "suiXiangCardPurchaseRecords_" + System.currentTimeMillis() + "_" + new Random().nextInt(10000) + ".csv";
        String filePath = TEMP_FILE_DIR + fileName;
        CsvWriter csvWriter = getWriter(filePath);
        if (csvWriter == null) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("创建数据文件失败");
            log.info("导出随享卡购买记录，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        try {
            while (true) {
                Page page = new Page(pageNum, pageSize);
                List<SuixiangCardPurchaseRecord> list = suixiangCardPurchaseRecordMapper.selectForPage(recordQuery, page);
                if (CollectionUtils.isEmpty(list)) {
                    log.info("导出随享卡购买记录！数据查询写入完毕，结束查询, fileName={}", fileName);
                    break;
                }
                log.info("导出随享卡购买记录！本分页查询完成, pageNum={}, pageSize={}, fileName={}", pageNum, pageSize, fileName);

                List<Long> baseIdList = new ArrayList<>();
                List<Long> rentDaysIdList = new ArrayList<>();
                List<Long> priceIdList = new ArrayList<>();
                for (SuixiangCardPurchaseRecord record : list) {
                    baseIdList.add(record.getCardBaseId());
                    rentDaysIdList.add(record.getCardRentId());
                    priceIdList.add(record.getCardPriceId());
                }

                SuixiangCardBaseExample baseExample = new SuixiangCardBaseExample();
                baseExample.createCriteria().andIdIn(baseIdList).andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
                List<SuixiangCardBase> baseList = suixiangCardBaseMapper.selectByExample(baseExample);
                Map<Long, SuixiangCardBase> baseMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(baseList)) {
                    baseMap = baseList.stream().collect(Collectors.toMap(SuixiangCardBase::getId, Function.identity(), (a, b) -> a));
                }

                SuixiangCardRentDaysExample rentDaysExample = new SuixiangCardRentDaysExample();
                rentDaysExample.createCriteria().andIdIn(rentDaysIdList).andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
                List<SuixiangCardRentDays> rentDaysList = suixiangCardRentDaysMapper.selectByExampleWithBLOBs(rentDaysExample);
                Map<Long, SuixiangCardRentDays> rentDaysMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(rentDaysList)) {
                    rentDaysMap = rentDaysList.stream().collect(Collectors.toMap(SuixiangCardRentDays::getId, Function.identity(), (a, b) -> a));
                }

                SuixiangCardPriceExample priceExample = new SuixiangCardPriceExample();
                priceExample.createCriteria().andIdIn(priceIdList).andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
                List<SuixiangCardPrice> priceList = suixiangCardPriceMapper.selectByExample(priceExample);
                Map<Long, SuixiangCardPrice> priceMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(priceList)) {
                    priceMap = priceList.stream().collect(Collectors.toMap(SuixiangCardPrice::getId, Function.identity(), (a, b) -> a));
                }

                for (SuixiangCardPurchaseRecord record : list) {
                    String cityNames = null;
                    SuixiangCardBase base = baseMap.get(record.getCardBaseId());
                    if (base != null && StringUtils.isNotBlank(base.getCityId())) {
                        cityNames = suixiangCardBaseManager.getCityNamesByCityId(base.getCityId());
                    }

                    String rentDaysStr = null;
                    String serviceFees = null;
                    SuixiangCardRentDays rentDays = rentDaysMap.get(record.getCardRentId());
                    if (rentDays != null) {
                        if (rentDays.getRentDays() != null) {
                            rentDaysStr = rentDays.getRentDays() + "天";
                        }
                        if (StringUtils.isNotBlank(rentDays.getServiceFees())) {
                            List<ServiceFeeCfgDto> feeList = JSON.parseArray(rentDays.getServiceFees(), ServiceFeeCfgDto.class);
                            StringBuffer feeSb = new StringBuffer();
                            if (CollectionUtils.isNotEmpty(feeList)) {
                                int size = feeList.size();
                                for (int i = 0; i < size; i++) {
                                    feeSb.append(SuiXiangCardServiceFeeEnum.getShortDescByFeeType(feeList.get(i).getFeeType()));
                                    if (i < size - 1) {
                                        feeSb.append(Constants.STR_SPLIT_ZH);
                                    }
                                }
                            }
                            serviceFees = feeSb.toString();
                        }
                    }

                    String modelNames = null;
                    SuixiangCardPrice price = priceMap.get(record.getCardPriceId());
                    if (price != null && StringUtils.isNotBlank(price.getCarModelIds())) {
                        String modelIdStr = price.getCarModelIds();
                        modelNames = suixiangCardBaseManager.getCarModelNamesByCarModelIds(modelIdStr);
                    }
                    MemberWrapInfoDto memberWrapInfoByPkId = membershipWrapService.getMemberWrapInfoByPkId(record.getUserId());
                    String authId = "";
                    String mid = "";
                    if (memberWrapInfoByPkId != null) {
                        authId = memberWrapInfoByPkId.getAuthId();
                        mid = memberWrapInfoByPkId.getMid();
                    }
                    String[] data = new String[]{
                            "\t" + record.getUserId(),
                            "\t" + authId,
                            "\t" + mid,
                            "\t" + SuiXiangCardIssueTypeEnum.getIssueType(record.getIssueType()).getDesc(),
                            "\t" + record.getOrderSeq(),
                            "\t" + DateUtil.getFormatDate(record.getCreateTime(), DateUtil.simple),
                            "\t" + record.getRealAmount().toString(),
                            "\t" + SuiXiangCardPaymentStatusEnum.getDescByPaymentStatus(record.getPaymentStatus()),
                            "\t" + record.getCardBaseId(),
                            "\t" + record.getCardName(),
                            "\t" + cityNames,
                            "\t" + rentDaysStr,
                            "\t" + modelNames,
                            "\t" + (serviceFees == null ? "" : serviceFees),
                            "\t" + orgService.getOrgNameByOrgId(record.getOrgId()),
                            "\t" + record.getUpdateOperName()
                    };
                    csvWriter.writeRecord(data, true);
                }

                csvWriter.flush();
                log.info("导出随享卡购买记录！本分页数据写入完成, pageNum={}, pageSize={}, fileName={}", pageNum, pageSize, fileName);
                pageNum++;
            }
        } catch (Exception e) {
            log.error("导出随享卡购买记录！数据导出失败，fileName={}", fileName, e);
            baseResponse.setCode(-1);
            baseResponse.setMessage("导出随享卡购买记录失败");
            return resp;
        } finally {
            if (csvWriter != null) {
                csvWriter.flush();
                csvWriter.close();
            }
        }

        // 上传阿里云
        log.info("导出随享卡购买记录文件，上传阿里云开始");
        String fileUrl = FileUtil.uploadSynByFilePath(filePath, "/export/" + fileName);
        if (fileUrl == null) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("导出随享卡购买记录文件，上传到阿里云失败！");
            return resp;
        }
        log.info("导出随享卡购买记录文件，上传阿里云结束");

        // 删除本地临时文件
        deleteTmpFile(new File(filePath));

        resp.setFileUrl(fileUrl);
        baseResponse.setCode(0);
        baseResponse.setMessage("成功");
        log.info("导出随享卡购买记录，应答参数：{}", JSON.toJSONString(resp));
        return resp;
    }

    private void deleteTmpFile(File file) {
        boolean result = FileUtils.deleteQuietly(file);
        log.info("删除临时文件！filePath[{}]result[{}]", file.getAbsolutePath(), result);
    }

    private byte[] fileToByteArray(File file) {
        byte[] buffer = null;
        try (FileInputStream fis = new FileInputStream(file);
             ByteArrayOutputStream bos = new ByteArrayOutputStream();) {
            byte[] b = new byte[1024];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            buffer = bos.toByteArray();
        } catch (FileNotFoundException e) {
            log.error("文件转文件流失败！", e);
        } catch (IOException e) {
            log.error("文件转文件流失败！", e);
        }
        return buffer;
    }

    private CsvWriter getWriter(String filePath) {
        CsvWriter csvWriter = null;
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                file.createNewFile();
            }
            csvWriter = new CsvWriter(filePath, ',', Charset.forName("GBK"));
            csvWriter.writeRecord(EXPORT_TITLES);
        } catch (Exception e) {
            log.error("导出随享卡购买记录！创建数据文件失败，filePath={}", filePath, e);
        }
        return csvWriter;
    }

    @Override
    public SearchOperateLogOutput searchOperateLog(SearchOperateLogInput req) {
        log.info("查询操作日志，请求参数：{}", JSON.toJSONString(req));
        SearchOperateLogOutput resp = new SearchOperateLogOutput();
        BaseResponse baseResponse = new BaseResponse();
        resp.setBaseResponse(baseResponse);

        // 检查入参
        if (req.getOperateType() <= 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请送正确的operateType");
            log.info("查询操作日志，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }
        if (StringUtils.isBlank(req.getForeignId())) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请送foreignId字段");
            log.info("查询操作日志，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }
        if (req.getPageNum() <= 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请送正确的pageNum");
            log.info("查询操作日志，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }
        if (req.getPageSize() <= 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请送正确的pageSize");
            log.info("查询操作日志，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        // 操作类型：1-随享卡
        if (req.getOperateType() == 1) {
            // 先查询总条数
            SuixiangCardConfigOperationLog recordQuery = new SuixiangCardConfigOperationLog();
            recordQuery.setCardBaseId(Long.parseLong(req.getForeignId()));
            int total = suixiangCardConfigOperationLogMapper.countForPage(recordQuery);
            resp.setTotal(total);

            // 如果总条数大于0，才需要进行分页查询
            List<OperateLogDto> dtoList = new ArrayList<>();
            if (total > 0) {
                Page page = new Page(req.getPageNum(), req.getPageSize());
                List<SuixiangCardConfigOperationLog> logList = suixiangCardConfigOperationLogMapper.selectForPage(recordQuery, page);
                if (CollectionUtils.isNotEmpty(logList)) {
                    for (SuixiangCardConfigOperationLog log : logList) {
                        OperateLogDto dto = new OperateLogDto();
                        dto.setId(log.getId());
                        dto.setOperateType(log.getOperateType());
                        dto.setForeignId(String.valueOf(log.getCardBaseId()));
                        dto.setOperateContent(log.getOperationContent());
                        dto.setCreateTime(DateUtil.getFormatDate(log.getCreateTime(), DateUtil.simple));
                        dto.setCreateOperName(log.getCreateOperName());
                        dtoList.add(dto);
                    }
                }
            }
            resp.setOperateLogDtoList(dtoList);
        }

        log.info("查询操作日志，请求参数：{}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public SendSuiXiangCardOutput sendSuiXiangCard(SendSuiXiangCardInput input) {
        SendSuiXiangCardOutput output = new SendSuiXiangCardOutput();
        BaseResponse baseResponse = new BaseResponse();
        output.setBaseResponse(baseResponse);

        if (!checkSendSuiXiangCardInput(input, baseResponse)) {
            return output;
        }

        long id = input.getId();
        SuixiangCardBase base = suixiangCardBaseMapper.selectByPrimaryKey(id);
        if (base == null) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("根据随享卡id[" + id + "]查询不到随享卡的配置记录");
            return output;
        }
        // 判断状态是否是 已上架
        if (!base.getCardStatus().equals(SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus())) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("随享卡id[" + id + "]不是上架状态，不能进行赠送操作");
            return output;
        }
        // 没有到售卖开始时间，不能赠卡
        if (base.getSaleStartTime().after(new Date())) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("随享卡id[" + id + "]未到售卖开始时间，不能进行赠送操作");
            return output;
        }


        List<SuiXiangCardGiveCardDto> dataList = new ArrayList<>();
        for (String authId : input.getAuthIds()) {
            SuiXiangCardGiveCardDto dto = new SuiXiangCardGiveCardDto();
            dto.setUserInfo(authId);
            dto.setRentDaysId(input.getRentDaysId());
            dto.setPrice(input.getPrice());
            dto.setZhang("1");
            dto.setZhangInt(1);
            dataList.add(dto);
        }

        return sendSuiXiangCardDataProcess(input, dataList, base);
    }

    private boolean checkSendSuiXiangCardInput(SendSuiXiangCardInput input, BaseResponse baseResponse) {

        if (input.getListType() != 1 && input.getListType() != 2) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("请求参数listType非法");
            return false;
        }

        if (CollectionUtils.isEmpty(input.getAuthIds())) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("会员id不能为空");
            return false;
        }

        if (input.getAuthIds().size() > 2000) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("会员id数量不能超过2000");
            return false;
        }

        if (input.getNumber() == 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("随享卡数量不能为0");
            return false;
        }

        if (StringUtils.isEmpty(input.getRentDaysId())) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("随享卡租期id不能为空");
            return false;
        }

        if (StringUtils.isEmpty(input.getPrice())) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("随享卡价格不能为空");
            return false;
        }

        return true;
    }

    private SendSuiXiangCardOutput sendSuiXiangCardDataProcess(SendSuiXiangCardInput input, List<SuiXiangCardGiveCardDto> dataList, SuixiangCardBase base) {
        int listType = input.getListType();
        long id = input.getId();

        int failCnt = 0; // 失败数
        Set<String> existUserSet = new HashSet<>(); // 已经出现过的用户集合，如果一个用户多次出现，只送第一个

        // 批量查询会员信息
        Set<String> userInfoSet = new HashSet<>();
        List<Long> rentDaysIdList = new ArrayList<>();
        for (SuiXiangCardGiveCardDto item : dataList) {
            userInfoSet.add(item.getUserInfo());
            rentDaysIdList.add(Long.parseLong(item.getRentDaysId()));
        }
        List<MembershipBasicInfo> memberList = null;
        Map<String, MembershipBasicInfo> memberMap = new HashMap<>(); // key:userInfo,value:member对象
        if (listType == SuiXiangCardFileTypeEnum.MOBILE_PHONE.getFileType()) {
            memberList = membershipWrapService.getMembersByMobilePhones(userInfoSet);
            if (CollectionUtils.isNotEmpty(memberList)) {
                memberMap = memberList.stream().collect(Collectors.toMap(MembershipBasicInfo::getMobilePhone, Function.identity(), (a, b) -> a));
            }
        } else {
            memberList = membershipWrapService.getMembersByAuthIds(userInfoSet);
            if (CollectionUtils.isNotEmpty(memberList)) {
                memberMap = memberList.stream().collect(Collectors.toMap(MembershipBasicInfo::getAuthId, Function.identity(), (a, b) -> a));
            }
        }

        // 批量查询会员标签
        List<String> authIdList = new ArrayList<>();
        List<Long> userIdList = new ArrayList<>();
        Map<String, MmpUserTagDto> tagMap = new HashMap<>(); // key:authId,value:标签对象
        if (CollectionUtils.isNotEmpty(memberList)) {
            for (MembershipBasicInfo member : memberList) {
                authIdList.add(member.getAuthId());
                userIdList.add(member.getPkId());
            }
        }
        if (CollectionUtils.isNotEmpty(authIdList)) {
            List<MmpUserTagDto> tagList = memberShipService.queryUserTagListByAuthIds(authIdList);
            if (CollectionUtils.isNotEmpty(tagList)) {
                tagMap = tagList.stream().collect(Collectors.toMap(MmpUserTagDto::getAuthId, Function.identity(), (a, b) -> a));
            }
        }
        log.info("赠送随享卡，批量查询会员信息完毕");

        // 批量查询suixiang_card_use表
        Map<Long, Integer> useCountMap = new HashMap<>(); // key:userId,value:count
        if (CollectionUtils.isNotEmpty(userIdList)) {
            List<SuixiangCardUseCountDto> useList = suixiangCardUseMapper.selectCountByUserId(userIdList);
            if (CollectionUtils.isNotEmpty(useList)) {
                useCountMap = useList.stream().collect(Collectors.toMap(SuixiangCardUseCountDto::getUserId, SuixiangCardUseCountDto::getCount));
            }
        }
        log.info("赠送随享卡，批量查询suixiang_card_use表完毕");

        Map<Long, SuixiangCardRentDays> rentDaysMap = new HashMap<>(); // key:rentDaysId,value:SuixiangCardRentDays
        Map<Long, List<SuixiangCardPrice>> priceMap = new HashMap<>(); // key:rentDaysId,value:List<SuixiangCardPrice>
        if (CollectionUtils.isNotEmpty(rentDaysIdList)) {
            // 批量查询suixiang_card_rent_days表
            SuixiangCardRentDaysExample rentDaysExample = new SuixiangCardRentDaysExample();
            rentDaysExample.createCriteria()
                    .andCardBaseIdEqualTo(id)
                    .andIdIn(rentDaysIdList)
                    .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
            List<SuixiangCardRentDays> rentDaysList = suixiangCardRentDaysMapper.selectByExample(rentDaysExample);
            if (CollectionUtils.isNotEmpty(rentDaysList)) {
                rentDaysMap = rentDaysList.stream().collect(Collectors.toMap(SuixiangCardRentDays::getId, Function.identity()));
            }
            log.info("赠送随享卡，批量查询suixiang_card_rent_days表完毕");

            // 批量查询suixiang_card_price表
            SuixiangCardPriceExample priceExample = new SuixiangCardPriceExample();
            priceExample.createCriteria()
                    .andCardRentIdIn(rentDaysIdList)
                    .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
            List<SuixiangCardPrice> priceList = suixiangCardPriceMapper.selectByExample(priceExample);
            if (CollectionUtils.isNotEmpty(priceList)) {
                priceMap = priceList.stream().collect(Collectors.groupingBy(SuixiangCardPrice::getCardRentId));
            }
            log.info("赠送随享卡，批量查询suixiang_card_price表完毕");
        }
        for (SuiXiangCardGiveCardDto giveCardDto : dataList) {
            // 判断该用户是否出现过
            if (existUserSet.contains(giveCardDto.getUserInfo())) {
                giveCardDto.setResult("同一次操作，同一个用户，不能多次赠送");
                failCnt++;
                continue;
            }
            existUserSet.add(giveCardDto.getUserInfo());

            // 根据userInfo和listType，查询会员系统，输出authId、userId和userMobile
            String authId = null;
            Long userId = null;
            String userMobile = null;
            if (listType == SuiXiangCardFileTypeEnum.MOBILE_PHONE.getFileType()) {
                userMobile = giveCardDto.getUserInfo();
                MembershipBasicInfo member = memberMap.get(userMobile);
                if (member == null || member.getPkId() == null || StringUtils.isBlank(member.getAuthId())) {
                    giveCardDto.setResult("根据会员手机号查询不到会员记录或会员编号或会员ID");
                    failCnt++;
                    continue;
                }
                userId = member.getPkId();
                authId = member.getAuthId();
            } else {
                authId = giveCardDto.getUserInfo();
                MembershipBasicInfo member = memberMap.get(authId);
                if (member == null || member.getPkId() == null || StringUtils.isBlank(member.getMobilePhone())) {
                    giveCardDto.setResult("根据会员ID查询不到会员记录或会员编号或会员手机号");
                    failCnt++;
                    continue;
                }
                userId = member.getPkId();
                userMobile = member.getMobilePhone();
            }

            // 查询用户个性化开关，如果是关闭，则不能做赠送，作为赠送失败处理
            MmpUserTagDto tag = tagMap.get(authId);
            // 个性化推荐 1:打开 2:关闭
            if (tag != null && "2".equals(tag.getSpare10())) {
                giveCardDto.setResult("该会员的个性化推荐开关为关闭，不做随享卡赠送");
                failCnt++;
                continue;
            }

//            // 查询该会员是否已经有 “1：生效中 或 3 生效中-已冻结” 的随享卡，如果有，不能再赠送
//            Integer count = useCountMap.get(userId);
//            if (count != null && count > 0) {
//                giveCardDto.setResult("该会员已有生效中的随享卡，不再赠送");
//                failCnt++;
//                continue;
//            }
            if (base.getStock() <= 0) {
                giveCardDto.setResult("该卡已售罄，请选择其他卡片购买");
                failCnt++;
                continue;
            }
            if(base.getStock() > 0 && giveCardDto.getZhangInt() > base.getStock()){
                giveCardDto.setResult("已达库存上限，最多能赠送"+base.getStock()+"张");
                failCnt++;
                continue;
            }
            // 用户 已购买的随享卡数
            Integer suiXiangCardNum = suiXiangCardService.getPurchaseSuiXiangCardNum(userId,id);
            if(suiXiangCardNum == null){
                giveCardDto.setResult("查询用户 已购买的随享卡数量异常");
                failCnt++;
                continue;
            }

            if(base.getPurchaseLimitNum() > 0 && suiXiangCardNum >= base.getPurchaseLimitNum()){
                giveCardDto.setResult("该会员已达购买上限，不再赠送");
                failCnt++;
                continue;
            }
            if(base.getPurchaseLimitNum() > 0 && base.getPurchaseLimitNum() < (giveCardDto.getZhangInt()+suiXiangCardNum)){
                giveCardDto.setResult("购买张数大于限购张数,最多能购买"+ (base.getPurchaseLimitNum() - suiXiangCardNum) + "张");
                failCnt++;
                continue;
            }

            // 准备数据库操作
            Date nowDate = new Date();

            // 准备更新suixiang_card_base剩余库存
            SuixiangCardBase baseUpdate = new SuixiangCardBase();
            baseUpdate.setId(id);
            baseUpdate.setStock(giveCardDto.getZhangInt()); // 这里借用stock字段作为需要赠送的数量，在sql中使用减法
            baseUpdate.setSales(giveCardDto.getZhangInt()); // 这里借用sales字段作为需要赠送的数量，在sql中使用加法
            if (input.getOperatorDto() != null) {
                baseUpdate.setUpdateOperId(input.getOperatorDto().getOperatorId());
                baseUpdate.setUpdateOperName(ComUtils.splitStr(input.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
            }

            // 准备新增suixiang_card_purchase_record表
            SuixiangCardPurchaseRecord record = new SuixiangCardPurchaseRecord();
            record.setOrderSeq(ComUtils.createOrderSeq(userId));
            record.setOrgId(base.getOrgId());
            record.setUserId(userId);
            record.setUserMobile(userMobile);
            record.setCardBaseId(id);
            record.setCardName(base.getCardName());
            record.setCardRentId(Long.parseLong(giveCardDto.getRentDaysId()));
            record.setCardUseIds("-1");

            List<SuixiangCardPrice> priceList = priceMap.get(record.getCardRentId());
            if (CollectionUtils.isEmpty(priceList)) {
                giveCardDto.setResult("根据租期ID[" + giveCardDto.getRentDaysId() + "]售价[" + giveCardDto.getPrice() + "]查询不到随享卡价格表记录");
                failCnt++;
                continue;
            }
            boolean haveThePrice = false;
            long cardPriceId = 0;
            for (SuixiangCardPrice price : priceList) {
                if (price.getSalesPrice() != null
                        && price.getSalesPrice().compareTo(new BigDecimal(giveCardDto.getPrice())) == 0) {
                    haveThePrice = true;
                    cardPriceId = price.getId();
                    break;
                }
            }
            if (!haveThePrice) {
                giveCardDto.setResult("根据租期ID[" + giveCardDto.getRentDaysId() + "]售价[" + giveCardDto.getPrice() + "]查询不到随享卡价格表记录");
                failCnt++;
                continue;
            }

            record.setCardPriceId(cardPriceId);
            record.setIssueType(SuiXiangCardIssueTypeEnum.SEND.getType());
            record.setPaymentStatus(SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus());
            record.setQuantity(giveCardDto.getZhangInt());
            record.setPayTime(nowDate);
            record.setRealAmount(BigDecimal.ZERO);
            record.setCreateTime(nowDate);
            if (input.getOperatorDto() != null) {
                record.setCreateOperId(input.getOperatorDto().getOperatorId());
                record.setCreateOperName(ComUtils.splitStr(input.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                record.setUpdateOperId(input.getOperatorDto().getOperatorId());
                record.setUpdateOperName(ComUtils.splitStr(input.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
            }

            // 准备新增suixiang_card_purchase_record_log表
            SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
            recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.GIVE_OUT.getOperationType());
            recordLog.setContent("赠送随享卡");
            recordLog.setCreateTime(nowDate);
            if (input.getOperatorDto() != null) {
                recordLog.setCreateOperId(input.getOperatorDto().getOperatorId());
                recordLog.setCreateOperName(ComUtils.splitStr(input.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                recordLog.setUpdateOperId(input.getOperatorDto().getOperatorId());
                recordLog.setUpdateOperName(ComUtils.splitStr(input.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
            }

            List<SuixiangCardUse> useList = new ArrayList<>();
            List<SuixiangCardUseOpreationLog> useLogList = new ArrayList<>();
            for(int i=0; i<record.getQuantity();i++){
                // 准备新增suixiang_card_use表
                SuixiangCardUse use = new SuixiangCardUse();
                use.setCardBaseId(id);
                use.setCardPriceId(record.getCardPriceId());
                use.setUserId(userId);
                use.setCardType(1); // 1-随享卡
                use.setCardName(record.getCardName());
                use.setCardStatus(SuiXiangCardStatusEnum.EFFECTIVE.getStatus());
                use.setStartTime(nowDate);
                LocalDateTime startLocalDateTime = DateUtil.dateToLocalDateTime(nowDate);
                LocalDateTime expiresLocalDateTime = startLocalDateTime.plusMonths(base.getValidDaysType());
                use.setExpiresTime(DateUtil.localDateTimeToDate(expiresLocalDateTime));

                SuixiangCardRentDays rentDays = rentDaysMap.get(record.getCardRentId());
                if (rentDays == null || rentDays.getRentDays() == null) {
                    giveCardDto.setResult("根据租期ID[" + giveCardDto.getRentDaysId() + "]查询不到随享卡租期表记录");
                    failCnt++;
                    continue;
                }
                use.setInitDays(rentDays.getRentDays());
                use.setAvailableDays(rentDays.getRentDays());
                use.setUsedDays(0);
                use.setFrozenDays(0);
                use.setCreateTime(nowDate);
                if (input.getOperatorDto() != null) {
                    use.setCreateOperId(input.getOperatorDto().getOperatorId());
                    use.setCreateOperName(ComUtils.splitStr(input.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                    use.setUpdateOperId(input.getOperatorDto().getOperatorId());
                    use.setUpdateOperName(ComUtils.splitStr(input.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                }
                useList.add(use);

                // 准备新增suixiang_card_use_opreation_log表
                SuixiangCardUseOpreationLog useLog = new SuixiangCardUseOpreationLog();
                useLog.setCardPriceId(record.getCardPriceId());
                useLog.setCardGroup(1); // 卡类别：1-随享卡
                useLog.setOperationType(SuiXiangCardUseLogOperationTypeEnum.GIVE_OUT.getOperationType());
                useLog.setOriginSystem("storeManagementSystem"); // 门店管理系统
                useLog.setInitDays(rentDays.getRentDays());
                useLog.setAvailableDays(rentDays.getRentDays());
                useLog.setUsedDays(0);
                useLog.setFrozenDays(0);
                useLog.setCreateTime(nowDate);
                if (input.getOperatorDto() != null) {
                    useLog.setCreateOperId(input.getOperatorDto().getOperatorId());
                    useLog.setCreateOperName(ComUtils.splitStr(input.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                    useLog.setUpdateOperId(input.getOperatorDto().getOperatorId());
                    useLog.setUpdateOperName(ComUtils.splitStr(input.getOperatorDto().getOperatorName(), Constants.OPER_NAME_LENGTH));
                }
                useLogList.add(useLog);
            }

            // 开启事务，新增记录
            try {
                suixiangCardBaseManager.giveSuiXiangCard(baseUpdate, record, recordLog, useList, useLogList);
            } catch (Exception e) {
                log.error("赠送随享卡数据库操作异常！", e);
                giveCardDto.setResult("赠送随享卡操作失败！" + e.getMessage());
                failCnt++;
                continue;
            }

            // 走到这里，就是赠送成功了
            giveCardDto.setSuccZhang(giveCardDto.getZhangInt());
            giveCardDto.setResult("成功");
            log.info("赠送随享卡，单个用户数据处理完毕！userInfo[{}]", giveCardDto.getUserInfo());
        }
        SendSuiXiangCardOutput output = new SendSuiXiangCardOutput();
        output.setBaseResponse(new BaseResponse());
        output.setSuiXiangCardGiveCardDtoList(dataList);
        return output;
    }
}
