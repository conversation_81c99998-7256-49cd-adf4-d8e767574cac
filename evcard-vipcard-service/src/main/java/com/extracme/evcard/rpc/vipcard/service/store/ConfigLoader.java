package com.extracme.evcard.rpc.vipcard.service.store;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.rest.MdRestClient;
import com.extracme.evcard.rpc.vipcard.rest.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ConfigLoader implements EnvironmentAware {

    private Environment environment;
    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    @Resource
    private MdRestClient mdRestClient;

    // 内存里的配置数据， 注意使用 volatile 修饰  ,注意一定要整体替换， 不要修改原来的对象);
    volatile Map<Long, String> goodsModelMap = new HashMap<>();
    volatile Map<Long, StoreInfo> storeInfoMap = new HashMap<>();
    volatile Map<Long, GoodsVehicleModelInfo> goodsDetailModelMap = new HashMap<>();
    /**
     * 淡旺季配置map
     * key ： 年份
     * value：map<lowAndPeakSeasonConfigId,lowAndPeakSeasonConfig>
     *      key：类型
     *      value：配置信息
     */
    volatile Map<String, Map<Integer,LowAndPeakSeasonConfig>> lowAndPeakSeasonConfigMap = new HashMap<>();

    String goodsModelMapMd5 = StringUtils.EMPTY; // org.quartz.threadPool.threadCount == 1 单线程访问， 无需使用 volatile
    String storeMapMd5 = StringUtils.EMPTY; // org.quartz.threadPool.threadCount == 1 单线程访问， 无需使用 volatile

    String lowAndPeakSeasonConfigMapMd5 = StringUtils.EMPTY; // org.quartz.threadPool.threadCount == 1 单线程访问， 无需使用 volatile

    // 这里是用rpc, 改成 实际的访问数据配置的类 或其他rpc服务
    Scheduler scheduler;

    @PostConstruct
    void init() throws Exception {

        loadData(true); // load in main thread

        Properties p = new Properties();
        p.put("org.quartz.threadPool.threadCount", "1");
        p.put("org.quartz.scheduler.instanceName", "config_loader_timer");
        StdSchedulerFactory factory = new StdSchedulerFactory();
        factory.initialize(p);
        scheduler = factory.getScheduler();

        JobDataMap map = new JobDataMap();
        map.put("service", this);

        JobDetail job1 = JobBuilder.newJob(LoadDataJob.class).withIdentity("job4").setJobData(map).build();
        CronTrigger cronTrigger1 = TriggerBuilder.newTrigger().withIdentity("trigger4").withSchedule(CronScheduleBuilder.cronSchedule("55 0/10 * * * ? ")).build();

        scheduler.scheduleJob(job1, cronTrigger1);

        scheduler.start();
    }

    static public class LoadDataJob implements Job {
        @Override
        public void execute(JobExecutionContext ctx) {
            ConfigLoader service = (ConfigLoader) ctx.getJobDetail().getJobDataMap().get("service");
            try {
                service.loadData(false);
            } catch (Exception e) {
                log.error("loadData exception", e);
            }
        }

    }

    @PreDestroy
    void close() {
        try {
            scheduler.shutdown();
        } catch (Exception e) {
        }
    }

    void loadData(boolean init) throws BusinessException {
        // 以下是加载配置的方法， 根据实际情况改成去访问数据库或RPC调用
        // 商品车型
        loadGoodsModel(init);
        //门店信息
        loadStoreList(init);
        // 获取更详细的商品车型
        loadGoodsModelDetail(init);
        // 获取淡旺季配置
        loadLowAndPeakSeasonConfig(init);
    }

    private void loadStoreList(boolean init) throws BusinessException {
        int retCode = loadStoreList();
        if (retCode == 0) return;
        if (!init) return;
        //if (!EnvGlobal.globalProfile.equals("prd")) return;
        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                break;
            }
            log.error("cannot load app key info, retCode={}", retCode);
            retCode = loadStoreList();
            if (retCode == 0) break;
        }
    }

    private int loadStoreList() throws BusinessException {
        GetCachedStoreListRes response = mdRestClient.getCachedStoreList(storeMapMd5);
        if (response.getCode() == 0 && null != response.getData()) {
            if(needReload(storeMapMd5, response.getData().getCfgMd5())) {
                List<StoreInfo> storeInfos = response.getData().getInfo();
                if(CollectionUtils.isNotEmpty(storeInfos)) {
                    Map<Long, StoreInfo> map = storeInfos.stream().map(store -> {
                        StoreInfo storeInfo = new StoreInfo();
                        storeInfo.setId(store.getId());
                        storeInfo.setStoreName(store.getStoreName());
                        storeInfo.setOperOrgCode(store.getOperOrgCode());
                        storeInfo.setOperOrgName(store.getOperOrgName());
                        storeInfo.setOperCityId(store.getOperCityId());
                        storeInfo.setOperCityName(store.getOperCityName());
                        return storeInfo;
                    }).collect(Collectors.toMap(StoreInfo::getId, a -> a));
                    //if (!Md5Utils.toMd5(map).equals(Md5Utils.toMd5(storeMap))) {
                    storeInfoMap = map;
                    // }
                }
                storeMapMd5 = response.getData().getCfgMd5();
            }
        }
        return response.getCode();
    }

    private void loadGoodsModel(boolean init) throws BusinessException {
        int retCode = loadGoodsModel();
        if (retCode == 0) return;
        if (!init) return;
        //if (!EnvGlobal.globalProfile.equals("prd")) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                break;
            }
            log.error("cannot load app key info, retCode={}", retCode);
            retCode = loadGoodsModel();
            if (retCode == 0) break;
        }
    }

    private int loadGoodsModel() throws BusinessException {
        GetCachedGoodsModelRes response = mdRestClient.getCachedGoodsModel(goodsModelMapMd5);
        if (response.getCode() == 0 && null != response.getData()) {
            if(needReload(goodsModelMapMd5, response.getData().getCfgMd5())) {
                if (CollectionUtils.isNotEmpty(response.getData().getGoodsModelNameInfo())) {
                    List<GoodsModelNameInfo> dictList = response.getData().getGoodsModelNameInfo();
                    Map<Long, String> newGoodsModelMap = dictList.stream().collect(Collectors.toMap(
                            GoodsModelNameInfo::getId, GoodsModelNameInfo::getGoodsModelName));
                    goodsModelMap = newGoodsModelMap;
                }
                goodsModelMapMd5 = response.getData().getCfgMd5();
            }
        }
        return response.getCode();
    }


    private void loadGoodsModelDetail(boolean init) throws BusinessException {
        int retCode = loadGoodsModelDetail();
        if (retCode == 0) return;
        if (!init) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                break;
            }
            log.error("cannot load app key info, retCode={}", retCode);
            retCode = loadGoodsModelDetail();
            if (retCode == 0) break;
        }
    }

    private int loadGoodsModelDetail() throws BusinessException {
        GetAllGoodsModeInfoRes response = mdRestClient.getAllGoodsModeInfo();
        if (response.getCode() == 0 && null != response.getData()) {
            List<GoodsVehicleModelInfo> goodsModel = response.getData().getGoodsModel();
            if(CollectionUtils.isNotEmpty(goodsModel)){
                Map<Long, GoodsVehicleModelInfo> newGoodsDetailModelMap = goodsModel.stream().collect(Collectors.toMap(GoodsVehicleModelInfo::getGoodsModelId, Function.identity()));
                goodsDetailModelMap = newGoodsDetailModelMap;
            }
        }
        return response.getCode();
    }


    public String getGoodsModelInfo(Long goodsModelId) {
        return goodsModelMap.get(goodsModelId);
    }

    public Collection<String> getGoodsModelInfo() {
        return goodsModelMap.values();
    }

    public Set<Long> getGoodsModelIds(){
        return goodsModelMap.keySet();
    }


    boolean needReload(String lastCfgMd5, String currentCfgMd5) {
        if( lastCfgMd5.isEmpty() || currentCfgMd5.isEmpty() ) return true;
        return !lastCfgMd5.equals(currentCfgMd5);
    }


    private void loadLowAndPeakSeasonConfig(boolean init) throws BusinessException {
        int retCode = loadLowAndPeakSeasonConfig();
        if (retCode == 0) return;
        if (!init) return;
        try {
            String[] activeProfiles = environment.getActiveProfiles();
            // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
            if(!(activeProfiles[0].equals("prod"))) return;
        } catch (Exception e) {
            log.error("load lowAndPeakSeason config,get fail retCode={}", retCode,e);
        }
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                break;
            }
            log.error("cannot load lowAndPeakSeason config, retCode={}", retCode);
            retCode = loadLowAndPeakSeasonConfig();
            if (retCode == 0) break;
        }
    }

    private int loadLowAndPeakSeasonConfig() throws BusinessException {
        GetCacheLowAndPeakSeasonConfigRes response = mdRestClient.getCacheLowAndPeakSeasonConfig(lowAndPeakSeasonConfigMapMd5);
        if (response.getCode() == 0 && null != response.getData()) {
            if(needReload(lowAndPeakSeasonConfigMapMd5, response.getData().getCfgMd5())) {
                List<LowAndPeakSeasonConfig> list = response.getData().getInfo();
                if(CollectionUtils.isNotEmpty(list)) {
                    Map<String, Map<Integer,LowAndPeakSeasonConfig>> tempLowAndPeakSeasonConfigMap = new HashMap<>();
                    for (LowAndPeakSeasonConfig lowAndPeakSeasonConfig : list) {
                        // 年份
                        String particularYear = lowAndPeakSeasonConfig.getParticularYear();
                        // 节日编号
                        Integer holidayId = lowAndPeakSeasonConfig.getHolidayId();
                        if (StringUtils.isBlank(particularYear) || holidayId == null) {
                            continue;
                        }

                        if (tempLowAndPeakSeasonConfigMap.containsKey(particularYear)) {
                            Map<Integer, LowAndPeakSeasonConfig> holidayIdAndInfoMap = tempLowAndPeakSeasonConfigMap.get(particularYear);
                            holidayIdAndInfoMap.put(holidayId, lowAndPeakSeasonConfig);
                        }else{
                            Map<Integer, LowAndPeakSeasonConfig> holidayIdAndInfoMap = new HashMap<>();
                            holidayIdAndInfoMap.put(holidayId, lowAndPeakSeasonConfig);
                            tempLowAndPeakSeasonConfigMap.put(particularYear, holidayIdAndInfoMap);
                        }

                    }
                    lowAndPeakSeasonConfigMap = tempLowAndPeakSeasonConfigMap;
                }
                lowAndPeakSeasonConfigMapMd5 = response.getData().getCfgMd5();
            }
        }
        return response.getCode();
    }

    /**
     * 获取某年的 淡旺季配置信息
     * @param particularYear
     * @param holidayId
     * @return
     */
    public LowAndPeakSeasonConfig getLowAndPeakSeasonConfig(String particularYear,Integer holidayId) {
        Map<Integer, LowAndPeakSeasonConfig> holidayIdAndInfoMap = lowAndPeakSeasonConfigMap.get(particularYear);
        if (holidayIdAndInfoMap != null) {
            return holidayIdAndInfoMap.get(holidayId);
        }
        return null;
    }
    public Map<Integer, LowAndPeakSeasonConfig> getLowAndPeakSeasonConfig(String particularYear) {
        return lowAndPeakSeasonConfigMap.get(particularYear);
    }
}
