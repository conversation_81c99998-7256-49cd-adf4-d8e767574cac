package com.extracme.evcard.rpc.vipcard.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SuixiangCardUseOpreationLogExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public SuixiangCardUseOpreationLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCardUseIdIsNull() {
            addCriterion("card_use_id is null");
            return (Criteria) this;
        }

        public Criteria andCardUseIdIsNotNull() {
            addCriterion("card_use_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardUseIdEqualTo(Long value) {
            addCriterion("card_use_id =", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdNotEqualTo(Long value) {
            addCriterion("card_use_id <>", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdGreaterThan(Long value) {
            addCriterion("card_use_id >", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_use_id >=", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdLessThan(Long value) {
            addCriterion("card_use_id <", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdLessThanOrEqualTo(Long value) {
            addCriterion("card_use_id <=", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdIn(List<Long> values) {
            addCriterion("card_use_id in", values, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdNotIn(List<Long> values) {
            addCriterion("card_use_id not in", values, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdBetween(Long value1, Long value2) {
            addCriterion("card_use_id between", value1, value2, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdNotBetween(Long value1, Long value2) {
            addCriterion("card_use_id not between", value1, value2, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIsNull() {
            addCriterion("card_price_id is null");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIsNotNull() {
            addCriterion("card_price_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdEqualTo(Long value) {
            addCriterion("card_price_id =", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotEqualTo(Long value) {
            addCriterion("card_price_id <>", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdGreaterThan(Long value) {
            addCriterion("card_price_id >", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_price_id >=", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdLessThan(Long value) {
            addCriterion("card_price_id <", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdLessThanOrEqualTo(Long value) {
            addCriterion("card_price_id <=", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIn(List<Long> values) {
            addCriterion("card_price_id in", values, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotIn(List<Long> values) {
            addCriterion("card_price_id not in", values, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdBetween(Long value1, Long value2) {
            addCriterion("card_price_id between", value1, value2, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotBetween(Long value1, Long value2) {
            addCriterion("card_price_id not between", value1, value2, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdIsNull() {
            addCriterion("purchase_id is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdIsNotNull() {
            addCriterion("purchase_id is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdEqualTo(Long value) {
            addCriterion("purchase_id =", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdNotEqualTo(Long value) {
            addCriterion("purchase_id <>", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdGreaterThan(Long value) {
            addCriterion("purchase_id >", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("purchase_id >=", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdLessThan(Long value) {
            addCriterion("purchase_id <", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdLessThanOrEqualTo(Long value) {
            addCriterion("purchase_id <=", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdIn(List<Long> values) {
            addCriterion("purchase_id in", values, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdNotIn(List<Long> values) {
            addCriterion("purchase_id not in", values, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdBetween(Long value1, Long value2) {
            addCriterion("purchase_id between", value1, value2, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdNotBetween(Long value1, Long value2) {
            addCriterion("purchase_id not between", value1, value2, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andCardGroupIsNull() {
            addCriterion("card_group is null");
            return (Criteria) this;
        }

        public Criteria andCardGroupIsNotNull() {
            addCriterion("card_group is not null");
            return (Criteria) this;
        }

        public Criteria andCardGroupEqualTo(Integer value) {
            addCriterion("card_group =", value, "cardGroup");
            return (Criteria) this;
        }

        public Criteria andCardGroupNotEqualTo(Integer value) {
            addCriterion("card_group <>", value, "cardGroup");
            return (Criteria) this;
        }

        public Criteria andCardGroupGreaterThan(Integer value) {
            addCriterion("card_group >", value, "cardGroup");
            return (Criteria) this;
        }

        public Criteria andCardGroupGreaterThanOrEqualTo(Integer value) {
            addCriterion("card_group >=", value, "cardGroup");
            return (Criteria) this;
        }

        public Criteria andCardGroupLessThan(Integer value) {
            addCriterion("card_group <", value, "cardGroup");
            return (Criteria) this;
        }

        public Criteria andCardGroupLessThanOrEqualTo(Integer value) {
            addCriterion("card_group <=", value, "cardGroup");
            return (Criteria) this;
        }

        public Criteria andCardGroupIn(List<Integer> values) {
            addCriterion("card_group in", values, "cardGroup");
            return (Criteria) this;
        }

        public Criteria andCardGroupNotIn(List<Integer> values) {
            addCriterion("card_group not in", values, "cardGroup");
            return (Criteria) this;
        }

        public Criteria andCardGroupBetween(Integer value1, Integer value2) {
            addCriterion("card_group between", value1, value2, "cardGroup");
            return (Criteria) this;
        }

        public Criteria andCardGroupNotBetween(Integer value1, Integer value2) {
            addCriterion("card_group not between", value1, value2, "cardGroup");
            return (Criteria) this;
        }

        public Criteria andOperationTypeIsNull() {
            addCriterion("operation_type is null");
            return (Criteria) this;
        }

        public Criteria andOperationTypeIsNotNull() {
            addCriterion("operation_type is not null");
            return (Criteria) this;
        }

        public Criteria andOperationTypeEqualTo(Long value) {
            addCriterion("operation_type =", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeNotEqualTo(Long value) {
            addCriterion("operation_type <>", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeGreaterThan(Long value) {
            addCriterion("operation_type >", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeGreaterThanOrEqualTo(Long value) {
            addCriterion("operation_type >=", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeLessThan(Long value) {
            addCriterion("operation_type <", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeLessThanOrEqualTo(Long value) {
            addCriterion("operation_type <=", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeIn(List<Long> values) {
            addCriterion("operation_type in", values, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeNotIn(List<Long> values) {
            addCriterion("operation_type not in", values, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeBetween(Long value1, Long value2) {
            addCriterion("operation_type between", value1, value2, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeNotBetween(Long value1, Long value2) {
            addCriterion("operation_type not between", value1, value2, "operationType");
            return (Criteria) this;
        }

        public Criteria andOriginSystemIsNull() {
            addCriterion("origin_system is null");
            return (Criteria) this;
        }

        public Criteria andOriginSystemIsNotNull() {
            addCriterion("origin_system is not null");
            return (Criteria) this;
        }

        public Criteria andOriginSystemEqualTo(String value) {
            addCriterion("origin_system =", value, "originSystem");
            return (Criteria) this;
        }

        public Criteria andOriginSystemNotEqualTo(String value) {
            addCriterion("origin_system <>", value, "originSystem");
            return (Criteria) this;
        }

        public Criteria andOriginSystemGreaterThan(String value) {
            addCriterion("origin_system >", value, "originSystem");
            return (Criteria) this;
        }

        public Criteria andOriginSystemGreaterThanOrEqualTo(String value) {
            addCriterion("origin_system >=", value, "originSystem");
            return (Criteria) this;
        }

        public Criteria andOriginSystemLessThan(String value) {
            addCriterion("origin_system <", value, "originSystem");
            return (Criteria) this;
        }

        public Criteria andOriginSystemLessThanOrEqualTo(String value) {
            addCriterion("origin_system <=", value, "originSystem");
            return (Criteria) this;
        }

        public Criteria andOriginSystemLike(String value) {
            addCriterion("origin_system like", value, "originSystem");
            return (Criteria) this;
        }

        public Criteria andOriginSystemNotLike(String value) {
            addCriterion("origin_system not like", value, "originSystem");
            return (Criteria) this;
        }

        public Criteria andOriginSystemIn(List<String> values) {
            addCriterion("origin_system in", values, "originSystem");
            return (Criteria) this;
        }

        public Criteria andOriginSystemNotIn(List<String> values) {
            addCriterion("origin_system not in", values, "originSystem");
            return (Criteria) this;
        }

        public Criteria andOriginSystemBetween(String value1, String value2) {
            addCriterion("origin_system between", value1, value2, "originSystem");
            return (Criteria) this;
        }

        public Criteria andOriginSystemNotBetween(String value1, String value2) {
            addCriterion("origin_system not between", value1, value2, "originSystem");
            return (Criteria) this;
        }

        public Criteria andRefKeyIsNull() {
            addCriterion("ref_key is null");
            return (Criteria) this;
        }

        public Criteria andRefKeyIsNotNull() {
            addCriterion("ref_key is not null");
            return (Criteria) this;
        }

        public Criteria andRefKeyEqualTo(String value) {
            addCriterion("ref_key =", value, "refKey");
            return (Criteria) this;
        }

        public Criteria andRefKeyNotEqualTo(String value) {
            addCriterion("ref_key <>", value, "refKey");
            return (Criteria) this;
        }

        public Criteria andRefKeyGreaterThan(String value) {
            addCriterion("ref_key >", value, "refKey");
            return (Criteria) this;
        }

        public Criteria andRefKeyGreaterThanOrEqualTo(String value) {
            addCriterion("ref_key >=", value, "refKey");
            return (Criteria) this;
        }

        public Criteria andRefKeyLessThan(String value) {
            addCriterion("ref_key <", value, "refKey");
            return (Criteria) this;
        }

        public Criteria andRefKeyLessThanOrEqualTo(String value) {
            addCriterion("ref_key <=", value, "refKey");
            return (Criteria) this;
        }

        public Criteria andRefKeyLike(String value) {
            addCriterion("ref_key like", value, "refKey");
            return (Criteria) this;
        }

        public Criteria andRefKeyNotLike(String value) {
            addCriterion("ref_key not like", value, "refKey");
            return (Criteria) this;
        }

        public Criteria andRefKeyIn(List<String> values) {
            addCriterion("ref_key in", values, "refKey");
            return (Criteria) this;
        }

        public Criteria andRefKeyNotIn(List<String> values) {
            addCriterion("ref_key not in", values, "refKey");
            return (Criteria) this;
        }

        public Criteria andRefKeyBetween(String value1, String value2) {
            addCriterion("ref_key between", value1, value2, "refKey");
            return (Criteria) this;
        }

        public Criteria andRefKeyNotBetween(String value1, String value2) {
            addCriterion("ref_key not between", value1, value2, "refKey");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIsNull() {
            addCriterion("order_seq is null");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIsNotNull() {
            addCriterion("order_seq is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSeqEqualTo(String value) {
            addCriterion("order_seq =", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotEqualTo(String value) {
            addCriterion("order_seq <>", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThan(String value) {
            addCriterion("order_seq >", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThanOrEqualTo(String value) {
            addCriterion("order_seq >=", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThan(String value) {
            addCriterion("order_seq <", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThanOrEqualTo(String value) {
            addCriterion("order_seq <=", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLike(String value) {
            addCriterion("order_seq like", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotLike(String value) {
            addCriterion("order_seq not like", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIn(List<String> values) {
            addCriterion("order_seq in", values, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotIn(List<String> values) {
            addCriterion("order_seq not in", values, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqBetween(String value1, String value2) {
            addCriterion("order_seq between", value1, value2, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotBetween(String value1, String value2) {
            addCriterion("order_seq not between", value1, value2, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysIsNull() {
            addCriterion("order_operation_days is null");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysIsNotNull() {
            addCriterion("order_operation_days is not null");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysEqualTo(Integer value) {
            addCriterion("order_operation_days =", value, "orderOperationDays");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysNotEqualTo(Integer value) {
            addCriterion("order_operation_days <>", value, "orderOperationDays");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysGreaterThan(Integer value) {
            addCriterion("order_operation_days >", value, "orderOperationDays");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_operation_days >=", value, "orderOperationDays");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysLessThan(Integer value) {
            addCriterion("order_operation_days <", value, "orderOperationDays");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysLessThanOrEqualTo(Integer value) {
            addCriterion("order_operation_days <=", value, "orderOperationDays");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysIn(List<Integer> values) {
            addCriterion("order_operation_days in", values, "orderOperationDays");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysNotIn(List<Integer> values) {
            addCriterion("order_operation_days not in", values, "orderOperationDays");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysBetween(Integer value1, Integer value2) {
            addCriterion("order_operation_days between", value1, value2, "orderOperationDays");
            return (Criteria) this;
        }

        public Criteria andOrderOperationDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("order_operation_days not between", value1, value2, "orderOperationDays");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountIsNull() {
            addCriterion("discount_amount is null");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountIsNotNull() {
            addCriterion("discount_amount is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountEqualTo(BigDecimal value) {
            addCriterion("discount_amount =", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountNotEqualTo(BigDecimal value) {
            addCriterion("discount_amount <>", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountGreaterThan(BigDecimal value) {
            addCriterion("discount_amount >", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_amount >=", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountLessThan(BigDecimal value) {
            addCriterion("discount_amount <", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_amount <=", value, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountIn(List<BigDecimal> values) {
            addCriterion("discount_amount in", values, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountNotIn(List<BigDecimal> values) {
            addCriterion("discount_amount not in", values, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_amount between", value1, value2, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_amount not between", value1, value2, "discountAmount");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(BigDecimal value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(BigDecimal value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(BigDecimal value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(BigDecimal value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<BigDecimal> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<BigDecimal> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andRealAmountIsNull() {
            addCriterion("real_amount is null");
            return (Criteria) this;
        }

        public Criteria andRealAmountIsNotNull() {
            addCriterion("real_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRealAmountEqualTo(BigDecimal value) {
            addCriterion("real_amount =", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountNotEqualTo(BigDecimal value) {
            addCriterion("real_amount <>", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountGreaterThan(BigDecimal value) {
            addCriterion("real_amount >", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("real_amount >=", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountLessThan(BigDecimal value) {
            addCriterion("real_amount <", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("real_amount <=", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountIn(List<BigDecimal> values) {
            addCriterion("real_amount in", values, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountNotIn(List<BigDecimal> values) {
            addCriterion("real_amount not in", values, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_amount between", value1, value2, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_amount not between", value1, value2, "realAmount");
            return (Criteria) this;
        }

        public Criteria andMiscDescIsNull() {
            addCriterion("misc_desc is null");
            return (Criteria) this;
        }

        public Criteria andMiscDescIsNotNull() {
            addCriterion("misc_desc is not null");
            return (Criteria) this;
        }

        public Criteria andMiscDescEqualTo(String value) {
            addCriterion("misc_desc =", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescNotEqualTo(String value) {
            addCriterion("misc_desc <>", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescGreaterThan(String value) {
            addCriterion("misc_desc >", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescGreaterThanOrEqualTo(String value) {
            addCriterion("misc_desc >=", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescLessThan(String value) {
            addCriterion("misc_desc <", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescLessThanOrEqualTo(String value) {
            addCriterion("misc_desc <=", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescLike(String value) {
            addCriterion("misc_desc like", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescNotLike(String value) {
            addCriterion("misc_desc not like", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescIn(List<String> values) {
            addCriterion("misc_desc in", values, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescNotIn(List<String> values) {
            addCriterion("misc_desc not in", values, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescBetween(String value1, String value2) {
            addCriterion("misc_desc between", value1, value2, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescNotBetween(String value1, String value2) {
            addCriterion("misc_desc not between", value1, value2, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andInitDaysIsNull() {
            addCriterion("init_days is null");
            return (Criteria) this;
        }

        public Criteria andInitDaysIsNotNull() {
            addCriterion("init_days is not null");
            return (Criteria) this;
        }

        public Criteria andInitDaysEqualTo(Integer value) {
            addCriterion("init_days =", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysNotEqualTo(Integer value) {
            addCriterion("init_days <>", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysGreaterThan(Integer value) {
            addCriterion("init_days >", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("init_days >=", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysLessThan(Integer value) {
            addCriterion("init_days <", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysLessThanOrEqualTo(Integer value) {
            addCriterion("init_days <=", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysIn(List<Integer> values) {
            addCriterion("init_days in", values, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysNotIn(List<Integer> values) {
            addCriterion("init_days not in", values, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysBetween(Integer value1, Integer value2) {
            addCriterion("init_days between", value1, value2, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("init_days not between", value1, value2, "initDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysIsNull() {
            addCriterion("available_days is null");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysIsNotNull() {
            addCriterion("available_days is not null");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysEqualTo(Integer value) {
            addCriterion("available_days =", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysNotEqualTo(Integer value) {
            addCriterion("available_days <>", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysGreaterThan(Integer value) {
            addCriterion("available_days >", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("available_days >=", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysLessThan(Integer value) {
            addCriterion("available_days <", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysLessThanOrEqualTo(Integer value) {
            addCriterion("available_days <=", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysIn(List<Integer> values) {
            addCriterion("available_days in", values, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysNotIn(List<Integer> values) {
            addCriterion("available_days not in", values, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysBetween(Integer value1, Integer value2) {
            addCriterion("available_days between", value1, value2, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("available_days not between", value1, value2, "availableDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysIsNull() {
            addCriterion("used_days is null");
            return (Criteria) this;
        }

        public Criteria andUsedDaysIsNotNull() {
            addCriterion("used_days is not null");
            return (Criteria) this;
        }

        public Criteria andUsedDaysEqualTo(Integer value) {
            addCriterion("used_days =", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysNotEqualTo(Integer value) {
            addCriterion("used_days <>", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysGreaterThan(Integer value) {
            addCriterion("used_days >", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("used_days >=", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysLessThan(Integer value) {
            addCriterion("used_days <", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysLessThanOrEqualTo(Integer value) {
            addCriterion("used_days <=", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysIn(List<Integer> values) {
            addCriterion("used_days in", values, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysNotIn(List<Integer> values) {
            addCriterion("used_days not in", values, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysBetween(Integer value1, Integer value2) {
            addCriterion("used_days between", value1, value2, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("used_days not between", value1, value2, "usedDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysIsNull() {
            addCriterion("frozen_days is null");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysIsNotNull() {
            addCriterion("frozen_days is not null");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysEqualTo(Integer value) {
            addCriterion("frozen_days =", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysNotEqualTo(Integer value) {
            addCriterion("frozen_days <>", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysGreaterThan(Integer value) {
            addCriterion("frozen_days >", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("frozen_days >=", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysLessThan(Integer value) {
            addCriterion("frozen_days <", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysLessThanOrEqualTo(Integer value) {
            addCriterion("frozen_days <=", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysIn(List<Integer> values) {
            addCriterion("frozen_days in", values, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysNotIn(List<Integer> values) {
            addCriterion("frozen_days not in", values, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysBetween(Integer value1, Integer value2) {
            addCriterion("frozen_days between", value1, value2, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("frozen_days not between", value1, value2, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNull() {
            addCriterion("create_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNotNull() {
            addCriterion("create_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdEqualTo(Long value) {
            addCriterion("create_oper_id =", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotEqualTo(Long value) {
            addCriterion("create_oper_id <>", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThan(Long value) {
            addCriterion("create_oper_id >", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_oper_id >=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThan(Long value) {
            addCriterion("create_oper_id <", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("create_oper_id <=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIn(List<Long> values) {
            addCriterion("create_oper_id in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotIn(List<Long> values) {
            addCriterion("create_oper_id not in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdBetween(Long value1, Long value2) {
            addCriterion("create_oper_id between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("create_oper_id not between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNull() {
            addCriterion("create_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNotNull() {
            addCriterion("create_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameEqualTo(String value) {
            addCriterion("create_oper_name =", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotEqualTo(String value) {
            addCriterion("create_oper_name <>", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThan(String value) {
            addCriterion("create_oper_name >", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_name >=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThan(String value) {
            addCriterion("create_oper_name <", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThanOrEqualTo(String value) {
            addCriterion("create_oper_name <=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLike(String value) {
            addCriterion("create_oper_name like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotLike(String value) {
            addCriterion("create_oper_name not like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIn(List<String> values) {
            addCriterion("create_oper_name in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotIn(List<String> values) {
            addCriterion("create_oper_name not in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameBetween(String value1, String value2) {
            addCriterion("create_oper_name between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotBetween(String value1, String value2) {
            addCriterion("create_oper_name not between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNull() {
            addCriterion("update_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNotNull() {
            addCriterion("update_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdEqualTo(Long value) {
            addCriterion("update_oper_id =", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotEqualTo(Long value) {
            addCriterion("update_oper_id <>", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThan(Long value) {
            addCriterion("update_oper_id >", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_oper_id >=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThan(Long value) {
            addCriterion("update_oper_id <", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("update_oper_id <=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIn(List<Long> values) {
            addCriterion("update_oper_id in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotIn(List<Long> values) {
            addCriterion("update_oper_id not in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdBetween(Long value1, Long value2) {
            addCriterion("update_oper_id between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("update_oper_id not between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNull() {
            addCriterion("update_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNotNull() {
            addCriterion("update_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameEqualTo(String value) {
            addCriterion("update_oper_name =", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotEqualTo(String value) {
            addCriterion("update_oper_name <>", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThan(String value) {
            addCriterion("update_oper_name >", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_oper_name >=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThan(String value) {
            addCriterion("update_oper_name <", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThanOrEqualTo(String value) {
            addCriterion("update_oper_name <=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLike(String value) {
            addCriterion("update_oper_name like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotLike(String value) {
            addCriterion("update_oper_name not like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIn(List<String> values) {
            addCriterion("update_oper_name in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotIn(List<String> values) {
            addCriterion("update_oper_name not in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameBetween(String value1, String value2) {
            addCriterion("update_oper_name between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotBetween(String value1, String value2) {
            addCriterion("update_oper_name not between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated do_not_delete_during_merge Tue Jan 31 16:13:21 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}