package com.extracme.evcard.rpc.vipcard.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SuixiangCardCdkExportDto implements Serializable {

    /**
     *  suixiang_card_cdk 主键
     */
    private Long cdkId;

    /**
     *  suixiang_card_base 主键
     */
    private Long cardBaseId;

    /**
     * 卡名字
     */
    private String cardName;
    /**
     * 卡片有效期天数类型 ：1-一个月、2-两个月、3-三个月、6-六个月、12-一年
     */
    //private int cardValidDaysType;
    /**
     * 租期n天-售价x元
     */
    private String cdkDesc;
    /**
     *
     */
    private String cdkey;
    /**
     *领取状态 未领取、已领取
     */
    private String isActivatedDesc;
    /**
     * 卡累计完成订单数  兑换后才有值
     */
    private int totalOrder;
    /**
     * 卡片有效期开始时间 兑换后才有值
     */
    private Date cardStartTime;
    /**
     * 卡片有效期结束时间 兑换后才有值
     */
    private Date cardExpiresTime;

    /**
     * 售卖 开始时间
     */
    private Date saleStartTime;

    /**
     * 售卖 结束时间
     */
    private Date saleEndTime;
    /**
     *兑换人姓名，兑换后才有值
     */
    private String activatedUserName;
    /**
     * 兑换人手机号，兑换后才有值
     */
    private String activatedUserMobile;
    /**
     * 发放人姓名   aaa(1234@hq)
     */
    private String createOperName;

    /**
     * 兑换人mid，兑换后才有值
     */
    private String activatedMid;

    /**
     * 用途
     */
    private String purpose;

    /**
     * 第三方售价
     */
    private String thirdSalesPrice;
}
