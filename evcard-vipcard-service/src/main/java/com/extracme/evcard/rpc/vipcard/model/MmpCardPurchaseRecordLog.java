package com.extracme.evcard.rpc.vipcard.model;

import java.util.Date;

public class MmpCardPurchaseRecordLog {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_purchase_record_log.id
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_purchase_record_log.purchase_id
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    private Long purchaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_purchase_record_log.operation_type
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    private Integer operationType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_purchase_record_log.out_trade_seq
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    private String outTradeSeq;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_purchase_record_log.content
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    private String content;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_purchase_record_log.misc_desc
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_purchase_record_log.status
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_purchase_record_log.create_time
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_purchase_record_log.create_oper_id
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_purchase_record_log.create_oper_name
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    private String createOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_purchase_record_log.id
     *
     * @return the value of mmp_card_purchase_record_log.id
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_purchase_record_log.id
     *
     * @param id the value for mmp_card_purchase_record_log.id
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_purchase_record_log.purchase_id
     *
     * @return the value of mmp_card_purchase_record_log.purchase_id
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public Long getPurchaseId() {
        return purchaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_purchase_record_log.purchase_id
     *
     * @param purchaseId the value for mmp_card_purchase_record_log.purchase_id
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public void setPurchaseId(Long purchaseId) {
        this.purchaseId = purchaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_purchase_record_log.operation_type
     *
     * @return the value of mmp_card_purchase_record_log.operation_type
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public Integer getOperationType() {
        return operationType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_purchase_record_log.operation_type
     *
     * @param operationType the value for mmp_card_purchase_record_log.operation_type
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_purchase_record_log.out_trade_seq
     *
     * @return the value of mmp_card_purchase_record_log.out_trade_seq
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public String getOutTradeSeq() {
        return outTradeSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_purchase_record_log.out_trade_seq
     *
     * @param outTradeSeq the value for mmp_card_purchase_record_log.out_trade_seq
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public void setOutTradeSeq(String outTradeSeq) {
        this.outTradeSeq = outTradeSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_purchase_record_log.content
     *
     * @return the value of mmp_card_purchase_record_log.content
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_purchase_record_log.content
     *
     * @param content the value for mmp_card_purchase_record_log.content
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_purchase_record_log.misc_desc
     *
     * @return the value of mmp_card_purchase_record_log.misc_desc
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_purchase_record_log.misc_desc
     *
     * @param miscDesc the value for mmp_card_purchase_record_log.misc_desc
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_purchase_record_log.status
     *
     * @return the value of mmp_card_purchase_record_log.status
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_purchase_record_log.status
     *
     * @param status the value for mmp_card_purchase_record_log.status
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_purchase_record_log.create_time
     *
     * @return the value of mmp_card_purchase_record_log.create_time
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_purchase_record_log.create_time
     *
     * @param createTime the value for mmp_card_purchase_record_log.create_time
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_purchase_record_log.create_oper_id
     *
     * @return the value of mmp_card_purchase_record_log.create_oper_id
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_purchase_record_log.create_oper_id
     *
     * @param createOperId the value for mmp_card_purchase_record_log.create_oper_id
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_purchase_record_log.create_oper_name
     *
     * @return the value of mmp_card_purchase_record_log.create_oper_name
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_purchase_record_log.create_oper_name
     *
     * @param createOperName the value for mmp_card_purchase_record_log.create_oper_name
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }
}