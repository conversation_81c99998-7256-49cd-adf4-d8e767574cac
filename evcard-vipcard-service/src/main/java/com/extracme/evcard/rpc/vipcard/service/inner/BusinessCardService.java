package com.extracme.evcard.rpc.vipcard.service.inner;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.bvm.dto.agencyPrice.CheckConditionDTO;
import com.extracme.evcard.bvm.dto.agencyPrice.CheckConditionResponse;
import com.extracme.evcard.bvm.service.IAgencyPriceService;
import com.extracme.evcard.bvm.service.IAgencyStoreService;
import com.extracme.evcard.bvm.to.agencyPrice.CheckConditionTo;
import com.extracme.evcard.bvm.to.agencyStore.CheckStoreBusinessConditionTO;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.vipcard.dto.BusinessCardCheckExtInput;
import com.extracme.evcard.rpc.vipcard.dto.GetAvailableCardByUseConditionInputDto;
import com.extracme.evcard.rpc.vipcard.dto.GetAvailableCardByUseConditionMdInput;
import com.extracme.evcard.rpc.vipcard.dto.GetAvailableCardByUseConditionOriginInput;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class BusinessCardService {

    @Autowired
    private IAgencyPriceService agencyPriceService;

    @Autowired
    private IAgencyStoreService agencyStoreService;

    /**
     * 适应大库模式 & 门店
     * @param inputDto
     * @param checkExtInput
     */
    public CheckConditionResponse checkCondition(GetAvailableCardByUseConditionInputDto inputDto, BusinessCardCheckExtInput checkExtInput) {
        boolean isMdOrder = Constants.RENT_METHOD_MD.equals(inputDto.getRentMethod());
        log.info("checkCondition入口：input={}, isMdOrder={}.", JSON.toJSONString(inputDto), isMdOrder);
        if(isMdOrder) {
            GetAvailableCardByUseConditionMdInput input = new GetAvailableCardByUseConditionMdInput();
            BeanCopyUtils.copyProperties(inputDto, input);
            return checkConditionMd(input, checkExtInput);
        }
        GetAvailableCardByUseConditionOriginInput input = new GetAvailableCardByUseConditionOriginInput();
        BeanCopyUtils.copyProperties(inputDto, input);
        return checkConditionOrigin(input, checkExtInput);
        //if (StringUtils.isNotBlank(inputDto.getPickUpShopSeq())) {
    }

    private CheckConditionResponse checkConditionOrigin(GetAvailableCardByUseConditionOriginInput inputDto,
                                                       BusinessCardCheckExtInput checkExtInput) {
        CheckConditionTo condition = new CheckConditionTo();
        condition.setAgencyId(checkExtInput.getAgencyId());
        condition.setOrderSeq(inputDto.getOrderSeq());
        condition.setPickupShopSeq(Long.valueOf(inputDto.getPickUpShopSeq()));
        condition.setReturnShopSeq(Long.valueOf(inputDto.getReturnShopSeq()));
        condition.setPickupTime(inputDto.getOrderPickUpDate());
        condition.setReturnTime(inputDto.getOrderReturnDate());
        condition.setAuthId(checkExtInput.getAuthId());
        condition.setAmount(checkExtInput.getAmount());
        condition.setUnitPrice(checkExtInput.getUnitPrice());
        condition.setActivityType(checkExtInput.getActivityType());
        condition.setVehicleNo(inputDto.getVehicleNo());
        CheckConditionResponse checkConditionResponse;
        if (StringUtils.isNotBlank(inputDto.getOrderSeq())) {
            checkConditionResponse = agencyPriceService.checkCondition(condition);
        } else {
            checkConditionResponse = agencyPriceService.checkConditionWithoutOrder(condition);
        }
        log.info("企业卡用车检查： checkCondition input={}, result={}", JSON.toJSON(condition), JSON.toJSON(checkConditionResponse));
        return checkConditionResponse;
    }

    private CheckConditionResponse checkConditionMd(GetAvailableCardByUseConditionMdInput inputDto, BusinessCardCheckExtInput checkExtInput) {
        CheckStoreBusinessConditionTO condition = new CheckStoreBusinessConditionTO();
        condition.setAgencyId(checkExtInput.getAgencyId());
        condition.setPickupStoreId(Long.valueOf(inputDto.getPickUpStoreId()));
        condition.setReturnStoreId(Long.valueOf(inputDto.getReturnStoreId()));
        condition.setPickupTime(inputDto.getOrderPickUpDate());
        condition.setReturnTime(inputDto.getOrderReturnDate());
        condition.setAuthId(checkExtInput.getAuthId());

        condition.setFinalAmount(checkExtInput.getAmount());
        //condition.setUnitPrice(checkExtInput.getUnitPrice());
        //condition.setActivityType(checkExtInput.getActivityType());
        condition.setVehicleNo(inputDto.getVehicleNo());
        CheckConditionResponse checkConditionResponse = agencyStoreService.checkStoreBusinessCondition(condition);
        log.info("企业卡用车检查： checkStoreBusinessCondition input={}, result={}", JSON.toJSON(condition), JSON.toJSON(checkConditionResponse));
        /**
         * TODO 调试用逻辑，等bvm接口调整后 应去除
         */
        wrapperCheckResp(checkConditionResponse.getBusinessCondition());
        wrapperCheckResp(checkConditionResponse.getPersonalCondition());
        return checkConditionResponse;
    }

    public void wrapperCheckResp(CheckConditionDTO checkConditionDTO) {
        if(checkConditionDTO != null) {
            if(checkConditionDTO.getPayFlag() == null) {
                checkConditionDTO.setPayFlag(1);
            }
        }
    }

}
