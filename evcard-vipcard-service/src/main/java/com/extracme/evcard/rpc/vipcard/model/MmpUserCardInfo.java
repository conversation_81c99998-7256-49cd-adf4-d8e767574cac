package com.extracme.evcard.rpc.vipcard.model;

import java.math.BigDecimal;
import java.util.Date;

public class MmpUserCardInfo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.user_card_no
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Long userCardNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.user_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Long userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.card_group
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Integer cardGroup;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.card_name
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private String cardName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.card_status
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Integer cardStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.agency_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private String agencyId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.card_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Long cardId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.start_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Date startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.expires_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Date expiresTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.total_order
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Long totalOrder;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.total_discount_amount
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private BigDecimal totalDiscountAmount;

    private Integer activeFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.status
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.create_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.update_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_card_info.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.user_card_no
     *
     * @return the value of mmp_user_card_info.user_card_no
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Long getUserCardNo() {
        return userCardNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.user_card_no
     *
     * @param userCardNo the value for mmp_user_card_info.user_card_no
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setUserCardNo(Long userCardNo) {
        this.userCardNo = userCardNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.user_id
     *
     * @return the value of mmp_user_card_info.user_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.user_id
     *
     * @param userId the value for mmp_user_card_info.user_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.card_group
     *
     * @return the value of mmp_user_card_info.card_group
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Integer getCardGroup() {
        return cardGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.card_group
     *
     * @param cardGroup the value for mmp_user_card_info.card_group
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setCardGroup(Integer cardGroup) {
        this.cardGroup = cardGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.card_name
     *
     * @return the value of mmp_user_card_info.card_name
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public String getCardName() {
        return cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.card_name
     *
     * @param cardName the value for mmp_user_card_info.card_name
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.card_status
     *
     * @return the value of mmp_user_card_info.card_status
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Integer getCardStatus() {
        return cardStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.card_status
     *
     * @param cardStatus the value for mmp_user_card_info.card_status
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setCardStatus(Integer cardStatus) {
        this.cardStatus = cardStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.agency_id
     *
     * @return the value of mmp_user_card_info.agency_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public String getAgencyId() {
        return agencyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.agency_id
     *
     * @param agencyId the value for mmp_user_card_info.agency_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.card_id
     *
     * @return the value of mmp_user_card_info.card_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Long getCardId() {
        return cardId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.card_id
     *
     * @param cardId the value for mmp_user_card_info.card_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.start_time
     *
     * @return the value of mmp_user_card_info.start_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.start_time
     *
     * @param startTime the value for mmp_user_card_info.start_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.expires_time
     *
     * @return the value of mmp_user_card_info.expires_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Date getExpiresTime() {
        return expiresTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.expires_time
     *
     * @param expiresTime the value for mmp_user_card_info.expires_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setExpiresTime(Date expiresTime) {
        this.expiresTime = expiresTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.total_order
     *
     * @return the value of mmp_user_card_info.total_order
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Long getTotalOrder() {
        return totalOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.total_order
     *
     * @param totalOrder the value for mmp_user_card_info.total_order
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setTotalOrder(Long totalOrder) {
        this.totalOrder = totalOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.total_discount_amount
     *
     * @return the value of mmp_user_card_info.total_discount_amount
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.total_discount_amount
     *
     * @param totalDiscountAmount the value for mmp_user_card_info.total_discount_amount
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    public Integer getActiveFlag() {
        return activeFlag;
    }

    public void setActiveFlag(Integer activeFlag) {
        this.activeFlag = activeFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.status
     *
     * @return the value of mmp_user_card_info.status
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.status
     *
     * @param status the value for mmp_user_card_info.status
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.create_time
     *
     * @return the value of mmp_user_card_info.create_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.create_time
     *
     * @param createTime the value for mmp_user_card_info.create_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.create_oper_id
     *
     * @return the value of mmp_user_card_info.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.create_oper_id
     *
     * @param createOperId the value for mmp_user_card_info.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.create_oper_name
     *
     * @return the value of mmp_user_card_info.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.create_oper_name
     *
     * @param createOperName the value for mmp_user_card_info.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.update_time
     *
     * @return the value of mmp_user_card_info.update_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.update_time
     *
     * @param updateTime the value for mmp_user_card_info.update_time
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.update_oper_id
     *
     * @return the value of mmp_user_card_info.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.update_oper_id
     *
     * @param updateOperId the value for mmp_user_card_info.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_card_info.update_oper_name
     *
     * @return the value of mmp_user_card_info.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_card_info.update_oper_name
     *
     * @param updateOperName the value for mmp_user_card_info.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}