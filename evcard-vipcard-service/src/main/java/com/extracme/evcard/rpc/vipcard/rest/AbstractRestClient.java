package com.extracme.evcard.rpc.vipcard.rest;


import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.config.MdRestApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;


@Slf4j
@Component
public abstract class AbstractRestClient {

    @Resource
    protected MdRestApiConfig mdRestApiConfig;

    @Resource
    protected RestTemplate restTemplate;

    /**
     * 可重写
     * @return
     */
    protected RestTemplate getRestTemplate() {
        return restTemplate;
    }

    protected String getBasePath() {
        return mdRestApiConfig.getBaseUrl();
    }

    @Nullable
    public <T> T postForObject(String path, @Nullable Object request, Class<T> responseType, Object... uriVariables) throws BusinessException {
        String url = getBasePath() + path;
        try {
            T resp = getRestTemplate().postForObject(url, request, responseType, uriVariables);
            log.info("rest请求: 请求完成，path={}, req={}, resp={}.", url, JSON.toJSONString(request), JSON.toJSONString(resp));
            return resp;
        }catch (Exception e) {
            log.error("rest请求: 请求异常，path=" + url + ", req=" + JSON.toJSONString(request), e);
            throw new BusinessException(-1, e.getMessage());
        }
    }
}
