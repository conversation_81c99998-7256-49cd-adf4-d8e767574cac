package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.model.SuixiangCardFileOperationLog;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardFileOperationLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardFileOperationLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_file_operation_log
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    int countByExample(SuixiangCardFileOperationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_file_operation_log
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_file_operation_log
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    int insert(SuixiangCardFileOperationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_file_operation_log
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    int insertSelective(SuixiangCardFileOperationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_file_operation_log
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    List<SuixiangCardFileOperationLog> selectByExample(SuixiangCardFileOperationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_file_operation_log
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    SuixiangCardFileOperationLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_file_operation_log
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    int updateByExampleSelective(@Param("record") SuixiangCardFileOperationLog record, @Param("example") SuixiangCardFileOperationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_file_operation_log
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    int updateByExample(@Param("record") SuixiangCardFileOperationLog record, @Param("example") SuixiangCardFileOperationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_file_operation_log
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    int updateByPrimaryKeySelective(SuixiangCardFileOperationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_file_operation_log
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    int updateByPrimaryKey(SuixiangCardFileOperationLog record);
}