package com.extracme.evcard.rpc.vipcard.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.vipcard.dao.MmpCardPurchaseRecordMapper;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.PurchaseRecordInfo;
import com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecord;
import com.extracme.evcard.rpc.vipcard.service.ICardTradeService;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 购卡订单自动取消job
 * 仅作为延时消息的补偿方案
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "vipcard-rpc-purchaseOrderCancelJob",
		cron = "0 0/10 * * * ?", description = "订单自动取消补偿job", overwrite = true)
public class PurchaseOrderCancelJob implements SimpleJob {

	@Resource
	private ICardTradeService cardTradeService;

	@Autowired
	private MmpCardPurchaseRecordMapper mmpCardPurchaseRecordMapper;

	private static int MAX_QUERY_LIMIT = 1000;
	@Override
	public void execute(ShardingContext shardingContext) {
		try{
			/**
			 * 每10分钟执行，[30, 8]分钟内容的未完成的订单
			 */
			Date now = new Date();
			Date startDate = DateUtil.addMin(now, - 30);
			Date endDate = DateUtil.addMin(now, - 8);

			Long id = 0L;
			List<MmpCardPurchaseRecord> list = null;
			while(true) {
				list = mmpCardPurchaseRecordMapper.selectTimeoutUnpayOrder(id, MAX_QUERY_LIMIT, startDate, endDate);
				if(CollectionUtils.isEmpty(list)) {
					break;
				}
				for(MmpCardPurchaseRecord record : list) {
					PurchaseRecordInfo purchaseRecord = new PurchaseRecordInfo();
					BeanCopyUtils.copyProperties(record, purchaseRecord);
					try {
						OperatorDto operator = new OperatorDto(-1L, "job", "vipcard");
						cardTradeService.cancelOrder(purchaseRecord, operator);
					}catch (Exception ex) {
						log.error("PurchaseOrderCancelJob，取消购卡订单失败，input=" + JSON.toJSONString(purchaseRecord), ex);
					}

				}
				id = list.get(list.size() - 1).getId();
			}
		}catch (Exception ex) {
			log.error("----PurchaseOrderCancelJob 超时订单自动取消失败...", ex);
		}
	}

}
