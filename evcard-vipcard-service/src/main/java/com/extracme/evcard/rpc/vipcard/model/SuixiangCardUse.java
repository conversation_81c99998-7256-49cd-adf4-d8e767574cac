package com.extracme.evcard.rpc.vipcard.model;

import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardUseDto;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

public class SuixiangCardUse {

    /**
     * 转换dto
     * @return
     */
    public SuixiangCardUseDto toDto(){
        SuixiangCardUseDto suixiangCardUseDto = new SuixiangCardUseDto();
        BeanUtils.copyProperties(this,suixiangCardUseDto);

        try {
            if (cardStatus == 1 && expiresTime.before(new Date())) {
                suixiangCardUseDto.setCardStatus(2);
            }
        } catch (Exception e) {
            
        }
        return suixiangCardUseDto;
    }

    /**
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.card_base_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Long cardBaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.card_price_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Long cardPriceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.user_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Long userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.purchase_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Long purchaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.card_type
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Integer cardType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.card_name
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private String cardName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.card_status
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Integer cardStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.start_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Date startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.expires_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Date expiresTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.total_order
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Integer totalOrder;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.total_discount_amount
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private BigDecimal totalDiscountAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.init_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Integer initDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.available_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Integer availableDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.used_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Integer usedDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.frozen_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Integer frozenDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.active_flag
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Integer activeFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.create_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.create_oper_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.create_oper_name
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.update_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.update_oper_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.update_oper_name
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_use.is_deleted
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    private Integer isDeleted;

    private Integer mergeFlag;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.id
     *
     * @return the value of suixiang_card_use.id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.id
     *
     * @param id the value for suixiang_card_use.id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.card_base_id
     *
     * @return the value of suixiang_card_use.card_base_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Long getCardBaseId() {
        return cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.card_base_id
     *
     * @param cardBaseId the value for suixiang_card_use.card_base_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setCardBaseId(Long cardBaseId) {
        this.cardBaseId = cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.card_price_id
     *
     * @return the value of suixiang_card_use.card_price_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Long getCardPriceId() {
        return cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.card_price_id
     *
     * @param cardPriceId the value for suixiang_card_use.card_price_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setCardPriceId(Long cardPriceId) {
        this.cardPriceId = cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.user_id
     *
     * @return the value of suixiang_card_use.user_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.user_id
     *
     * @param userId the value for suixiang_card_use.user_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.purchase_id
     *
     * @return the value of suixiang_card_use.purchase_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Long getPurchaseId() {
        return purchaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.purchase_id
     *
     * @param purchaseId the value for suixiang_card_use.purchase_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setPurchaseId(Long purchaseId) {
        this.purchaseId = purchaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.card_type
     *
     * @return the value of suixiang_card_use.card_type
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Integer getCardType() {
        return cardType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.card_type
     *
     * @param cardType the value for suixiang_card_use.card_type
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.card_name
     *
     * @return the value of suixiang_card_use.card_name
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public String getCardName() {
        return cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.card_name
     *
     * @param cardName the value for suixiang_card_use.card_name
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.card_status
     *
     * @return the value of suixiang_card_use.card_status
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Integer getCardStatus() {
        return cardStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.card_status
     *
     * @param cardStatus the value for suixiang_card_use.card_status
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setCardStatus(Integer cardStatus) {
        this.cardStatus = cardStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.start_time
     *
     * @return the value of suixiang_card_use.start_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.start_time
     *
     * @param startTime the value for suixiang_card_use.start_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.expires_time
     *
     * @return the value of suixiang_card_use.expires_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Date getExpiresTime() {
        return expiresTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.expires_time
     *
     * @param expiresTime the value for suixiang_card_use.expires_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setExpiresTime(Date expiresTime) {
        this.expiresTime = expiresTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.total_order
     *
     * @return the value of suixiang_card_use.total_order
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Integer getTotalOrder() {
        return totalOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.total_order
     *
     * @param totalOrder the value for suixiang_card_use.total_order
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setTotalOrder(Integer totalOrder) {
        this.totalOrder = totalOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.total_discount_amount
     *
     * @return the value of suixiang_card_use.total_discount_amount
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.total_discount_amount
     *
     * @param totalDiscountAmount the value for suixiang_card_use.total_discount_amount
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.init_days
     *
     * @return the value of suixiang_card_use.init_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Integer getInitDays() {
        return initDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.init_days
     *
     * @param initDays the value for suixiang_card_use.init_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setInitDays(Integer initDays) {
        this.initDays = initDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.available_days
     *
     * @return the value of suixiang_card_use.available_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Integer getAvailableDays() {
        return availableDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.available_days
     *
     * @param availableDays the value for suixiang_card_use.available_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setAvailableDays(Integer availableDays) {
        this.availableDays = availableDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.used_days
     *
     * @return the value of suixiang_card_use.used_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Integer getUsedDays() {
        return usedDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.used_days
     *
     * @param usedDays the value for suixiang_card_use.used_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setUsedDays(Integer usedDays) {
        this.usedDays = usedDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.frozen_days
     *
     * @return the value of suixiang_card_use.frozen_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Integer getFrozenDays() {
        return frozenDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.frozen_days
     *
     * @param frozenDays the value for suixiang_card_use.frozen_days
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setFrozenDays(Integer frozenDays) {
        this.frozenDays = frozenDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.active_flag
     *
     * @return the value of suixiang_card_use.active_flag
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Integer getActiveFlag() {
        return activeFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.active_flag
     *
     * @param activeFlag the value for suixiang_card_use.active_flag
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setActiveFlag(Integer activeFlag) {
        this.activeFlag = activeFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.create_time
     *
     * @return the value of suixiang_card_use.create_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.create_time
     *
     * @param createTime the value for suixiang_card_use.create_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.create_oper_id
     *
     * @return the value of suixiang_card_use.create_oper_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.create_oper_id
     *
     * @param createOperId the value for suixiang_card_use.create_oper_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.create_oper_name
     *
     * @return the value of suixiang_card_use.create_oper_name
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.create_oper_name
     *
     * @param createOperName the value for suixiang_card_use.create_oper_name
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.update_time
     *
     * @return the value of suixiang_card_use.update_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.update_time
     *
     * @param updateTime the value for suixiang_card_use.update_time
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.update_oper_id
     *
     * @return the value of suixiang_card_use.update_oper_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_use.update_oper_id
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.update_oper_name
     *
     * @return the value of suixiang_card_use.update_oper_name
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_use.update_oper_name
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_use.is_deleted
     *
     * @return the value of suixiang_card_use.is_deleted
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_use.is_deleted
     *
     * @param isDeleted the value for suixiang_card_use.is_deleted
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}