package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.redis.JedisLock;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.evcard.rpc.messagepush.service.ISensorsdataService;
import com.extracme.evcard.rpc.vipcard.dao.*;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.CreateCardDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.PurchaseSuiXiangCardDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.PurchaseSuiXiangCardRecordInfo;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardInfoDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.pay.PayOrderBo;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.pay.PayOrderDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.pay.PaySuiXiangCardPurchaseDto;
import com.extracme.evcard.rpc.vipcard.enums.*;
import com.extracme.evcard.rpc.vipcard.manager.SuixiangCardBaseManager;
import com.extracme.evcard.rpc.vipcard.model.*;
import com.extracme.evcard.rpc.vipcard.rest.MdRestClient;
import com.extracme.evcard.rpc.vipcard.rest.entity.CreatePayOrderReq;
import com.extracme.evcard.rpc.vipcard.rest.entity.PaySuiXiangCardOrderReq;
import com.extracme.evcard.rpc.vipcard.rest.entity.PaySuiXiangCardOrderRes;
import com.extracme.evcard.rpc.vipcard.rest.entity.RefundForCriticalPointPaymentRes;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
public class SuiXiangCardTradeService implements ISuiXiangCardTradeService {

    @Resource
    private SuixiangCardPriceMapper suixiangCardPriceMapper;

    @Resource
    private SuixiangCardPurchaseRecordMapper cardPurchaseRecordMapper;


    @Resource
    private SuixiangCardPurchaseRecordLogMapper cardPurchaseRecordLogMapper;

    @Resource
    private MdRestClient mdRestClient;

    @Resource
    private IMemberShipService memberShipService;

    @Resource
    private SuixiangCardBaseMapper suixiangCardBaseMapper;
    @Resource
    private SuixiangCardBaseManager suixiangCardBaseManager;

    @Resource
    private SuixiangCardPurchaseRecordMapper suixiangCardPurchaseRecordMapper;

    @Resource
    private SuixiangCardUseOpreationLogMapper suixiangCardUseOpreationLogMapper;
    @Resource
    private SuixiangCardUseMapper suixiangCardUseMapper;
    @Resource
    private ISuiXiangCardService suiXiangCardService;

    @Resource
    private ISensorsdataService sensorsdataService;

    /**
     * 创建支付订单
     * <p>
     * suixiang_card_purchase_record 增加记录
     * 调用pay 创建支付单，拿到pay_order_no，更新记录suixiang_card_purchase_record
     * <p>
     * suixiang_card_purchase_record_log 增加记录
     * 更新suixiang_card_base的库存字段
     * 创建5分钟的未支付订单取消任务
     * <p>
     * <p>
     * <p>
     * -检查库存，扣减库存
     * -不自动下架活动
     * -活动库存释放
     */
    @Override
    public PurchaseSuiXiangCardRecordInfo createSuiXiangCardOrder(PurchaseSuiXiangCardDto input, OperatorDto optUser) throws BusinessException {
        // 入参校验
        Long cardPriceId = input.getCardPriceId();
        Long userId = input.getUserId();
        Integer quantity = input.getQuantity();

        if (cardPriceId == null || userId == null || quantity == null
                || quantity < 0 || optUser == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }

        Jedis jedis = JedisUtil.getJedis();
        int expireMillis = 300000;
        String key = "lock_create_suixiangcard_order_" + userId;
        JedisLock lock = new JedisLock(key, expireMillis);
        try {
            // 一个用户同时只允许一个用户创建订单
            if (lock.acquire(jedis)) {
                // 创建支付单 限制条件判断
                MembershipBasicInfo membershipBasicInfo = payOrderLimit(userId);

                // 查询随享卡
                SuixiangCardInfoDto suixiangCardInfoDto = suixiangCardPriceMapper.selectInfoByPriceId(cardPriceId);
                if (suixiangCardInfoDto == null) {
                    log.error("创建随享卡订单：卡不存在, input={}", JSON.toJSONString(input));
                    throw new BusinessException(StatusCode.ACTIVITY_NOT_EXIST);
                }

                BigDecimal salesPrice = suixiangCardInfoDto.getSalesPrice();
                if ((salesPrice == null || BigDecimal.ZERO.compareTo(salesPrice) >= 0)) {
                    throw new BusinessException(-1, "请指定实际售价");
                }
                salesPrice = salesPrice.multiply(new BigDecimal(quantity));

                //库存、活动状态校验
                if (!(SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus() == suixiangCardInfoDto.getCardStatus())) {
                    log.error("创建随享卡订单：不满足发卡条件,活动非生效中 input={}", JSON.toJSONString(input));
                    throw new BusinessException(-1, StatusCode.ACTIVITY_NOT_RUNNING.getMsg());
                }

                // 活动开始结束时间校验
                Date saleStartTime = suixiangCardInfoDto.getSaleStartTime();
                Date saleEndTime = suixiangCardInfoDto.getSaleEndTime();

                // 将 now 设置为秒级精度, 因为数据库的时间是秒级精度，出现过重复提交的问题
                long nowSeconds = (System.currentTimeMillis() / 1000) * 1000; // 秒级精度的时间戳
                Date now = new Date(nowSeconds);
                if (saleStartTime.compareTo(now) > 0 || saleEndTime.compareTo(now) < 0) {
                    log.error("创建随享卡订单：不满足发卡条件,活动未开始 input={},suixiangCardInfoDto={}", JSON.toJSONString(input),JSON.toJSONString(suixiangCardInfoDto));
                    throw new BusinessException(-1, StatusCode.ACTIVITY_NOT_RUNNING.getMsg());
                }

                Integer stock = suixiangCardInfoDto.getStock();
                if (stock <= 0) {
                    log.error("创建随享卡订单：不满足发卡条件,stock <= 0, input={}", JSON.toJSONString(input));
                    throw new BusinessException(-1, "该卡已售罄，请选择其他卡片购买");
                }
                if (stock < quantity) {
                    log.error("创建随享卡订单：不满足发卡条件,购买张数大于库存。input={}", JSON.toJSONString(input));
                    throw new BusinessException(-1, StatusCode.UNDER_STOCK.getMsg());
                }
                // 用户 已购买的随享卡数
                Long cardBaseId = suixiangCardInfoDto.getCardBaseId();
                Integer suiXiangCardNum = suiXiangCardService.getPurchaseSuiXiangCardNum(userId, cardBaseId);
                if (suiXiangCardNum == null) {
                    throw new BusinessException(-1, "查询用户 已购买的随享卡数异常");
                }
                Integer purchaseLimitNum = suixiangCardInfoDto.getPurchaseLimitNum();
                if (purchaseLimitNum > 0 && suiXiangCardNum >= purchaseLimitNum) {
                    log.error("创建随享卡订单：不满足发卡条件,已达到购买上限, input={}", JSON.toJSONString(input));
                    throw new BusinessException(-1, "该卡已达到购买上限，请选择其他卡片购买");
                }
                if (purchaseLimitNum > 0 && purchaseLimitNum < (quantity + suiXiangCardNum)) {
                    log.error("创建随享卡订单：不满足发卡条件,购买张数大于限购张数。input={}", JSON.toJSONString(input));
                    throw new BusinessException(-1, "购买张数大于限购张数,最多能购买" + (purchaseLimitNum - suiXiangCardNum) + "张");
                }

                //相同cardPriceId 只能创建一笔待支付订单
                SuixiangCardPurchaseRecordExample purchaseRecordExample = new SuixiangCardPurchaseRecordExample();
                Date before = DateUtil.addMin(now, -5);
                purchaseRecordExample.createCriteria().andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete())
                        .andUserIdEqualTo(userId)
                        .andCardPriceIdEqualTo(cardPriceId)
                        .andIssueTypeEqualTo(SuiXiangCardIssueTypeEnum.PURCHASE.getType())
                        .andCreateTimeBetween(before, now)
                        .andPaymentStatusEqualTo(SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus());
                List<SuixiangCardPurchaseRecord> suixiangCardPurchaseRecords = suixiangCardPurchaseRecordMapper.selectByExample(purchaseRecordExample);
                if (CollectionUtils.isNotEmpty(suixiangCardPurchaseRecords)) {
                    log.error("创建随享卡订单：已有相同的待支付订单，请勿重复提交。input={}", JSON.toJSONString(input));
                    throw new BusinessException(-1, "已有相同的待支付订单，请勿重复提交");
                }


                // 1 suixiang_card_purchase_record 增加记录
                Date addDate = now;
                SuixiangCardPurchaseRecord record = new SuixiangCardPurchaseRecord();
                record.setOrgId(suixiangCardInfoDto.getOrgId());
                record.setUserId(userId);
                record.setOrderSeq(ComUtils.createOrderSeq(userId));
                record.setUserMobile(membershipBasicInfo.getMobilePhone());
                record.setCardBaseId(suixiangCardInfoDto.getCardBaseId());
                record.setCardPriceId(suixiangCardInfoDto.getCardPriceId());
                record.setCardRentId(suixiangCardInfoDto.getCardRentId());
                record.setCardName(suixiangCardInfoDto.getCardName());
                record.setIssueType(SuiXiangCardIssueTypeEnum.PURCHASE.getType());
                record.setPaymentStatus(SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus());
                record.setQuantity(quantity);
                record.setRealAmount(salesPrice);
                record.setStartTime(addDate);
                record.setCreateTime(addDate);
                record.setCreateOperId(optUser.getOperatorId());
                record.setCreateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
                record.setCardUseIds("-1");

                //2 创建支付单
                CreatePayOrderReq createPayOrderReq = new CreatePayOrderReq();
                createPayOrderReq.setMid(membershipBasicInfo.getMid());
                createPayOrderReq.setBillType(3);
                //createPayOrderReq.setOrderNo(cardPriceId.toString());
                createPayOrderReq.setAmount(salesPrice.toPlainString());
                createPayOrderReq.setGoodsSubject("购买随享卡");
                createPayOrderReq.setSource(1);
                createPayOrderReq.setBizLine(4);

                // 4 记录日志
                SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
                recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.ORDER.getOperationType());
                recordLog.setContent("用户：" + userId + "创建随享卡支付订单");
                recordLog.setCreateTime(addDate);
                recordLog.setCreateOperId(optUser.getOperatorId());
                recordLog.setCreateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));

                // 5 更新库存
                SuixiangCardBase baseUpdate = new SuixiangCardBase();
                baseUpdate.setId(suixiangCardInfoDto.getCardBaseId());
                baseUpdate.setStock(quantity);
                baseUpdate.setUpdateOperId(optUser.getOperatorId());
                baseUpdate.setUpdateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));

                suixiangCardBaseManager.createSuiXiangCardOrder(baseUpdate, record, createPayOrderReq, recordLog);

                PurchaseSuiXiangCardRecordInfo recordInfo = new PurchaseSuiXiangCardRecordInfo();
                SuixiangCardPurchaseRecord newRecord = suixiangCardPurchaseRecordMapper.selectByPrimaryKey(record.getId());
                BeanUtils.copyProperties(newRecord, recordInfo);
                recordInfo.setPlanCancelTime(recordInfo.gainCancelTime());

                // 埋点
                try {
                    Map<String, Object> map = new HashMap<>();
                    map.put("enjoy_card_id", suixiangCardInfoDto.getCardBaseId().toString());
                    map.put("order_id", record.getOrderSeq());
                    map.put("city_name", "");
                    map.put("item_name", suixiangCardInfoDto.getCardName());
                    //随享订单原价
                    map.put("price", suixiangCardInfoDto.getUnderlinePrice().intValue());
                    //随享订单优惠价
                    map.put("payment_amount", suixiangCardInfoDto.getSalesPrice().intValue());
                    map.put("rent_length", suixiangCardInfoDto.getRentDays());
                    map.put("car_type", suixiangCardInfoDto.getCarModelGroup());
                    map.put("operation_model", "门店");
                    sensorsdataService.track(membershipBasicInfo.getAuthId(), true, "submit_enjoy_card_order", map);
                } catch (Exception e) {
                    log.error("创建支付单 submit_enjoy_card_order 埋点异常，input[{}]", JSON.toJSONString(input), e);
                }
                return recordInfo;
            } else {
                log.error("创建随享卡订单，redis锁获取失败，key:{},input={}", key,  JSON.toJSONString(input));
                throw new BusinessException(-1, "已有相同的待支付订单，请勿重复提交");
            }
        }catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建随享卡订单，异常，input={}", JSON.toJSONString(input),e);
            throw new BusinessException(-1, StatusCode.UPDATE_FAILED.getMsg());
        } finally {
            if (jedis != null) {
                lock.releaseExcu(jedis);
                jedis.close();
            }
        }
    }


    /**
     * 检查购卡条件
     *
     * @return
     */
    public StatusCode checkPurchaseCondition(int activityStatus, int quantity, int stock) {
        if (!(SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus() == activityStatus)) {
            return StatusCode.ACTIVITY_NOT_RUNNING;
        }
        if (stock < quantity || stock <= 0) {
            return StatusCode.UNDER_STOCK;
        }
        return null;
    }

    //TODO 可迁移到manageer
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayOrderBo payeSuiXiangCardOrder(PayOrderDto input, OperatorDto optUser) throws BusinessException {
        Long userId = input.getUserId();
        String payOrderNo = input.getPayOrderNo();

        // 支付 限制条件判断
        MembershipBasicInfo membershipBasicInfo = payOrderLimit(userId);

        SuixiangCardPurchaseRecordExample example = new SuixiangCardPurchaseRecordExample();
        example.createCriteria().andUserIdEqualTo(userId).andPayOrderNoEqualTo(payOrderNo).andIsDeletedEqualTo(0);
        List<SuixiangCardPurchaseRecord> records = cardPurchaseRecordMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(records)) {
            if (records.size() > 1) {
                throw new BusinessException(-1, "匹配到多个支付订单");
            }

            SuixiangCardPurchaseRecord record = records.get(0);
            if (record.getPaymentStatus() != SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus()) {
                throw new BusinessException(-1, "订单状态非待支付");
            }

            // 调用支付接口
            PaySuiXiangCardOrderReq req = new PaySuiXiangCardOrderReq();
            BeanUtils.copyProperties(input, req);
            req.setMid(membershipBasicInfo.getMid());
            req.setRealAmount(record.getRealAmount());
            req.setDensityFree(2);

            PaySuiXiangCardOrderRes paySuiXiangCardOrderRes = mdRestClient.paySuiXiangCardOrder(req);
            if (paySuiXiangCardOrderRes == null || paySuiXiangCardOrderRes.getCode() != 0 || paySuiXiangCardOrderRes.getData() == null) {
                String message = paySuiXiangCardOrderRes.getMessage();
                throw new BusinessException(-1, StringUtils.isNotEmpty(message) ? message : "支付失败");
            }
            PayOrderBo bo = paySuiXiangCardOrderRes.getData();

            // 更新支付时间
            SuixiangCardPurchaseRecord updateRecord = new SuixiangCardPurchaseRecord();
            updateRecord.setId(record.getId());
            updateRecord.setPayTime(new Date());
            int ret = cardPurchaseRecordMapper.updateByPrimaryKeySelective(updateRecord);
            if (ret != 1) {
                throw new BusinessException(-1, "更新支付时间失败");
            }

            // 日志记录
            SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
            recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.PAYED.getOperationType());
            recordLog.setContent("用户：" + userId + "发起支付随享卡订单");
            recordLog.setCreateTime(new Date());
            recordLog.setPayOrderNo(record.getPayOrderNo());
            recordLog.setPurchaseId(record.getId());
            recordLog.setCreateOperId(optUser.getOperatorId());
            recordLog.setCreateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
            cardPurchaseRecordLogMapper.insertSelective(recordLog);

            // TODO  走mq消息 临时 注意 注意去除
            //paySuiXiangCardPurchaseCallBack(record.getId());
            return bo;
        } else {
            throw new BusinessException(-1, "无待支付订单");
        }
    }

    @Override
    public void cancelOrder(Long purchaseId, OperatorDto optUser) throws BusinessException {
        SuixiangCardPurchaseRecord purchaseRecord = cardPurchaseRecordMapper.selectByPrimaryKey(purchaseId);
        log.info("取消订单：购随享卡卡订单待支付，开始自动取消,purchaseRecord[{}]", JSON.toJSONString(purchaseRecord));
        if (purchaseRecord == null || purchaseRecord.getPaymentStatus() != SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus()) {
            log.error("取消订单：购随享卡订单待支付，购买记录状态非待支付,无需取消。purchaseRecord[{}]", JSON.toJSONString(purchaseRecord));
            return;
        }

        Long userId = purchaseRecord.getUserId();
        // 查询用户
        MembershipBasicInfo membershipBasicInfo = memberShipService.getUserBasicInfoByPkId(userId);
        if (membershipBasicInfo == null) {
            throw new BusinessException(StatusCode.USER_INFO_NO_EXIST);
        }

        // TODO 是否需要 用支付账单号 去查询支付流成
        if (StringUtils.isNotBlank(purchaseRecord.getPayOrderNo())) {
            //调用财务系统接口，校验此交易号是否已经发起第三方支付
        } else {
            log.error("取消时 未查到支付账单号");
        }

        /**
         * 进行订单取消操作
         * 库存释放
         */

        //更新购买记录
        Date nowTime = new Date();
        SuixiangCardPurchaseRecord updateRecord = new SuixiangCardPurchaseRecord();
        updateRecord.setId(purchaseId);
        updateRecord.setPaymentStatus(SuiXiangCardPaymentStatusEnum.CANCEL.getPaymentStatus());
        updateRecord.setEndTime(nowTime);
        updateRecord.setCancelTime(nowTime);
        updateRecord.setUpdateOperId(optUser.getOperatorId());
        updateRecord.setUpdateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));


        // 日志记录
        SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
        recordLog.setPurchaseId(purchaseId);
        recordLog.setPayOrderNo(purchaseRecord.getPayOrderNo());
        recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.CANCELED.getOperationType());
        recordLog.setContent("自动取消");
        recordLog.setCreateOperId(optUser.getOperatorId());
        recordLog.setCreateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
        recordLog.setCreateTime(nowTime);


        // 释放库存
        SuixiangCardBase cardBase = new SuixiangCardBase();
        cardBase.setId(purchaseRecord.getCardBaseId());
        // 这个负数，sql相当于 +了库存
        cardBase.setStock(0 - purchaseRecord.getQuantity());
        cardBase.setUpdateOperId(optUser.getOperatorId());
        cardBase.setUpdateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));

        suixiangCardBaseManager.cancelOrder(cardBase, updateRecord, recordLog, membershipBasicInfo.getMid(), purchaseRecord);
    }


    @Override
    public PaySuiXiangCardPurchaseDto paySuiXiangCardPurchaseCallBack(Long purchaseId) throws BusinessException {
        log.info("随享卡购卡回调, start, input={}", JSON.toJSONString(purchaseId));
        SuixiangCardPurchaseRecord purchaseRecord = cardPurchaseRecordMapper.selectByPrimaryKey(purchaseId);
        if (purchaseRecord == null) {
            throw new BusinessException(-1, "交易记录不存在");
        }

        SuixiangCardInfoDto suixiangCardInfoDto = suixiangCardPriceMapper.selectInfoByPriceId(purchaseRecord.getCardPriceId());
        if (suixiangCardInfoDto == null) {
            throw new BusinessException(-1, "交易记录不存在");
        }

        Long purchaseRecordId = purchaseRecord.getId();
        Long userId = purchaseRecord.getUserId();

        // 更新人员
        OperatorDto optUser = new OperatorDto(userId, StringUtils.isBlank(purchaseRecord.getCreateOperName()) ? "pay-rpc" : purchaseRecord.getCreateOperName(), "evcard-app");
        PaySuiXiangCardPurchaseDto payCarPurchaseDto = new PaySuiXiangCardPurchaseDto();
        payCarPurchaseDto.setPurchaseId(purchaseRecordId);
        /**
         * 购买记录尚未支付，则完成支付并发放卡片
         * 购买回调，只做发卡，不做活动状态检查和库存检查
         * 存在超卖可能性, 超卖或库存减为0，则立即下架活动
         */
        Date nowTime = new Date();
        List<SuixiangCardUse> useList = new ArrayList<>();
        List<SuixiangCardUseOpreationLog> useLogList = new ArrayList<>();
        if (purchaseRecord.getPaymentStatus() == SuiXiangCardPaymentStatusEnum.UNPAY.getPaymentStatus()) {

            for(int i=0; i<purchaseRecord.getQuantity();i++){
                /**
                 *  使用卡记录
                 */
                SuixiangCardUse use = new SuixiangCardUse();
                use.setCardBaseId(purchaseRecord.getCardBaseId());
                use.setCardPriceId(purchaseRecord.getCardPriceId());
                use.setUserId(userId);
                use.setPurchaseId(purchaseRecordId);
                use.setCardType(1); // 1-随享卡
                use.setCardName(purchaseRecord.getCardName());
                use.setCardStatus(SuiXiangCardStatusEnum.EFFECTIVE.getStatus());
                use.setStartTime(nowTime);
                LocalDateTime startLocalDateTime = DateUtil.dateToLocalDateTime(nowTime);
                LocalDateTime expiresLocalDateTime = startLocalDateTime.plusMonths(suixiangCardInfoDto.getValidDaysType());
                use.setExpiresTime(DateUtil.localDateTimeToDate(expiresLocalDateTime));
                use.setInitDays(suixiangCardInfoDto.getRentDays());
                use.setAvailableDays(suixiangCardInfoDto.getRentDays());
                use.setUsedDays(0);
                use.setFrozenDays(0);
                use.setCreateTime(nowTime);
                use.setCreateOperId(optUser.getOperatorId());
                use.setCreateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
                useList.add(use);
                /**
                 * 使用卡日志表
                 *
                 */
                SuixiangCardUseOpreationLog useLog = new SuixiangCardUseOpreationLog();
                useLog.setCardPriceId(purchaseRecord.getCardPriceId());
                useLog.setPurchaseId(purchaseRecordId);
                useLog.setCardGroup(1); // 卡类别：1-随享卡
                useLog.setOperationType(SuiXiangCardUseLogOperationTypeEnum.GIVE_OUT.getOperationType());
                useLog.setOriginSystem(optUser.getOriginSystem()); // 门店管理系统
                useLog.setInitDays(suixiangCardInfoDto.getRentDays());
                useLog.setAvailableDays(suixiangCardInfoDto.getRentDays());
                useLog.setUsedDays(0);
                useLog.setFrozenDays(0);
                useLog.setMiscDesc("购卡成功发卡");
                useLog.setCreateTime(nowTime);
                useLog.setCreateOperId(optUser.getOperatorId());
                useLog.setCreateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
                useLog.setOrderSeq("");
                useLogList.add(useLog);
            }

            /**
             * 更新 购买记录,修改支付状态
             */
            SuixiangCardPurchaseRecord updateRecord = new SuixiangCardPurchaseRecord();
            updateRecord.setId(purchaseRecordId);
            updateRecord.setPaymentStatus(SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus());
            updateRecord.setEndTime(nowTime);
            updateRecord.setUpdateOperId(optUser.getOperatorId());
            updateRecord.setUpdateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));

            /**
             *  增加购卡日志记录
             */
            SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
            recordLog.setPurchaseId(purchaseRecordId);
            recordLog.setPayOrderNo(purchaseRecord.getPayOrderNo());
            recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.GIVE_OUT.getOperationType());
            recordLog.setContent("支付回调成功，发卡");
            recordLog.setCreateOperId(optUser.getOperatorId());
            recordLog.setCreateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
            recordLog.setCreateTime(nowTime);

            /**
             * 更新活动APP
             *    更新活动库存，若库存不足，则下架活动
             *
             */
            // 销量大于等于 初始库存 下架
            // boolean soldOut = (suixiangCardInfoDto.getStock() <= 0);
            boolean soldOut = (suixiangCardInfoDto.getInitStock() <= suixiangCardInfoDto.getTotalSales());
            if (soldOut) {
                log.info("随享卡活动卖完了。suixiangCardInfoDto[{}]", JSON.toJSONString(suixiangCardInfoDto));
            }

            // 更新销量 购卡，发卡时无需再更新库存，只累计售出数量
            SuixiangCardBase updateCardBase = new SuixiangCardBase();
            updateCardBase.setId(suixiangCardInfoDto.getCardBaseId());
            updateCardBase.setStock(0);
            updateCardBase.setSales(purchaseRecord.getQuantity());
            //事务统一操作
            List<String> cardUseIds = suixiangCardBaseManager.paySuccessCallBack(purchaseRecord, useList, useLogList, updateRecord, recordLog, soldOut, updateCardBase);

            payCarPurchaseDto.setUseCardId(cardUseIds);
        } else {
            log.error("购卡回调, 购卡记录状态异常, purchaseRecord={}, paymentStatus={}", JSON.toJSONString(purchaseRecord), purchaseRecord.getPaymentStatus());
            SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
            recordLog.setPurchaseId(purchaseRecordId);
            recordLog.setPayOrderNo(purchaseRecord.getPayOrderNo());
            recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.CANCELED.getOperationType());
            recordLog.setContent("自动取消");
            recordLog.setCreateOperId(optUser.getOperatorId());
            recordLog.setCreateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
            recordLog.setCreateTime(nowTime);

            if (purchaseRecord.getPaymentStatus() == SuiXiangCardPaymentStatusEnum.CANCEL.getPaymentStatus()) {
                recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.ABNORMAL_PAY_CANCELED.getOperationType());
                // 查询用户
                MembershipBasicInfo membershipBasicInfo = memberShipService.getUserBasicInfoByPkId(userId);
                if (membershipBasicInfo == null) {
                    throw new BusinessException(StatusCode.USER_INFO_NO_EXIST);
                }
                // 支付单 已经取消 发起退款
                /*String orderNo = "SXK" + purchaseRecordId;
                CancelPayOrderRes cancelPayOrderRes = mdRestClient.cancelPayOrder(membershipBasicInfo.getMid(), orderNo);*/
                String payOrderNo = purchaseRecord.getPayOrderNo();
                if (StringUtils.isBlank(payOrderNo)) {
                    log.error("购卡回调, 购卡记录状态异常,发起退卡流程时，payOrderNo为空，input={}",JSON.toJSONString(purchaseRecord));
                    throw new BusinessException(-1, "payOrderNo不存在");
                }

                RefundForCriticalPointPaymentRes refundForCriticalPointPaymentRes = mdRestClient.refundForCriticalPointPayment(purchaseRecord.getPayOrderNo());
                if (refundForCriticalPointPaymentRes == null || refundForCriticalPointPaymentRes.getCode() != 0) {
                    log.error("支付回调， 取消随享卡支付订单失败，refundForCriticalPointPaymentRes[{}]", JSON.toJSONString(refundForCriticalPointPaymentRes));
                    recordLog.setContent("无效的支付，订单已经取消，发起退款失败。");
                }else{
                    log.error("支付回调 取消随享卡支付订单成功，refundForCriticalPointPaymentRes[{}]", JSON.toJSONString(refundForCriticalPointPaymentRes));
                    recordLog.setContent("无效的支付，订单已经取消，发起退款成功。");
                }
            } else if (purchaseRecord.getPaymentStatus() == SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus()) {
                recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.ABNORMAL_PAY_PAYED.getOperationType());
                recordLog.setContent("重复的支付，订单已支付，待退款。");
            }
            //购卡订单操作日志
            cardPurchaseRecordLogMapper.insertSelective(recordLog);
        }

        log.info("购卡回调, end, input={}", JSON.toJSONString(purchaseRecord));
        return payCarPurchaseDto;

    }

    /**
     * 购卡限制
     * 1: 有冻结，有生效中的 禁止购买
     * 2: 身份认证不通过禁止购买
     * 3:
     *
     * @param userId
     * @throws BusinessException
     */
    public MembershipBasicInfo payOrderLimit(Long userId) throws BusinessException {

//        if (suiXiangCardService.hasEffectiveSuiXiangCard(userId)) {
//            throw new BusinessException(-1, "已有生效中的随享卡，限制购买");
//        }

        // 查询用户
        MembershipBasicInfo membershipBasicInfo = memberShipService.getUserBasicInfoByPkId(userId);
        if (membershipBasicInfo == null) {
            throw new BusinessException(-1, StatusCode.USER_INFO_NO_EXIST.getMsg());
        }

       /* // 身份认证不通过
        if (!(Constants.LICENCE_STATUS_PASS.equals(membershipBasicInfo.getLicenseReviewStatus()) && Constants.IDCARD_STATUS_PASS.equals(membershipBasicInfo.getAuthenticationStatusNew()))) {
            throw new BusinessException(-1, "完成身份认证后方可买卡哦");
        }*/

        return membershipBasicInfo;
    }

    @Override
    public List<Long> issueCard(CreateCardDto input, boolean checkActivity, OperatorDto optUser) throws BusinessException {
        try {
            Long cardPriceId = input.getCardPriceId();
            Long purchaseId = input.getPurchaseId();

            if (cardPriceId == null || input.getUserId() == null || input.getQuantity() == null || input.getQuantity() < 0 || optUser == null) {
                throw BusinessRuntimeException.PARAM_EXEPTION;
            }
            // 购买 时校验参数
            if (input.getIssueType() == 0) {
                if (purchaseId == null || StringUtils.isBlank(input.getPayOrderNo())) {
                    log.error("付费会员卡发放：购卡需要基于购买记录ID，此ID不可为空, input={}", JSON.toJSONString(input));
                    throw BusinessRuntimeException.PARAM_EXEPTION;
                }
            }
            SuixiangCardInfoDto suixiangCardInfoDto = suixiangCardPriceMapper.selectInfoByPriceId(cardPriceId);
            /**
             * 1. 校验活动状态及库存
             */
            //查询活动对应的卡
            if (suixiangCardInfoDto == null) {
                log.error("随想卡发放：卡信息不存在, input={}", JSON.toJSONString(input));
                throw new BusinessException(StatusCode.ACTIVITY_NOT_EXIST);
            }

            SuixiangCardPurchaseRecord purchaseRecord = suixiangCardPurchaseRecordMapper.selectByPrimaryKey(purchaseId);
            // 购买记录
            if (purchaseRecord == null) {
                log.error("随想卡发放：购买记录不存在, input={}", JSON.toJSONString(input));
                throw new BusinessException(StatusCode.ACTIVITY_NOT_EXIST);
            }

            if (checkActivity) {
                StatusCode checkResult = checkPurchaseCondition(suixiangCardInfoDto.getCardStatus(), input.getQuantity(), suixiangCardInfoDto.getStock());
                if (checkResult != null) {
                    log.error("随想卡发放：不满足发卡条件, checkResult={}, input={}", JSON.toJSONString(checkResult), JSON.toJSONString(input));
                }
            }
            Date nowTime = new Date();
            /**
             * 2. 卡发放
             */
            List<Long> cardUseIds = new ArrayList<>();
            for(int i=0; i<purchaseRecord.getQuantity();i++){
                /**
                 *  使用卡记录
                 */
                SuixiangCardUse use = new SuixiangCardUse();
                use.setUserId(purchaseRecord.getUserId());
                use.setPurchaseId(purchaseRecord.getId());
                use.setCardBaseId(purchaseRecord.getCardBaseId());
                use.setCardPriceId(purchaseRecord.getCardPriceId());
                use.setCardType(1); // 1-随享卡
                use.setCardName(purchaseRecord.getCardName());
                use.setCardStatus(SuiXiangCardStatusEnum.EFFECTIVE.getStatus());
                use.setStartTime(nowTime);
                LocalDateTime startLocalDateTime = DateUtil.dateToLocalDateTime(nowTime);
                LocalDateTime expiresLocalDateTime = startLocalDateTime.plusMonths(suixiangCardInfoDto.getValidDaysType());
                use.setExpiresTime(DateUtil.localDateTimeToDate(expiresLocalDateTime));
                use.setInitDays(suixiangCardInfoDto.getRentDays());
                use.setAvailableDays(suixiangCardInfoDto.getRentDays());
                use.setUsedDays(0);
                use.setFrozenDays(0);
                use.setCreateTime(nowTime);
                use.setCreateOperId(optUser.getOperatorId());
                use.setCreateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
                suixiangCardUseMapper.insertSelective(use);
                cardUseIds.add(use.getId());
                /**
                 * 使用卡日志表
                 *
                 */
                SuixiangCardUseOpreationLog useLog = new SuixiangCardUseOpreationLog();
                useLog.setCardUseId(use.getId());
                useLog.setCardPriceId(purchaseRecord.getCardPriceId());
                useLog.setPurchaseId(purchaseRecord.getId());
                useLog.setCardGroup(1); // 卡类别：1-随享卡
                useLog.setOperationType(SuiXiangCardUseLogOperationTypeEnum.GIVE_OUT.getOperationType());
                useLog.setOriginSystem("app"); // 门店管理系统
                useLog.setInitDays(suixiangCardInfoDto.getRentDays());
                useLog.setAvailableDays(suixiangCardInfoDto.getRentDays());
                useLog.setUsedDays(0);
                useLog.setFrozenDays(0);
                useLog.setCreateTime(nowTime);
                useLog.setCreateOperId(optUser.getOperatorId());
                useLog.setCreateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
                useLog.setOrderSeq("");
                suixiangCardUseOpreationLogMapper.insertSelective(useLog);
            }


            /**
             * 更新购买记录
             */
            SuixiangCardPurchaseRecord updateRecord = new SuixiangCardPurchaseRecord();
            updateRecord.setId(purchaseRecord.getId());
            updateRecord.setPaymentStatus(SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus());
            updateRecord.setUpdateOperId(optUser.getOperatorId());
            updateRecord.setUpdateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
            int ret = cardPurchaseRecordMapper.updateByPrimaryKeySelective(updateRecord);
            if (ret != 1) {
                log.info("支付回调：购随享卡卡订单,更新状态失败，purchaseId={}, PayOrderNo={}", purchaseRecord.getId(), purchaseRecord.getPayOrderNo());
                throw new BusinessException(-1, "更新支付状态失败");
            }

            /**
             *  增加购卡日志记录
             */
            SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
            recordLog.setPurchaseId(purchaseRecord.getId());
            recordLog.setPayOrderNo(purchaseRecord.getPayOrderNo());
            recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.GIVE_OUT.getOperationType());
            recordLog.setContent("支付回调成功，发卡");
            recordLog.setCreateOperId(optUser.getOperatorId());
            recordLog.setCreateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
            recordLog.setCreateTime(nowTime);
            cardPurchaseRecordLogMapper.insertSelective(recordLog);


            /**
             * 更新活动APP
             *    更新活动库存，若库存不足，则下架活动
             *
             */
            //购卡，发卡时无需再更新库存，只累计售出数量
            boolean soldOut = (suixiangCardInfoDto.getStock() <= 0);
            if (soldOut) {
                SuixiangCardBase base = new SuixiangCardBase();
                base.setId(suixiangCardInfoDto.getCardBaseId());
                base.setCardStatus(SuiXiangCardBaseCardStatusEnum.OFF_SHELF.getStatus());
                base.setUpdateOperId(optUser.getOperatorId());
                base.setUpdateOperName(ComUtils.splitStr(optUser.getOperatorName(), Constants.OPER_NAME_LENGTH));
                ret = suixiangCardBaseMapper.updateByPrimaryKeySelective(base);
                if (ret != 1) {
                    log.error("支付回调：下架活动。更新suixiang_card_base表失败！base={}", JSON.toJSONString(base));
                    throw new BusinessException(-1, "下架时更新失败");
                }
            }

            // 更新销量
            SuixiangCardBase base2 = new SuixiangCardBase();
            base2.setId(suixiangCardInfoDto.getCardBaseId());
            base2.setStock(0);
            base2.setSales(input.getQuantity());
            ret = suixiangCardBaseMapper.updateStock(base2);
            if (ret != 1) {
                log.error("支付回调：更新suixiang_card_base表失败！base={}", JSON.toJSONString(base2));
                throw new RuntimeException("更新库存失败");
            }
            return cardUseIds;
        } catch (Exception e) {
            log.error("支付回调：异常！input={}", JSON.toJSONString(input));
            return null;
        }
    }


}
