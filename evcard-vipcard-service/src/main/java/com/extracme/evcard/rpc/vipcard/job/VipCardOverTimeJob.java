package com.extracme.evcard.rpc.vipcard.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.vipcard.dao.MmpUserCardInfoMapper;
import com.extracme.evcard.rpc.vipcard.model.MmpUserCardInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@ElasticSimpleJob(jobName = "vipcard-rpc-vipCardOverTimeJob",
        cron = "0 0 8 * * ? *", description = "会员卡即将到期提醒", overwrite = true)
public class VipCardOverTimeJob implements SimpleJob {

    @Autowired
    MmpUserCardInfoMapper mmpUserCardInfoMapper;

    @Resource
    private IMessagepushServ messageServ;

    @Resource
    private IMemberShipService memberShipService;

    @Override
    public void execute(ShardingContext shardingContext) {

        List<MmpUserCardInfo> mmpUserCardInfos =  mmpUserCardInfoMapper.queryUserOverTimeCardInfo();

        if (CollectionUtils.isEmpty(mmpUserCardInfos)){
            return;
        }

        mmpUserCardInfos.stream().forEach(p->{

            MembershipBasicInfo membershipBasicInfo = memberShipService.getUserBasicInfoByPkId(p.getUserId());
            if (membershipBasicInfo != null){
                messageServ.push(membershipBasicInfo.getAuthId(),membershipBasicInfo.getMembershipType(),215,2,"vipCard-rpc");
                // 短信优化需求：https://wiki.gcsrental.com/wiki/spaces/T-1681206805087/pages/6524a9addaa28a4b20f6abb9，不再发送
//                messageServ.asyncSendSMSTemplateForMarket(membershipBasicInfo.getMobilePhone(), 229, null, "vipCard-rpc");
            }

        });

    }
}
