
package com.extracme.evcard.rpc.vipcard.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;
import java.util.TimeZone;

/* *
 *类名：UtilDate
 *功能：自定义订单类
 *详细：工具类，可以用作获取系统日期、订单编号等
 *版本：3.3
 *日期：2012-09-21
 *说明：
 *以下代码只是为了方便商户测试而提供的样例代码，商户可以根据自己网站的需要，按照技术文档编写,并非一定要使用该代码。
 *该代码仅供学习和研究支付宝接口使用，只是提供一个参考。
 */
public class DateUtil {

    /**
     * 年月日时分秒(无下划线) yyyyMMddHHmmss
     */
    public static final String dtLong = "yyyyMMddHHmmss";

    /**
     * 完整时间 yyyy-MM-dd HH:mm:ss
     */
    public static final String simple = "yyyy-MM-dd HH:mm:ss";

    /**
     * 年月日(无下划线) yyyyMMdd
     */
    public static final String dtShort = "yyyyMMdd";

    /**
     * 时分秒
     **/
    public static final String timeShort = "HHmmss";
    /**
     * date时间格式
     */
    public static final String DATE_TYPE1 = "yyyy-MM-dd";
    /**
     * 自定义时间格式yyyyMMdd000000000
     */
    public static final String DATE_TYPE2 = "yyyyMMdd000000000";
    /**
     * 自定义时间格式yyyyMMdd235959999
     */
    public static final String DATE_TYPE3 = "yyyyMMdd235959999";
    /**
     * 自定义时间格式yyyy-MM-dd 00:00:00
     */
    public static final String DATE_TYPE4 = "yyyy-MM-dd 00:00:00";
    /**
     * 自定义时间格式yyyy-MM-dd 23:59:59
     */
    public static final String DATE_TYPE5 = "yyyy-MM-dd 23:59:59";
    /**
     * 自定义时间格式yyyyMMddHHmm
     */
    public static final String DATE_TYPE10 = "yyyyMMddHHmm";
    public static final String DATE_TYPE6 = "HH:mm";
    public static final String DATE_TYPE7 = "yyyy/MM/dd HH:mm";

    /**
     * 完整时间 yyyy-MM-dd HH:mm
     */
    public static final String DATE_TYPE8 = "yyyy-MM-dd HH:mm";
    public static final String DATE_TYPE9 = "yyyyMMddHH0000";
    public static final String DATE_TYPE11 = "HHmm";
    public static final String DATE_TYPE12 = "yyyy年MM月dd日";
    public static final String DATE_TYPE13 = "yyyy年MM月dd日 HH:mm";
    public static final DateTimeFormatter DATE_TYPE14 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    public static final String DATE_TYPE15 = "yyyy/MM/dd";
    public static TimeZone timeZoneChina = TimeZone.getTimeZone("Asia/Shanghai");// 获取时区

    static Logger logger = LoggerFactory.getLogger(DateUtils.class);


    /**
     * 返回系统当前时间(精确到毫秒),作为一个唯一的订单编号
     *
     * @return 以yyyyMMddHHmmss为格式的当前系统时间
     */
    public static String getOrderNum() {
        Date date = new Date();
        DateFormat df = new SimpleDateFormat(dtLong);
        return df.format(date);
    }

    /**
     * 获取系统当前日期(精确到毫秒)，格式：yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public static String getDateFormatter() {
        Date date = new Date();
        DateFormat df = new SimpleDateFormat(simple);
        return df.format(date);
    }

    /**
     * 获取系统当期年月日(精确到天)，格式：yyyyMMdd
     *
     * @return
     */
    public static String getDate() {
        Date date = new Date();
        DateFormat df = new SimpleDateFormat(dtShort);
        return df.format(date);
    }

    /**
     * 获取当前时间
     *
     * @param type 指定格式
     * @return
     */
    public static String getSystemDate(String type) {
        // 指定格式
        DateFormat date_format = new SimpleDateFormat(type);
        date_format.setTimeZone(timeZoneChina);
        // 范围指定格式的字符串
        return date_format.format(new Date());
    }

    /**
     * 产生随机的三位数
     *
     * @return
     */
    public static String getThree() {
        Random rad = new Random();
        return rad.nextInt(1000) + "";
    }

    /**
     * 获取时间 时分秒
     *
     * @param date
     * @return
     */
    public static String getTimeShort(Date date) {
        DateFormat df = new SimpleDateFormat(timeShort);
        return df.format(date);
    }

    /**
     * 时间转化
     *
     * @param dateStr
     * @param fromType
     * @param toType
     * @return
     */
    public static String getFormatDate(String dateStr, String fromType, String toType) {

        try {
            DateFormat dateFromFmt = new SimpleDateFormat(fromType);
            DateFormat dateToFmt = new SimpleDateFormat(toType);
            // 非空检查
            if (dateStr == null || dateStr.equals("")) {
                return "";
            } else {
                Date tmpDate = dateFromFmt.parse(dateStr);

                if (dateFromFmt.format(tmpDate).equals(dateStr)) {
                    return dateToFmt.format(tmpDate);
                } else {
                    return "";
                }
            }
        } catch (Exception e) {
            return "";
        }
    }

    public static String getFormatDate(Date date, String toType) {

        try {
            DateFormat dateToFmt = new SimpleDateFormat(toType);
            dateToFmt.setTimeZone(timeZoneChina);
            // 非空检查
            if (date == null) {
                return "";
            } else {
                return dateToFmt.format(date);
            }
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 将字符串转为日期，会做自动转换，如24点，2月31号等
     *
     * @param dateStr
     * @param fromType
     * @return
     */
    public static Date getDateFromStr(String dateStr, String fromType) {
        try {
            DateFormat dateFromFmt = new SimpleDateFormat(fromType);
            dateFromFmt.setTimeZone(timeZoneChina);

            return dateFromFmt.parse(dateStr);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String dateToString(Date date, String fromType) {
        SimpleDateFormat sdf1 = new SimpleDateFormat(fromType);
        String strDate1 = sdf1.format(date);
        return strDate1;
    }

    public static LocalDateTime getLocalDateFromStr(String dateStr, DateTimeFormatter fromType) {
        return LocalDateTime.parse(dateStr, fromType);
    }

    /**
     * 将字符串转为日期，强校验，不做自动转换(如24点，2月31号等)
     *
     * @param dateStr
     * @param fromType
     * @return
     */
    public static Date getDateFromStrEx(String dateStr, String fromType) {
        try {
            DateFormat dateFromFmt = new SimpleDateFormat(fromType);
            dateFromFmt.setTimeZone(timeZoneChina);

            Date date = dateFromFmt.parse(dateStr);
            if (!dateFromFmt.format(date).equals(dateStr)) {
                return null;
            }
            return date;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 加减分钟
     *
     * @param date
     * @param min
     * @return
     */
    public static Date addMin(Date date, Integer min) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, min);
        date = calendar.getTime();
        return date;
    }

    /**
     * 加减天数.
     *
     * @param date
     * @param day
     * @return
     */
    public static Date addDay(Date date, Integer day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, day);
        Date result = calendar.getTime();
        return result;
    }


    public static void main(String[] args) {
     /*   Date date1 = new Date();
        Date date = addDay(date1, 2);
        System.out.println(getFormatDate(date, "yyyy-MM-dd"));*/

        Date e1 = new Date();

        String s="2023-02-02 20:40:20";
        Date s1 = DateUtil.getDateFromStr(s, simple);
        String s2 = mmssBetween(s1, e1);
        System.out.println(s2);


    }

    public static BigDecimal hoursBetween(Date smdate, Date bdate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(smdate);
        long time1 = cal.getTimeInMillis();
        cal.setTime(bdate);
        long time2 = cal.getTimeInMillis();
        double hours = (time2 - time1) / (1000 * 3600.00);
        BigDecimal hour = new BigDecimal(hours);
        return hour;
    }

    /**
     * 计算分钟数(起始时间向下取整，结束时间向上取整)
     *
     * @param smdate
     * @param bdate
     * @return
     */
    public static int minutesBetween(Date smdate, Date bdate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(smdate);
        long time1 = cal.getTimeInMillis() / (1000 * 60);
        cal.setTime(bdate);
        double time2 = cal.getTimeInMillis() / (1000 * 60.00);
        //分钟数
        BigDecimal minutes = new BigDecimal(time2 - time1);
        minutes = minutes.setScale(0, RoundingMode.UP);
        return minutes.intValue();
    }


    /**
     * 计算秒数(起始时间向下取整，结束时间向上取整)
     *
     * @param smdate
     * @param bdate
     * @return
     */
    public static long secondsBetween(Date smdate, Date bdate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(smdate);
        long seconds1 = cal.getTimeInMillis() / (1000);
        cal.setTime(bdate);
        long seconds2 = cal.getTimeInMillis() / (1000);
        return seconds2 - seconds1;
    }


    public static String mmssBetween(Date smdate, Date bdate) {
        if (smdate.after(bdate)) {
            return "0000";
        }

        long millSeconds1 = smdate.getTime();
        long millSeconds2 = bdate.getTime();
        long diff = (millSeconds2 - millSeconds1) / 1000;

        long min = diff / 60;
        long seconds = diff % 60;
        if (min >= 5) {
            return "0500";
        }

        StringBuffer sb = new StringBuffer();
        if (min >= 10) {
            sb.append(min);
        } else {
            sb.append("0");
            sb.append(min);
        }

        if (seconds >= 10) {
            sb.append(seconds);
        } else {
            sb.append("0");
            sb.append(seconds);
        }
        return sb.toString();
    }



    public static String formatChange(String dateStr, String srcFormat, String descFormat) {
        try {
            Date date = DateUtils.parseDate(dateStr, srcFormat);
            return DateFormatUtils.format(date, descFormat);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 将分钟数转换为 X天X小时X分钟 格式的文案
     *
     * @param minuteNum
     * @return
     */
    public static String formatTimeDesc(long minuteNum) {
        StringBuffer formatDesc = new StringBuffer();
        int days = (int) (minuteNum / 60 / 24);
        int hour = (int) ((minuteNum - days * 60 * 24) / 60);
        int minute = (int) (minuteNum - days * 60 * 24 - hour * 60);
        boolean flag = false;
        if (days > 0) {
            flag = true;
            formatDesc.append(days).append("天");
        }
        if ((flag && minute > 0) || hour > 0) {
//			if (hour > 0 && hour < 10) {
//				formatDesc.append("0");
//			}
            formatDesc.append(hour).append("小时");
        }
        if (minute > 0) {
//			if (minute > 0 && minute < 10) {
//				formatDesc.append("0");
//			}
            formatDesc.append(minute).append("分钟");
        }
        return formatDesc.toString();
    }

    /**
     * 判断字符串格式是否符合要求
     *
     * @param str
     * @param format
     * @return
     */
    public static boolean isValidDateString(String str, String format) {
        if (StringUtils.isAnyBlank(str, format)) {
            return false;
        }
        try {
            DateFormat dateFormat = new SimpleDateFormat(format);
            dateFormat.setTimeZone(timeZoneChina);
            dateFormat.setLenient(false); // 严格解析日期
            dateFormat.parse(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

}
