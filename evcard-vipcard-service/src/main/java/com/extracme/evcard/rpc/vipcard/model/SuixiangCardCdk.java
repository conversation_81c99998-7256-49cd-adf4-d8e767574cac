package com.extracme.evcard.rpc.vipcard.model;

import java.util.Date;

public class SuixiangCardCdk {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.card_cdk_config_detail_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Long cardCdkConfigDetailId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.card_base_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Long cardBaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.card_rent_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Long cardRentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.card_price_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Long cardPriceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.cdkey
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private String cdkey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.is_activated
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Integer isActivated;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.activated_mid
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private String activatedMid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.activated_user_name
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private String activatedUserName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.activated_user_mobile
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private String activatedUserMobile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.activated_time
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Date activatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.card_use_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Long cardUseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.wechat_cdk_qr_url
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private String wechatCdkQrUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.create_time
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.create_oper_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.create_oper_name
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.update_time
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.update_oper_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.update_oper_name
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk.is_deleted
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    private Integer isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.id
     *
     * @return the value of suixiang_card_cdk.id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.id
     *
     * @param id the value for suixiang_card_cdk.id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.card_cdk_config_detail_id
     *
     * @return the value of suixiang_card_cdk.card_cdk_config_detail_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Long getCardCdkConfigDetailId() {
        return cardCdkConfigDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.card_cdk_config_detail_id
     *
     * @param cardCdkConfigDetailId the value for suixiang_card_cdk.card_cdk_config_detail_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setCardCdkConfigDetailId(Long cardCdkConfigDetailId) {
        this.cardCdkConfigDetailId = cardCdkConfigDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.card_base_id
     *
     * @return the value of suixiang_card_cdk.card_base_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Long getCardBaseId() {
        return cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.card_base_id
     *
     * @param cardBaseId the value for suixiang_card_cdk.card_base_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setCardBaseId(Long cardBaseId) {
        this.cardBaseId = cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.card_rent_id
     *
     * @return the value of suixiang_card_cdk.card_rent_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Long getCardRentId() {
        return cardRentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.card_rent_id
     *
     * @param cardRentId the value for suixiang_card_cdk.card_rent_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setCardRentId(Long cardRentId) {
        this.cardRentId = cardRentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.card_price_id
     *
     * @return the value of suixiang_card_cdk.card_price_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Long getCardPriceId() {
        return cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.card_price_id
     *
     * @param cardPriceId the value for suixiang_card_cdk.card_price_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setCardPriceId(Long cardPriceId) {
        this.cardPriceId = cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.cdkey
     *
     * @return the value of suixiang_card_cdk.cdkey
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public String getCdkey() {
        return cdkey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.cdkey
     *
     * @param cdkey the value for suixiang_card_cdk.cdkey
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setCdkey(String cdkey) {
        this.cdkey = cdkey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.is_activated
     *
     * @return the value of suixiang_card_cdk.is_activated
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Integer getIsActivated() {
        return isActivated;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.is_activated
     *
     * @param isActivated the value for suixiang_card_cdk.is_activated
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setIsActivated(Integer isActivated) {
        this.isActivated = isActivated;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.activated_mid
     *
     * @return the value of suixiang_card_cdk.activated_mid
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public String getActivatedMid() {
        return activatedMid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.activated_mid
     *
     * @param activatedMid the value for suixiang_card_cdk.activated_mid
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setActivatedMid(String activatedMid) {
        this.activatedMid = activatedMid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.activated_user_name
     *
     * @return the value of suixiang_card_cdk.activated_user_name
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public String getActivatedUserName() {
        return activatedUserName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.activated_user_name
     *
     * @param activatedUserName the value for suixiang_card_cdk.activated_user_name
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setActivatedUserName(String activatedUserName) {
        this.activatedUserName = activatedUserName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.activated_user_mobile
     *
     * @return the value of suixiang_card_cdk.activated_user_mobile
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public String getActivatedUserMobile() {
        return activatedUserMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.activated_user_mobile
     *
     * @param activatedUserMobile the value for suixiang_card_cdk.activated_user_mobile
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setActivatedUserMobile(String activatedUserMobile) {
        this.activatedUserMobile = activatedUserMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.activated_time
     *
     * @return the value of suixiang_card_cdk.activated_time
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Date getActivatedTime() {
        return activatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.activated_time
     *
     * @param activatedTime the value for suixiang_card_cdk.activated_time
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setActivatedTime(Date activatedTime) {
        this.activatedTime = activatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.card_use_id
     *
     * @return the value of suixiang_card_cdk.card_use_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Long getCardUseId() {
        return cardUseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.card_use_id
     *
     * @param cardUseId the value for suixiang_card_cdk.card_use_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setCardUseId(Long cardUseId) {
        this.cardUseId = cardUseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.wechat_cdk_qr_url
     *
     * @return the value of suixiang_card_cdk.wechat_cdk_qr_url
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public String getWechatCdkQrUrl() {
        return wechatCdkQrUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.wechat_cdk_qr_url
     *
     * @param wechatCdkQrUrl the value for suixiang_card_cdk.wechat_cdk_qr_url
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setWechatCdkQrUrl(String wechatCdkQrUrl) {
        this.wechatCdkQrUrl = wechatCdkQrUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.create_time
     *
     * @return the value of suixiang_card_cdk.create_time
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.create_time
     *
     * @param createTime the value for suixiang_card_cdk.create_time
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.create_oper_id
     *
     * @return the value of suixiang_card_cdk.create_oper_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.create_oper_id
     *
     * @param createOperId the value for suixiang_card_cdk.create_oper_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.create_oper_name
     *
     * @return the value of suixiang_card_cdk.create_oper_name
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.create_oper_name
     *
     * @param createOperName the value for suixiang_card_cdk.create_oper_name
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.update_time
     *
     * @return the value of suixiang_card_cdk.update_time
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.update_time
     *
     * @param updateTime the value for suixiang_card_cdk.update_time
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.update_oper_id
     *
     * @return the value of suixiang_card_cdk.update_oper_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_cdk.update_oper_id
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.update_oper_name
     *
     * @return the value of suixiang_card_cdk.update_oper_name
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_cdk.update_oper_name
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk.is_deleted
     *
     * @return the value of suixiang_card_cdk.is_deleted
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk.is_deleted
     *
     * @param isDeleted the value for suixiang_card_cdk.is_deleted
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}