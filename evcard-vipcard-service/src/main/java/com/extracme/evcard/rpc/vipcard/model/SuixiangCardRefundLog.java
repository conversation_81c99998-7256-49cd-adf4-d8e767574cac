package com.extracme.evcard.rpc.vipcard.model;

import java.math.BigDecimal;
import java.util.Date;

public class SuixiangCardRefundLog {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.card_base_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Long cardBaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.card_use_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Long cardUseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.user_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Long userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.mobile_phone
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private String mobilePhone;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.username
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private String username;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.order_seq
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private String orderSeq;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.refund_amount
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private BigDecimal refundAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.refund_time
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Date refundTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.type
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Integer type;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.success
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Integer success;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.create_time
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.create_oper_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.create_oper_name
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.update_time
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.update_oper_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.update_oper_name
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.remark
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_refund_log.is_deleted
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    private Integer isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.id
     *
     * @return the value of suixiang_card_refund_log.id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.id
     *
     * @param id the value for suixiang_card_refund_log.id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.card_base_id
     *
     * @return the value of suixiang_card_refund_log.card_base_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Long getCardBaseId() {
        return cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.card_base_id
     *
     * @param cardBaseId the value for suixiang_card_refund_log.card_base_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setCardBaseId(Long cardBaseId) {
        this.cardBaseId = cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.card_use_id
     *
     * @return the value of suixiang_card_refund_log.card_use_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Long getCardUseId() {
        return cardUseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.card_use_id
     *
     * @param cardUseId the value for suixiang_card_refund_log.card_use_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setCardUseId(Long cardUseId) {
        this.cardUseId = cardUseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.user_id
     *
     * @return the value of suixiang_card_refund_log.user_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.user_id
     *
     * @param userId the value for suixiang_card_refund_log.user_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.mobile_phone
     *
     * @return the value of suixiang_card_refund_log.mobile_phone
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public String getMobilePhone() {
        return mobilePhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.mobile_phone
     *
     * @param mobilePhone the value for suixiang_card_refund_log.mobile_phone
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.username
     *
     * @return the value of suixiang_card_refund_log.username
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public String getUsername() {
        return username;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.username
     *
     * @param username the value for suixiang_card_refund_log.username
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.order_seq
     *
     * @return the value of suixiang_card_refund_log.order_seq
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public String getOrderSeq() {
        return orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.order_seq
     *
     * @param orderSeq the value for suixiang_card_refund_log.order_seq
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.refund_amount
     *
     * @return the value of suixiang_card_refund_log.refund_amount
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.refund_amount
     *
     * @param refundAmount the value for suixiang_card_refund_log.refund_amount
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.refund_time
     *
     * @return the value of suixiang_card_refund_log.refund_time
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Date getRefundTime() {
        return refundTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.refund_time
     *
     * @param refundTime the value for suixiang_card_refund_log.refund_time
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setRefundTime(Date refundTime) {
        this.refundTime = refundTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.type
     *
     * @return the value of suixiang_card_refund_log.type
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.type
     *
     * @param type the value for suixiang_card_refund_log.type
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.success
     *
     * @return the value of suixiang_card_refund_log.success
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Integer getSuccess() {
        return success;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.success
     *
     * @param success the value for suixiang_card_refund_log.success
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setSuccess(Integer success) {
        this.success = success;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.create_time
     *
     * @return the value of suixiang_card_refund_log.create_time
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.create_time
     *
     * @param createTime the value for suixiang_card_refund_log.create_time
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.create_oper_id
     *
     * @return the value of suixiang_card_refund_log.create_oper_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.create_oper_id
     *
     * @param createOperId the value for suixiang_card_refund_log.create_oper_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.create_oper_name
     *
     * @return the value of suixiang_card_refund_log.create_oper_name
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.create_oper_name
     *
     * @param createOperName the value for suixiang_card_refund_log.create_oper_name
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.update_time
     *
     * @return the value of suixiang_card_refund_log.update_time
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.update_time
     *
     * @param updateTime the value for suixiang_card_refund_log.update_time
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.update_oper_id
     *
     * @return the value of suixiang_card_refund_log.update_oper_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_refund_log.update_oper_id
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.update_oper_name
     *
     * @return the value of suixiang_card_refund_log.update_oper_name
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_refund_log.update_oper_name
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.remark
     *
     * @return the value of suixiang_card_refund_log.remark
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.remark
     *
     * @param remark the value for suixiang_card_refund_log.remark
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_refund_log.is_deleted
     *
     * @return the value of suixiang_card_refund_log.is_deleted
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_refund_log.is_deleted
     *
     * @param isDeleted the value for suixiang_card_refund_log.is_deleted
     *
     * @mbggenerated Wed Jul 17 14:38:43 CST 2024
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}