package com.extracme.evcard.rpc.vipcard.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardPurchaseRecordMapper;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecord;
import com.extracme.evcard.rpc.vipcard.service.ISuiXiangCardTradeService;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 购卡订单自动取消job
 * 作为延时消息的补偿方案
 *
 * <AUTHOR>
 * @date 2023/2/7
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "vipcard-rpc-suiXiangCardPurchaseOrderCancelJob",
        cron = "0 0/10 * * * ?", description = "随享卡购卡订单自动取消补偿任务", overwrite = true)
public class SuiXiangCardPurchaseOrderCancelJob implements SimpleJob {

    @Resource
    private ISuiXiangCardTradeService suiXiangCardTradeService;

    @Resource
    private SuixiangCardPurchaseRecordMapper suixiangCardPurchaseRecordMapper;

    private static int MAX_QUERY_LIMIT = 1000;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("随享卡购卡订单自动取消补偿任务begin");
        /**
         * 每10分钟执行，[30, 8]分钟内容的未完成的订单
         */
        Date now = new Date();
        Date startDate = DateUtil.addMin(now, -30);
        Date endDate = DateUtil.addMin(now, -8);

        Long minId = 1L;
        while (true) {
            List<SuixiangCardPurchaseRecord> list = suixiangCardPurchaseRecordMapper.selectTimeoutUnpayOrder(minId, MAX_QUERY_LIMIT, startDate, endDate);
            if (CollectionUtils.isEmpty(list)) {
                log.info("随享卡购卡订单自动取消补偿任务！数据查询完毕，跳出循环");
                break;
            }
            log.info("随享卡购卡订单自动取消补偿任务！本分页查询完成，minId={}，size={}", minId, list.size());
            for (SuixiangCardPurchaseRecord record : list) {
                try {
                    log.info("随享卡购卡订单自动取消补偿任务，单笔发起取消！id[{}]", record.getId());
                    OperatorDto operator = new OperatorDto(Constants.OPER_ID_SYSTEM, Constants.OPER_NAME_SYSTEM, "vipcard");
                    suiXiangCardTradeService.cancelOrder(record.getId(), operator);
                } catch (Exception e) {
                    log.error("随享卡购卡订单自动取消补偿任务，单笔失败！id[{}]", record.getId(), e);
                    continue;
                }
            }
            minId = list.get(list.size() - 1).getId() + 1;
        }
        log.info("随享卡购卡订单自动取消补偿任务begin");
    }

}
