package com.extracme.evcard.rpc.vipcard.model;

import java.util.Date;

public class MmpCardConfigOperationLog {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.config_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Long configId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.config_type
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Integer configType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.operate_type
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Integer operateType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.operation_content
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private String operationContent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.misc_desc
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.status
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.create_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.update_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_config_operation_log.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.id
     *
     * @return the value of mmp_card_config_operation_log.id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.id
     *
     * @param id the value for mmp_card_config_operation_log.id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.config_id
     *
     * @return the value of mmp_card_config_operation_log.config_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Long getConfigId() {
        return configId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.config_id
     *
     * @param configId the value for mmp_card_config_operation_log.config_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.config_type
     *
     * @return the value of mmp_card_config_operation_log.config_type
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Integer getConfigType() {
        return configType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.config_type
     *
     * @param configType the value for mmp_card_config_operation_log.config_type
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setConfigType(Integer configType) {
        this.configType = configType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.operate_type
     *
     * @return the value of mmp_card_config_operation_log.operate_type
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Integer getOperateType() {
        return operateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.operate_type
     *
     * @param operateType the value for mmp_card_config_operation_log.operate_type
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.operation_content
     *
     * @return the value of mmp_card_config_operation_log.operation_content
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public String getOperationContent() {
        return operationContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.operation_content
     *
     * @param operationContent the value for mmp_card_config_operation_log.operation_content
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setOperationContent(String operationContent) {
        this.operationContent = operationContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.misc_desc
     *
     * @return the value of mmp_card_config_operation_log.misc_desc
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.misc_desc
     *
     * @param miscDesc the value for mmp_card_config_operation_log.misc_desc
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.status
     *
     * @return the value of mmp_card_config_operation_log.status
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.status
     *
     * @param status the value for mmp_card_config_operation_log.status
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.create_time
     *
     * @return the value of mmp_card_config_operation_log.create_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.create_time
     *
     * @param createTime the value for mmp_card_config_operation_log.create_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.create_oper_id
     *
     * @return the value of mmp_card_config_operation_log.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.create_oper_id
     *
     * @param createOperId the value for mmp_card_config_operation_log.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.create_oper_name
     *
     * @return the value of mmp_card_config_operation_log.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.create_oper_name
     *
     * @param createOperName the value for mmp_card_config_operation_log.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.update_time
     *
     * @return the value of mmp_card_config_operation_log.update_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.update_time
     *
     * @param updateTime the value for mmp_card_config_operation_log.update_time
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.update_oper_id
     *
     * @return the value of mmp_card_config_operation_log.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.update_oper_id
     *
     * @param updateOperId the value for mmp_card_config_operation_log.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_config_operation_log.update_oper_name
     *
     * @return the value of mmp_card_config_operation_log.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_config_operation_log.update_oper_name
     *
     * @param updateOperName the value for mmp_card_config_operation_log.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}