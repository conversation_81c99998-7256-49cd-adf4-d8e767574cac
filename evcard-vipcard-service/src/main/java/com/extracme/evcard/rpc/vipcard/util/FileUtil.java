package com.extracme.evcard.rpc.vipcard.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.extracme.evcard.rpc.vipcard.config.OssConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;

/**
 * 文件工具类
 *
 * <AUTHOR>
 * @date 2023/1/16
 */
@Slf4j
public class FileUtil {
    public final static String OSS_BUCKET = OssConfigUtil.getOssBucket();
    public final static String OSS_SENSITIVE_BUCKET = OssConfigUtil.getOssSensitiveBucket();
    private final static String OSS_ENDPOINT = OssConfigUtil.getOssEndpoint();
    private final static String ALI_ACCESSID = OssConfigUtil.getAliAccessId();
    private final static String ALI_ACCESSKEY = OssConfigUtil.getAliAccessKey();
    public final static String ENV = OssConfigUtil.getENV();
    public final static String WEB_URL = OssConfigUtil.getFileBaseUrl();

    /**
     * 上传  同步上传
     *
     * @param sourceFile 文件
     * @param pathUrl    存放路径地址
     * <AUTHOR>
     */
    public static String uploadStreamSyn(final InputStream sourceFile, final String pathUrl) {
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
            ossClient.putObject(OSS_BUCKET, ENV + pathUrl, sourceFile);
            IOUtils.closeQuietly(sourceFile);
            return WEB_URL + pathUrl;
        } catch (Exception e) {
            log.error("uploadStreamSyn失败，pathUrl={}", pathUrl, e);
            return null;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 根据文件路径转化为文件流 ，上传到OSS
     *
     * @param filePath   文件本地地址
     * @param objectName 对象名称
     */
    public static String uploadSynByFilePath(final String filePath, String objectName) {
        HttpURLConnection conn = null;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(filePath);
            return uploadStreamSyn(fis, objectName);
        } catch (Exception e) {
            log.error("uploadSynByFileUrl失败，filePath={}", filePath, e);
            return null;
        } finally {
            IOUtils.closeQuietly(fis);
            IOUtils.close(conn);
        }
    }

    /**
     * 从url下载到本地localFile
     *
     * @param urlString
     * @param localFile
     * @return
     */
    public static boolean downloadFromUrl(String urlString, File localFile) {
        InputStream is = null;
        FileOutputStream os = null;
        try {
            URL url = new URL(urlString);
            URLConnection con = url.openConnection();
            is = con.getInputStream();
            os = new FileOutputStream(localFile, true);
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
            return true;
        } catch (Exception e) {
            log.error("downloadFromUrl error!", e);
            return false;
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error("close os error!", e);
                }
            }
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("close is error!", e);
                }
            }
        }
    }

    /**
     * 删除临时文件
     *
     * @param file
     */
    public static void deleteTmpFile(File file) {
        boolean result = FileUtils.deleteQuietly(file);
        log.info("删除文件！filePath[{}],result[{}]", file.getAbsolutePath(), result);
    }

    public static OSSObject downloadStream(String key) {
		OSS ossClient = new OSSClientBuilder().build(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
		OSSObject object = ossClient.getObject(OSS_BUCKET, key);
		return object;
	}
}
