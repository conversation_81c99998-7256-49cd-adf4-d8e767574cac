package com.extracme.evcard.rpc.vipcard.model;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SuixiangCardCdkThirdSaleDto {

    private Long cdkConfigId;
    private Long cdkConfigDetailId;
    private Long cardRentId;
    private Long cardBaseId;
    private Long cardPriceId;

    private int cdkPurpose; // issueType=3时，才生效。用途 1=活动赠送，2=第三方售卖

    private BigDecimal thirdSalesPrice; // 兑换码用途为第三方售卖时的售价，单位：元

    // 租期天数
    private int rentDays;

    // 可用车型
    private String carModelIds;
}
