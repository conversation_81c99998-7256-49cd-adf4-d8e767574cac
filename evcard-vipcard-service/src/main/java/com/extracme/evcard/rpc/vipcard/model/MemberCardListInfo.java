package com.extracme.evcard.rpc.vipcard.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 会员卡片信息
 * <AUTHOR>
 * @Discription
 * @date 2020/12/28
 */
@Data
public class MemberCardListInfo extends MmpUserCardInfo {
    /**
     * 付费会员卡：：卡片类型 0周卡 1月卡 季卡
     */
    private Integer cardType;

    /**
     * 付费会员卡：：享有折扣，1~99整数
     */
    private Integer discount;

    /**
     * 付费会员卡：：单笔订单最大抵扣金额，单位：元
     */
    private BigDecimal maxValue;

    /**
     *付费会员卡：：订单时长限制，单位分钟
     */
    private Integer durationLimit;

    /**
     * 付费会员卡：：卡片配置信息-可用区域限制
     */
    private String cityLimit;

    /**
     * 付费会员卡：：卡片配置信息-车型限制
     */
    private String vehicleModelLimit;

    /**
     * 付费会员卡：：卡片配置信息-商品车型限制
     */
    private String goodsModelId;

    /**
     * 付费会员卡：：门店ids限制
     */
    private String storeIds;

    /**
     * 付费会员卡：卡片配置信息-产品线限制
     */
    private String rentMethod;

    /**
     * 付费会员卡：租车模式大类
     */
    private String rentMethodGroup;

    /**
     * 购卡条件： 0 无  1 学生卡
     * @since 1.1.0
     */
    private Integer purchaseType;
}
