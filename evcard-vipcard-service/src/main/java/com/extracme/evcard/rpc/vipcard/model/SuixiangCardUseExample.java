package com.extracme.evcard.rpc.vipcard.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SuixiangCardUseExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public SuixiangCardUseExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdIsNull() {
            addCriterion("card_base_id is null");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdIsNotNull() {
            addCriterion("card_base_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdEqualTo(Long value) {
            addCriterion("card_base_id =", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdNotEqualTo(Long value) {
            addCriterion("card_base_id <>", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdGreaterThan(Long value) {
            addCriterion("card_base_id >", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_base_id >=", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdLessThan(Long value) {
            addCriterion("card_base_id <", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("card_base_id <=", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdIn(List<Long> values) {
            addCriterion("card_base_id in", values, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdNotIn(List<Long> values) {
            addCriterion("card_base_id not in", values, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdBetween(Long value1, Long value2) {
            addCriterion("card_base_id between", value1, value2, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("card_base_id not between", value1, value2, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIsNull() {
            addCriterion("card_price_id is null");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIsNotNull() {
            addCriterion("card_price_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdEqualTo(Long value) {
            addCriterion("card_price_id =", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotEqualTo(Long value) {
            addCriterion("card_price_id <>", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdGreaterThan(Long value) {
            addCriterion("card_price_id >", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_price_id >=", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdLessThan(Long value) {
            addCriterion("card_price_id <", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdLessThanOrEqualTo(Long value) {
            addCriterion("card_price_id <=", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIn(List<Long> values) {
            addCriterion("card_price_id in", values, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotIn(List<Long> values) {
            addCriterion("card_price_id not in", values, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdBetween(Long value1, Long value2) {
            addCriterion("card_price_id between", value1, value2, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotBetween(Long value1, Long value2) {
            addCriterion("card_price_id not between", value1, value2, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdIsNull() {
            addCriterion("purchase_id is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdIsNotNull() {
            addCriterion("purchase_id is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdEqualTo(Long value) {
            addCriterion("purchase_id =", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdNotEqualTo(Long value) {
            addCriterion("purchase_id <>", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdGreaterThan(Long value) {
            addCriterion("purchase_id >", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("purchase_id >=", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdLessThan(Long value) {
            addCriterion("purchase_id <", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdLessThanOrEqualTo(Long value) {
            addCriterion("purchase_id <=", value, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdIn(List<Long> values) {
            addCriterion("purchase_id in", values, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdNotIn(List<Long> values) {
            addCriterion("purchase_id not in", values, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdBetween(Long value1, Long value2) {
            addCriterion("purchase_id between", value1, value2, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andPurchaseIdNotBetween(Long value1, Long value2) {
            addCriterion("purchase_id not between", value1, value2, "purchaseId");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNull() {
            addCriterion("card_type is null");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNotNull() {
            addCriterion("card_type is not null");
            return (Criteria) this;
        }

        public Criteria andCardTypeEqualTo(Integer value) {
            addCriterion("card_type =", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotEqualTo(Integer value) {
            addCriterion("card_type <>", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThan(Integer value) {
            addCriterion("card_type >", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("card_type >=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThan(Integer value) {
            addCriterion("card_type <", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanOrEqualTo(Integer value) {
            addCriterion("card_type <=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeIn(List<Integer> values) {
            addCriterion("card_type in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotIn(List<Integer> values) {
            addCriterion("card_type not in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeBetween(Integer value1, Integer value2) {
            addCriterion("card_type between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("card_type not between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardNameIsNull() {
            addCriterion("card_name is null");
            return (Criteria) this;
        }

        public Criteria andCardNameIsNotNull() {
            addCriterion("card_name is not null");
            return (Criteria) this;
        }

        public Criteria andCardNameEqualTo(String value) {
            addCriterion("card_name =", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotEqualTo(String value) {
            addCriterion("card_name <>", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameGreaterThan(String value) {
            addCriterion("card_name >", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameGreaterThanOrEqualTo(String value) {
            addCriterion("card_name >=", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameLessThan(String value) {
            addCriterion("card_name <", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameLessThanOrEqualTo(String value) {
            addCriterion("card_name <=", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameLike(String value) {
            addCriterion("card_name like", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotLike(String value) {
            addCriterion("card_name not like", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameIn(List<String> values) {
            addCriterion("card_name in", values, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotIn(List<String> values) {
            addCriterion("card_name not in", values, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameBetween(String value1, String value2) {
            addCriterion("card_name between", value1, value2, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotBetween(String value1, String value2) {
            addCriterion("card_name not between", value1, value2, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardStatusIsNull() {
            addCriterion("card_status is null");
            return (Criteria) this;
        }

        public Criteria andCardStatusIsNotNull() {
            addCriterion("card_status is not null");
            return (Criteria) this;
        }

        public Criteria andCardStatusEqualTo(Integer value) {
            addCriterion("card_status =", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusNotEqualTo(Integer value) {
            addCriterion("card_status <>", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusGreaterThan(Integer value) {
            addCriterion("card_status >", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("card_status >=", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusLessThan(Integer value) {
            addCriterion("card_status <", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusLessThanOrEqualTo(Integer value) {
            addCriterion("card_status <=", value, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusIn(List<Integer> values) {
            addCriterion("card_status in", values, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusNotIn(List<Integer> values) {
            addCriterion("card_status not in", values, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusBetween(Integer value1, Integer value2) {
            addCriterion("card_status between", value1, value2, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andCardStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("card_status not between", value1, value2, "cardStatus");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeIsNull() {
            addCriterion("expires_time is null");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeIsNotNull() {
            addCriterion("expires_time is not null");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeEqualTo(Date value) {
            addCriterion("expires_time =", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeNotEqualTo(Date value) {
            addCriterion("expires_time <>", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeGreaterThan(Date value) {
            addCriterion("expires_time >", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("expires_time >=", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeLessThan(Date value) {
            addCriterion("expires_time <", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeLessThanOrEqualTo(Date value) {
            addCriterion("expires_time <=", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeIn(List<Date> values) {
            addCriterion("expires_time in", values, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeNotIn(List<Date> values) {
            addCriterion("expires_time not in", values, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeBetween(Date value1, Date value2) {
            addCriterion("expires_time between", value1, value2, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeNotBetween(Date value1, Date value2) {
            addCriterion("expires_time not between", value1, value2, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andTotalOrderIsNull() {
            addCriterion("total_order is null");
            return (Criteria) this;
        }

        public Criteria andTotalOrderIsNotNull() {
            addCriterion("total_order is not null");
            return (Criteria) this;
        }

        public Criteria andTotalOrderEqualTo(Long value) {
            addCriterion("total_order =", value, "totalOrder");
            return (Criteria) this;
        }

        public Criteria andTotalOrderNotEqualTo(Long value) {
            addCriterion("total_order <>", value, "totalOrder");
            return (Criteria) this;
        }

        public Criteria andTotalOrderGreaterThan(Long value) {
            addCriterion("total_order >", value, "totalOrder");
            return (Criteria) this;
        }

        public Criteria andTotalOrderGreaterThanOrEqualTo(Long value) {
            addCriterion("total_order >=", value, "totalOrder");
            return (Criteria) this;
        }

        public Criteria andTotalOrderLessThan(Long value) {
            addCriterion("total_order <", value, "totalOrder");
            return (Criteria) this;
        }

        public Criteria andTotalOrderLessThanOrEqualTo(Long value) {
            addCriterion("total_order <=", value, "totalOrder");
            return (Criteria) this;
        }

        public Criteria andTotalOrderIn(List<Long> values) {
            addCriterion("total_order in", values, "totalOrder");
            return (Criteria) this;
        }

        public Criteria andTotalOrderNotIn(List<Long> values) {
            addCriterion("total_order not in", values, "totalOrder");
            return (Criteria) this;
        }

        public Criteria andTotalOrderBetween(Long value1, Long value2) {
            addCriterion("total_order between", value1, value2, "totalOrder");
            return (Criteria) this;
        }

        public Criteria andTotalOrderNotBetween(Long value1, Long value2) {
            addCriterion("total_order not between", value1, value2, "totalOrder");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountIsNull() {
            addCriterion("total_discount_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountIsNotNull() {
            addCriterion("total_discount_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountEqualTo(BigDecimal value) {
            addCriterion("total_discount_amount =", value, "totalDiscountAmount");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountNotEqualTo(BigDecimal value) {
            addCriterion("total_discount_amount <>", value, "totalDiscountAmount");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountGreaterThan(BigDecimal value) {
            addCriterion("total_discount_amount >", value, "totalDiscountAmount");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_discount_amount >=", value, "totalDiscountAmount");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountLessThan(BigDecimal value) {
            addCriterion("total_discount_amount <", value, "totalDiscountAmount");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_discount_amount <=", value, "totalDiscountAmount");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountIn(List<BigDecimal> values) {
            addCriterion("total_discount_amount in", values, "totalDiscountAmount");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountNotIn(List<BigDecimal> values) {
            addCriterion("total_discount_amount not in", values, "totalDiscountAmount");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_discount_amount between", value1, value2, "totalDiscountAmount");
            return (Criteria) this;
        }

        public Criteria andTotalDiscountAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_discount_amount not between", value1, value2, "totalDiscountAmount");
            return (Criteria) this;
        }

        public Criteria andInitDaysIsNull() {
            addCriterion("init_days is null");
            return (Criteria) this;
        }

        public Criteria andInitDaysIsNotNull() {
            addCriterion("init_days is not null");
            return (Criteria) this;
        }

        public Criteria andInitDaysEqualTo(Integer value) {
            addCriterion("init_days =", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysNotEqualTo(Integer value) {
            addCriterion("init_days <>", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysGreaterThan(Integer value) {
            addCriterion("init_days >", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("init_days >=", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysLessThan(Integer value) {
            addCriterion("init_days <", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysLessThanOrEqualTo(Integer value) {
            addCriterion("init_days <=", value, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysIn(List<Integer> values) {
            addCriterion("init_days in", values, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysNotIn(List<Integer> values) {
            addCriterion("init_days not in", values, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysBetween(Integer value1, Integer value2) {
            addCriterion("init_days between", value1, value2, "initDays");
            return (Criteria) this;
        }

        public Criteria andInitDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("init_days not between", value1, value2, "initDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysIsNull() {
            addCriterion("available_days is null");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysIsNotNull() {
            addCriterion("available_days is not null");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysEqualTo(Integer value) {
            addCriterion("available_days =", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysNotEqualTo(Integer value) {
            addCriterion("available_days <>", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysGreaterThan(Integer value) {
            addCriterion("available_days >", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("available_days >=", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysLessThan(Integer value) {
            addCriterion("available_days <", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysLessThanOrEqualTo(Integer value) {
            addCriterion("available_days <=", value, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysIn(List<Integer> values) {
            addCriterion("available_days in", values, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysNotIn(List<Integer> values) {
            addCriterion("available_days not in", values, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysBetween(Integer value1, Integer value2) {
            addCriterion("available_days between", value1, value2, "availableDays");
            return (Criteria) this;
        }

        public Criteria andAvailableDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("available_days not between", value1, value2, "availableDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysIsNull() {
            addCriterion("used_days is null");
            return (Criteria) this;
        }

        public Criteria andUsedDaysIsNotNull() {
            addCriterion("used_days is not null");
            return (Criteria) this;
        }

        public Criteria andUsedDaysEqualTo(Integer value) {
            addCriterion("used_days =", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysNotEqualTo(Integer value) {
            addCriterion("used_days <>", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysGreaterThan(Integer value) {
            addCriterion("used_days >", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("used_days >=", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysLessThan(Integer value) {
            addCriterion("used_days <", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysLessThanOrEqualTo(Integer value) {
            addCriterion("used_days <=", value, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysIn(List<Integer> values) {
            addCriterion("used_days in", values, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysNotIn(List<Integer> values) {
            addCriterion("used_days not in", values, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysBetween(Integer value1, Integer value2) {
            addCriterion("used_days between", value1, value2, "usedDays");
            return (Criteria) this;
        }

        public Criteria andUsedDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("used_days not between", value1, value2, "usedDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysIsNull() {
            addCriterion("frozen_days is null");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysIsNotNull() {
            addCriterion("frozen_days is not null");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysEqualTo(Integer value) {
            addCriterion("frozen_days =", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysNotEqualTo(Integer value) {
            addCriterion("frozen_days <>", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysGreaterThan(Integer value) {
            addCriterion("frozen_days >", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("frozen_days >=", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysLessThan(Integer value) {
            addCriterion("frozen_days <", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysLessThanOrEqualTo(Integer value) {
            addCriterion("frozen_days <=", value, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysIn(List<Integer> values) {
            addCriterion("frozen_days in", values, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysNotIn(List<Integer> values) {
            addCriterion("frozen_days not in", values, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysBetween(Integer value1, Integer value2) {
            addCriterion("frozen_days between", value1, value2, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andFrozenDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("frozen_days not between", value1, value2, "frozenDays");
            return (Criteria) this;
        }

        public Criteria andActiveFlagIsNull() {
            addCriterion("active_flag is null");
            return (Criteria) this;
        }

        public Criteria andActiveFlagIsNotNull() {
            addCriterion("active_flag is not null");
            return (Criteria) this;
        }

        public Criteria andActiveFlagEqualTo(Integer value) {
            addCriterion("active_flag =", value, "activeFlag");
            return (Criteria) this;
        }

        public Criteria andActiveFlagNotEqualTo(Integer value) {
            addCriterion("active_flag <>", value, "activeFlag");
            return (Criteria) this;
        }

        public Criteria andActiveFlagGreaterThan(Integer value) {
            addCriterion("active_flag >", value, "activeFlag");
            return (Criteria) this;
        }

        public Criteria andActiveFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("active_flag >=", value, "activeFlag");
            return (Criteria) this;
        }

        public Criteria andActiveFlagLessThan(Integer value) {
            addCriterion("active_flag <", value, "activeFlag");
            return (Criteria) this;
        }

        public Criteria andActiveFlagLessThanOrEqualTo(Integer value) {
            addCriterion("active_flag <=", value, "activeFlag");
            return (Criteria) this;
        }

        public Criteria andActiveFlagIn(List<Integer> values) {
            addCriterion("active_flag in", values, "activeFlag");
            return (Criteria) this;
        }

        public Criteria andActiveFlagNotIn(List<Integer> values) {
            addCriterion("active_flag not in", values, "activeFlag");
            return (Criteria) this;
        }

        public Criteria andActiveFlagBetween(Integer value1, Integer value2) {
            addCriterion("active_flag between", value1, value2, "activeFlag");
            return (Criteria) this;
        }

        public Criteria andActiveFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("active_flag not between", value1, value2, "activeFlag");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNull() {
            addCriterion("create_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNotNull() {
            addCriterion("create_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdEqualTo(Long value) {
            addCriterion("create_oper_id =", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotEqualTo(Long value) {
            addCriterion("create_oper_id <>", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThan(Long value) {
            addCriterion("create_oper_id >", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_oper_id >=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThan(Long value) {
            addCriterion("create_oper_id <", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("create_oper_id <=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIn(List<Long> values) {
            addCriterion("create_oper_id in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotIn(List<Long> values) {
            addCriterion("create_oper_id not in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdBetween(Long value1, Long value2) {
            addCriterion("create_oper_id between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("create_oper_id not between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNull() {
            addCriterion("create_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNotNull() {
            addCriterion("create_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameEqualTo(String value) {
            addCriterion("create_oper_name =", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotEqualTo(String value) {
            addCriterion("create_oper_name <>", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThan(String value) {
            addCriterion("create_oper_name >", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_name >=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThan(String value) {
            addCriterion("create_oper_name <", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThanOrEqualTo(String value) {
            addCriterion("create_oper_name <=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLike(String value) {
            addCriterion("create_oper_name like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotLike(String value) {
            addCriterion("create_oper_name not like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIn(List<String> values) {
            addCriterion("create_oper_name in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotIn(List<String> values) {
            addCriterion("create_oper_name not in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameBetween(String value1, String value2) {
            addCriterion("create_oper_name between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotBetween(String value1, String value2) {
            addCriterion("create_oper_name not between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNull() {
            addCriterion("update_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNotNull() {
            addCriterion("update_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdEqualTo(Long value) {
            addCriterion("update_oper_id =", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotEqualTo(Long value) {
            addCriterion("update_oper_id <>", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThan(Long value) {
            addCriterion("update_oper_id >", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_oper_id >=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThan(Long value) {
            addCriterion("update_oper_id <", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("update_oper_id <=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIn(List<Long> values) {
            addCriterion("update_oper_id in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotIn(List<Long> values) {
            addCriterion("update_oper_id not in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdBetween(Long value1, Long value2) {
            addCriterion("update_oper_id between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("update_oper_id not between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNull() {
            addCriterion("update_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNotNull() {
            addCriterion("update_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameEqualTo(String value) {
            addCriterion("update_oper_name =", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotEqualTo(String value) {
            addCriterion("update_oper_name <>", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThan(String value) {
            addCriterion("update_oper_name >", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_oper_name >=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThan(String value) {
            addCriterion("update_oper_name <", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThanOrEqualTo(String value) {
            addCriterion("update_oper_name <=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLike(String value) {
            addCriterion("update_oper_name like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotLike(String value) {
            addCriterion("update_oper_name not like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIn(List<String> values) {
            addCriterion("update_oper_name in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotIn(List<String> values) {
            addCriterion("update_oper_name not in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameBetween(String value1, String value2) {
            addCriterion("update_oper_name between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotBetween(String value1, String value2) {
            addCriterion("update_oper_name not between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_use
     *
     * @mbggenerated do_not_delete_during_merge Mon Jan 30 16:57:10 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_use
     *
     * @mbggenerated Mon Jan 30 16:57:10 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}