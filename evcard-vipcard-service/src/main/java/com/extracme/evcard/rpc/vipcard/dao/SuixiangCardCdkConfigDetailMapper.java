package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigDetail;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigDetailExample;
import java.util.List;

import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkThirdSaleDto;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardCdkConfigDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config_detail
     *
     * @mbggenerated Tue Dec 17 09:43:57 CST 2024
     */
    int countByExample(SuixiangCardCdkConfigDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config_detail
     *
     * @mbggenerated Tue Dec 17 09:43:57 CST 2024
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config_detail
     *
     * @mbggenerated Tue Dec 17 09:43:57 CST 2024
     */
    int insert(SuixiangCardCdkConfigDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config_detail
     *
     * @mbggenerated Tue Dec 17 09:43:57 CST 2024
     */
    int insertSelective(SuixiangCardCdkConfigDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config_detail
     *
     * @mbggenerated Tue Dec 17 09:43:57 CST 2024
     */
    List<SuixiangCardCdkConfigDetail> selectByExample(SuixiangCardCdkConfigDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config_detail
     *
     * @mbggenerated Tue Dec 17 09:43:57 CST 2024
     */
    SuixiangCardCdkConfigDetail selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config_detail
     *
     * @mbggenerated Tue Dec 17 09:43:57 CST 2024
     */
    int updateByExampleSelective(@Param("record") SuixiangCardCdkConfigDetail record, @Param("example") SuixiangCardCdkConfigDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config_detail
     *
     * @mbggenerated Tue Dec 17 09:43:57 CST 2024
     */
    int updateByExample(@Param("record") SuixiangCardCdkConfigDetail record, @Param("example") SuixiangCardCdkConfigDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config_detail
     *
     * @mbggenerated Tue Dec 17 09:43:57 CST 2024
     */
    int updateByPrimaryKeySelective(SuixiangCardCdkConfigDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_config_detail
     *
     * @mbggenerated Tue Dec 17 09:43:57 CST 2024
     */
    int updateByPrimaryKey(SuixiangCardCdkConfigDetail record);

    List<SuixiangCardCdkThirdSaleDto> selectThirdSalesPriceByBaseId(@Param("cardBaseId")Long cardBaseId);
    List<SuixiangCardCdkThirdSaleDto> selectThirdSalesPriceInfoByBaseId(@Param("cardBaseId")Long cardBaseId);
}