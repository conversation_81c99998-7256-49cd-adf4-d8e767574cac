package com.extracme.evcard.rpc.vipcard.model;

import java.util.Date;

public class SuixiangCardCdkConfig {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.card_base_id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private Long cardBaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.act_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private String actName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.card_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private String cardName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.total_quantity
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private Integer totalQuantity;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.is_deleted
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private Integer isDeleted;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.create_time
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.create_oper_id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.create_oper_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.update_time
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.update_oper_id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.update_oper_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.qr_code_zip_url
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private String qrCodeZipUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_config.purpose
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    private Integer purpose;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.id
     *
     * @return the value of suixiang_card_cdk_config.id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.id
     *
     * @param id the value for suixiang_card_cdk_config.id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.card_base_id
     *
     * @return the value of suixiang_card_cdk_config.card_base_id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public Long getCardBaseId() {
        return cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.card_base_id
     *
     * @param cardBaseId the value for suixiang_card_cdk_config.card_base_id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setCardBaseId(Long cardBaseId) {
        this.cardBaseId = cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.act_name
     *
     * @return the value of suixiang_card_cdk_config.act_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public String getActName() {
        return actName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.act_name
     *
     * @param actName the value for suixiang_card_cdk_config.act_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setActName(String actName) {
        this.actName = actName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.card_name
     *
     * @return the value of suixiang_card_cdk_config.card_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public String getCardName() {
        return cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.card_name
     *
     * @param cardName the value for suixiang_card_cdk_config.card_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.total_quantity
     *
     * @return the value of suixiang_card_cdk_config.total_quantity
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.total_quantity
     *
     * @param totalQuantity the value for suixiang_card_cdk_config.total_quantity
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.is_deleted
     *
     * @return the value of suixiang_card_cdk_config.is_deleted
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.is_deleted
     *
     * @param isDeleted the value for suixiang_card_cdk_config.is_deleted
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.create_time
     *
     * @return the value of suixiang_card_cdk_config.create_time
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.create_time
     *
     * @param createTime the value for suixiang_card_cdk_config.create_time
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.create_oper_id
     *
     * @return the value of suixiang_card_cdk_config.create_oper_id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.create_oper_id
     *
     * @param createOperId the value for suixiang_card_cdk_config.create_oper_id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.create_oper_name
     *
     * @return the value of suixiang_card_cdk_config.create_oper_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.create_oper_name
     *
     * @param createOperName the value for suixiang_card_cdk_config.create_oper_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.update_time
     *
     * @return the value of suixiang_card_cdk_config.update_time
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.update_time
     *
     * @param updateTime the value for suixiang_card_cdk_config.update_time
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.update_oper_id
     *
     * @return the value of suixiang_card_cdk_config.update_oper_id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_cdk_config.update_oper_id
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.update_oper_name
     *
     * @return the value of suixiang_card_cdk_config.update_oper_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_cdk_config.update_oper_name
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.qr_code_zip_url
     *
     * @return the value of suixiang_card_cdk_config.qr_code_zip_url
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public String getQrCodeZipUrl() {
        return qrCodeZipUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.qr_code_zip_url
     *
     * @param qrCodeZipUrl the value for suixiang_card_cdk_config.qr_code_zip_url
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setQrCodeZipUrl(String qrCodeZipUrl) {
        this.qrCodeZipUrl = qrCodeZipUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_config.purpose
     *
     * @return the value of suixiang_card_cdk_config.purpose
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public Integer getPurpose() {
        return purpose;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_config.purpose
     *
     * @param purpose the value for suixiang_card_cdk_config.purpose
     *
     * @mbggenerated Tue Dec 17 09:41:11 CST 2024
     */
    public void setPurpose(Integer purpose) {
        this.purpose = purpose;
    }
}