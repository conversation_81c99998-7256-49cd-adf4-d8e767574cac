package com.extracme.evcard.rpc.vipcard.config;

import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.OrderConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.aliyun.openservices.ons.api.order.MessageOrderListener;
import com.extracme.evcard.rpc.vipcard.mq.OrderEventListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Configuration
public class ConsumerClient {

    @Resource
    OrderEventListener orderEventListener;

    @Resource
    private OnsConfiguration mqConfig;

    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public OrderConsumerBean normalConsumer() {
        OrderConsumerBean consumerBean = new OrderConsumerBean();
        // 配置文件
        Properties properties = mqConfig.getMqProperties();
        properties.setProperty(PropertyKeyConst.GROUP_ID, mqConfig.getContractGroupId());
        // 将消费者线程数固定为5个 20为默认值
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "5");
        consumerBean.setProperties(properties);
        // 订阅关系
        Map<Subscription, MessageOrderListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic(mqConfig.getContractNormalTopic());
        String tag = "CARD_REFUND_SUCCESS||CARD_PAY";
        subscription.setExpression(tag);
        subscriptionTable.put(subscription, orderEventListener);

        consumerBean.setSubscriptionTable(subscriptionTable);
        return consumerBean;
    }

}
