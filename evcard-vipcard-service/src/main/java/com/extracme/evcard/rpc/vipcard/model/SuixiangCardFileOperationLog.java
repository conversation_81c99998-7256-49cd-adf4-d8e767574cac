package com.extracme.evcard.rpc.vipcard.model;

import java.util.Date;

public class SuixiangCardFileOperationLog {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.card_base_id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private Long cardBaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.source_file
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private String sourceFile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.target_file
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private String targetFile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.total_cnt
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private Long totalCnt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.success_cnt
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private Long successCnt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.fail_cnt
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private Long failCnt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.create_time
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.create_oper_id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.create_oper_name
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.update_time
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.update_oper_id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.update_oper_name
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_file_operation_log.is_deleted
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    private Integer isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.id
     *
     * @return the value of suixiang_card_file_operation_log.id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.id
     *
     * @param id the value for suixiang_card_file_operation_log.id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.card_base_id
     *
     * @return the value of suixiang_card_file_operation_log.card_base_id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public Long getCardBaseId() {
        return cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.card_base_id
     *
     * @param cardBaseId the value for suixiang_card_file_operation_log.card_base_id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setCardBaseId(Long cardBaseId) {
        this.cardBaseId = cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.source_file
     *
     * @return the value of suixiang_card_file_operation_log.source_file
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public String getSourceFile() {
        return sourceFile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.source_file
     *
     * @param sourceFile the value for suixiang_card_file_operation_log.source_file
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setSourceFile(String sourceFile) {
        this.sourceFile = sourceFile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.target_file
     *
     * @return the value of suixiang_card_file_operation_log.target_file
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public String getTargetFile() {
        return targetFile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.target_file
     *
     * @param targetFile the value for suixiang_card_file_operation_log.target_file
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setTargetFile(String targetFile) {
        this.targetFile = targetFile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.total_cnt
     *
     * @return the value of suixiang_card_file_operation_log.total_cnt
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public Long getTotalCnt() {
        return totalCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.total_cnt
     *
     * @param totalCnt the value for suixiang_card_file_operation_log.total_cnt
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setTotalCnt(Long totalCnt) {
        this.totalCnt = totalCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.success_cnt
     *
     * @return the value of suixiang_card_file_operation_log.success_cnt
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public Long getSuccessCnt() {
        return successCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.success_cnt
     *
     * @param successCnt the value for suixiang_card_file_operation_log.success_cnt
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setSuccessCnt(Long successCnt) {
        this.successCnt = successCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.fail_cnt
     *
     * @return the value of suixiang_card_file_operation_log.fail_cnt
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public Long getFailCnt() {
        return failCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.fail_cnt
     *
     * @param failCnt the value for suixiang_card_file_operation_log.fail_cnt
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setFailCnt(Long failCnt) {
        this.failCnt = failCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.create_time
     *
     * @return the value of suixiang_card_file_operation_log.create_time
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.create_time
     *
     * @param createTime the value for suixiang_card_file_operation_log.create_time
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.create_oper_id
     *
     * @return the value of suixiang_card_file_operation_log.create_oper_id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.create_oper_id
     *
     * @param createOperId the value for suixiang_card_file_operation_log.create_oper_id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.create_oper_name
     *
     * @return the value of suixiang_card_file_operation_log.create_oper_name
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.create_oper_name
     *
     * @param createOperName the value for suixiang_card_file_operation_log.create_oper_name
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.update_time
     *
     * @return the value of suixiang_card_file_operation_log.update_time
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.update_time
     *
     * @param updateTime the value for suixiang_card_file_operation_log.update_time
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.update_oper_id
     *
     * @return the value of suixiang_card_file_operation_log.update_oper_id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_file_operation_log.update_oper_id
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.update_oper_name
     *
     * @return the value of suixiang_card_file_operation_log.update_oper_name
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_file_operation_log.update_oper_name
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_file_operation_log.is_deleted
     *
     * @return the value of suixiang_card_file_operation_log.is_deleted
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_file_operation_log.is_deleted
     *
     * @param isDeleted the value for suixiang_card_file_operation_log.is_deleted
     *
     * @mbggenerated Fri Jun 07 16:58:01 CST 2024
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}