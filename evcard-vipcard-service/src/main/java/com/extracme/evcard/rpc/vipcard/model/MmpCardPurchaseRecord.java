package com.extracme.evcard.rpc.vipcard.model;

import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.vipcard.dto.CardPurchaseRecordDto;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;

import java.util.Date;

public class MmpCardPurchaseRecord {
    private Long id;

    private Long userId;

    private Long cardActivityId;

    private Long cardId;

    private Integer paymentStatus;

    /**
     * 购卡方式： 0 购买  1 赠送 2积分兑换
     */
    private Integer issueType;

    private Integer quantity;

    private Date payTime;

    private Double realAmount;

    private String outTradeSeq;

    private String userCardNo;

    private Date startTime;

    private Date endTime;

    private String orderSeq;

    private Integer status;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    private Date cancelTime;

    /**
     * 合并买卡来源 0单独&后付订单 1预付订单
     */
    private Integer mergePayOrigin;

    public Integer getMergePayOrigin() {
        return mergePayOrigin;
    }

    public void setMergePayOrigin(Integer mergePayOrigin) {
        this.mergePayOrigin = mergePayOrigin;
    }

    public Date getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCardActivityId() {
        return cardActivityId;
    }

    public void setCardActivityId(Long cardActivityId) {
        this.cardActivityId = cardActivityId;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public Integer getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(Integer paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Double getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(Double realAmount) {
        this.realAmount = realAmount;
    }

    public String getOutTradeSeq() {
        return outTradeSeq;
    }

    public void setOutTradeSeq(String outTradeSeq) {
        this.outTradeSeq = outTradeSeq;
    }

    public String getUserCardNo() {
        return userCardNo;
    }

    public void setUserCardNo(String userCardNo) {
        this.userCardNo = userCardNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    public Integer getIssueType() {
        return issueType;
    }

    public void setIssueType(Integer issueType) {
        this.issueType = issueType;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getOrderSeq() {
        return orderSeq;
    }

    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    public static CardPurchaseRecordDto toDto(MmpCardPurchaseRecord record) {
        CardPurchaseRecordDto dto = new CardPurchaseRecordDto();
        try {
            CardPurchaseRecordDto cardPurchaseRecordDto = new CardPurchaseRecordDto();
            BeanCopyUtils.copyProperties(record,cardPurchaseRecordDto);
            cardPurchaseRecordDto.setPurchaseId(record.getId());
            if(record.getPayTime() != null){
                cardPurchaseRecordDto.setPayTime(DateUtil.getFormatDate(record.getPayTime(), DateUtil.DATE_TYPE7));
            }
            if(record.getCancelTime() != null){
                cardPurchaseRecordDto.setCancelTime(DateUtil.getFormatDate(record.getCancelTime(), DateUtil.DATE_TYPE7));
            }
            if (record.getPaymentStatus() == 0) {
                Date createTime = record.getCreateTime();
                Date planCancelTime = DateUtil.addMin(createTime, 5);
                cardPurchaseRecordDto.setPlanCancelTime(DateUtil.getFormatDate(planCancelTime, DateUtil.simple));
            }
        } catch (Exception e) {
        }
        return dto;
    }
}