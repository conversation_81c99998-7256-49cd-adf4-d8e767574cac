package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardInfoDto;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardPrice;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardPriceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardPriceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    int countByExample(SuixiangCardPriceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    int insert(SuixiangCardPrice record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    int insertSelective(SuixiangCardPrice record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    List<SuixiangCardPrice> selectByExample(SuixiangCardPriceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    SuixiangCardPrice selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    int updateByExampleSelective(@Param("record") SuixiangCardPrice record, @Param("example") SuixiangCardPriceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    int updateByExample(@Param("record") SuixiangCardPrice record, @Param("example") SuixiangCardPriceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    int updateByPrimaryKeySelective(SuixiangCardPrice record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    int updateByPrimaryKey(SuixiangCardPrice record);

    SuixiangCardInfoDto selectInfoByPriceId(@Param("priceId")Long priceId);

    List<SuixiangCardInfoDto> selectInfoListByPriceIds(@Param("priceIds")List<Long> priceIds);

    List<SuixiangCardInfoDto> getListByCityIdAndCarModelId(@Param("cityId")int cityId, @Param("carModelId")int carModelId);

    // 根据cardBaseId查询记录，按照 baseid、days、sales_price 排序
    List<SuixiangCardInfoDto> selectInfoByBaseId(@Param("cardBaseId")Long cardBaseId);
}