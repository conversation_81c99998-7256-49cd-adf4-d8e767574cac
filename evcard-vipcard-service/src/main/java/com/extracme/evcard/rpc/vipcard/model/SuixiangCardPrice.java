package com.extracme.evcard.rpc.vipcard.model;

import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuiXiangCardPriceDto;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

public class SuixiangCardPrice {

    /**
     * 转换dto
     * @return
     */
    public SuiXiangCardPriceDto toDto(){
        SuiXiangCardPriceDto suiXiangCardPriceDto = new SuiXiangCardPriceDto();
        BeanUtils.copyProperties(this,suiXiangCardPriceDto);
        return suiXiangCardPriceDto;
    }
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.card_base_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private Long cardBaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.card_rent_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private Long cardRentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.sales_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private BigDecimal salesPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.underline_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private BigDecimal underlinePrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.car_model_group
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private String carModelGroup;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.car_model_ids
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private String carModelIds;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.sales
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private Integer sales;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.create_time
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.update_time
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_price.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    private Integer isDeleted;

    private String landingPagePicUrl; // 非必填，落地页引导图完整路径地址

    private String landingPageHeadPicUrl; // 非必填，落地页头图完整路径地址

    public String getLandingPageHeadPicUrl() {
        return landingPageHeadPicUrl;
    }

    public void setLandingPageHeadPicUrl(String landingPageHeadPicUrl) {
        this.landingPageHeadPicUrl = landingPageHeadPicUrl;
    }

    public String getLandingPagePicUrl() {
        return landingPagePicUrl;
    }

    public void setLandingPagePicUrl(String landingPagePicUrl) {
        this.landingPagePicUrl = landingPagePicUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.id
     *
     * @return the value of suixiang_card_price.id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.id
     *
     * @param id the value for suixiang_card_price.id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.card_base_id
     *
     * @return the value of suixiang_card_price.card_base_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public Long getCardBaseId() {
        return cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.card_base_id
     *
     * @param cardBaseId the value for suixiang_card_price.card_base_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setCardBaseId(Long cardBaseId) {
        this.cardBaseId = cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.card_rent_id
     *
     * @return the value of suixiang_card_price.card_rent_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public Long getCardRentId() {
        return cardRentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.card_rent_id
     *
     * @param cardRentId the value for suixiang_card_price.card_rent_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setCardRentId(Long cardRentId) {
        this.cardRentId = cardRentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.sales_price
     *
     * @return the value of suixiang_card_price.sales_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public BigDecimal getSalesPrice() {
        return salesPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.sales_price
     *
     * @param salesPrice the value for suixiang_card_price.sales_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setSalesPrice(BigDecimal salesPrice) {
        this.salesPrice = salesPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.underline_price
     *
     * @return the value of suixiang_card_price.underline_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public BigDecimal getUnderlinePrice() {
        return underlinePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.underline_price
     *
     * @param underlinePrice the value for suixiang_card_price.underline_price
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setUnderlinePrice(BigDecimal underlinePrice) {
        this.underlinePrice = underlinePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.car_model_group
     *
     * @return the value of suixiang_card_price.car_model_group
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public String getCarModelGroup() {
        return carModelGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.car_model_group
     *
     * @param carModelGroup the value for suixiang_card_price.car_model_group
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setCarModelGroup(String carModelGroup) {
        this.carModelGroup = carModelGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.car_model_ids
     *
     * @return the value of suixiang_card_price.car_model_ids
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public String getCarModelIds() {
        return carModelIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.car_model_ids
     *
     * @param carModelIds the value for suixiang_card_price.car_model_ids
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setCarModelIds(String carModelIds) {
        this.carModelIds = carModelIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.sales
     *
     * @return the value of suixiang_card_price.sales
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public Integer getSales() {
        return sales;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.sales
     *
     * @param sales the value for suixiang_card_price.sales
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setSales(Integer sales) {
        this.sales = sales;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.create_time
     *
     * @return the value of suixiang_card_price.create_time
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.create_time
     *
     * @param createTime the value for suixiang_card_price.create_time
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.create_oper_id
     *
     * @return the value of suixiang_card_price.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.create_oper_id
     *
     * @param createOperId the value for suixiang_card_price.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.create_oper_name
     *
     * @return the value of suixiang_card_price.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.create_oper_name
     *
     * @param createOperName the value for suixiang_card_price.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.update_time
     *
     * @return the value of suixiang_card_price.update_time
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.update_time
     *
     * @param updateTime the value for suixiang_card_price.update_time
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.update_oper_id
     *
     * @return the value of suixiang_card_price.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_price.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.update_oper_name
     *
     * @return the value of suixiang_card_price.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_price.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_price.is_deleted
     *
     * @return the value of suixiang_card_price.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_price.is_deleted
     *
     * @param isDeleted the value for suixiang_card_price.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:33:47 CST 2023
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}