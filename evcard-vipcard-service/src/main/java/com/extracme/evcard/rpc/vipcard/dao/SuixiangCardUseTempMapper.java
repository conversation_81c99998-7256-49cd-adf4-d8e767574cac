package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseTemp;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseTempExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardUseTempMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_temp
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    int countByExample(SuixiangCardUseTempExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_temp
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_temp
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    int insert(SuixiangCardUseTemp record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_temp
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    int insertSelective(SuixiangCardUseTemp record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_temp
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    List<SuixiangCardUseTemp> selectByExample(SuixiangCardUseTempExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_temp
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    SuixiangCardUseTemp selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_temp
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    int updateByExampleSelective(@Param("record") SuixiangCardUseTemp record, @Param("example") SuixiangCardUseTempExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_temp
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    int updateByExample(@Param("record") SuixiangCardUseTemp record, @Param("example") SuixiangCardUseTempExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_temp
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    int updateByPrimaryKeySelective(SuixiangCardUseTemp record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_temp
     *
     * @mbggenerated Wed Nov 20 10:32:49 CST 2024
     */
    int updateByPrimaryKey(SuixiangCardUseTemp record);
}