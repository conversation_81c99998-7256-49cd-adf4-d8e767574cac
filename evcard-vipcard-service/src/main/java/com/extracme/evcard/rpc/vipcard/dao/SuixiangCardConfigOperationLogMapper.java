package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardConfigOperationLog;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardConfigOperationLogExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SuixiangCardConfigOperationLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_config_operation_log
     *
     * @mbggenerated Wed Jan 11 20:31:27 CST 2023
     */
    int countByExample(SuixiangCardConfigOperationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_config_operation_log
     *
     * @mbggenerated Wed Jan 11 20:31:27 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_config_operation_log
     *
     * @mbggenerated Wed Jan 11 20:31:27 CST 2023
     */
    int insert(SuixiangCardConfigOperationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_config_operation_log
     *
     * @mbggenerated Wed Jan 11 20:31:27 CST 2023
     */
    int insertSelective(SuixiangCardConfigOperationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_config_operation_log
     *
     * @mbggenerated Wed Jan 11 20:31:27 CST 2023
     */
    List<SuixiangCardConfigOperationLog> selectByExample(SuixiangCardConfigOperationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_config_operation_log
     *
     * @mbggenerated Wed Jan 11 20:31:27 CST 2023
     */
    SuixiangCardConfigOperationLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_config_operation_log
     *
     * @mbggenerated Wed Jan 11 20:31:27 CST 2023
     */
    int updateByExampleSelective(@Param("record") SuixiangCardConfigOperationLog record, @Param("example") SuixiangCardConfigOperationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_config_operation_log
     *
     * @mbggenerated Wed Jan 11 20:31:27 CST 2023
     */
    int updateByExample(@Param("record") SuixiangCardConfigOperationLog record, @Param("example") SuixiangCardConfigOperationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_config_operation_log
     *
     * @mbggenerated Wed Jan 11 20:31:27 CST 2023
     */
    int updateByPrimaryKeySelective(SuixiangCardConfigOperationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_config_operation_log
     *
     * @mbggenerated Wed Jan 11 20:31:27 CST 2023
     */
    int updateByPrimaryKey(SuixiangCardConfigOperationLog record);

    int countForPage(@Param("record") SuixiangCardConfigOperationLog record);

    List<SuixiangCardConfigOperationLog> selectForPage(@Param("record") SuixiangCardConfigOperationLog record, @Param("page") Page page);

    int batchInsert(@Param("list") List<SuixiangCardConfigOperationLog> records);
}