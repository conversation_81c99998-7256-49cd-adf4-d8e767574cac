package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.model.OrgInfo;

public interface OrgInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table org_info
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table org_info
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    int insert(OrgInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table org_info
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    int insertSelective(OrgInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table org_info
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    OrgInfo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table org_info
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    int updateByPrimaryKeySelective(OrgInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table org_info
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    int updateByPrimaryKey(OrgInfo record);

    OrgInfo getOrgSeqByOrgId(String orgId);
}