package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/3
 */
@Data
public class CardRefundDto {
    private String goodsOrderNo; // 商品订单号
    private String payOrderNo; // 支付订单号
    private String mid; // 支付订单号
    private BigDecimal amount; // 账单金额
    private Date payFinishTime; // 支付完成时间
    private String contractId; // 卡标识
}
