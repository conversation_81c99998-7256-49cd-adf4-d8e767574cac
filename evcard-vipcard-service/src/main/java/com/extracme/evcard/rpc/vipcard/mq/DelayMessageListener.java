package com.extracme.evcard.rpc.vipcard.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.PurchaseRecordInfo;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.PurchaseSuiXiangCardRecordInfo;
import com.extracme.evcard.rpc.vipcard.service.ICardTradeService;
import com.extracme.evcard.rpc.vipcard.service.ISuiXiangCardTradeService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class DelayMessageListener implements MessageListener {
	
	Logger logger = LoggerFactory.getLogger(this.getClass());
	
	@Autowired
	ICardTradeService cardTradeService;

	@Autowired
	ISuiXiangCardTradeService suiXiangCardTradeService;


	@Override
	public Action consume(Message message, ConsumeContext arg1) {
		String tag = message.getTag();
		String reqId = message.getKey() == null?UUID.randomUUID().toString():message.getKey();
		HidLog.putTraceParam(HidLog.REQ_ID, reqId);
		if(StringUtils.equals(tag, EventEnum.CARD_PURCHASE_CANCEL.getTag())) {
			PurchaseRecordInfo recordInfo = new PurchaseRecordInfo();
			try {
				logger.info("消费订单取消延时消息，tag={}, reqId={}", tag, reqId);
				ProtobufUtil.deserializeProtobuf(message.getBody(), recordInfo);
				cardTradeService.cancelOrder(recordInfo, OperatorDto.getSystemOp());
			} catch (Exception e) {
				logger.error("处理购卡订单取消延时消息出错，purchaseRecord=" + JSON.toJSONString(recordInfo), e);
			}
		}else if(StringUtils.equals(tag, EventEnum.SUIXIANGCARD_PURCHASE_CANCEL.getTag())){
			PurchaseSuiXiangCardRecordInfo suiXiangCardRecordInfo = new PurchaseSuiXiangCardRecordInfo();
			try {
				logger.info("消费随享卡订单取消延时消息，tag={}, reqId={}", tag, reqId);
				ProtobufUtil.deserializeProtobuf(message.getBody(), suiXiangCardRecordInfo);
				suiXiangCardTradeService.cancelOrder(suiXiangCardRecordInfo.getId(), OperatorDto.getSystemOp());
			} catch (Exception e) {
				logger.error("处理随享卡购卡订单取消延时消息出错，purchaseRecord=" + JSON.toJSONString(suiXiangCardRecordInfo), e);
			}
		}
		return Action.CommitMessage;
	}

}
