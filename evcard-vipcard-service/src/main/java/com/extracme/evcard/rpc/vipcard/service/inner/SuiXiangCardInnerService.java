package com.extracme.evcard.rpc.vipcard.service.inner;

import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardCdkConfigDetailMapper;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardUseOpreationLogMapper;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardUseLogOperationTypeEnum;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkThirdSaleDto;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseOpreationLog;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseOpreationLogExample;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.google.common.collect.Lists;
import com.extracme.evcard.rpc.vipcard.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Component
public class SuiXiangCardInnerService {

    @Resource
    private SuixiangCardUseOpreationLogMapper suixiangCardUseOpreationLogMapper;

    @Autowired
    private SuixiangCardCdkConfigDetailMapper suixiangCardCdkConfigDetailMapper;

    /**
     * true 有冻结，value ：冻结天数
     * false 无冻结，value：0
     *
     * @param userCardId
     * @param contractId
     * @return
     */
    public Pair<Boolean, Integer> getFreezeDays(Long userCardId, String contractId) {
        try {
            if (userCardId == null || userCardId == 0 || StringUtils.isBlank(contractId)) {
                return new Pair<>(false, 0);
            }

            List<Long> operationType = Lists.newArrayList(SuiXiangCardUseLogOperationTypeEnum.FREEZE.getOperationType()
                    , SuiXiangCardUseLogOperationTypeEnum.CARD_CONSUME.getOperationType(), SuiXiangCardUseLogOperationTypeEnum.UNFREEZE.getOperationType());
            SuixiangCardUseOpreationLogExample example = new SuixiangCardUseOpreationLogExample();
            example.createCriteria().andCardUseIdEqualTo(userCardId)
                    .andOrderSeqEqualTo(contractId)
                    .andOperationTypeIn(operationType);
            List<SuixiangCardUseOpreationLog> suixiangCardUseOpreationLogs = suixiangCardUseOpreationLogMapper.selectByExample(example);

            int freezeLogDays = 0;
            int unfreezeAndDecutionLogDays = 0;
            if (CollectionUtils.isNotEmpty(suixiangCardUseOpreationLogs)) {
                for (SuixiangCardUseOpreationLog s : suixiangCardUseOpreationLogs) {
                    if (SuiXiangCardUseLogOperationTypeEnum.FREEZE.getOperationType() == s.getOperationType()) {
                        freezeLogDays = freezeLogDays + s.getOrderOperationDays();
                    } else if (SuiXiangCardUseLogOperationTypeEnum.CARD_CONSUME.getOperationType() == s.getOperationType() || SuiXiangCardUseLogOperationTypeEnum.UNFREEZE.getOperationType() == s.getOperationType()) {
                        unfreezeAndDecutionLogDays = unfreezeAndDecutionLogDays + s.getOrderOperationDays();
                    }
                }
            }

            if (freezeLogDays > unfreezeAndDecutionLogDays) {
                return new Pair<>(true, (freezeLogDays - unfreezeAndDecutionLogDays));
            }
        } catch (Exception e) {
            log.error("getFreezeDays异常，userCardId={},contractId={}", userCardId, contractId, e);
        }
        return new Pair<>(false, 0);
    }

    /**
     * 根据baseId获取第三方售卖价
     *
     * @param cardBaseId
     * @return key cardRentId+","+cardPriceId  value 第三方售卖价
     */
    public Map<String, BigDecimal> getThirdPriceByBaseId(Long cardBaseId) {
        // 第三方售卖价
        List<SuixiangCardCdkThirdSaleDto> suixiangCardCdkThirdSaleDtos = suixiangCardCdkConfigDetailMapper.selectThirdSalesPriceByBaseId(cardBaseId);
        Map<String, BigDecimal> rentIdAndThirdPriceMap = suixiangCardCdkThirdSaleDtos.stream().collect(Collectors.toMap(suixiangCardCdkThirdSaleDto -> suixiangCardCdkThirdSaleDto.getCardRentId() + Constants.STR_COMMA + suixiangCardCdkThirdSaleDto.getCardPriceId(), SuixiangCardCdkThirdSaleDto::getThirdSalesPrice, (bigDecimal, bigDecimal2) -> {
            if (bigDecimal.compareTo(BigDecimal.ZERO) > 0) {
                return bigDecimal;
            }

            if (bigDecimal2.compareTo(BigDecimal.ZERO) > 0) {
                return bigDecimal2;
            }
            return bigDecimal;
        }));
        return rentIdAndThirdPriceMap;
    }


    /**
     *
     *
     * @param cardBaseId
     * @return key carModelIds  value  单价 第三方售卖价除以天数
     */
    public Map<String, BigDecimal> getUnitThirdPriceByBaseId(Long cardBaseId) {
        // 第三方售卖价
        List<SuixiangCardCdkThirdSaleDto> suixiangCardCdkThirdSaleDtos = suixiangCardCdkConfigDetailMapper.selectThirdSalesPriceInfoByBaseId(cardBaseId);

        Map<String, BigDecimal> rentIdAndThirdPriceMap = suixiangCardCdkThirdSaleDtos.stream().collect(Collectors.toMap(dto -> {
            String carModelIds = dto.getCarModelIds();
            if (StringUtils.isNotBlank(carModelIds)) {
                List<String> carModelIdList = Arrays.asList(carModelIds.split(","));
                Collections.sort(carModelIdList); // 排序
                carModelIds = String.join(",", carModelIdList);
            }
            return carModelIds;
        }, dto -> {
            BigDecimal thirdSalesPrice = dto.getThirdSalesPrice();
            BigDecimal price = thirdSalesPrice.divide(new BigDecimal(dto.getRentDays()), 2, RoundingMode.HALF_UP);
            return price;
        }, (bigDecimal, bigDecimal2) -> {
            if (bigDecimal.compareTo(BigDecimal.ZERO) > 0) {
                return bigDecimal;
            }

            if (bigDecimal2.compareTo(BigDecimal.ZERO) > 0) {
                return bigDecimal2;
            }
            return bigDecimal;
        }));
        return rentIdAndThirdPriceMap;
    }
}
