package com.extracme.evcard.rpc.vipcard.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "md.rest.api")
public class MdRestApiConfig {
    private String baseUrl;

    private String getCachedGoodsModelUrl;
    private String getCachedStoreListUrl;
    private String getGoodsModelByVehicleModelUrl;

    private String payOrderIsPayingOrNotUrl;
    private String getAllGoodsModeInfoUrl;

    // 创建支付订单
    private String createPayOrderUrl;

    // 取消支付订单
    private String cancelPayOrderUrl;

    // 临界值退卡
    private String refundForCriticalPointPaymentUrl;

    // 去支付
    private String paySuiXiangCardOrderUrl;

    // 退卡费
    private String returnCardFeeUrl;

    // 查询淡旺季配置
    private String getCacheLowAndPeakSeasonConfigUrl;
}
