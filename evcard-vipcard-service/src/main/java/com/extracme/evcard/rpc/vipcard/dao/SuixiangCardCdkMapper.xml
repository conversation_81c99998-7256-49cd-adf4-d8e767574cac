<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.SuixiangCardCdkMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdk">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_cdk_config_detail_id" jdbcType="BIGINT" property="cardCdkConfigDetailId" />
    <result column="card_base_id" jdbcType="BIGINT" property="cardBaseId" />
    <result column="card_rent_id" jdbcType="BIGINT" property="cardRentId" />
    <result column="card_price_id" jdbcType="BIGINT" property="cardPriceId" />
    <result column="cdkey" jdbcType="VARCHAR" property="cdkey" />
    <result column="is_activated" jdbcType="INTEGER" property="isActivated" />
    <result column="activated_mid" jdbcType="VARCHAR" property="activatedMid" />
    <result column="activated_user_name" jdbcType="VARCHAR" property="activatedUserName" />
    <result column="activated_user_mobile" jdbcType="VARCHAR" property="activatedUserMobile" />
    <result column="activated_time" jdbcType="TIMESTAMP" property="activatedTime" />
    <result column="card_use_id" jdbcType="BIGINT" property="cardUseId" />
    <result column="wechat_cdk_qr_url" jdbcType="VARCHAR" property="wechatCdkQrUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    id, card_cdk_config_detail_id, card_base_id, card_rent_id, card_price_id, cdkey, 
    is_activated, activated_mid, activated_user_name, activated_user_mobile, activated_time, 
    card_use_id, wechat_cdk_qr_url, create_time, create_oper_id, create_oper_name, update_time, 
    update_oper_id, update_oper_name, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from suixiang_card_cdk
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from suixiang_card_cdk
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    delete from suixiang_card_cdk
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdk">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    insert into suixiang_card_cdk (id, card_cdk_config_detail_id, card_base_id, 
      card_rent_id, card_price_id, cdkey, 
      is_activated, activated_mid, activated_user_name, 
      activated_user_mobile, activated_time, card_use_id, 
      wechat_cdk_qr_url, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{cardCdkConfigDetailId,jdbcType=BIGINT}, #{cardBaseId,jdbcType=BIGINT}, 
      #{cardRentId,jdbcType=BIGINT}, #{cardPriceId,jdbcType=BIGINT}, #{cdkey,jdbcType=VARCHAR}, 
      #{isActivated,jdbcType=INTEGER}, #{activatedMid,jdbcType=VARCHAR}, #{activatedUserName,jdbcType=VARCHAR}, 
      #{activatedUserMobile,jdbcType=VARCHAR}, #{activatedTime,jdbcType=TIMESTAMP}, #{cardUseId,jdbcType=BIGINT}, 
      #{wechatCdkQrUrl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdk">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    insert into suixiang_card_cdk
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cardCdkConfigDetailId != null">
        card_cdk_config_detail_id,
      </if>
      <if test="cardBaseId != null">
        card_base_id,
      </if>
      <if test="cardRentId != null">
        card_rent_id,
      </if>
      <if test="cardPriceId != null">
        card_price_id,
      </if>
      <if test="cdkey != null">
        cdkey,
      </if>
      <if test="isActivated != null">
        is_activated,
      </if>
      <if test="activatedMid != null">
        activated_mid,
      </if>
      <if test="activatedUserName != null">
        activated_user_name,
      </if>
      <if test="activatedUserMobile != null">
        activated_user_mobile,
      </if>
      <if test="activatedTime != null">
        activated_time,
      </if>
      <if test="cardUseId != null">
        card_use_id,
      </if>
      <if test="wechatCdkQrUrl != null">
        wechat_cdk_qr_url,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cardCdkConfigDetailId != null">
        #{cardCdkConfigDetailId,jdbcType=BIGINT},
      </if>
      <if test="cardBaseId != null">
        #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardRentId != null">
        #{cardRentId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null">
        #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="cdkey != null">
        #{cdkey,jdbcType=VARCHAR},
      </if>
      <if test="isActivated != null">
        #{isActivated,jdbcType=INTEGER},
      </if>
      <if test="activatedMid != null">
        #{activatedMid,jdbcType=VARCHAR},
      </if>
      <if test="activatedUserName != null">
        #{activatedUserName,jdbcType=VARCHAR},
      </if>
      <if test="activatedUserMobile != null">
        #{activatedUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="activatedTime != null">
        #{activatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardUseId != null">
        #{cardUseId,jdbcType=BIGINT},
      </if>
      <if test="wechatCdkQrUrl != null">
        #{wechatCdkQrUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    select count(*) from suixiang_card_cdk
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    update suixiang_card_cdk
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardCdkConfigDetailId != null">
        card_cdk_config_detail_id = #{record.cardCdkConfigDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.cardBaseId != null">
        card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.cardRentId != null">
        card_rent_id = #{record.cardRentId,jdbcType=BIGINT},
      </if>
      <if test="record.cardPriceId != null">
        card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="record.cdkey != null">
        cdkey = #{record.cdkey,jdbcType=VARCHAR},
      </if>
      <if test="record.isActivated != null">
        is_activated = #{record.isActivated,jdbcType=INTEGER},
      </if>
      <if test="record.activatedMid != null">
        activated_mid = #{record.activatedMid,jdbcType=VARCHAR},
      </if>
      <if test="record.activatedUserName != null">
        activated_user_name = #{record.activatedUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.activatedUserMobile != null">
        activated_user_mobile = #{record.activatedUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.activatedTime != null">
        activated_time = #{record.activatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cardUseId != null">
        card_use_id = #{record.cardUseId,jdbcType=BIGINT},
      </if>
      <if test="record.wechatCdkQrUrl != null">
        wechat_cdk_qr_url = #{record.wechatCdkQrUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null">
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null">
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null">
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null">
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    update suixiang_card_cdk
    set id = #{record.id,jdbcType=BIGINT},
      card_cdk_config_detail_id = #{record.cardCdkConfigDetailId,jdbcType=BIGINT},
      card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      card_rent_id = #{record.cardRentId,jdbcType=BIGINT},
      card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      cdkey = #{record.cdkey,jdbcType=VARCHAR},
      is_activated = #{record.isActivated,jdbcType=INTEGER},
      activated_mid = #{record.activatedMid,jdbcType=VARCHAR},
      activated_user_name = #{record.activatedUserName,jdbcType=VARCHAR},
      activated_user_mobile = #{record.activatedUserMobile,jdbcType=VARCHAR},
      activated_time = #{record.activatedTime,jdbcType=TIMESTAMP},
      card_use_id = #{record.cardUseId,jdbcType=BIGINT},
      wechat_cdk_qr_url = #{record.wechatCdkQrUrl,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdk">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    update suixiang_card_cdk
    <set>
      <if test="cardCdkConfigDetailId != null">
        card_cdk_config_detail_id = #{cardCdkConfigDetailId,jdbcType=BIGINT},
      </if>
      <if test="cardBaseId != null">
        card_base_id = #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardRentId != null">
        card_rent_id = #{cardRentId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null">
        card_price_id = #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="cdkey != null">
        cdkey = #{cdkey,jdbcType=VARCHAR},
      </if>
      <if test="isActivated != null">
        is_activated = #{isActivated,jdbcType=INTEGER},
      </if>
      <if test="activatedMid != null">
        activated_mid = #{activatedMid,jdbcType=VARCHAR},
      </if>
      <if test="activatedUserName != null">
        activated_user_name = #{activatedUserName,jdbcType=VARCHAR},
      </if>
      <if test="activatedUserMobile != null">
        activated_user_mobile = #{activatedUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="activatedTime != null">
        activated_time = #{activatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardUseId != null">
        card_use_id = #{cardUseId,jdbcType=BIGINT},
      </if>
      <if test="wechatCdkQrUrl != null">
        wechat_cdk_qr_url = #{wechatCdkQrUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdk">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 10:20:00 CST 2024.
    -->
    update suixiang_card_cdk
    set card_cdk_config_detail_id = #{cardCdkConfigDetailId,jdbcType=BIGINT},
      card_base_id = #{cardBaseId,jdbcType=BIGINT},
      card_rent_id = #{cardRentId,jdbcType=BIGINT},
      card_price_id = #{cardPriceId,jdbcType=BIGINT},
      cdkey = #{cdkey,jdbcType=VARCHAR},
      is_activated = #{isActivated,jdbcType=INTEGER},
      activated_mid = #{activatedMid,jdbcType=VARCHAR},
      activated_user_name = #{activatedUserName,jdbcType=VARCHAR},
      activated_user_mobile = #{activatedUserMobile,jdbcType=VARCHAR},
      activated_time = #{activatedTime,jdbcType=TIMESTAMP},
      card_use_id = #{cardUseId,jdbcType=BIGINT},
      wechat_cdk_qr_url = #{wechatCdkQrUrl,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchSaveCdk" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdk">
    insert into suixiang_card_cdk (card_cdk_config_detail_id, card_base_id, card_rent_id, card_price_id, cdkey,
    create_oper_id, create_oper_name, update_oper_id, update_oper_name) values
    <foreach item="item" index="index" collection="list" separator=",">
      (#{item.cardCdkConfigDetailId,jdbcType=BIGINT}, #{item.cardBaseId,jdbcType=BIGINT}, #{item.cardRentId,jdbcType=BIGINT},
      #{item.cardPriceId,jdbcType=BIGINT}, #{item.cdkey,jdbcType=VARCHAR},
      #{item.createOperId,jdbcType=BIGINT}, #{item.createOperName,jdbcType=VARCHAR}, #{item.updateOperId,jdbcType=BIGINT}, #{item.updateOperName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <select id="getLatestKey" resultType="java.lang.String">
    SELECT cdkey from suixiang_card_cdk WHERE cdkey is not NULL ORDER BY id DESC LIMIT 1;
  </select>


  <select id="queryExportListBySuixiangCardCdkConfigId" parameterType="map" resultType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkExportDto">
    select
    c.id as cdkId,
    b.id as cardBaseId,
    b.card_name as cardName,
    b.sale_start_time as saleStartTime,
    b.sale_end_time as saleEndTime,
    ccd.act_desc as cdkDesc,
    c.cdkey as cdkey,

    CASE c.is_activated
    WHEN 0 THEN
    '未领取'
    WHEN 1 THEN
    '已领取'
    END as isActivatedDesc,

    u.total_order as totalOrder,
    u.start_time as cardStartTime,
    u.expires_time as cardExpiresTime,
    c.activated_user_name as activatedUserName,
    c.activated_user_mobile as activatedUserMobile,
    c.activated_mid as activatedMid,
    c.create_oper_name as createOperName,

    CASE cc.purpose
    WHEN 1 THEN
    '活动赠送'
    WHEN 2 THEN
    '第三方售卖'
    END as purpose,

    CASE cc.purpose
    WHEN 1 THEN
    'null'
    WHEN 2 THEN
    ccd.third_sales_price
    END as thirdSalesPrice

    from suixiang_card_cdk c
    left join   suixiang_card_cdk_config_detail ccd on c.card_cdk_config_detail_id = ccd.id
    left join   suixiang_card_cdk_config cc on ccd.card_cdk_config_id = cc.id
    left join   suixiang_card_base b on c.card_base_id = b.id
    left join   suixiang_card_rent_days rd on c.card_rent_id = rd.id
    left join   suixiang_card_price p on c.card_price_id = p.id
    left join   suixiang_card_use u on c.card_use_id = u.id
    where cc.id = #{suixiangCardCdkConfigId,jdbcType=BIGINT}
    order by c.id desc
    <if test="page != null">
      LIMIT #{page.offSet},#{page.limitSet}
    </if>
  </select>

  <select id="getCkdOtherDtoByCkdId" resultType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkOtherDto">
    select
    c.id as cdkId,
    cc.id as cdkConfigId,
    cc.purpose as purpose,
    ccd.id as cdkConfigDetailId,
    ccd.third_sales_price as thirdSalesPrice

    from suixiang_card_cdk c
    left join   suixiang_card_cdk_config_detail ccd on c.card_cdk_config_detail_id = ccd.id
    left join   suixiang_card_cdk_config cc on ccd.card_cdk_config_id = cc.id
    where c.id = #{cdkId,jdbcType=BIGINT}
  </select>

</mapper>