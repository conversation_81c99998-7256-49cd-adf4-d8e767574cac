package com.extracme.evcard.rpc.vipcard.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 活动配置信息&卡片关键信息
 */
@Data
public class MmpCardSalesActivityInfo extends MmpCardSalesActivity {
    /**
     * 活动运营机构
     */
    String orgName;

    /**
     * 卡片：卡片名称
     */
    private String cardName;

    /**
     * 卡片：卡片类型 0周卡 1月卡 季卡
     */
    private Integer cardType;

    /**
     * 卡片：享有折扣，1~99整数
     */
    private Integer discount;

    /**
     * 卡片：单笔订单最大抵扣金额，单位：元
     */
    private BigDecimal maxValue;

    /**
     *卡片：订单时长限制，单位分钟
     */
    private Integer durationLimit;

    /**
     * 卡片：卡片配置信息-可用区域限制
     */
    private String cityLimit;

    /**
     * 卡片：卡片配置信息-车型限制
     */
    private String vehicleModelLimit;

    /**
     * 卡片：卡片配置信息-资产车型限制
     */
    private String goodsModelId;

    /**
     * 卡片： 门店限制(取车或还车满足即可)
     */
    private String storeIds;

    /**
     * 卡片：卡片配置信息-产品线限制
     */
    private String rentMethod;

    /**
     * 卡片：卡片配置信息-产品线大类
     */
    private String rentMethodGroup;
}