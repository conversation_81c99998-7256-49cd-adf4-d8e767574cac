package com.extracme.evcard.rpc.vipcard.service.inner;

import com.extracme.evcard.rpc.vehicle.dto.VehicleModelDTO;
import com.extracme.evcard.rpc.vehicle.service.IVehicleModelService;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class VehicleModelServ {

	@Resource
	private IVehicleModelService vehicleModelService;

	private Cache<Long, String> vehicleModelCache = CacheBuilder.newBuilder()
			.expireAfterWrite(1 * DateUtils.MILLIS_PER_HOUR, TimeUnit.MILLISECONDS).maximumSize(500).build();

	public Map<Long, String> getVehicleNames(List<Long> vehicleModelSeqs) {
		if (CollectionUtils.isEmpty(vehicleModelSeqs)) {
			return null;
		}
		Map<Long, String> map = new HashMap<>();
		Set<Long> seqSet = new HashSet<>();
		for (Long vehicleModelSeq : vehicleModelSeqs) {
			String vehicleModelName = vehicleModelCache.getIfPresent(vehicleModelSeq);
			if (vehicleModelName != null) {
				map.put(vehicleModelSeq, vehicleModelName);
			} else {
				seqSet.add(vehicleModelSeq);
			}
		}
		if (!CollectionUtils.isEmpty(seqSet)) {
			try {
				List<VehicleModelDTO> list = vehicleModelService.getVehicleModelInfoBySeqs(seqSet.toArray(new Long[seqSet.size()]));
				if (!CollectionUtils.isEmpty(list)) {
					for (VehicleModelDTO vehicleModel : list) {
						if (StringUtils.isBlank(vehicleModel.getVehicleModelInfo())) {
							continue;
						}
						map.put(vehicleModel.getVehicleModelSeq(), vehicleModel.getVehicleModelInfo());
						vehicleModelCache.put(vehicleModel.getVehicleModelSeq(), vehicleModel.getVehicleModelInfo());
					}
				}
			} catch (Exception ex) {
				log.error("查询车型名称失败", ex);
			}
		}
		return map;
	}
}
