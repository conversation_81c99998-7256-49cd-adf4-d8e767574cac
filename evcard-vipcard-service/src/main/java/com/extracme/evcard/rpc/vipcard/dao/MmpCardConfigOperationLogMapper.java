package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.dto.CardActivityConfigLogDto;
import com.extracme.evcard.rpc.vipcard.model.MmpCardConfigOperationLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpCardConfigOperationLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_config_operation_log
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_config_operation_log
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    int insert(MmpCardConfigOperationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_config_operation_log
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    int insertSelective(MmpCardConfigOperationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_config_operation_log
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    MmpCardConfigOperationLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_config_operation_log
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    int updateByPrimaryKeySelective(MmpCardConfigOperationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_config_operation_log
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    int updateByPrimaryKey(MmpCardConfigOperationLog record);

    /**
     * 配置相关操作日志保存
     * @param record
     * @return
     */
    int save(MmpCardConfigOperationLog record);

    /**
     * 批量保存配置操作日志
     * @param records
     * @return
     */
    int batchSave(List<MmpCardConfigOperationLog> records);

    /**
     * 查询配置日志
     * @param configIds
     * @param configType
     * @return
     */
    List<CardActivityConfigLogDto> selectByConfigTypeAndId(@Param("list")List configIds,
                                                           @Param("configType")Integer configType);
}