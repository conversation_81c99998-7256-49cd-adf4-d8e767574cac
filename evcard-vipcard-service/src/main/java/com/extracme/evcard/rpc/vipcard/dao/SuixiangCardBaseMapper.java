package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardBase;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardBaseExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SuixiangCardBaseMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    int countByExample(SuixiangCardBaseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    int insert(SuixiangCardBase record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    int insertSelective(SuixiangCardBase record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    List<SuixiangCardBase> selectByExample(SuixiangCardBaseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    SuixiangCardBase selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    int updateByExampleSelective(@Param("record") SuixiangCardBase record, @Param("example") SuixiangCardBaseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    int updateByExample(@Param("record") SuixiangCardBase record, @Param("example") SuixiangCardBaseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    int updateByPrimaryKeySelective(SuixiangCardBase record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_base
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    int updateByPrimaryKey(SuixiangCardBase record);

    int updateByPrimaryKey2(SuixiangCardBase record);

    int countForPage(@Param("record")SuixiangCardBase base);

    List<SuixiangCardBase> selectForPage(@Param("record")SuixiangCardBase base, @Param("page") Page page);

    /**
     * 根据城市查询开始预售/已上架的随想卡购买活动
     * @param cityId
     * @return
     */
    List<SuixiangCardBase> getEffectiveSuiXiangCardList(@Param("cityId") Long cityId);

    int updateStock(SuixiangCardBase record);
}