<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.MmpCardPurchaseRecordMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="card_activity_id" property="cardActivityId" jdbcType="BIGINT" />
    <result column="card_id" property="cardId" jdbcType="BIGINT" />
    <result column="payment_status" property="paymentStatus" jdbcType="INTEGER" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="issue_type" property="issueType" jdbcType="INTEGER" />
    <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
    <result column="cancel_time" property="cancelTime" jdbcType="TIMESTAMP" />
    <result column="real_amount" property="realAmount" jdbcType="DOUBLE" />
    <result column="out_trade_seq" property="outTradeSeq" jdbcType="VARCHAR" />
    <result column="user_card_no" property="userCardNo" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="order_seq" property="orderSeq" jdbcType="VARCHAR" />
    <result column="merge_pay_origin" property="mergePayOrigin" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, card_activity_id, card_id, payment_status, quantity, issue_type, pay_time, cancel_time, real_amount,
    out_trade_seq, user_card_no, start_time,end_time,order_seq,merge_pay_origin, status, create_time, create_oper_id, create_oper_name,
    update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_purchase_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectBySelective" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_purchase_record
    where 1=1
    <if test="userId != null">
      and user_id = #{userId}
    </if>
    <if test="cardId != null">
      and card_id = #{cardId}
    </if>
    <if test="issueType != null">
      and issue_type = #{issueType}
    </if>

    <if test="status != null">
      and status = #{status}
    </if>
    <if test="outTradeSeq != null">
      and out_trade_seq = #{outTradeSeq}
    </if>
    <if test="paymentStatus != null">
      and payment_status = #{paymentStatus}
    </if>
    <if test="issueType != null">
      and issue_type = #{issueType}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ${issSchema}.mmp_card_purchase_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecord" >
    insert into ${issSchema}.mmp_card_purchase_record (id, user_id, card_activity_id,
      card_id, payment_status, quantity, issue_type,
      pay_time, real_amount, out_trade_seq, 
      user_card_no, merge_pay_origin, status, create_time,
      create_oper_id, create_oper_name, update_time, 
      update_oper_id, update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{cardActivityId,jdbcType=BIGINT}, 
      #{cardId,jdbcType=BIGINT}, #{paymentStatus,jdbcType=INTEGER}, #{quantity,jdbcType=INTEGER}, #{issueType,jdbcType=INTEGER},
      #{payTime,jdbcType=TIMESTAMP}, #{realAmount,jdbcType=DOUBLE}, #{outTradeSeq,jdbcType=VARCHAR}, 
      #{userCardNo,jdbcType=VARCHAR}, #{mergePayOrigin,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
      #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecord" >
    insert into ${issSchema}.mmp_card_purchase_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="cardActivityId != null" >
        card_activity_id,
      </if>
      <if test="cardId != null" >
        card_id,
      </if>
      <if test="paymentStatus != null" >
        payment_status,
      </if>
      <if test="quantity != null" >
        quantity,
      </if>
      <if test="issueType != null" >
        issue_type,
      </if>
      <if test="payTime != null" >
        pay_time,
      </if>
      <if test="cancelTime != null" >
        cancel_time,
      </if>
      <if test="realAmount != null" >
        real_amount,
      </if>
      <if test="outTradeSeq != null" >
        out_trade_seq,
      </if>
      <if test="userCardNo != null" >
        user_card_no,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="orderSeq != null" >
        order_seq,
      </if>
      <if test="mergePayOrigin != null" >
        merge_pay_origin,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="cardActivityId != null" >
        #{cardActivityId,jdbcType=BIGINT},
      </if>
      <if test="cardId != null" >
        #{cardId,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null" >
        #{paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="quantity != null" >
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="issueType != null" >
        #{issueType,jdbcType=INTEGER},
      </if>
      <if test="payTime != null" >
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null" >
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realAmount != null" >
        #{realAmount,jdbcType=DOUBLE},
      </if>
      <if test="outTradeSeq != null" >
        #{outTradeSeq,jdbcType=VARCHAR},
      </if>
      <if test="userCardNo != null" >
        #{userCardNo,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        #{startTime},
      </if>
      <if test="endTime != null" >
        #{endTime},
      </if>
      <if test="orderSeq != null" >
        #{orderSeq},
      </if>
      <if test="mergePayOrigin != null" >
        #{mergePayOrigin,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecord" >
    update ${issSchema}.mmp_card_purchase_record
    <set >
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="cardActivityId != null" >
        card_activity_id = #{cardActivityId,jdbcType=BIGINT},
      </if>
      <if test="cardId != null" >
        card_id = #{cardId,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null" >
        payment_status = #{paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="issueType != null" >
        issue_type = #{issueType,jdbcType=INTEGER},
      </if>
      <if test="payTime != null" >
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null" >
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realAmount != null" >
        real_amount = #{realAmount,jdbcType=DOUBLE},
      </if>
      <if test="outTradeSeq != null" >
        out_trade_seq = #{outTradeSeq,jdbcType=VARCHAR},
      </if>
      <if test="userCardNo != null" >
        user_card_no = #{userCardNo,jdbcType=VARCHAR},
      </if>
      <if test="mergePayOrigin != null" >
        merge_pay_origin = #{mergePayOrigin,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecord" >
    update ${issSchema}.mmp_card_purchase_record
    set user_id = #{userId,jdbcType=BIGINT},
      card_activity_id = #{cardActivityId,jdbcType=BIGINT},
      card_id = #{cardId,jdbcType=BIGINT},
      payment_status = #{paymentStatus,jdbcType=INTEGER},
      quantity = #{quantity,jdbcType=INTEGER},
      issue_type = #{issueType,jdbcType=INTEGER},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      real_amount = #{realAmount,jdbcType=DOUBLE},
      out_trade_seq = #{outTradeSeq,jdbcType=VARCHAR},
      user_card_no = #{userCardNo,jdbcType=VARCHAR},
      merge_pay_origin = #{mergePayOrigin,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="batchGetRecordByActivityId" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_purchase_record
    where
        payment_status in (0,1)
    and user_id = #{userId}
    <if test="activityIdList != null and activityIdList.size > 0">
      and card_activity_id in
      <foreach collection="activityIdList" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </select>


  <select id="selectLastUserPurchaseRecords" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_purchase_record
    where status = 0 and payment_status = 1
    and user_id = #{userId}
    and card_id = #{cardId}
    order by id desc
    limit 200
  </select>

  <resultMap id="fullResultMap" type="com.extracme.evcard.rpc.vipcard.model.CardPurchaseRecordInfo" extends="BaseResultMap">
    <result column="issue_type" property="issueType" jdbcType="INTEGER" />
    <result column="purchase_type" property="purchaseType" jdbcType="INTEGER" />
    <result column="org_name" property="orgName" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="VARCHAR" />
    <result column="card_type" property="cardType" jdbcType="INTEGER" />
    <result column="card_name" property="cardName" jdbcType="VARCHAR" />
    <result column="discount" property="discount" jdbcType="INTEGER" />
    <result column="max_value" property="maxValue" jdbcType="DECIMAL" />
    <result column="duration_limit" property="durationLimit" jdbcType="INTEGER" />
    <result column="city_limit" property="cityLimit" jdbcType="VARCHAR" />
    <result column="vehicle_model_limit" property="vehicleModelLimit" jdbcType="VARCHAR" />
    <result column="goods_model_id" property="goodsModelId" jdbcType="VARCHAR" />
    <result column="store_ids" property="storeIds" jdbcType="VARCHAR" />
    <result column="rent_method" property="rentMethod" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectPage" resultMap="fullResultMap"
          parameterType="com.extracme.evcard.rpc.vipcard.dto.CardPurchasePageQueryDto" >
    select
    cp.id, cp.order_seq,  user_id, cp.card_activity_id, cp.card_id, cp.payment_status, cp.quantity,
    cp.pay_time, cp.cancel_time, cp.real_amount,cp.out_trade_seq, cp.user_card_no, cp.issue_type,
    cp.status, cp.create_time, cp.create_oper_id, cp.create_oper_name,
    cp.update_time, cp.update_oper_id, cp.update_oper_name,
    c.card_type, c.discount, c.max_value, c.duration_limit, c.city_limit,
    c.vehicle_model_limit, c.goods_model_id, c.store_ids as storeIds, c.rent_method,c.card_name, c.purchase_type,
    c.org_id, o.org_name
    from ${issSchema}.mmp_card_purchase_record cp
    LEFT JOIN ${issSchema}.mmp_card_def c ON c.card_id = cp.card_id
    LEFT JOIN ${issSchema}.mmp_card_sales_activity a ON a.id = cp.card_activity_id
    LEFT JOIN ${isvSchema}.org_info o ON o.org_id = a.org_id
    where 1 = 1 and cp.status = 0
    <if test="refSeq != null and refSeq != '' ">
      and cp.id = #{refSeq}
    </if>
  <if test="orderSeq != null and orderSeq != '' ">
      and cp.order_seq = #{orderSeq}
  </if>
    <if test="userId != null">
      and cp.user_id = #{userId}
    </if>
    <if test="cardId != null">
      and cp.card_id = #{cardId}
    </if>
    <if test="cardName != null and cardName != '' ">
      and c.card_name like concat('%',#{cardName},'%')
    </if>
    <if test="cardType != null">
      and c.card_type = #{cardType}
    </if>
    <if test="issueType != null">
      and cp.issue_type = #{issueType}
    </if>
    <if test="purchaseType != null">
      and c.purchase_type = #{purchaseType}
    </if>
    <if test="paymentStatus != null">
      and cp.payment_status = #{paymentStatus}
    </if>
    <if test="purchaseTimeStart != null and purchaseTimeStart != '' ">
      and cp.pay_time &gt;= #{purchaseTimeStart}
    </if>
    <if test="purchaseTimeEnd != null and purchaseTimeEnd != '' ">
      and cp.pay_time &lt; #{purchaseTimeEnd}
    </if>
    <if test="orgId != null and orgId != '' ">
      and a.org_id like concat(#{orgId},'%')
    </if>
    <if test="cityId != null">
      and c.city_limit like concat('%',#{cityId},',%')
    </if>
    order by cp.id desc
  </select>


  <select id="selectList" resultMap="fullResultMap">
    select
    cp.id, cp.order_seq, cp.user_id, cp.card_activity_id, cp.card_id, cp.payment_status, cp.quantity,
    cp.pay_time, cp.cancel_time, cp.real_amount,cp.out_trade_seq, cp.user_card_no,cp.issue_type,
    cp.status, cp.create_time, cp.create_oper_id, cp.create_oper_name,
    cp.update_time, cp.update_oper_id, cp.update_oper_name,
    c.card_type, c.discount, c.max_value, c.duration_limit, c.city_limit,
    c.vehicle_model_limit, c.goods_model_id, c.store_ids, c.rent_method, c.card_name,c.purchase_type,
    c.org_id, o.org_name
    from ${issSchema}.mmp_card_purchase_record cp
    LEFT JOIN ${issSchema}.mmp_card_def c ON c.card_id = cp.card_id
    LEFT JOIN ${issSchema}.mmp_card_sales_activity a ON a.id = cp.card_activity_id
    LEFT JOIN ${isvSchema}.org_info o ON o.org_id = c.org_id
    where 1 = 1 and cp.status = 0
    <if test="condition.refSeq != null and condition.refSeq != '' ">
      and cp.id = #{condition.refSeq}
    </if>
    <if test="condition.orderSeq != null and condition.orderSeq != '' ">
      and cp.order_seq = #{condition.orderSeq}
    </if>
    <if test="condition.userId != null">
      and cp.user_id = #{condition.userId}
    </if>
    <if test="condition.cardId != null">
      and cp.card_id = #{condition.cardId}
    </if>
    <if test="condition.cardName != null and condition.cardName != '' ">
      and c.card_name like concat('%',#{condition.cardName},'%')
    </if>
    <if test="condition.cardType != null">
      and c.card_type = #{condition.cardType}
    </if>
    <if test="condition.issueType != null">
      and cp.issue_type = #{condition.issueType}
    </if>
    <if test="condition.purchaseType != null">
      and c.purchase_type = #{condition.purchaseType}
    </if>
    <if test="condition.paymentStatus != null">
      and cp.payment_status = #{condition.paymentStatus}
    </if>
    <if test="condition.purchaseTimeStart != null and condition.purchaseTimeStart != '' ">
      and cp.pay_time &gt;= #{condition.purchaseTimeStart}
    </if>
    <if test="condition.purchaseTimeEnd != null and condition.purchaseTimeEnd != '' ">
      and cp.pay_time &lt; #{condition.purchaseTimeEnd}
    </if>
    <if test="condition.orgId != null and condition.orgId != '' ">
      and a.org_id like concat(#{condition.orgId},'%')
    </if>
    <if test="condition.cityId != null">
      and c.city_limit like concat('%',#{condition.cityId},',%')
    </if>
    <if test="id != null">
      and cp.id &lt; #{id}
    </if>
    order by cp.id desc
    limit #{limit}
  </select>

  <select id="selectByUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_purchase_record
    where user_id = #{pkId}
    order by create_time desc
    limit #{pageSize} OFFSET #{pageNum}
  </select>

  <select id="selectByUserId2" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_purchase_record
    where user_id = #{pkId}
    order by create_time desc
  </select>

  <select id="selectByOrderSeq" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_purchase_record
    where order_seq = #{orderSeq,jdbcType=VARCHAR}
  </select>

  <select id="selectUnreadCardOfferList" resultMap="fullResultMap">
    select
    cp.id, cp.order_seq, cp.user_id, cp.card_activity_id, cp.card_id, cp.payment_status, cp.quantity,
    cp.pay_time, cp.real_amount,cp.out_trade_seq, cp.user_card_no,cp.issue_type,
    cp.status, cp.create_time, cp.create_oper_id, cp.create_oper_name,
    cp.update_time, cp.update_oper_id, cp.update_oper_name,
    c.card_type, c.discount, c.max_value, c.duration_limit, c.city_limit,
    c.vehicle_model_limit, c.goods_model_id, c.store_ids, c.rent_method, c.card_name,c.purchase_type,
    c.org_id, o.org_name
    from ${issSchema}.mmp_card_purchase_record cp
    LEFT JOIN ${issSchema}.mmp_card_def c ON c.card_id = cp.card_id
    LEFT JOIN ${issSchema}.mmp_user_card_info uc ON uc.user_card_no = cp.user_card_no
    LEFT JOIN ${isvSchema}.org_info o ON o.org_id = c.org_id
    where 1 = 1 and cp.status = 0 and cp.payment_status = 1 and cp.issue_type in (1,2)
    and cp.user_id = #{userId}
    and cp.remind_status = 0
    and uc.card_status=1  AND uc.expires_time &gt;= NOW()
    and cp.create_time &gt;= #{startDate}
    ORDER BY cp.create_time desc
    limit #{limit}
  </select>


  <!-- 将新发卡记录标记为已读 -->
  <update id="readCards">
    UPDATE ${issSchema}.mmp_card_purchase_record
    SET  remind_status = 1
    WHERE id  IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and issue_type in (1, 2)
  </update>

  <select id="selectCountByCondition" resultType="int">
    select
      count(*)
    from ${issSchema}.mmp_card_purchase_record
    where 1=1
    <if test="userId != null">
      and user_id = #{userId}
    </if>
    <if test="cardId != null">
      and card_id = #{cardId}
    </if>
    <if test="issueType != null">
      and issue_type = #{issueType}
    </if>
    <if test="status != null">
      and status = #{status}
    </if>
    <if test="outTradeSeq != null">
      and out_trade_seq = #{outTradeSeq}
    </if>
    <if test="paymentStatus != null">
      and payment_status = #{paymentStatus}
    </if>
  </select>

  <select id="selectTimeoutUnpayOrder" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_purchase_record
    where status = 0
    and payment_status = 0
    and merge_pay_origin = 0
    and create_time &lt;= #{endTime}
    and create_time &gt; #{startTime}
    and id &gt;= #{id}
    order by id asc
    limit #{limit}
  </select>

</mapper>