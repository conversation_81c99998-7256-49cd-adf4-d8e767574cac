package com.extracme.evcard.rpc.vipcard.dao;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.rpc.vipcard.model.City;

import java.util.List;
import java.util.Set;

public interface CityMapper {

    /**
     * 根据id查询城市.<br>
     * @param id
     * @return
     */
    City selectByCityid(@Param("cityId")Long cityId);
    
    /**
     * 根据区域id查询城市.<br>
     * @param areaId
     * @return
     */
    City selectByAreaid(@Param("areaId")String areaId);

    /**
     * 根据城市名称查询城市.<br>
     * @param city
     * @return
     */
    City selectByCityName(@Param("city")String city);

    /**
     * 批量根据城市Id查询城市.<br>
     * @param cityIds
     * @return
     */
    List<City> selectByCityIds(@Param("cityIds") Set<Long> cityIds);

}