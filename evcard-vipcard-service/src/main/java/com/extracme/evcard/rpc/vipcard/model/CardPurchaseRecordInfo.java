package com.extracme.evcard.rpc.vipcard.model;

import lombok.Data;

import java.math.BigDecimal;


/**
 * 会员卡片信息
 * <AUTHOR>
 * @Discription
 * @date 2020/12/28
 */
@Data
public class CardPurchaseRecordInfo extends MmpCardPurchaseRecord {
    /**
     * 购卡条件： 0 无  1 学生卡
     */
    private Integer purchaseType;

    /**
     * 付费会员卡：：卡片类型 0周卡 1月卡 季卡
     */
    private Integer cardType;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 付费会员卡：：享有折扣，1~99整数
     */
    private Integer discount;

    /**
     * 付费会员卡：：单笔订单最大抵扣金额，单位：元
     */
    private BigDecimal maxValue;

    /**
     *付费会员卡：：订单时长限制，单位分钟
     */
    private Integer durationLimit;

    /**
     * 付费会员卡：：卡片配置信息-可用区域限制
     */
    private String cityLimit;

    /**
     * 付费会员卡：卡片配置信息-车型限制
     */
    private String vehicleModelLimit;

    /**
     * 商品车型
     */
    private String goodsModelId;

    /**
     * 付费会员卡：门店限制列表
     */
    private String storeIds;

    /**
     * 付费会员卡：卡片配置信息-产品线限制
     */
    private String rentMethod;

    private String orgId;

    private String orgName;
}
