<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.SuixiangCardBaseMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.SuixiangCardBase">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_name" jdbcType="VARCHAR" property="cardName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="advance_notice_time" jdbcType="TIMESTAMP" property="advanceNoticeTime" />
    <result column="sale_start_time" jdbcType="TIMESTAMP" property="saleStartTime" />
    <result column="sale_end_time" jdbcType="TIMESTAMP" property="saleEndTime" />
    <result column="effective_days" jdbcType="INTEGER" property="effectiveDays" />
    <result column="valid_days_type" jdbcType="INTEGER" property="validDaysType" />
    <result column="init_stock" jdbcType="INTEGER" property="initStock" />
    <result column="stock" jdbcType="INTEGER" property="stock" />
    <result column="sales" jdbcType="INTEGER" property="sales" />
    <result column="display_flag" jdbcType="INTEGER" property="displayFlag" />
    <result column="single_order_duration" jdbcType="DECIMAL" property="singleOrderDuration" />
    <result column="style_type" jdbcType="INTEGER" property="styleType" />
    <result column="back_url" jdbcType="VARCHAR" property="backUrl" />
    <result column="rules" jdbcType="VARCHAR" property="rules" />
    <result column="holiday_available" jdbcType="INTEGER" property="holidayAvailable"/>
    <result column="unavailable_date" jdbcType="VARCHAR" property="unavailableDate"/>
    <result column="card_status" jdbcType="INTEGER" property="cardStatus" />
    <result column="purchase_limit_num" jdbcType="INTEGER" property="purchaseLimitNum"/>
    <result column="merge_flag" jdbcType="INTEGER" property="mergeFlag"/>
    <result column="landing_page_flag" jdbcType="INTEGER" property="landingPageFlag"/>
    <result column="vehicle_brand_ids" jdbcType="VARCHAR" property="vehicleBrandIds"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    id, card_name, org_id, city_id, advance_notice_time, sale_start_time, sale_end_time, 
    effective_days, valid_days_type, init_stock, stock, sales, display_flag, single_order_duration, 
    style_type, back_url, rules,holiday_available,unavailable_date, card_status, purchase_limit_num,create_time, create_oper_id, create_oper_name,
    update_time, update_oper_id, update_oper_name, is_deleted, merge_flag, landing_page_flag, vehicle_brand_ids
  </sql>
  <select id="selectByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardBaseExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from suixiang_card_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from suixiang_card_base
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    delete from suixiang_card_base
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardBase">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    insert into suixiang_card_base (id, card_name, org_id, 
      city_id, advance_notice_time, sale_start_time, 
      sale_end_time, effective_days, valid_days_type, 
      init_stock, stock, sales, 
      display_flag, single_order_duration, style_type, 
      back_url, rules, card_status, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name, 
      is_deleted)
    values (#{id,jdbcType=BIGINT}, #{cardName,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, 
      #{cityId,jdbcType=VARCHAR}, #{advanceNoticeTime,jdbcType=TIMESTAMP}, #{saleStartTime,jdbcType=TIMESTAMP}, 
      #{saleEndTime,jdbcType=TIMESTAMP}, #{effectiveDays,jdbcType=INTEGER}, #{validDaysType,jdbcType=INTEGER}, 
      #{initStock,jdbcType=INTEGER}, #{stock,jdbcType=INTEGER}, #{sales,jdbcType=INTEGER}, 
      #{displayFlag,jdbcType=INTEGER}, #{singleOrderDuration,jdbcType=DECIMAL}, #{styleType,jdbcType=INTEGER}, 
      #{backUrl,jdbcType=VARCHAR}, #{rules,jdbcType=VARCHAR}, #{cardStatus,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardBase" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    insert into suixiang_card_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cardName != null">
        card_name,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="advanceNoticeTime != null">
        advance_notice_time,
      </if>
      <if test="saleStartTime != null">
        sale_start_time,
      </if>
      <if test="saleEndTime != null">
        sale_end_time,
      </if>
      <if test="effectiveDays != null">
        effective_days,
      </if>
      <if test="validDaysType != null">
        valid_days_type,
      </if>
      <if test="initStock != null">
        init_stock,
      </if>
      <if test="stock != null">
        stock,
      </if>
      <if test="sales != null">
        sales,
      </if>
      <if test="displayFlag != null">
        display_flag,
      </if>
      <if test="singleOrderDuration != null">
        single_order_duration,
      </if>
      <if test="styleType != null">
        style_type,
      </if>
      <if test="backUrl != null">
        back_url,
      </if>
      <if test="rules != null">
        rules,
      </if>
      <if test="holidayAvailable != null">
        holiday_available,
      </if>
      <if test="unavailableDate != null">
        unavailable_date,
      </if>
      <if test="purchaseLimitNum != null">
        purchase_limit_num,
      </if>
      <if test="cardStatus != null">
        card_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>

      <if test="mergeFlag != null">
        merge_flag,
      </if>
      <if test="landingPageFlag != null">
        landing_page_flag,
      </if>
      <if test="vehicleBrandIds != null">
        vehicle_brand_ids,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cardName != null">
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="advanceNoticeTime != null">
        #{advanceNoticeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="saleStartTime != null">
        #{saleStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="saleEndTime != null">
        #{saleEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDays != null">
        #{effectiveDays,jdbcType=INTEGER},
      </if>
      <if test="validDaysType != null">
        #{validDaysType,jdbcType=INTEGER},
      </if>
      <if test="initStock != null">
        #{initStock,jdbcType=INTEGER},
      </if>
      <if test="stock != null">
        #{stock,jdbcType=INTEGER},
      </if>
      <if test="sales != null">
        #{sales,jdbcType=INTEGER},
      </if>
      <if test="displayFlag != null">
        #{displayFlag,jdbcType=INTEGER},
      </if>
      <if test="singleOrderDuration != null">
        #{singleOrderDuration,jdbcType=DECIMAL},
      </if>
      <if test="styleType != null">
        #{styleType,jdbcType=INTEGER},
      </if>
      <if test="backUrl != null">
        #{backUrl,jdbcType=VARCHAR},
      </if>
      <if test="rules != null">
        #{rules,jdbcType=VARCHAR},
      </if>
      <if test="holidayAvailable != null">
        #{holidayAvailable,jdbcType=INTEGER},
      </if>
      <if test="unavailableDate != null">
        #{unavailableDate,jdbcType=VARCHAR},
      </if>
      <if test="purchaseLimitNum != null">
        #{purchaseLimitNum,jdbcType=INTEGER},
      </if>
      <if test="cardStatus != null">
        #{cardStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>

      <if test="mergeFlag != null">
        #{mergeFlag,jdbcType=INTEGER},
      </if>
      <if test="landingPageFlag != null">
        #{landingPageFlag,jdbcType=INTEGER},
      </if>
      <if test="vehicleBrandIds != null">
        #{vehicleBrandIds,jdbcType=VARCHAR},
      </if>

    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardBaseExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    select count(*) from suixiang_card_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    update suixiang_card_base
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardName != null">
        card_name = #{record.cardName,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=VARCHAR},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=VARCHAR},
      </if>
      <if test="record.advanceNoticeTime != null">
        advance_notice_time = #{record.advanceNoticeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.saleStartTime != null">
        sale_start_time = #{record.saleStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.saleEndTime != null">
        sale_end_time = #{record.saleEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.effectiveDays != null">
        effective_days = #{record.effectiveDays,jdbcType=INTEGER},
      </if>
      <if test="record.validDaysType != null">
        valid_days_type = #{record.validDaysType,jdbcType=INTEGER},
      </if>
      <if test="record.initStock != null">
        init_stock = #{record.initStock,jdbcType=INTEGER},
      </if>
      <if test="record.stock != null">
        stock = #{record.stock,jdbcType=INTEGER},
      </if>
      <if test="record.sales != null">
        sales = #{record.sales,jdbcType=INTEGER},
      </if>
      <if test="record.displayFlag != null">
        display_flag = #{record.displayFlag,jdbcType=INTEGER},
      </if>
      <if test="record.singleOrderDuration != null">
        single_order_duration = #{record.singleOrderDuration,jdbcType=DECIMAL},
      </if>
      <if test="record.styleType != null">
        style_type = #{record.styleType,jdbcType=INTEGER},
      </if>
      <if test="record.backUrl != null">
        back_url = #{record.backUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.rules != null">
        rules = #{record.rules,jdbcType=VARCHAR},
      </if>
      <if test="record.cardStatus != null">
        card_status = #{record.cardStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null">
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null">
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null">
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null">
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    update suixiang_card_base
    set id = #{record.id,jdbcType=BIGINT},
      card_name = #{record.cardName,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=VARCHAR},
      city_id = #{record.cityId,jdbcType=VARCHAR},
      advance_notice_time = #{record.advanceNoticeTime,jdbcType=TIMESTAMP},
      sale_start_time = #{record.saleStartTime,jdbcType=TIMESTAMP},
      sale_end_time = #{record.saleEndTime,jdbcType=TIMESTAMP},
      effective_days = #{record.effectiveDays,jdbcType=INTEGER},
      valid_days_type = #{record.validDaysType,jdbcType=INTEGER},
      init_stock = #{record.initStock,jdbcType=INTEGER},
      stock = #{record.stock,jdbcType=INTEGER},
      sales = #{record.sales,jdbcType=INTEGER},
      display_flag = #{record.displayFlag,jdbcType=INTEGER},
      single_order_duration = #{record.singleOrderDuration,jdbcType=DECIMAL},
      style_type = #{record.styleType,jdbcType=INTEGER},
      back_url = #{record.backUrl,jdbcType=VARCHAR},
      rules = #{record.rules,jdbcType=VARCHAR},
      card_status = #{record.cardStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardBase">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    update suixiang_card_base
    <set>
      <if test="cardName != null">
        card_name = #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="advanceNoticeTime != null">
        advance_notice_time = #{advanceNoticeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="saleStartTime != null">
        sale_start_time = #{saleStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="saleEndTime != null">
        sale_end_time = #{saleEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDays != null">
        effective_days = #{effectiveDays,jdbcType=INTEGER},
      </if>
      <if test="validDaysType != null">
        valid_days_type = #{validDaysType,jdbcType=INTEGER},
      </if>
      <if test="initStock != null">
        init_stock = #{initStock,jdbcType=INTEGER},
      </if>
      <if test="stock != null">
        stock = #{stock,jdbcType=INTEGER},
      </if>
      <if test="sales != null">
        sales = #{sales,jdbcType=INTEGER},
      </if>
      <if test="displayFlag != null">
        display_flag = #{displayFlag,jdbcType=INTEGER},
      </if>
      <if test="singleOrderDuration != null">
        single_order_duration = #{singleOrderDuration,jdbcType=DECIMAL},
      </if>
      <if test="styleType != null">
        style_type = #{styleType,jdbcType=INTEGER},
      </if>
      <if test="backUrl != null">
        back_url = #{backUrl,jdbcType=VARCHAR},
      </if>
      <if test="rules != null">
        rules = #{rules,jdbcType=VARCHAR},
      </if>
      <if test="cardStatus != null">
        card_status = #{cardStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardBase">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:28:14 CST 2023.
    -->
    update suixiang_card_base
    set card_name = #{cardName,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=VARCHAR},
      advance_notice_time = #{advanceNoticeTime,jdbcType=TIMESTAMP},
      sale_start_time = #{saleStartTime,jdbcType=TIMESTAMP},
      sale_end_time = #{saleEndTime,jdbcType=TIMESTAMP},
      effective_days = #{effectiveDays,jdbcType=INTEGER},
      valid_days_type = #{validDaysType,jdbcType=INTEGER},
      init_stock = #{initStock,jdbcType=INTEGER},
      stock = #{stock,jdbcType=INTEGER},
      sales = #{sales,jdbcType=INTEGER},
      display_flag = #{displayFlag,jdbcType=INTEGER},
      single_order_duration = #{singleOrderDuration,jdbcType=DECIMAL},
      style_type = #{styleType,jdbcType=INTEGER},
      back_url = #{backUrl,jdbcType=VARCHAR},
      rules = #{rules,jdbcType=VARCHAR},
      card_status = #{cardStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey2" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardBase">
    update suixiang_card_base
    set card_name = #{cardName,jdbcType=VARCHAR},
    <if test="landingPageFlag != null">
      landing_page_flag = #{landingPageFlag,jdbcType=INTEGER},
    </if>
    org_id = #{orgId,jdbcType=VARCHAR},
    city_id = #{cityId,jdbcType=VARCHAR},
    advance_notice_time = #{advanceNoticeTime,jdbcType=TIMESTAMP},
    sale_start_time = #{saleStartTime,jdbcType=TIMESTAMP},
    sale_end_time = #{saleEndTime,jdbcType=TIMESTAMP},
    valid_days_type = #{validDaysType,jdbcType=INTEGER},
    init_stock = #{initStock,jdbcType=INTEGER},
    stock = #{stock,jdbcType=INTEGER},
    display_flag = #{displayFlag,jdbcType=INTEGER},
    single_order_duration = #{singleOrderDuration,jdbcType=DECIMAL},
    style_type = #{styleType,jdbcType=INTEGER},
    back_url = #{backUrl,jdbcType=VARCHAR},
    rules = #{rules,jdbcType=VARCHAR},
    holiday_available = #{holidayAvailable,jdbcType=INTEGER},
    unavailable_date = #{unavailableDate,jdbcType=VARCHAR},
    purchase_limit_num = #{purchaseLimitNum,jdbcType=INTEGER},
    card_status = #{cardStatus,jdbcType=INTEGER},
    update_oper_id = #{updateOperId,jdbcType=BIGINT},
    update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="countForPage" parameterType="map" resultType="java.lang.Integer">
    select
    count(1)
    from suixiang_card_base
    where is_deleted = 0
    <if test="record.cardName != null">
      and card_name like concat('%',#{record.cardName,jdbcType=VARCHAR},'%')
    </if>
    <if test="record.id != null">
      and id = #{record.id,jdbcType=BIGINT}
    </if>
    <if test="record.cardStatus != null">
      and card_status = #{record.cardStatus,jdbcType=INTEGER}
    </if>
    <if test="record.orgId != null">
      and org_id = #{record.orgId,jdbcType=VARCHAR}
    </if>
    <if test="record.saleStartTime != null">
      and sale_start_time >= #{record.saleStartTime,jdbcType=TIMESTAMP}
    </if>
    <if test="record.saleEndTime != null">
      and sale_end_time &lt;= #{record.saleEndTime,jdbcType=TIMESTAMP}
    </if>
    <if test="record.advanceNoticeTimeStart != null">
      and advance_notice_time >= #{record.advanceNoticeTimeStart,jdbcType=TIMESTAMP}
    </if>
    <if test="record.advanceNoticeTimeEnd != null">
      and advance_notice_time &lt;= #{record.advanceNoticeTimeEnd,jdbcType=TIMESTAMP}
    </if>
    <if test="record.cityId != null">
      and (city_id like concat('%',#{record.cityId,jdbcType=VARCHAR},'%') or city_id like concat('%','-1','%'))
    </if>
    <if test="record.displayFlag != null">
      and display_flag = #{record.displayFlag,jdbcType=INTEGER}
    </if>
    <if test="record.saleMin != null">
      and sales >= #{record.saleMin,jdbcType=INTEGER}
    </if>
    <if test="record.saleMax != null">
      and sales &lt;= #{record.saleMax,jdbcType=INTEGER}
    </if>
    <if test="record.leftStockMin != null">
      and stock >= #{record.leftStockMin,jdbcType=INTEGER}
    </if>
    <if test="record.leftStockMax != null">
      and stock &lt;= #{record.leftStockMax,jdbcType=INTEGER}
    </if>
  </select>

    <select id="selectForPage" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from suixiang_card_base
        where is_deleted = 0
        <if test="record.cardName != null">
          and card_name like concat('%',#{record.cardName,jdbcType=VARCHAR},'%')
        </if>
        <if test="record.id != null">
          and id = #{record.id,jdbcType=BIGINT}
        </if>
        <if test="record.cardStatus != null">
          and card_status = #{record.cardStatus,jdbcType=INTEGER}
        </if>
        <if test="record.orgId != null">
          and org_id = #{record.orgId,jdbcType=VARCHAR}
        </if>
        <if test="record.saleStartTime != null">
          and sale_start_time >= #{record.saleStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="record.saleEndTime != null">
          and sale_end_time &lt;= #{record.saleEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="record.advanceNoticeTimeStart != null">
          and advance_notice_time >= #{record.advanceNoticeTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="record.advanceNoticeTimeEnd != null">
          and advance_notice_time &lt;= #{record.advanceNoticeTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="record.cityId != null">
          and (city_id like concat('%',#{record.cityId,jdbcType=VARCHAR},'%') or city_id like concat('%','-1','%'))
        </if>
        <if test="record.displayFlag != null">
          and display_flag = #{record.displayFlag,jdbcType=INTEGER}
        </if>
        <if test="record.saleMin != null">
          and sales >= #{record.saleMin,jdbcType=INTEGER}
        </if>
        <if test="record.saleMax != null">
          and sales &lt;= #{record.saleMax,jdbcType=INTEGER}
        </if>
        <if test="record.leftStockMin != null">
          and stock >= #{record.leftStockMin,jdbcType=INTEGER}
        </if>
        <if test="record.leftStockMax != null">
          and stock &lt;= #{record.leftStockMax,jdbcType=INTEGER}
        </if>
        order by field(card_status,3,2,1,4), update_time desc
        <if test="page != null">
            LIMIT #{page.offSet},#{page.limitSet}
        </if>
    </select>

  <select id="getEffectiveSuiXiangCardList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from suixiang_card_base a
    where
    a.is_deleted = 0
    and a.card_status = 3
    and a.display_flag = 1
    AND a.stock >= 0
    AND (a.city_id LIKE CONCAT('%', #{cityId}, '%') or a.city_id LIKE CONCAT('%', '-1', '%'))
    order by  a.sale_start_time ASC
  </select>

  <update id="updateStock" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardBase">
    update suixiang_card_base
    set stock = stock - #{stock,jdbcType=INTEGER}
        <if test="sales != null">
          ,sales = sales + #{sales,jdbcType=INTEGER}
        </if>
        <if test="updateOperId != null">
          ,update_oper_id = #{updateOperId,jdbcType=BIGINT}
        </if>
        <if test="updateOperName != null">
          ,update_oper_name = #{updateOperName,jdbcType=VARCHAR}
        </if>
    where id = #{id,jdbcType=BIGINT}
          and stock - #{stock,jdbcType=INTEGER} >= 0
  </update>


</mapper>