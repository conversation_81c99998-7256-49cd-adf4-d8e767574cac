package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.dto.CardActivityQueryDto;
import com.extracme.evcard.rpc.vipcard.dto.CardModelQueryDto;
import com.extracme.evcard.rpc.vipcard.model.CardSalesActivityCardModel;
import com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity;
import com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivityInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface MmpCardSalesActivityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_sales_activity
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_sales_activity
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    int insert(MmpCardSalesActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_sales_activity
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    int insertSelective(MmpCardSalesActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_sales_activity
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    MmpCardSalesActivity selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_sales_activity
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    int updateByPrimaryKeySelective(MmpCardSalesActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_sales_activity
     *
     * @mbggenerated Thu Dec 24 14:39:34 CST 2020
     */
    int updateByPrimaryKey(MmpCardSalesActivity record);

    /**
     * 创建活动
     * @param record
     * @return
     */
    int add(MmpCardSalesActivity record);

    /**
     * 逻辑删除活动
     * @param id
     * @param operId
     * @param operUser
     * @return
     */
    int disable(@Param("id") Long id, @Param("operId") Long operId, @Param("operUser") String operUser);

    /**
     * 修改活动状态
     * @param id
     * @param operId
     * @param operUser
     * @return
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("operId") Long operId, @Param("operUser") String operUser);

    /**
     * 修改活动状态
     * @param id
     * @param operId
     * @param operUser
     * @return
     */
    int updateStatusStart(@Param("id") Long id, @Param("operId") Long operId, @Param("operUser") String operUser);

    /**
     * 修改活动状态
     * @param id
     * @param operId
     * @param operUser
     * @return
     */
    int updateStatusStop(@Param("id") Long id, @Param("operId") Long operId, @Param("operUser") String operUser);

    /**
     * 列表查询活动配置信息
     * @param queryDto
     * @return
     * @remark (包含模糊查询，配置系统使用), 交易不可用
     */
    List<MmpCardSalesActivityInfo> selectList(CardActivityQueryDto queryDto);

    /**
     * 根据城市查询开始预售/已上架的会员卡购买活动
     * @param cityId
     * @return
     */
    List<CardSalesActivityCardModel> getEffectiveActivityCardList(@Param("cityId") Long cityId);

    /**
     * 根据活动id查询
     * @param id
     * @return
     */
    CardSalesActivityCardModel getEffectiveActivityCardDetailById(@Param("id") Long id);

    /**
     * 根据卡id和状态查询
     * @param cardId
     * @param status
     */
    List<MmpCardSalesActivity> getActivityByCardIdAndStatus(@Param("cardId") Long cardId, @Param("status") int status);

    /**
     * 根据订单可购买的进行中的活动
     * @return
     */
    List<CardSalesActivityCardModel> getEffectiveActivityByOrder();

    /**
     * 查询待上架活动
     * @return
     */
    List<MmpCardSalesActivity> selectReadyToStartActivityList(Date date);

    /**
     * 查询待上架活动
     * @return
     */
    List<MmpCardSalesActivity> selectReadyToStopActivityList(Date date);

    /**
     * 批量上架
     * @param ids
     * @param taskStatus
     * @param oriStatus
     * @param operId
     * @param operUser
     * @return
     */
    int batchUpdateActivityStatusStart(@Param("list") List<Long> ids, @Param("activityStatus") Integer taskStatus,
                                  @Param("oriStatus") Integer oriStatus,
                                  @Param("operId") Long operId, @Param("operUser") String operUser);

    /**
     * 批量下架
     * @param ids
     * @param taskStatus
     * @param oriStatus
     * @param operId
     * @param operUser
     * @return
     */
    int batchUpdateActivityStatusStop(@Param("list") List<Long> ids, @Param("activityStatus") Integer taskStatus,
                                       @Param("oriStatus") Integer oriStatus,
                                       @Param("operId") Long operId, @Param("operUser") String operUser);

    /**
     * 查询即将开始活动
     * @return
     */
    List<MmpCardSalesActivity> queryActivityNow();

    /**
     * 更新活动库存、活动状态
     * @param record
     * @return
     */
    int updateActivityStock(MmpCardSalesActivity record);


    List<Long>  queryOrgTimeConflict(@Param("id") Long id, @Param("orgId") String orgId, @Param("cardId") Long cardId,
                                                     @Param("startDate") String startDate, @Param("endDate") String endDate);

    MmpCardSalesActivity selectRunningActivityfOrg(@Param("orgId") String orgId, @Param("cardId") Long cardId);
}