package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.MemberWrapInfoDto;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.MmpUserTagDto;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.IMembershipWrapService;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.vipcard.dto.UnFreezeThenFreezeDiscountAmountDto;
import com.extracme.evcard.rpc.vipcard.dao.*;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.dto.wrap.PurchaseCardInput;
import com.extracme.evcard.rpc.vipcard.enums.CardActivityStatusEnum;
import com.extracme.evcard.rpc.vipcard.enums.CardIssueTypeEnum;
import com.extracme.evcard.rpc.vipcard.enums.CardTypeEnum;
import com.extracme.evcard.rpc.vipcard.model.*;
import com.extracme.evcard.rpc.vipcard.service.inner.MemberCardInnerService;
import com.extracme.evcard.rpc.vipcard.service.inner.OrderPayCheckService;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;


/**
 * <AUTHOR> chennian.
 * @Date ：Created in 16:36 2020/12/24
 */
@Service
public class CardTradeService implements ICardTradeService {

    Logger logger = LoggerFactory.getLogger(CardTradeService.class);

    @Autowired
    private MmpCardPurchaseRecordMapper mmpCardPurchaseRecordMapper;

    @Autowired
    private MmpCardPurchaseRecordLogMapper mmpCardPurchaseRecordLogMapper;

    @Autowired
    private MmpUserCardInfoMapper mmpUserCardInfoMapper;

    @Autowired
    private MmpCardSalesActivityMapper mmpCardSalesActivityMapper;

    @Autowired
    private MmpCardDefMapper mmpCardDefMapper;

    @Autowired
    private MmpUserCardOperationLogMapper mmpUserCardOperationLogMapper;

    @Autowired
    private MemberCardInnerService memberCardInnerService;

    @Resource
    private IMemberShipService memberShipService;
    @Resource
    IMessagepushServ messageServ;

    @Resource
    IMembershipWrapService membershipWrapService;

    @Resource
    OrderPayCheckService orderPayCheckService;

    @Autowired
    IMessagepushServ messagepushServ;

    @Value("${ons.topic.delay}")
    private String delayTopic;

    @Autowired
    private MmpUserCardDiscountInfoMapper mmpUserCardDiscountInfoMapper;

    @Override
    public List<MemberCardPurchaseRecordDto> getMemberCardPurchaseRecord(Long pkId, List<Long> activityIdList) {
        List<MemberCardPurchaseRecordDto> recordDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(activityIdList)) {
            List<MmpCardPurchaseRecord> recordList = mmpCardPurchaseRecordMapper.batchGetRecordByActivityId(pkId, activityIdList);
            if (CollectionUtils.isNotEmpty(recordList)) {
                for (MmpCardPurchaseRecord record : recordList) {
                    MemberCardPurchaseRecordDto dto = new MemberCardPurchaseRecordDto();
                    BeanUtils.copyProperties(record, dto);
                    recordDtoList.add(dto);
                }
            }
        }
        return recordDtoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayCarPurchaseDto payCarPurchaseCallBack(AddWaitPayCarPurchaseRecord callBack) throws BusinessException {
        logger.info("购卡回调, start, input={}", JSON.toJSONString(callBack));
        if(callBack.getPurchaseId() == null) {
            payCarPurchaseCallBackWithoutId(callBack);
        }
        if(callBack.getUserId() == null || callBack.getQuantity() == null
                || callBack.getQuantity() < 0 || StringUtils.isBlank(callBack.getOutTradeSeq()) || callBack == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        MmpCardPurchaseRecord purchaseRecord = mmpCardPurchaseRecordMapper.selectByPrimaryKey(callBack.getPurchaseId());
        if(purchaseRecord == null) {
            throw new BusinessException(-1, "交易记录不存在");
        }

        OperatorDto optUser = new OperatorDto(callBack.getUserId(), "pay-rpc", "evcard-app");
        if(purchaseRecord.getPaymentStatus() == 0) {
            /**
             * 购买记录尚未支付，则完成支付并发放卡片
             * 购买回调，只做发卡，不做活动状态检查和库存检查
             * 存在超卖可能性, 超卖或库存减为0，则立即下架活动
             */
            CardIssueDto issueDto = new CardIssueDto();
            issueDto.setPurchaseId(callBack.getPurchaseId());
            issueDto.setActivityId(purchaseRecord.getCardActivityId());
            issueDto.setUserId(callBack.getUserId());
            issueDto.setQuantity(callBack.getQuantity());
            issueDto.setIssueType(CardIssueTypeEnum.PURCHASE.getType());
            issueDto.setOutTradeSeq(callBack.getOutTradeSeq());
            issueDto.setRealAmount(callBack.getRealAmount());
            issueDto.setDiscountAmount(callBack.getDiscountAmount());
            issueDto.setRemark(CardIssueTypeEnum.PURCHASE.getDesc());
            issueDto.setActiveStartTime(callBack.getActiveStartTime());

            logger.info("付费会员卡发放：issueCard, activityId={}, userId={}, num={}", issueDto.getActivityId(),
                    issueDto.getUserId(), issueDto.getQuantity());
            //执行送卡，不校验活动状态与活动库存
            purchaseRecord = issueCard(issueDto, false, optUser);
        } else {
            logger.error("购卡回调, 购卡记录状态异常, input={}, paymentStatus={}",
                    JSON.toJSONString(callBack), purchaseRecord.getPaymentStatus());
            MmpCardPurchaseRecordLog log = new MmpCardPurchaseRecordLog();
            log.setPurchaseId(purchaseRecord.getId());
            if(purchaseRecord.getPaymentStatus() == -1) {
                log.setOperationType(4);
                log.setContent("无效的支付，订单已经取消，待退款。");
            }else if(purchaseRecord.getPaymentStatus() == 1) {
                log.setOperationType(5);
                log.setContent("重复的支付，订单已支付，待退款。");
            }
            //购卡订单操作日志
            log.setCreateOperId(optUser.getOperatorId());
            log.setCreateOperName(optUser.getOperatorName());
            log.setCreateTime(new Date());
            mmpCardPurchaseRecordLogMapper.insertSelective(log);
        }
        PayCarPurchaseDto payCarPurchaseDto = new PayCarPurchaseDto();
        payCarPurchaseDto.setPurchaseId(purchaseRecord.getId());
        payCarPurchaseDto.setUserCardNo(purchaseRecord.getUserCardNo());
        logger.info("购卡回调, end, input={}", JSON.toJSONString(callBack));
        return payCarPurchaseDto;
    }

    /**
     * 旧版本购卡回调入口，短期保留，发版后可移除
     * @param callBack
     * @return
     * @throws BusinessException
     */
    @Deprecated
    public PayCarPurchaseDto payCarPurchaseCallBackWithoutId(AddWaitPayCarPurchaseRecord callBack) throws BusinessException {
        logger.info("进入旧版本支付回调, input={}", JSON.toJSONString(callBack));
        /**
         * 购买回调，只做发卡，不做活动状态检查和库存检查
         * 存在超卖可能性, 超卖或库存减为0，则立即下架活动
         */
        CardIssueDto issueDto = new CardIssueDto();
        issueDto.setActivityId(callBack.getCardActivityId());
        issueDto.setUserId(callBack.getUserId());
        issueDto.setQuantity(callBack.getQuantity());
        issueDto.setIssueType(CardIssueTypeEnum.PURCHASE.getType());
        issueDto.setOutTradeSeq(callBack.getOutTradeSeq());
        issueDto.setRealAmount(callBack.getRealAmount());
        issueDto.setDiscountAmount(callBack.getDiscountAmount());
        issueDto.setRemark(CardIssueTypeEnum.PURCHASE.getDesc());
        OperatorDto optUser = new OperatorDto(callBack.getUserId(), "pay-rpc", "evcard-app");
        logger.info("付费会员卡发放：issueCard, activityId={}, userId={}, num={}", issueDto.getActivityId(),
                issueDto.getUserId(), issueDto.getQuantity());
        //执行送卡，不校验活动状态与活动库存
        MmpCardPurchaseRecord purchaseRecord = issueCard(issueDto, false, optUser);

        PayCarPurchaseDto payCarPurchaseDto = new PayCarPurchaseDto();
        payCarPurchaseDto.setPurchaseId(purchaseRecord.getId());
        return payCarPurchaseDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userCardHistory(UserCardOperationDto userCardOperationDto) throws BusinessException {
        logger.info("调用卡消费记录：{}",JSON.toJSONString(userCardOperationDto));
        if (userCardOperationDto.getUserCardNo() == null){
            throw new BusinessException(StatusCode.PARAM_EMPTY.getCode(),StatusCode.PARAM_EMPTY.getMsg());
        }
        MmpUserCardOperationLog operationLog = new MmpUserCardOperationLog();
        MmpUserCardInfo mmpUserCardInfo = mmpUserCardInfoMapper.selectByPrimaryKey(userCardOperationDto.getUserCardNo());
        if (mmpUserCardInfo == null){
            return;
        }
        operationLog.setUserCardNo(mmpUserCardInfo.getUserCardNo());
        operationLog.setCardId(mmpUserCardInfo.getCardId());
        operationLog.setCardGroup(mmpUserCardInfo.getCardGroup());
        operationLog.setOperationType(1L);
        operationLog.setOriginSystem(userCardOperationDto.getOriginSystem());
        operationLog.setRefKey(userCardOperationDto.getOrderSeq());
        operationLog.setOrderSeq(userCardOperationDto.getOrderSeq());
        operationLog.setDiscountAmount(userCardOperationDto.getDiscountAmount());
        operationLog.setAmount(userCardOperationDto.getAmount());
        operationLog.setCreateTime(new Date());
        operationLog.setCreateOperName(userCardOperationDto.getAuthId());
        mmpUserCardOperationLogMapper.insertSelective(operationLog);

        MmpUserCardInfo update = new MmpUserCardInfo();
        update.setUserCardNo(mmpUserCardInfo.getUserCardNo());
        update.setTotalDiscountAmount(mmpUserCardInfo.getTotalDiscountAmount().add(userCardOperationDto.getDiscountAmount()));
        mmpUserCardInfoMapper.updateUseCounts(update);

        //查询是否存在折扣上线
        MmpUserCardDiscountInfo mmpUserCardDiscountInfo = mmpUserCardDiscountInfoMapper.userCardDiscount(userCardOperationDto.getUserCardNo());
        if (mmpUserCardDiscountInfo != null){
            mmpUserCardDiscountInfo.setDiscountAmount(mmpUserCardDiscountInfo.getDiscountAmount().add(userCardOperationDto.getDiscountAmount()));
            mmpUserCardDiscountInfo.setUpdateTime(new Date());
            mmpUserCardDiscountInfo.setUpdateOperName(userCardOperationDto.getAuthId());
            mmpUserCardDiscountInfoMapper.updateByPrimaryKeySelective(mmpUserCardDiscountInfo);
        }
    }

    @Override
    public List<MemberCardPurchaseLogDto> queryUserPurchaseListByCardId(Long userId, Long cardId) {
        if (userId == null || cardId == null){
             throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        List<MmpCardPurchaseRecord> list = mmpCardPurchaseRecordMapper.selectLastUserPurchaseRecords(userId, cardId);
        List<MemberCardPurchaseLogDto> result = new ArrayList<>();
        for(MmpCardPurchaseRecord record : list) {
            MemberCardPurchaseLogDto dto = new MemberCardPurchaseLogDto();
            BeanCopyUtils.copyProperties(record, dto);
            dto.setPayTime(DateUtil.getFormatDate(record.getPayTime(), DateUtil.DATE_TYPE8));
            result.add(dto);
        }
        return result;
    }

    @Override
    public PageBeanBO<CardPurchaseListDetailDto> queryPurchaseRecordsPage(CardPurchasePageQueryDto queryDto) {
        if(queryDto == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        PageBeanBO<CardPurchaseListDetailDto> pageBeanBO = new PageBeanBO<>();
        if(queryDto.getPageNum() == null) {
            queryDto.setPageNum(1);
        }
        if(queryDto.getPageSize() == null) {
            queryDto.setPageSize(10);
        }
        Page page;
        if (queryDto.getIsAll() == null || queryDto.getIsAll() == 0) {
            page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize(), false);
        } else {
            page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        }
        List<CardPurchaseRecordInfo> list = mmpCardPurchaseRecordMapper.selectPage( queryDto);
        List<CardPurchaseListDetailDto> result = new ArrayList<>();
        for(CardPurchaseRecordInfo record : list) {
            CardPurchaseListDetailDto dto = new CardPurchaseListDetailDto();
            memberCardInnerService.buildCardPurchaseListView(record, dto);
            result.add(dto);
        }
        PageBO pageBO = new PageBO();
        pageBO.setTotal(page.getTotal());
        pageBO.setPageNum(page.getPageNum());
        pageBO.setPageSize(page.getPageSize());
        pageBeanBO.setPage(pageBO);
        pageBeanBO.setList(result);
        return pageBeanBO;
    }

    private static final int MAX_QUERY_LIMIT = 3000;
    @Override
    public List<CardPurchaseListViewDto> queryPurchaseRecordsList(CardPurchaseListQueryDto queryDto, Long id, Integer limit) {
        if(queryDto == null || limit == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        if(limit > MAX_QUERY_LIMIT) {
            limit = MAX_QUERY_LIMIT;
        }
        List<CardPurchaseRecordInfo> list = mmpCardPurchaseRecordMapper.selectList(queryDto, id, limit);
        List<CardPurchaseListViewDto> result = new ArrayList<>();
        for(CardPurchaseRecordInfo record : list) {
            CardPurchaseListViewDto dto = new CardPurchaseListViewDto();
            memberCardInnerService.buildCardPurchaseListView(record, dto);
            result.add(dto);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchOfferCards(CardBatchOfferDto batchOfferDto, OperatorDto optUser) throws BusinessException {
        if(batchOfferDto == null || CollectionUtils.isEmpty(batchOfferDto.getList())
                || batchOfferDto.getActivityId() == null) {
            throw new BusinessException(StatusCode.PARAM_EMPTY);
        }
        if(batchOfferDto.getList().size() > 10) {
            throw new BusinessException(StatusCode.OVER_BATCH_LIMIT);
        }
        logger.info("批量送卡：size=", batchOfferDto.getList().size());
        for(CardOfferDto dto : batchOfferDto.getList()) {
            CardIssueDto issueDto = new CardIssueDto();
            issueDto.setActivityId(batchOfferDto.getActivityId());
            issueDto.setUserId(dto.getUserId());
            issueDto.setQuantity(dto.getNum());
            issueDto.setIssueType(batchOfferDto.getOfferType());
            issueDto.setOutTradeSeq(batchOfferDto.getRequestId());
            issueDto.setRealAmount(0d);
            issueDto.setDiscountAmount(BigDecimal.ZERO);
            issueDto.setRemark(CardIssueTypeEnum.getIssueType(batchOfferDto.getOfferType()).getDesc());
            //执行送卡，需要校验活动状态与活动库存
            logger.info("批量送卡：issueCard, activityId={}, userId={}, num={}", issueDto.getActivityId(),
                    issueDto.getUserId(), issueDto.getQuantity());
            issueCard(issueDto, true, optUser);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CardPurchaseRecordDto issueCard(CardIssueDto input, OperatorDto optUser) throws BusinessException {
        MmpCardPurchaseRecord purchaseRecord = issueCard(input, true, optUser);
        CardPurchaseRecordDto resultDto = new CardPurchaseRecordDto();
        BeanCopyUtils.copyProperties(purchaseRecord, resultDto);
        return resultDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public PurchaseRecordInfo purchaseCard(PurchaseCardInput input) throws BusinessException {
        return purchaseCard(input, input.getOptUser());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseRecordInfo purchaseCard(PurchaseCardDto input, OperatorDto optUser) throws BusinessException {
        /**
         *  -检查库存，扣减库存
         * 	-不自动下架活动
         * 	-活动库存释放
         */
        if(input.getActivityId() == null || input.getUserId() == null || input.getQuantity() == null
                || input.getQuantity() < 0 || optUser == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        /**
         * 1. 校验活动状态及库存
         */
        //查询活动对应的卡
        MmpCardSalesActivity activity = mmpCardSalesActivityMapper.selectByPrimaryKey(input.getActivityId());
        if (activity == null){
            logger.error("创建购卡订单：活动不存在, input={}", JSON.toJSONString(input));
            throw new BusinessException(StatusCode.ACTIVITY_NOT_EXIST);
        }
        MmpCardDef cardDef = mmpCardDefMapper.selectByPrimaryKey(activity.getCardId());
        if (cardDef == null){
            logger.error("创建购卡订单：卡片不存在, input={}", JSON.toJSONString(input));
            throw new BusinessException(StatusCode.ACTIVITY_NOT_EXIST);
        }

        /**
         * TODO 是否需要增加分布式锁，锁活动ID
         */
        StatusCode checkResult = checkPurchaseCondition(input.getUserId(), input.getQuantity(), 0, activity, cardDef);
        if(checkResult != null) {
            logger.error("创建购卡订单：不满足发卡条件, checkResult={}, input={}", JSON.toJSONString(checkResult),
                    JSON.toJSONString(input));
        }

        /**
         * 2. 新增待支付购卡记录
         */
        MmpCardPurchaseRecord purchaseRecord = new MmpCardPurchaseRecord();
        purchaseRecord.setUserId(input.getUserId());
        purchaseRecord.setCardActivityId(input.getActivityId());
        purchaseRecord.setCardId(activity.getCardId());
        purchaseRecord.setPaymentStatus(0);
        purchaseRecord.setQuantity(input.getQuantity());
        purchaseRecord.setPayTime(new Date());
        purchaseRecord.setRealAmount(input.getRealAmount());
        purchaseRecord.setOrderSeq(ComUtils.createOrderSeq(input.getUserId()));
        purchaseRecord.setCreateOperId(optUser.getOperatorId());
        purchaseRecord.setCreateOperName(optUser.getOperatorName());
        purchaseRecord.setIssueType(0);
        Date nowTime = new Date();
        purchaseRecord.setCreateTime(nowTime);
        purchaseRecord.setMergePayOrigin(input.getMergePayOrigin());
        mmpCardPurchaseRecordMapper.insertSelective(purchaseRecord);
        //购卡订单操作日志
        MmpCardPurchaseRecordLog log = new MmpCardPurchaseRecordLog();
        log.setPurchaseId(purchaseRecord.getId());
        log.setOperationType(0);
        log.setContent("下单");
        log.setCreateOperId(optUser.getOperatorId());
        log.setCreateOperName(optUser.getOperatorName());
        log.setCreateTime(nowTime);
        mmpCardPurchaseRecordLogMapper.insertSelective(log);

        /**
         * 3. 原子操作修改库存, 但不更新售出数量。
         *    库存扣减为0不自动下架活动(排队人数>剩余库存)
         *    发卡时，若库存为0则下架活动。
         */
        MmpCardSalesActivity updateActivity = new MmpCardSalesActivity();
        updateActivity.setId(activity.getId());
        updateActivity.setStock(Long.valueOf(input.getQuantity()));
        updateActivity.setSalesVolume(null);
        updateActivity.setUpdateOperId(optUser.getOperatorId());
        updateActivity.setUpdateOperName(optUser.getOperatorName());
        updateActivity.setUpdateTime(nowTime);
        mmpCardSalesActivityMapper.updateActivityStock(updateActivity);

        PurchaseRecordInfo recordInfo = getPurchaseRecordById(purchaseRecord.getId());
        if (input.getMergePayOrigin().intValue() == 0) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    logger.info("创建订单|推送延时消息，purchaseId= {}.", purchaseRecord.getId());
                    try {
                        //long tm = purchaseRecord.gainCancelTimeMills();
                        recordInfo.setCancelTime(recordInfo.gainCancelTime());
                        logger.info("创建订单|推送延时消息，purchaseId={}, createTime={}, cancelTime={}",
                                recordInfo.getId(), recordInfo.getCreateTime(), recordInfo.getCancelTime());

                        byte[] body = ProtobufUtil.serializeProtobuf(recordInfo);
                        messagepushServ.pushMq(delayTopic, EventEnum.CARD_PURCHASE_CANCEL.getTag(), body,
                                recordInfo.getCancelTime().getTime());
                    } catch (Exception e) {
                        logger.error("创建订单|推送延时消息失败，purchaseId=" + recordInfo.getId(), e);
                    }
                }
            });
        }
        return recordInfo;
    }


    @Override
    public PurchaseRecordInfo getPurchaseRecordById(Long recordId) {
        MmpCardPurchaseRecord record = mmpCardPurchaseRecordMapper.selectByPrimaryKey(recordId);
        if(record == null || !record.getStatus().equals(0)) {
            return null;
        }
        PurchaseRecordInfo recordInfo = new PurchaseRecordInfo();
        BeanCopyUtils.copyProperties(record, recordInfo);
        return recordInfo;
    }

    @Override
    public void updateOutTradeSeq(Long recordId, String outTradeSeq, OperatorDto optUser) throws BusinessException {
        PurchaseRecordInfo record = getPurchaseRecordById(recordId);
        if(record == null) {
            throw new BusinessException(-1, "购卡订单不存在");
        }
        if(record.getPaymentStatus().equals(-1)) {
            throw new BusinessException(-1, "购卡订单已取消");
        }
        if(!record.getPaymentStatus().equals(0)) {
            throw new BusinessException(-1, "购卡订单已支付");
        }
        if(StringUtils.isNotBlank(record.getOutTradeSeq())) {
            /**
             * 调用财务系统接口，校验此交易号是否已经发起第三方支付
             */
            int isPaying = orderPayCheckService.checkPurchasePayingOrNot(record);
            if(isPaying == 1) {
                throw new BusinessException(-1, "购卡订单已在支付中，请稍后重试");
            } else if(isPaying == -1) {
                logger.warn("更新购卡支付流水号，交易号未确认是否已发起支付, recordId={}, outTradeSeq={}",
                        record.getId(), record.getOutTradeSeq());
            }
        }
        MmpCardPurchaseRecord updateRecord = new MmpCardPurchaseRecord();
        updateRecord.setId(recordId);
        updateRecord.setOutTradeSeq(outTradeSeq);
        updateRecord.setUpdateOperId(optUser.getOperatorId());
        updateRecord.setUpdateOperName(optUser.getOperatorName());
        updateRecord.setUpdateTime(new Date());
        mmpCardPurchaseRecordMapper.updateByPrimaryKeySelective(updateRecord);

        logger.warn("更新购卡支付流水号，更新已完成 {}->{}",
                record.getOutTradeSeq(), updateRecord.getOutTradeSeq());
    }

    @Override
    @Transactional
    public void cancelOrder(PurchaseRecordInfo record, OperatorDto optUser) throws BusinessException {
        cancelOrder(record.getId(), null, optUser);
    }

    @Override
    @Transactional
    public void cancelOrder(Long purchaseId, Integer cancelType, OperatorDto optUser) throws BusinessException {
        PurchaseRecordInfo purchaseRecord = getPurchaseRecordById(purchaseId);
        if(purchaseRecord == null || purchaseRecord.getPaymentStatus() != 0) {
            return;
        }
        if(cancelType == null) {
            cancelType = 2;
        }
        boolean inCharge = false;
        String remark = StringUtils.EMPTY;
        if(StringUtils.isNotBlank(purchaseRecord.getOutTradeSeq())) {
            //调用财务系统接口，校验此交易号是否已经发起第三方支付
            int isPaying = orderPayCheckService.checkPurchasePayingOrNot(purchaseRecord);
            inCharge = (isPaying == 1);
            if(isPaying == -1) {
                remark = "，交易号未确认是否已发起支付";
            }
        }

        if(inCharge) {
            //TODO 确认是否需要锁活动库存
            logger.warn("取消订单：购卡订单在支付中，不自动取消，purchaseId={}, outTradeSeq={}",
                    purchaseRecord.getId(), purchaseRecord.getOutTradeSeq());
            return;
        }

        logger.info("取消订单：购卡订单待支付，开始自动取消，purchaseId={}, outTradeSeq={}",
                purchaseRecord.getId(), purchaseRecord.getOutTradeSeq());
        /**
         * 进行订单取消操作
         * 库存释放
         */
        Date nowTime = new Date();
        MmpCardPurchaseRecord updateRecord = new MmpCardPurchaseRecord();
        updateRecord.setId(purchaseRecord.getId());
        updateRecord.setPaymentStatus(-1);
        updateRecord.setCancelTime(nowTime);
        updateRecord.setUpdateOperId(optUser.getOperatorId());
        updateRecord.setUpdateOperName(optUser.getOperatorName());
        updateRecord.setUpdateTime(nowTime);
        mmpCardPurchaseRecordMapper.updateByPrimaryKeySelective(updateRecord);

        MmpCardPurchaseRecordLog log = new MmpCardPurchaseRecordLog();
        log.setPurchaseId(purchaseRecord.getId());
        log.setOperationType(2);
        log.setContent("自动取消" + remark);
        log.setCreateOperId(optUser.getOperatorId());
        log.setCreateOperName(optUser.getOperatorName());
        log.setCreateTime(nowTime);
        mmpCardPurchaseRecordLogMapper.insertSelective(log);

        MmpCardSalesActivity updateActivity = new MmpCardSalesActivity();
        updateActivity.setId(purchaseRecord.getCardActivityId());
        updateActivity.setStock(0 - Long.valueOf(purchaseRecord.getQuantity()));
        updateActivity.setSalesVolume(null);
        updateActivity.setUpdateOperId(optUser.getOperatorId());
        updateActivity.setUpdateOperName(optUser.getOperatorName());
        updateActivity.setUpdateTime(nowTime);
        mmpCardSalesActivityMapper.updateActivityStock(updateActivity);
    }

    @Override
    public void freezeDiscountAmount(FreezeDiscountAmountDto input, OperatorDto operateDto) throws BusinessException {
        Assert.notNull(input.getUserCardNo(), "会员卡号不能为空");
        Assert.notNull(input.getFreezeTime(), "申请冻结时间不能为空");
        BigDecimal frozenAmount = input.getAmount();
        Assert.notNull(frozenAmount, "申请冻结金额不能为空");
        if (frozenAmount.compareTo(BigDecimal.ZERO) <= 0) {
            logger.warn("freezeDiscountAmount：申请冻结金额必须大于0，userCardNo={}，FreezeTime={}.",
                    input.getUserCardNo(), input.getFreezeTime());
            throw new BusinessException("申请冻结金额必须大于0");
        }
        MmpUserCardInfo mmpUserCardInfo = mmpUserCardInfoMapper.selectByPrimaryKey(input.getUserCardNo());
        if (mmpUserCardInfo == null){
            return;
        }
        MmpUserCardDiscountInfo userCardDiscountInfo = mmpUserCardDiscountInfoMapper.getUserDiscountByTime(input.getUserCardNo(),
                input.getFreezeTime());
        if (userCardDiscountInfo != null) {
            if (userCardDiscountInfo.getStatus() == 1) {
                logger.warn("freezeDiscountAmount：申请冻结时间对应的会员卡使用周期无效，userCardNo={}，FreezeTime={}.",
                        input.getUserCardNo(), input.getFreezeTime());
                throw new BusinessException("申请冻结时间对应的会员卡使用周期无效");
            }
            logger.info("申请冻结会员卡累计折扣，userCardNo={}, id:{}, 上限：{}, 累计折扣：{}, 累计冻结：{}, 申请冻结：{}",
                    input.getUserCardNo(), userCardDiscountInfo.getId(), userCardDiscountInfo.getTotalDiscountAmount(),
                    userCardDiscountInfo.getDiscountAmount(), userCardDiscountInfo.getFrozenAmount(), frozenAmount);
            if (userCardDiscountInfo.getDiscountAmount().add(frozenAmount).compareTo(userCardDiscountInfo.getTotalDiscountAmount()) > 0) {
                throw new BusinessException("申请冻结金额超出会员卡周期内累计折扣上限");
            }
            // 冻结折扣金额
            int update = mmpUserCardDiscountInfoMapper.freezeDiscountAmount(userCardDiscountInfo.getId(), frozenAmount,
                    userCardDiscountInfo.getDiscountAmount(),
                    operateDto.getOperatorId(), operateDto.getOperatorName(), new Date());
            if (update == 0) {
                logger.error("申请冻结会员卡累计折扣失败，可能用户会员卡使用详情发生变更，无法进行更新， id:{}", userCardDiscountInfo.getId());
                throw new BusinessException("用户会员卡发生变更，申请冻结会员卡累计折扣失败");
            }
            //操作日志
            MmpUserCardOperationLog operationLog = new MmpUserCardOperationLog();
            operationLog.setUserCardNo(mmpUserCardInfo.getUserCardNo());
            operationLog.setCardId(mmpUserCardInfo.getCardId());
            operationLog.setCardGroup(mmpUserCardInfo.getCardGroup());
            operationLog.setOperationType(3L);
            operationLog.setOriginSystem(operateDto.getOriginSystem());
            operationLog.setRefKey(input.getOrderSeq());
            operationLog.setOrderSeq(input.getOrderSeq());
            operationLog.setDiscountAmount(frozenAmount);
            operationLog.setCreateTime(new Date());
            operationLog.setCreateOperName(operateDto.getOperatorName());
            mmpUserCardOperationLogMapper.insertSelective(operationLog);
        }
    }

    @Override
    public void unfreezeDiscountAmount(UnfreezeDiscountAmountDto input, OperatorDto operateDto) throws BusinessException {
        Assert.notNull(input.getUserCardNo(), "会员卡号不能为空");
        Assert.notNull(input.getFreezeTime(), "申请冻结时间不能为空");
        Assert.notNull(input.getOrderSeq(), "订单号不能为空");
        BigDecimal unfreezeAmount = input.getAmount();
        Assert.notNull(unfreezeAmount, "申请解冻金额不能为空");
        if (unfreezeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            logger.warn("unfreezeDiscountAmount：申请解冻金额必须大于0，userCardNo={}，FreezeTime={}.",
                    input.getUserCardNo(), input.getFreezeTime());
            throw new BusinessException("申请解冻金额必须大于0");
        }
        MmpUserCardInfo mmpUserCardInfo = mmpUserCardInfoMapper.selectByPrimaryKey(input.getUserCardNo());
        if (mmpUserCardInfo == null){
            return;
        }
        MmpUserCardDiscountInfo userCardDiscountInfo = mmpUserCardDiscountInfoMapper.getUserDiscountByTime(input.getUserCardNo(),
                input.getFreezeTime());
        if (userCardDiscountInfo != null) {
            if (userCardDiscountInfo.getStatus() == 1) {
                logger.warn("unfreezeDiscountAmount：申请解冻时间对应的会员卡使用周期无效，userCardNo={}，FreezeTime={}.",
                        input.getUserCardNo(), input.getFreezeTime());
                throw new BusinessException("申请冻结时间对应的会员卡使用周期无效");
            }
            logger.info("申请解冻会员卡冻结金额，userCardNo={}, id:{}, 上限：{}, 累计折扣：{}, 累计冻结：{}, 申请解冻：{}",
                    input.getUserCardNo(), userCardDiscountInfo.getId(), userCardDiscountInfo.getTotalDiscountAmount(),
                    userCardDiscountInfo.getDiscountAmount(), userCardDiscountInfo.getFrozenAmount(), unfreezeAmount);
            if (userCardDiscountInfo.getFrozenAmount().compareTo(unfreezeAmount) < 0) {
                throw new BusinessException("申请解冻金额大于冻结金额");
            }
            if (userCardDiscountInfo.getDiscountAmount().compareTo(unfreezeAmount) < 0) {
                throw new BusinessException("申请解冻金额大于累计折扣金额");
            }
            // 解冻折扣金额
            int update = mmpUserCardDiscountInfoMapper.unfreezeDiscountAmount(userCardDiscountInfo.getId(), unfreezeAmount,
                    userCardDiscountInfo.getDiscountAmount(),
                    operateDto.getOperatorId(), operateDto.getOperatorName(), new Date());
            if (update == 0) {
                logger.error("申请解冻会员卡累计折扣失败，可能用户会员卡使用详情发生变更，无法进行更新， id:{}", userCardDiscountInfo.getId());
                throw new BusinessException("用户会员卡发生变更，申请解冻会员卡冻结金额失败");
            }
            MmpUserCardOperationLog operationLog = new MmpUserCardOperationLog();
            operationLog.setUserCardNo(mmpUserCardInfo.getUserCardNo());
            operationLog.setCardId(mmpUserCardInfo.getCardId());
            operationLog.setCardGroup(mmpUserCardInfo.getCardGroup());
            operationLog.setOperationType(4L);
            operationLog.setOriginSystem(operateDto.getOriginSystem());
            operationLog.setRefKey(input.getOrderSeq());
            operationLog.setOrderSeq(input.getOrderSeq());
            operationLog.setDiscountAmount(unfreezeAmount);
            operationLog.setCreateTime(new Date());
            operationLog.setCreateOperName(operateDto.getOperatorName());
            mmpUserCardOperationLogMapper.insertSelective(operationLog);

            //待作废状态下且解冻金额清0，则作废此卡片。
            tryCompleteCancelCard(mmpUserCardInfo, userCardDiscountInfo.getId(), operateDto);
        }
    }

    @Override
    public void deductFrozenDiscountAmount(DeductFrozenDiscountAmountDto input, OperatorDto operateDto) throws BusinessException {
        Assert.notNull(input.getUserCardNo(), "会员卡号不能为空");
        Assert.notNull(input.getFreezeTime(), "申请冻结时间不能为空");
        Assert.notNull(input.getOrderSeq(), "订单号不能为空");
        BigDecimal deductAmount = input.getAmount();
        Assert.notNull(deductAmount, "申请扣除冻结金额不能为空");
        if (deductAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("申请扣除冻结金额必须大于0");
        }
        MmpUserCardOperationLog operationLog = new MmpUserCardOperationLog();
        MmpUserCardInfo mmpUserCardInfo = mmpUserCardInfoMapper.selectByPrimaryKey(input.getUserCardNo());
        if (mmpUserCardInfo == null){
            return;
        }
        operationLog.setUserCardNo(mmpUserCardInfo.getUserCardNo());
        operationLog.setCardId(mmpUserCardInfo.getCardId());
        operationLog.setCardGroup(mmpUserCardInfo.getCardGroup());
        operationLog.setOperationType(1L);
        operationLog.setOriginSystem(operateDto.getOriginSystem());
        operationLog.setRefKey(input.getOrderSeq());
        operationLog.setOrderSeq(input.getOrderSeq());
        operationLog.setDiscountAmount(deductAmount);
        operationLog.setCreateTime(new Date());
        operationLog.setCreateOperName(operateDto.getOperatorName());
        mmpUserCardOperationLogMapper.insertSelective(operationLog);

        MmpUserCardInfo updateInfo = new MmpUserCardInfo();
        updateInfo.setUserCardNo(mmpUserCardInfo.getUserCardNo());
        updateInfo.setTotalDiscountAmount(mmpUserCardInfo.getTotalDiscountAmount().add(deductAmount));
        mmpUserCardInfoMapper.updateUseCounts(updateInfo);

        MmpUserCardDiscountInfo userCardDiscountInfo = mmpUserCardDiscountInfoMapper.getUserDiscountByTime(input.getUserCardNo(),
                input.getFreezeTime());
        if (userCardDiscountInfo != null) {
            if (userCardDiscountInfo.getStatus() == 1) {
                logger.warn("deductFrozenDiscountAmount：申请扣除冻结金额的时间对应的会员卡使用周期无效，userCardNo={}，FreezeTime={}.",
                        input.getUserCardNo(), input.getFreezeTime());
                throw new BusinessException("申请冻结时间对应的会员卡使用周期无效");
            }
            logger.info("申请扣除会员卡冻结金额，id:{}, 上限：{}, 累计折扣：{}, 累计冻结：{}, 申请扣除：{}",
                    userCardDiscountInfo.getId(), userCardDiscountInfo.getTotalDiscountAmount(), userCardDiscountInfo.getDiscountAmount(),
                    userCardDiscountInfo.getFrozenAmount(), deductAmount);
            if (userCardDiscountInfo.getFrozenAmount().compareTo(deductAmount) < 0) {
                logger.warn("deductFrozenDiscountAmount：冻结金额小于申请扣除金额，userCardNo={}，FreezeTime={}.",
                        input.getUserCardNo(), input.getFreezeTime());
                throw new BusinessException("冻结金额小于申请扣除金额");
            }
            // 扣除冻结金额
            int update = mmpUserCardDiscountInfoMapper.deductFrozenDiscountAmount(userCardDiscountInfo.getId(), deductAmount,
                    userCardDiscountInfo.getFrozenAmount(),
                    operateDto.getOperatorId(), operateDto.getOperatorName(), new Date());
            if (update == 0) {
                logger.error("申请扣除会员卡冻结金额失败，可能用户会员卡使用详情发生变更，无法进行更新， id:{}", userCardDiscountInfo.getId());
                throw new BusinessException("用户会员卡发生变更，申请扣除会员卡冻结金额失败");
            }
            //待作废状态下且解冻金额清0，则作废此卡片。
            tryCompleteCancelCard(mmpUserCardInfo, userCardDiscountInfo.getId(), operateDto);
        }
    }

    protected void tryCompleteCancelCard(MmpUserCardInfo mmpUserCardInfo, Long discountRecordId, OperatorDto operator) {
        /**
         * 若卡片再待作废状态下且解冻金额清0，则作废此卡片。
         */
        if(mmpUserCardInfo.getActiveFlag().equals(Constants.TO_DISABLE_TAG)) {
            MmpUserCardDiscountInfo discountInfo = mmpUserCardDiscountInfoMapper.selectByPrimaryKey(discountRecordId);
            if (discountInfo != null && discountInfo.getFrozenAmount().compareTo(BigDecimal.ZERO) <= 0) {
                memberCardInnerService.cancelMemberCard(mmpUserCardInfo, operator);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unfreezeThenFreezeDiscountAmount(UnFreezeThenFreezeDiscountAmountDto input, OperatorDto operateDto) throws BusinessException {
        logger.info("unfreezeThenFreezeDiscountAmount开始,input={},operateDto={}", JSON.toJSONString(input), JSON.toJSONString(operateDto));
        UnfreezeDiscountAmountDto unfreezeDiscountAmountDto = UnFreezeThenFreezeDiscountAmountDto.getUnfreezeDiscountAmountDto(input);
        unfreezeDiscountAmount(unfreezeDiscountAmountDto,operateDto);

        FreezeDiscountAmountDto freezeDiscountAmountDto = UnFreezeThenFreezeDiscountAmountDto.getFreezeDiscountAmountDto(input);
        freezeDiscountAmount(freezeDiscountAmountDto,operateDto);

    }


    @Override
    public GetCardDiscountAmountInfoDto getCardDiscountAmountInfo(Long userCardNo, Date queryTime) {
        GetCardDiscountAmountInfoDto amountInfoDto = null;
        MmpUserCardDiscountInfo userCardDiscountInfo = mmpUserCardDiscountInfoMapper.getUserDiscountByTime(userCardNo, queryTime);
        if (userCardDiscountInfo != null) {
            amountInfoDto = new GetCardDiscountAmountInfoDto();
            amountInfoDto.setTotalDiscountAmount(userCardDiscountInfo.getTotalDiscountAmount());
            amountInfoDto.setDiscountAmount(userCardDiscountInfo.getDiscountAmount());
            amountInfoDto.setFrozenAmount(userCardDiscountInfo.getFrozenAmount());
            amountInfoDto.setCanUseAmount(userCardDiscountInfo.getTotalDiscountAmount().subtract(userCardDiscountInfo.getDiscountAmount()));
        }
        return null;
    }

    /**
     * 付费会员卡-卡片发放
     * @param input
     *        activityId 活动id，必须
     *        offerType 发卡方式：0购买 1赠送 2兑换， 必须
     *        userId 用户id， 必须
     *        num 张数， 必须
     *        outTradeSeq 交易流水号或请求ID， 必须
     *        remark 备注
     * @param checkActivity 是否检查库存
     * @param optUser
     *        offerType 0购买 1赠送 2兑换
     * @param optUser
     * @return
     * @remark 注意：不校验活动单人购买上限
     *         购卡支付回调调用该方法进行重写(支付回调扣库存，存在超卖)
     * @since 1.1.0
     */
    @Transactional(rollbackFor = Exception.class)
    public MmpCardPurchaseRecord issueCard(CardIssueDto input, boolean checkActivity, OperatorDto optUser) throws BusinessException {
        if(input.getActivityId() == null || input.getUserId() == null || input.getQuantity() == null
                || input.getQuantity() < 0 || StringUtils.isBlank(input.getOutTradeSeq()) || optUser == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        if(input.getIssueType() == 0) {
            if(input.getPurchaseId() == null) {
                logger.error("付费会员卡发放：购卡需要基于购买记录ID，此ID不可为空, input={}", JSON.toJSONString(input));
                throw BusinessRuntimeException.PARAM_EXEPTION;
            }
        }
        /**
         * 1. 校验活动状态及库存
         */
        //查询活动对应的卡
        MmpCardSalesActivity activity = mmpCardSalesActivityMapper.selectByPrimaryKey(input.getActivityId());
        if (activity == null){
            logger.error("付费会员卡发放：活动不存在, input={}", JSON.toJSONString(input));
            throw new BusinessException(StatusCode.ACTIVITY_NOT_EXIST);
        }
        MmpCardDef cardDef = mmpCardDefMapper.selectByPrimaryKey(activity.getCardId());
        if (cardDef == null){
            logger.error("付费会员卡发放：卡片不存在, input={}", JSON.toJSONString(input));
            throw new BusinessException(StatusCode.ACTIVITY_NOT_EXIST);
        }
        if(checkActivity) {
            StatusCode checkResult = checkPurchaseCondition(input.getUserId(), input.getQuantity(), input.getIssueType(),
                    activity, cardDef);
            if(checkResult != null) {
                logger.error("付费会员卡发放：不满足发卡条件, checkResult={}, input={}", JSON.toJSONString(checkResult),
                        JSON.toJSONString(input));
            }
        }
        /**
         * 2. 卡发放，判断是续卡or发放新卡
         */
        //根据卡片类型和发卡张数，计算本次发放的有效天数
        CardTypeEnum cardType = CardTypeEnum.getCardType(cardDef.getCardType());
        int effectiveDays = cardType.getDays();
        if (input.getQuantity() != null && effectiveDays > 0){
            effectiveDays = effectiveDays * input.getQuantity();
        }
        MmpUserCardInfo mmpUserCardInfo = mmpUserCardInfoMapper.queryUserVipCardByIdAndCard(input.getUserId(), activity.getCardId());
        if (mmpUserCardInfo == null) {
            //卡不存在，为用户创建一张新卡
            mmpUserCardInfo = createUserCard(input, cardDef, effectiveDays, optUser);
        }else {
            //卡存在，为用户做续卡
            mmpUserCardInfo = renewUserCard(input, mmpUserCardInfo, effectiveDays, optUser);
        }
        /**
         * 3. 保存购买记录
         */
        MmpCardPurchaseRecord cardPurchaseRecord = savePurchaseRecords(input, mmpUserCardInfo, input.getPurchaseId(), optUser);

        /**
         * 4. 写入周期内折扣记录
         */
        createDiscountRecords(input, mmpUserCardInfo, cardDef, mmpUserCardInfo.getStartTime(), optUser);

        /**
         * 5. 更新活动APP
         *    更新活动库存，若库存不足，则下架活动
         *    变更为原子操作修改库存
         */
        boolean soldOut = false;
        MmpCardSalesActivity updateActivity = new MmpCardSalesActivity();
        updateActivity.setId(activity.getId());
        if(input.getIssueType() == 0) {
            //购卡，发卡时无需再更新库存，只累计售出数量
            updateActivity.setStock(null);
            soldOut = (activity.getStock() <= 0);
        }else {
            //非购卡，发卡时扣减库存、累计售出数量
            updateActivity.setStock(Long.valueOf(input.getQuantity()));
            soldOut = (activity.getStock() != null && activity.getStock() - input.getQuantity() < 1);
        }
        updateActivity.setSalesVolume(Long.valueOf(input.getQuantity()));
        if (soldOut) {
            //下架活动
            updateActivity.setEndTime(new Date());
            updateActivity.setActivityStatus(CardActivityStatusEnum.STOPPED.getStatus());
        }
        updateActivity.setUpdateOperId(optUser.getOperatorId());
        updateActivity.setUpdateOperName(optUser.getOperatorName());
        updateActivity.setUpdateTime(new Date());
        mmpCardSalesActivityMapper.updateActivityStock(updateActivity);

        if(input.getIssueType() != 0) {
            /**
             * 6. 后台发卡完成经推送卡片到账通知
             *    推送失败不影响流程
             */
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                  @Override
                  public void afterCommit() {
                      try {
                          MembershipBasicInfo member = memberShipService.getUserBasicInfoByPkId(input.getUserId());
                          messageServ.push(member.getAuthId(), member.getMembershipType(), 214, 2, 5, null, "vipcard-rpc");
                          // 短信优化需求：https://wiki.gcsrental.com/wiki/spaces/T-1681206805087/pages/6524a9addaa28a4b20f6abb9，不再发送
//                          messageServ.asyncSendSMSTemplateForMarket(member.getMobilePhone(), 228, null, "vipCard-rpc");
                      }catch (Exception ex) {
                          logger.error("发卡完成，推送app通知失败，input=" + JSON.toJSONString(input), ex);
                      }
                  }
              }
            );
        }
        return cardPurchaseRecord;
    }


    /**
     * 检查购卡条件
     * @param userId
     * @param quantity
     * @param issueType
     * @param activity
     * @param cardDef
     * @return
     */
    public StatusCode checkPurchaseCondition(Long userId, int quantity, int issueType,
                                                MmpCardSalesActivity activity, MmpCardDef cardDef) {
        if(!CardActivityStatusEnum.RUNNING.getStatus().equals(activity.getActivityStatus())) {
            return StatusCode.ACTIVITY_NOT_RUNNING;
        }
        if(activity.getStock() < quantity) {
            return StatusCode.UNDER_STOCK;
        }
        //仅自行购卡需要验证学生卡购卡条件
        //学生卡则校验学生证照片以及年龄
        if (cardDef.getPurchaseType() == 1 && issueType == 0) {
            boolean checkStudent = false;
            MemberWrapInfoDto member = membershipWrapService.getMemberWrapInfoByPkId(userId);
            if (member.getAge() != null &&  0 < member.getAge() && member.getAge() < 24) {
                MmpUserTagDto mmpUserTagDto = memberShipService.queryUserTagByAuthId(member.getAuthId());
                String studentCardUrl = mmpUserTagDto.getStudentCardUrl();
                if (StringUtils.isNotBlank(studentCardUrl)) {
                    checkStudent = true;
                }
            }
            if(!checkStudent) {
                return StatusCode.UNDER_STOCK;
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public MmpUserCardInfo createUserCard(CardIssueDto input, MmpCardDef mmpCardDef, int effectiveDays, OperatorDto optUser) {
        logger.info("卡发放：为用户发放新卡, input={}", JSON.toJSONString(input));
        MmpUserCardInfo mmpUserCardInfo = new MmpUserCardInfo();
        mmpUserCardInfo.setUserId(input.getUserId());
        mmpUserCardInfo.setCardGroup(3);
        mmpUserCardInfo.setCardName(mmpCardDef.getCardName());
        mmpUserCardInfo.setCardStatus(1);
        mmpUserCardInfo.setCardId(mmpCardDef.getCardId());
        /**
         * 卡的有效期
         */
        Date startTime = new Date();
        //若提供了卡片激活时间，则起始时间为发卡时间
        if(input.getActiveStartTime() != null) {
            startTime = input.getActiveStartTime();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.add(Calendar.DATE, effectiveDays);
        mmpUserCardInfo.setStartTime(startTime);
        mmpUserCardInfo.setExpiresTime(calendar.getTime());
        /**
         * 卡片发放信息
         */
        mmpUserCardInfo.setCreateOperId(optUser.getOperatorId());
        mmpUserCardInfo.setCreateOperName(optUser.getOperatorName());
        mmpUserCardInfo.setCreateTime(new Date());
        mmpUserCardInfo.setUpdateOperId(optUser.getOperatorId());
        mmpUserCardInfo.setUpdateOperName(optUser.getOperatorName());
        mmpUserCardInfoMapper.insertSelective(mmpUserCardInfo);

        //写入卡操作记录-卡发放
        String content = CardIssueTypeEnum.getIssueType(input.getIssueType()).getDesc();
        memberCardInnerService.saveCardPurchaseLog(mmpUserCardInfo, content, optUser);
        return mmpUserCardInfo;
    }


    @Transactional(rollbackFor = Exception.class)
    public MmpUserCardInfo renewUserCard(CardIssueDto input, MmpUserCardInfo mmpUserCardInfo, int effectiveDays, OperatorDto optUser) {
        logger.info("卡发放：为用户续卡, input={}", JSON.toJSONString(input));
        Date now = new Date();
        MmpUserCardInfo updateUserCard = new MmpUserCardInfo();
        updateUserCard.setUserCardNo(mmpUserCardInfo.getUserCardNo());
        /**
         * 计算卡片的有效期
         */
        Calendar calendar = Calendar.getInstance();
        /**
         * **
         */
        Date effectiveStartTime = now;
        //** 变更，若有指定生效开始时间，则以此时间作为续卡时间 **
        if(input.getActiveStartTime() != null) {
            effectiveStartTime = input.getActiveStartTime();
        }
        if (mmpUserCardInfo.getCardStatus().equals(1) && mmpUserCardInfo.getExpiresTime().getTime() >= effectiveStartTime.getTime()){
            //生效中的卡片，有效期起始时间为上次过期时间
            effectiveStartTime = mmpUserCardInfo.getExpiresTime();
        }else {
            //已到期的卡片，有效期起始时间为当前时间(激活时间)
            //effectiveStartTime = now;
            updateUserCard.setCardStatus(1);
        }
        calendar.setTime(effectiveStartTime);
        calendar.add(Calendar.DATE, effectiveDays);
        Date expiresTime = calendar.getTime();
        updateUserCard.setExpiresTime(expiresTime);
        //操作人
        updateUserCard.setUpdateOperId(optUser.getOperatorId());
        updateUserCard.setUpdateOperName(optUser.getOperatorName());
        updateUserCard.setUpdateTime(now);
        mmpUserCardInfoMapper.updateByPrimaryKeySelective(updateUserCard);

        //写入卡操作记录-卡续期
        String content = CardIssueTypeEnum.getIssueType(input.getIssueType()).getDesc();
        content += "，续卡" + effectiveDays + "天";
        memberCardInnerService.saveCardPurchaseLog(mmpUserCardInfo, content, optUser);

        mmpUserCardInfo.setStartTime(effectiveStartTime);
        mmpUserCardInfo.setExpiresTime(expiresTime);
        return mmpUserCardInfo;
    }

    /**
     * 保存购买记录
     * @param input
     * @param mmpUserCardInfo
     * @param optUser
     */
    @Transactional(rollbackFor = Exception.class)
    public MmpCardPurchaseRecord savePurchaseRecords(CardIssueDto input, MmpUserCardInfo mmpUserCardInfo,
                                                     Long purchaseId, OperatorDto optUser) {
        Date nowTime = new Date();
        MmpCardPurchaseRecord purchaseRecord = new MmpCardPurchaseRecord();
        MmpCardPurchaseRecordLog log = new MmpCardPurchaseRecordLog();
        if(purchaseId == null) {
            purchaseRecord.setUserId(input.getUserId());
            purchaseRecord.setCardActivityId(input.getActivityId());
            purchaseRecord.setCardId(mmpUserCardInfo.getCardId());
            purchaseRecord.setPaymentStatus(1);
            purchaseRecord.setQuantity(input.getQuantity());
            purchaseRecord.setPayTime(new Date());
            purchaseRecord.setRealAmount(input.getRealAmount());
            purchaseRecord.setOutTradeSeq(input.getOutTradeSeq());
            purchaseRecord.setUserCardNo(String.valueOf(mmpUserCardInfo.getUserCardNo()));
            purchaseRecord.setStartTime(mmpUserCardInfo.getStartTime());
            purchaseRecord.setEndTime(mmpUserCardInfo.getExpiresTime());
            purchaseRecord.setOrderSeq(ComUtils.createOrderSeq(input.getUserId()));
            purchaseRecord.setCreateOperId(optUser.getOperatorId());
            purchaseRecord.setCreateOperName(optUser.getOperatorName());
            purchaseRecord.setUpdateOperId(optUser.getOperatorId());
            purchaseRecord.setUpdateOperName(optUser.getOperatorName());
            purchaseRecord.setIssueType(input.getIssueType());
            purchaseRecord.setCreateTime(nowTime);
            purchaseRecord.setUpdateTime(nowTime);
            mmpCardPurchaseRecordMapper.insertSelective(purchaseRecord);
            log.setOperationType(3);
            log.setContent("卡发放");
        } else {
            purchaseRecord = new MmpCardPurchaseRecord();
            purchaseRecord.setId(purchaseId);
            purchaseRecord.setPaymentStatus(1);
            purchaseRecord.setPayTime(new Date());
            purchaseRecord.setRealAmount(input.getRealAmount());
            purchaseRecord.setOutTradeSeq(input.getOutTradeSeq());
            purchaseRecord.setUserCardNo(String.valueOf(mmpUserCardInfo.getUserCardNo()));
            purchaseRecord.setStartTime(mmpUserCardInfo.getStartTime());
            purchaseRecord.setEndTime(mmpUserCardInfo.getExpiresTime());
            purchaseRecord.setUpdateOperId(optUser.getOperatorId());
            purchaseRecord.setUpdateOperName(optUser.getOperatorName());
            purchaseRecord.setUpdateTime(nowTime);
            mmpCardPurchaseRecordMapper.updateByPrimaryKeySelective(purchaseRecord);
            log.setOperationType(1);
            log.setContent("支付完成, 交易号" + input.getOutTradeSeq() + "，卡号" + purchaseRecord.getUserCardNo());
        }
        log.setPurchaseId(purchaseRecord.getId());
        log.setOutTradeSeq(input.getOutTradeSeq());
        log.setCreateOperId(optUser.getOperatorId());
        log.setCreateOperName(optUser.getOperatorName());
        log.setCreateTime(nowTime);
        mmpCardPurchaseRecordLogMapper.insertSelective(log);

        purchaseRecord = mmpCardPurchaseRecordMapper.selectByPrimaryKey(purchaseRecord.getId());
        return purchaseRecord;
    }

    @Transactional(rollbackFor = Exception.class)
    public void createDiscountRecords(CardIssueDto input, MmpUserCardInfo mmpUserCardInfo, MmpCardDef mmpCardDef,
                                      Date effectiveStartTime, OperatorDto optUser) {

        Date now = new Date();
        Date discountTime = now;
        if(input.getActiveStartTime() != null) {
            discountTime = input.getActiveStartTime();
        }
        CardTypeEnum cardType = CardTypeEnum.getCardType(mmpCardDef.getCardType());
        Calendar discountCalender = Calendar.getInstance();
        discountCalender.setTime(effectiveStartTime);
        if (mmpCardDef.getTotalDiscountAmount() == null || mmpCardDef.getTotalDiscountAmount() <= 0){
            return;
        }
        for (int i = 0; i < input.getQuantity(); i++){
            //购买数量
            MmpUserCardDiscountInfo userCardDiscountInfo = new MmpUserCardDiscountInfo();
            userCardDiscountInfo.setUserCardNo(mmpUserCardInfo.getUserCardNo());
            userCardDiscountInfo.setStartTime(discountCalender.getTime());
            discountCalender.add(Calendar.DATE, cardType.getDays());
            userCardDiscountInfo.setEndTime(discountCalender.getTime());
            userCardDiscountInfo.setTotalDiscountAmount(new BigDecimal(mmpCardDef.getTotalDiscountAmount()));
            //---> 待修改： 此处扣除额度，续卡(延期的场景) 并未在相应的折扣周期中做扣除
            if (discountTime.compareTo(userCardDiscountInfo.getStartTime())>=0 && discountTime.before(userCardDiscountInfo.getEndTime())){
                userCardDiscountInfo.setDiscountAmount(input.getDiscountAmount());
            }
            userCardDiscountInfo.setCreateOperId(optUser.getOperatorId());
            userCardDiscountInfo.setCreateOperName(optUser.getOperatorName());
            userCardDiscountInfo.setUpdateOperId(optUser.getOperatorId());
            userCardDiscountInfo.setUpdateOperName(optUser.getOperatorName());
            userCardDiscountInfo.setCreateTime(now);
            userCardDiscountInfo.setUpdateTime(now);
            mmpUserCardDiscountInfoMapper.insertSelective(userCardDiscountInfo);
        }
    }
}
