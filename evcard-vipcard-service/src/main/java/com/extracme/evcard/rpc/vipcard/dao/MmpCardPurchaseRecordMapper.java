package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.dto.CardPurchaseListQueryDto;
import com.extracme.evcard.rpc.vipcard.dto.CardPurchasePageQueryDto;
import com.extracme.evcard.rpc.vipcard.dto.QueryCardPurchaseInfoDto;
import com.extracme.evcard.rpc.vipcard.model.CardPurchaseRecordInfo;
import com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface MmpCardPurchaseRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpCardPurchaseRecord record);

    int insertSelective(MmpCardPurchaseRecord record);

    MmpCardPurchaseRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpCardPurchaseRecord record);

    int updateByPrimaryKey(MmpCardPurchaseRecord record);

    /**
     * 根据条件查询
     * @param mmpCardPurchaseRecord
     * @return
     */
    List<MmpCardPurchaseRecord> selectBySelective(MmpCardPurchaseRecord mmpCardPurchaseRecord);

    /**
     * 根据活动id批量查询购买记录
     * @param userId
     * @param activityIdList
     * @return
     */
    List<MmpCardPurchaseRecord> batchGetRecordByActivityId(@Param("userId") Long userId,
                                                           @Param("activityIdList") List<Long> activityIdList);

    /**
     * 查询用户指定卡的最新购买记录
     * @param userId
     * @param cardId
     * @return
     * @remark 最多查询前200条
     */
    List<MmpCardPurchaseRecord> selectLastUserPurchaseRecords(@Param("userId") Long userId, @Param("cardId") Long cardId);

    /**
     * 查询购买记录(分页)， 业务系统使用
     * @param queryDto
     * @return
     */
    List<CardPurchaseRecordInfo> selectPage(CardPurchasePageQueryDto queryDto);

    /**
     *
     * @param queryDto
     * @return
     */
    List<CardPurchaseRecordInfo> selectList(@Param("condition") CardPurchaseListQueryDto queryDto,
                                            @Param("id")Long id, @Param("limit") Integer limit);

    /**
     *
     * @param queryCardPurchaseInfoDto
     * @return
     */
    List<MmpCardPurchaseRecord> selectByUserId(QueryCardPurchaseInfoDto queryCardPurchaseInfoDto);

    /**
     *
     * @param
     * @return
     */
    List<MmpCardPurchaseRecord> selectByUserId2(@Param("pkId") Long pkId);

    /**
     *
     * @param orderSeq
     * @return
     */
    MmpCardPurchaseRecord selectByOrderSeq(String orderSeq);


    /**
     * 查询时间范围内未读的发卡记录
     * @param userId
     * @param startDate
     * @param limit
     * @return
     */
    List<CardPurchaseRecordInfo> selectUnreadCardOfferList(@Param("userId") Long userId,
                                                      @Param("startDate") Date startDate, @Param("limit") Integer limit);

    /**
     * 将新发卡记录标记为已读
     * @param ids
     * @return
     */
    int readCards(@Param("list") List ids);

    /**
     * 根据条件查询数量
     * @param mmpCardPurchaseRecord
     * @return
     */
    int selectCountByCondition(MmpCardPurchaseRecord mmpCardPurchaseRecord);

    /**
     * 查询时间段内未自动取消的订单
     * @param id
     * @param limit
     * @param startTime
     * @param endTime
     * @return
     */
    List<MmpCardPurchaseRecord> selectTimeoutUnpayOrder(@Param("id")Long id,
                                                        @Param("limit")Integer limit,
                                                        @Param("startTime")Date startTime,
                                                        @Param("endTime")Date endTime);
}