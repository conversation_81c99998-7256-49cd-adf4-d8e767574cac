package com.extracme.evcard.rpc.vipcard.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardUseMapper;
import com.extracme.evcard.rpc.vipcard.enums.IsDeleteEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardStatusEnum;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUse;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseExample;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/18
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "vipcard-rpc-suiXiangCardOverTimeNoticeJob",
        cron = "0 0 8 * * ? *", description = "随享卡即将到期提醒任务", overwrite = true)
public class SuiXiangCardOverTimeNoticeJob implements SimpleJob {

    @Resource
    private SuixiangCardUseMapper suixiangCardUseMapper;

    @Resource
    private IMemberShipService memberShipService;

    @Resource
    private IMessagepushServ messageServ;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("随享卡即将到期提醒任务begin");

        // 推出7天后的0点和23：59:59，作为过期时间的查询条件
        Date expiresBeginTime = DateUtil.localDateTimeToDate(LocalDateTime.of(LocalDate.now(), LocalTime.MIN).plusDays(7));
        Date expiresEndTime = DateUtil.localDateTimeToDate(LocalDateTime.of(LocalDate.now(), LocalTime.MAX).plusDays(7));

        // 查询suixiang_card_use表
        SuixiangCardUseExample example = new SuixiangCardUseExample();
        example.createCriteria()
                .andCardTypeEqualTo(1) // 卡类别：1随享卡
                .andCardStatusEqualTo(SuiXiangCardStatusEnum.EFFECTIVE.getStatus())
                .andExpiresTimeBetween(expiresBeginTime, expiresEndTime)
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardUse> list = suixiangCardUseMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            log.info("没有7天后即将到期的随享卡");
        }
        List<Long> idList = list.stream().map(item -> item.getId()).collect(Collectors.toList());

        log.info("需要处理的7天后即将到期的随享卡的提醒数为：{}，idList：{}", list.size(), JSON.toJSONString(idList));
        // 【出行卡即将到期】消息中心+push+短信 距离到期日还剩7天（短信文案需告知用户卡的剩余天数、到期剩余天数）【EVCARD租车】尊敬的<名字><先生/女士>，您的<X>天随享卡还有7天到期，剩余<Y>天尚未使用哦，建议您尽快使用以免卡片过期。
        // 尊敬的<name>先生/女士，您的<rentDays>天随享卡还有7天到期，剩余<leftRentDays>天尚未使用哦，建议您尽快使用以免卡片过期。
        for (SuixiangCardUse item : list) {
            try {
                Map<String, String> param = new HashMap<>();
                MembershipBasicInfo member = memberShipService.getUserBasicInfoByPkId(item.getUserId());
                if (member == null) {
                    log.info("根据userId[{}]查询不到会员信息", item.getUserId());
                    continue;
                }
                if (item.getInitDays() == null || item.getAvailableDays() == null) {
                    log.info("suixiang_card_use表的init_days或available_days字段为空！id[{}]", item.getId());
                    continue;
                }

                param.put("name", member.getName() == null ? "" : member.getName());
                param.put("rentDays", item.getInitDays().toString());
                param.put("leftRentDays", item.getAvailableDays().toString());

                log.info("给用户发送随享卡7天后即将到期提醒！userId[{}]", item.getUserId());
                // 消息中心+push
                messageServ.push(member.getAuthId(), member.getMembershipType(), 202, 2, param, "vipCard-rpc");
                // 短信
                messageServ.syncSendSMSTemplate(member.getMobilePhone(), 202, param, "vipCard-rpc");
            } catch (Exception e) {
                log.error("随享卡即将到期提醒，单笔处理异常！id[{}]userId[{}]", item.getId(), item.getUserId(), e);
                continue;
            }
        }

        log.info("随享卡即将到期提醒任务end");
    }
}
