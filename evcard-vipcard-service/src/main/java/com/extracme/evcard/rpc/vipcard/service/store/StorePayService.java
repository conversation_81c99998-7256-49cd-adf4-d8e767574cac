package com.extracme.evcard.rpc.vipcard.service.store;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.rest.MdRestClient;
import com.extracme.evcard.rpc.vipcard.rest.entity.PayOrderIsPayingOrNotRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Slf4j
@Component
public class StorePayService {
    @Resource
    private MdRestClient mdRestClient;


    /**
     * 判断门店支付订单是否
     * @param payOrderNo
     * @return
     */
    public boolean isOrderPayingOrNot(String payOrderNo) throws BusinessException {
        if(StringUtils.isBlank(payOrderNo)) {
            return false;
        }
        try {
            PayOrderIsPayingOrNotRes res = mdRestClient.orderIsPayingOrNot(payOrderNo);
            if(res != null && res.getCode() == 0 && res.getData() != null) {
                int isPaying = res.getData().getIsPaying();
                return (isPaying == 1);
            }
            log.error("payOrderIsPayingOrNot：获取支付订单状态-失败, payOrderNo={}, resp={}", payOrderNo,
                    JSON.toJSONString(res));
        }catch (Exception ex) {
            log.error("payOrderIsPayingOrNot：获取支付订单状态-是否已发起第三方支付异常, payOrderNo=" + payOrderNo, ex);
            throw ex;
        }
        throw new BusinessException(-1, "获取支付发起状态失败");
    }
}
