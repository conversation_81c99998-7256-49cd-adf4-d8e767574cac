package com.extracme.evcard.rpc.vipcard.util;

import redis.clients.jedis.Jedis;

public class SimpleMethodLimiter {

    /**
     * 判断是否允许执行请求方法
     * 该方法通过Redis来限制请求的访问频率，使用计数器模式进行限流控制
     *
     * @param jedis        Redis客户端实例，用于操作Redis数据库
     * @param redisKey     Redis中的键，用于标识每个请求的计数器
     * @param limitRequest 允许的最大请求次数
     * @param timeout      计数器的过期时间，单位为秒
     * @return 如果当前请求次数未超过允许的最大次数，则返回true；否则返回false
     */
    public static boolean allowMethod(Jedis jedis, String redisKey, int limitRequest, int timeout) {
        // 增加计数器的值并返回新的计数
        long currentCount = jedis.incr(redisKey);
        if (timeout != 0) {
            if (currentCount == 1) {
                // 设置过期时间，避免长时间占用Redis资源, 注意 ：这里如果方法操作时间过长，这里会redis会失效
                jedis.expire(redisKey, timeout);
            }
        }
        // 如果当前计数超过允许的最大请求次数
        if (currentCount > limitRequest) {
            // 回退计数器，将其减少到增量之前的值
            jedis.decr(redisKey);
            // 返回false，表示请求被拒绝
            return false;
        }
        // 返回true，表示请求被允许
        return true;
    }

    /**
     * 释放资源，减少计数器，
     * 如果计数器为0，则删除Redis中的键，释放资源
     *
     * @param jedis
     * @param redisKey
     */
    public static void releaseMethod(Jedis jedis, String redisKey) {
        if (jedis != null) {
            Long decr = jedis.decr(redisKey);// 方法调用结束，减少计数器
            if (decr == 0) {
                jedis.del(redisKey);// 如果计数器为0，则删除Redis中的键，释放资源
                jedis.close();
            }
        }
    }
}
