package com.extracme.evcard.rpc.vipcard.util.compare;

import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardInfoDto;

import java.util.Comparator;

public class SuiXiangCardInfoComparator implements Comparator<SuixiangCardInfoDto> {

    @Override
    public int compare(SuixiangCardInfoDto o1, SuixiangCardInfoDto o2) {
        if (o1.getCardBaseId() > o2.getCardBaseId()) {
            return -1;
        } else if (o1.getCardBaseId() < o2.getCardBaseId()) {
            return -1;
        } else {
            return o1.getRentDays() - o2.getRentDays();
        }
    }

}
