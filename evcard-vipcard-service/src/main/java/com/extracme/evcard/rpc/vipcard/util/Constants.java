package com.extracme.evcard.rpc.vipcard.util;

import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardStatusEnum;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/24
 */
public class Constants {
    /**
     * 状态码：OK
     */
    public static final Integer STATUS_OK = 0;

    public static final String STR_COMMA = ",";

    public static final String STR_COMMA_ZH = "，";

    public static final String STR_SPLIT_ZH = "、";

    public static final String[] RENT_METHODS = {"立即用车(分时)", "预约用车(分时)", "立即用车(日租)", "预约用车(日租)", "门店日租"};

    public static final String[] RENT_METHOD_GROUPS = {"时租", "日租"};
    public static final Integer RENT_METHOD_GROUP_TIME = 1;
    public static final Integer RENT_METHOD_GROUP_DAILY = 2;

    public static final Integer[] RENT_METHODS_OF_TIME = {0};
    public static final Integer[] RENT_METHODS_OF_DAILY = {2, 3, 4};

    public static final Integer RENT_METHOD_MD = 4;


    public static final String[] CARD_PREFIX = {"", "B", "P", "V"};

    public static final String[] CARD_TYPE_NAMES = {"周卡", "月卡", "季卡"};

    public static final String[] PURCHASE_TYPE_NAMES = {"标准卡", "学生卡"};

    public static final String[] ISSUE_TYPE_NAMES = {"购买", "赠送", "积分兑换"};

    public static final String[] PURCHASE_STATUS_NAMES = {"已取消", "待支付", "已支付"};

    /**
     * 待作废标记
     */
    public static final Integer TO_DISABLE_TAG = 1;

    /**
     * 支付订单号前缀-门店
     */
    public static final String PREFIX_PAY_ORDER_NO = "MP";

    /**
     * 操作员姓名的长度
     */
    public static final int OPER_NAME_LENGTH = 128;

    /**
     * 操作员id -1，代表本系统（一般是批处理任务使用）
     */
    public static final long OPER_ID_SYSTEM = -1L;

    /**
     * 操作员姓名 system
     */
    public static final String OPER_NAME_SYSTEM = "system";

    /**
     * 本系统代号
     */
    public static final String VIPCARD_SYSTEM_CODE = "evcard-vipcard-rpc";

    /**
     * 随享卡 使用表前缀
     */
    public static final String SXK_USEID_PREFIX = "SXK_USEID";


    public static final Integer IDCARD_STATUS_PASS =4;
    public static final Integer LICENCE_STATUS_PASS =3;

    //随享卡 生效中
    public static final Integer[]  SUIXIANGCARD_STATUS_EFFECTIVE= {SuiXiangCardStatusEnum.EFFECTIVE.getStatus()};
    //随享卡 已冻结
    public static final Integer[]  SUIXIANGCARD_STATUS_FROZENED= {SuiXiangCardStatusEnum.FROZENED.getStatus(),SuiXiangCardStatusEnum.AUTO_FROZENED.getStatus()};
    //随享卡 已失效
    public static final Integer[]  SUIXIANGCARD_STATUS_INVALIDITY= {SuiXiangCardStatusEnum.EXPIRED.getStatus(),SuiXiangCardStatusEnum.RETURN.getStatus(),
            SuiXiangCardStatusEnum.DISCARD.getStatus(),SuiXiangCardStatusEnum.EXPIRED_FROZENED.getStatus(),SuiXiangCardStatusEnum.USED.getStatus()};

}
