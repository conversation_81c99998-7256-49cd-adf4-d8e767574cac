<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.MmpCardConfigOperationLogMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.MmpCardConfigOperationLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="config_id" property="configId" jdbcType="BIGINT" />
    <result column="config_type" property="configType" jdbcType="INTEGER" />
    <result column="operate_type" property="operateType" jdbcType="INTEGER" />
    <result column="operation_content" property="operationContent" jdbcType="VARCHAR" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    id, config_id, config_type, operate_type, operation_content, misc_desc, status, create_time, 
    create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_config_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    delete from ${issSchema}.mmp_card_config_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardConfigOperationLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    insert into ${issSchema}.mmp_card_config_operation_log (id, config_id, config_type, 
      operate_type, operation_content, misc_desc, 
      status, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{configId,jdbcType=BIGINT}, #{configType,jdbcType=INTEGER}, 
      #{operateType,jdbcType=INTEGER}, #{operationContent,jdbcType=VARCHAR}, #{miscDesc,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardConfigOperationLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    insert into ${issSchema}.mmp_card_config_operation_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="configId != null" >
        config_id,
      </if>
      <if test="configType != null" >
        config_type,
      </if>
      <if test="operateType != null" >
        operate_type,
      </if>
      <if test="operationContent != null" >
        operation_content,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="configId != null" >
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="configType != null" >
        #{configType,jdbcType=INTEGER},
      </if>
      <if test="operateType != null" >
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="operationContent != null" >
        #{operationContent,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardConfigOperationLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    update ${issSchema}.mmp_card_config_operation_log
    <set >
      <if test="configId != null" >
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="configType != null" >
        config_type = #{configType,jdbcType=INTEGER},
      </if>
      <if test="operateType != null" >
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="operationContent != null" >
        operation_content = #{operationContent,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardConfigOperationLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    update ${issSchema}.mmp_card_config_operation_log
    set config_id = #{configId,jdbcType=BIGINT},
      config_type = #{configType,jdbcType=INTEGER},
      operate_type = #{operateType,jdbcType=INTEGER},
      operation_content = #{operationContent,jdbcType=VARCHAR},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="save" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardConfigOperationLog" >
    insert into ${issSchema}.mmp_card_config_operation_log (config_id, config_type,
    operate_type, operation_content,
    status, create_time, create_oper_id,
    create_oper_name, update_time, update_oper_id,
    update_oper_name)
    values (#{configId,jdbcType=BIGINT}, #{configType,jdbcType=INTEGER},
    #{operateType,jdbcType=INTEGER}, #{operationContent,jdbcType=VARCHAR},
    #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT},
    #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT},
    #{updateOperName,jdbcType=VARCHAR})
  </insert>



  <insert id="batchSave" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    insert into ${issSchema}.mmp_card_config_operation_log (config_id, config_type,
    operate_type, operation_content,
    create_time, create_oper_id,
    create_oper_name, update_time, update_oper_id,
    update_oper_name)
    values
    <foreach collection="list" item="item" separator=","  index="index">
      (#{item.configId,jdbcType=BIGINT}, #{item.configType,jdbcType=INTEGER},
      #{item.operateType,jdbcType=INTEGER}, #{item.operationContent,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.createOperId,jdbcType=BIGINT},
      #{item.createOperName,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateOperId,jdbcType=BIGINT},
      #{item.updateOperName,jdbcType=VARCHAR})
    </foreach>

  </insert>

  <select id="selectByConfigTypeAndId" resultType="com.extracme.evcard.rpc.vipcard.dto.CardActivityConfigLogDto">
    select
    DATE_FORMAT(create_time,"%Y-%m-%d %H:%i:%s") as operateTime,
    create_oper_name as createOperName,
    create_oper_id as createOperId,
    "" as orgName,
    operate_type as operateType,
    operation_content as content
    from ${issSchema}.mmp_card_config_operation_log
    where config_type = #{configType}
    <if test="list != null and list.size>0">
      and config_id in
      <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
    order by create_time desc
  </select>

</mapper>