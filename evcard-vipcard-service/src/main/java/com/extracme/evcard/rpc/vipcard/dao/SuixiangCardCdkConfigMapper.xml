<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.SuixiangCardCdkConfigMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfig">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_base_id" jdbcType="BIGINT" property="cardBaseId" />
    <result column="act_name" jdbcType="VARCHAR" property="actName" />
    <result column="card_name" jdbcType="VARCHAR" property="cardName" />
    <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    <result column="qr_code_zip_url" jdbcType="VARCHAR" property="qrCodeZipUrl" />
    <result column="purpose" jdbcType="INTEGER" property="purpose" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    id, card_base_id, act_name, card_name, total_quantity, is_deleted, create_time, create_oper_id,
    create_oper_name, update_time, update_oper_id, update_oper_name, qr_code_zip_url, purpose
  </sql>
  <select id="selectByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from suixiang_card_cdk_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    select
    <include refid="Base_Column_List" />
    from suixiang_card_cdk_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    delete from suixiang_card_cdk_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfig">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    insert into suixiang_card_cdk_config (id, card_base_id, act_name,
    card_name, total_quantity, is_deleted,
    create_time, create_oper_id, create_oper_name,
    update_time, update_oper_id, update_oper_name,
    qr_code_zip_url)
    values (#{id,jdbcType=BIGINT}, #{cardBaseId,jdbcType=BIGINT}, #{actName,jdbcType=VARCHAR},
    #{cardName,jdbcType=VARCHAR}, #{totalQuantity,jdbcType=INTEGER}, #{isDeleted,jdbcType=INTEGER},
    #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR},
    #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR},
    #{qrCodeZipUrl,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfig" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    insert into suixiang_card_cdk_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cardBaseId != null">
        card_base_id,
      </if>
      <if test="actName != null">
        act_name,
      </if>
      <if test="cardName != null">
        card_name,
      </if>
      <if test="totalQuantity != null">
        total_quantity,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
      <if test="qrCodeZipUrl != null">
        qr_code_zip_url,
      </if>
      <if test="purpose != null">
        purpose,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cardBaseId != null">
        #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="actName != null">
        #{actName,jdbcType=VARCHAR},
      </if>
      <if test="cardName != null">
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="totalQuantity != null">
        #{totalQuantity,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="qrCodeZipUrl != null">
        #{qrCodeZipUrl,jdbcType=VARCHAR},
      </if>
      <if test="purpose != null">
        #{purpose,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfigExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    select count(*) from suixiang_card_cdk_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    update suixiang_card_cdk_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardBaseId != null">
        card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.actName != null">
        act_name = #{record.actName,jdbcType=VARCHAR},
      </if>
      <if test="record.cardName != null">
        card_name = #{record.cardName,jdbcType=VARCHAR},
      </if>
      <if test="record.totalQuantity != null">
        total_quantity = #{record.totalQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null">
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null">
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null">
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null">
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.qrCodeZipUrl != null">
        qr_code_zip_url = #{record.qrCodeZipUrl,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    update suixiang_card_cdk_config
    set id = #{record.id,jdbcType=BIGINT},
    card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
    act_name = #{record.actName,jdbcType=VARCHAR},
    card_name = #{record.cardName,jdbcType=VARCHAR},
    total_quantity = #{record.totalQuantity,jdbcType=INTEGER},
    is_deleted = #{record.isDeleted,jdbcType=INTEGER},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    create_oper_id = #{record.createOperId,jdbcType=BIGINT},
    create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
    update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
    qr_code_zip_url = #{record.qrCodeZipUrl,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfig">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    update suixiang_card_cdk_config
    <set>
      <if test="cardBaseId != null">
        card_base_id = #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="actName != null">
        act_name = #{actName,jdbcType=VARCHAR},
      </if>
      <if test="cardName != null">
        card_name = #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="totalQuantity != null">
        total_quantity = #{totalQuantity,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="qrCodeZipUrl != null">
        qr_code_zip_url = #{qrCodeZipUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkConfig">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 18 08:56:52 CST 2024.
    -->
    update suixiang_card_cdk_config
    set card_base_id = #{cardBaseId,jdbcType=BIGINT},
    act_name = #{actName,jdbcType=VARCHAR},
    card_name = #{cardName,jdbcType=VARCHAR},
    total_quantity = #{totalQuantity,jdbcType=INTEGER},
    is_deleted = #{isDeleted,jdbcType=INTEGER},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    create_oper_id = #{createOperId,jdbcType=BIGINT},
    create_oper_name = #{createOperName,jdbcType=VARCHAR},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    update_oper_id = #{updateOperId,jdbcType=BIGINT},
    update_oper_name = #{updateOperName,jdbcType=VARCHAR},
    qr_code_zip_url = #{qrCodeZipUrl,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>