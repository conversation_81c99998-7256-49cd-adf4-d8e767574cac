package com.extracme.evcard.rpc.vipcard.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;


@Slf4j
@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate(ClientHttpRequestFactory httpRequestFactory) {
        return new RestTemplate(httpRequestFactory);
    }

    @Bean
    public ClientHttpRequestFactory simpleClientHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        /**
         * 默认连接超时500ms，读取超时2s
         */
        factory.setReadTimeout(5000);
        factory.setConnectTimeout(500);
        CloseableHttpClient httpClient = getHttpsClient();
        factory.setHttpClient(httpClient);
        return factory;
    }

    public CloseableHttpClient getHttpsClient() {
        CloseableHttpClient httpClient;
        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom().loadTrustMaterial(null, new TrustStrategy() {
                @Override
                public boolean isTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                    return true;
                }
            }).build();
        } catch (Exception e) {
            log.error("init restTemplate falied...", e);
        }


        PoolingHttpClientConnectionManager connectionManager = null;
        try {
            Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
                    .register("https", SSLConnectionSocketFactory.getSocketFactory())
                    .build();
            connectionManager = new PoolingHttpClientConnectionManager(registry);
            //设置整个连接池最大连接数 根据自己的场景决定
            connectionManager.setMaxTotal(128);

            //路由是对maxTotal的细分,针对一个url的最大并发数,每路由最大连接数，默认值是2
            connectionManager.setDefaultMaxPerRoute(32);
        } catch (Exception e) {
            log.error("init restTemplate connectionManager falied...", e);
        }


        httpClient = HttpClients.custom().setSSLContext(sslContext).
                setSSLHostnameVerifier(new NoopHostnameVerifier()).setConnectionManager(connectionManager).build();
        return httpClient;
    }
}
