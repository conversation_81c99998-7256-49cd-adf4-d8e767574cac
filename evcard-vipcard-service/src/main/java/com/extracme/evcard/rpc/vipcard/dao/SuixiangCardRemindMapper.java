package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.model.SuixiangCardRemind;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardRemindExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardRemindMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_remind
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    int countByExample(SuixiangCardRemindExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_remind
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_remind
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    int insert(SuixiangCardRemind record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_remind
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    int insertSelective(SuixiangCardRemind record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_remind
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    List<SuixiangCardRemind> selectByExample(SuixiangCardRemindExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_remind
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    SuixiangCardRemind selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_remind
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    int updateByExampleSelective(@Param("record") SuixiangCardRemind record, @Param("example") SuixiangCardRemindExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_remind
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    int updateByExample(@Param("record") SuixiangCardRemind record, @Param("example") SuixiangCardRemindExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_remind
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    int updateByPrimaryKeySelective(SuixiangCardRemind record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_remind
     *
     * @mbggenerated Wed Jan 11 20:38:07 CST 2023
     */
    int updateByPrimaryKey(SuixiangCardRemind record);
}