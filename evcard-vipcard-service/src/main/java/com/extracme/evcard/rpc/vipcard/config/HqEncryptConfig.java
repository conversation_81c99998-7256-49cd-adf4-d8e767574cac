package com.extracme.evcard.rpc.vipcard.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "hq.encrypt.config")
public class HqEncryptConfig {

    public String outPublicKey;
    public String outPrivateKey;


    public String innerPublicKey;
    public String innerPrivateKey;
}
