package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkLog;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardCdkLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_log
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    int countByExample(SuixiangCardCdkLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_log
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_log
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    int insert(SuixiangCardCdkLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_log
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    int insertSelective(SuixiangCardCdkLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_log
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    List<SuixiangCardCdkLog> selectByExample(SuixiangCardCdkLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_log
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    SuixiangCardCdkLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_log
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    int updateByExampleSelective(@Param("record") SuixiangCardCdkLog record, @Param("example") SuixiangCardCdkLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_log
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    int updateByExample(@Param("record") SuixiangCardCdkLog record, @Param("example") SuixiangCardCdkLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_log
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    int updateByPrimaryKeySelective(SuixiangCardCdkLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk_log
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    int updateByPrimaryKey(SuixiangCardCdkLog record);
}