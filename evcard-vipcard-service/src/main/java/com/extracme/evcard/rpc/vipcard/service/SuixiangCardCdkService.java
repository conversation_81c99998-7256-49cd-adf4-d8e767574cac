package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.OSSObject;
import com.csvreader.CsvWriter;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.input.CreateWxQrCodeDto;
import com.extracme.evcard.membership.core.dto.input.WxQrCodeDto;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.IShortlinkManagementService;
import com.extracme.evcard.redis.JedisLock;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dao.*;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.SuiXiangCardMergeDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;
import com.extracme.evcard.rpc.vipcard.enums.*;
import com.extracme.evcard.rpc.vipcard.manager.SuixiangCardBaseManager;
import com.extracme.evcard.rpc.vipcard.model.*;
import com.extracme.evcard.rpc.vipcard.service.inner.SuiXiangCardInnerService;
import com.extracme.evcard.rpc.vipcard.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SuixiangCardCdkService implements ISuixiangCardCdkService {

    @Autowired
    private SuixiangCardBaseMapper suixiangCardBaseMapper;

    @Autowired
    private SuixiangCardRentDaysMapper suixiangCardRentDaysMapper;

    @Autowired
    private SuixiangCardPriceMapper suixiangCardPriceMapper;

    @Autowired
    private SuixiangCardCdkMapper suixiangCardCdkMapper;

    @Autowired
    private SuixiangCardCdkConfigDetailMapper suixiangCardCdkConfigDetailMapper;

    @Autowired
    private SuixiangCardCdkConfigMapper suixiangCardCdkConfigMapper;

    @Autowired
    private IMemberShipService memberShipService;

    @Autowired
    private SuixiangCardUseMapper suixiangCardUseMapper;

    @Autowired
    private SuiXiangCardService suiXiangCardService;

    @Autowired
    private SuixiangCardBaseManager suixiangCardBaseManager;

    @Autowired
    private SuixiangCardCdkService suixiangCardCdkService;

    @Autowired
    private IShortlinkManagementService shortlinkManagementService;

    @Autowired
    private SuixiangCardUseTempMapper suixiangCardUseTempMapper;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Value("${suixiangcard.cdk.wechatPage}")
    private String wechatPage;

    @Autowired
    private SuiXiangCardInnerService suiXiangCardInnerService;

    @Override
    public GenerateSuiXiangCardCdkOutput generateSuixiangCardCdk(GenerateSuiXiangCardCdkInput generateSuiXiangCardCdkInput) {
        BaseResponse baseResponse = new BaseResponse();
        GenerateSuiXiangCardCdkOutput generateSuiXiangCardCdkOutput = new GenerateSuiXiangCardCdkOutput();
        generateSuiXiangCardCdkOutput.setBaseResponse(baseResponse);
        log.info("生成随享卡兑换码.入参:{}", JSON.toJSONString(generateSuiXiangCardCdkInput));
        long cardBaseId = generateSuiXiangCardCdkInput.getCardBaseId();
        Date nowDate = new Date();
        // 根据id查询随享卡基础表
        SuixiangCardBase suixiangCardBase = suixiangCardBaseMapper.selectByPrimaryKey(cardBaseId);
        if (suixiangCardBase == null) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("查询不到[" + cardBaseId + "]随享卡记录");
            return generateSuiXiangCardCdkOutput;
        }
        if (suixiangCardBase.getCardStatus() == null || SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus() != suixiangCardBase.getCardStatus()) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("随享卡[" + cardBaseId + "]未已上架，不能生成兑换码");
            return generateSuiXiangCardCdkOutput;
        }
        // 没有到售卖开始时间，不能赠卡
        if (suixiangCardBase.getSaleStartTime().after(nowDate)) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("随享卡[" + cardBaseId + "]未到售卖开始时间，不能进行赠送操作");
            return generateSuiXiangCardCdkOutput;
        }
        // 如果当前系统时间超过售卖结束时间，则报错“当前已超过售卖结束时间，请修改时间后再操作上架”
        if (nowDate.after(suixiangCardBase.getSaleEndTime())) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("随享卡id[" + cardBaseId + "]已超过售卖结束时间，请修改时间后再操作上架");
            return generateSuiXiangCardCdkOutput;
        }
        // 已保存的 售卖价的单价
        Map<String, BigDecimal> dbUnitThirdPriceByBaseIdMap = suiXiangCardInnerService.getUnitThirdPriceByBaseId(cardBaseId);
        // 入参的 售卖价的单价
        Map<String, BigDecimal> inputUnitThirdPriceMap = new HashMap<>();

        List<SuiXiangCardCdkDto> suiXiangCardCdkDtos = generateSuiXiangCardCdkInput.getSuiXiangCardCdkDtos();
        // 需要发放的兑换码总数
        int sum = 0;
        for (SuiXiangCardCdkDto suiXiangCardCdkDto : suiXiangCardCdkDtos) {
            long cardRentId = suiXiangCardCdkDto.getCardRentId();
            long cardPriceId = suiXiangCardCdkDto.getCardPriceId();
            BigDecimal thirdSalesPrice = suiXiangCardCdkDto.getThirdSalesPrice();
            if (thirdSalesPrice == null) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("售卖价参数错误");
                return generateSuiXiangCardCdkOutput;
            }
            if (BigDecimal.ZERO.compareTo(thirdSalesPrice) > 0 || new BigDecimal(100000).compareTo(thirdSalesPrice) <= 0) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("售卖价不在0-99999.99之间");
                return generateSuiXiangCardCdkOutput;
            }

            int num = suiXiangCardCdkDto.getNum();
            if (num < 0 || num > 100000) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("发放数量不在0-100000之间");
                return generateSuiXiangCardCdkOutput;
            }
            sum += num;
            SuixiangCardRentDaysExample suixiangCardRentDaysExample = new SuixiangCardRentDaysExample();
            suixiangCardRentDaysExample
                    .createCriteria()
                    .andIdEqualTo(cardRentId)
                    .andCardBaseIdEqualTo(cardBaseId);
            List<SuixiangCardRentDays> suixiangCardRentDays = suixiangCardRentDaysMapper.selectByExample(suixiangCardRentDaysExample);
            if (CollectionUtils.isEmpty(suixiangCardRentDays)) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("查询不到[" + cardRentId + "]随享卡租期记录");
                return generateSuiXiangCardCdkOutput;
            }
            SuixiangCardPriceExample suixiangCardPriceExample = new SuixiangCardPriceExample();
            suixiangCardPriceExample
                    .createCriteria()
                    .andIdEqualTo(cardPriceId)
                    .andCardBaseIdEqualTo(cardBaseId)
                    .andCardRentIdEqualTo(cardRentId);
            List<SuixiangCardPrice> suixiangCardPrices = suixiangCardPriceMapper.selectByExample(suixiangCardPriceExample);
            if (CollectionUtils.isEmpty(suixiangCardPrices)) {
                baseResponse.setCode(-1);
                baseResponse.setMessage("查询不到[" + cardPriceId + "]随享卡价格记录");
                return generateSuiXiangCardCdkOutput;
            }

            // 校验填写售卖价之间，单价是否是否一致 ，校验填写售卖价是否与数据库的售卖价的单价是否一致
            if (generateSuiXiangCardCdkInput.getPurpose() == 2 && num != 0 ) {
                SuixiangCardRentDays suixiangCardRentDay = suixiangCardRentDays.get(0);
                SuixiangCardPrice suixiangCardPrice = suixiangCardPrices.get(0);
                String carModelIdsKey = suixiangCardPrice.getCarModelIds();
                if (StringUtils.isNotBlank(carModelIdsKey)) {
                    List<String> carModelIdList = Arrays.asList(carModelIdsKey.split(","));
                    Collections.sort(carModelIdList); // 排序
                    carModelIdsKey = String.join(",", carModelIdList);
                }
                Integer rentDays = suixiangCardRentDay.getRentDays();
                BigDecimal newUnitPrice = thirdSalesPrice.divide(new BigDecimal(rentDays), 2, RoundingMode.HALF_UP);

                // 入参的售卖价单价 之间的比较
                if (inputUnitThirdPriceMap.containsKey(carModelIdsKey)) {
                    BigDecimal oldInputUnitThirdPrice = inputUnitThirdPriceMap.get(carModelIdsKey);
                    if (oldInputUnitThirdPrice.compareTo(newUnitPrice) != 0) {
                        log.error("填写的第三方售卖价单价{}和入参其他的的单价不一致{},newUnitPrice={},oldInputUnitThirdPrice={}", newUnitPrice.toPlainString(), oldInputUnitThirdPrice.toPlainString(),JSON.toJSONString(newUnitPrice),JSON.toJSONString(oldInputUnitThirdPrice));
                        baseResponse.setCode(-1);
                        baseResponse.setMessage("填写的售卖价"+thirdSalesPrice.toPlainString()+"，单价"+newUnitPrice.toPlainString()+"，与填写的其他的售卖价单价"+oldInputUnitThirdPrice.toPlainString()+"不一致");
                        return generateSuiXiangCardCdkOutput;
                    }
                }
                inputUnitThirdPriceMap.put(carModelIdsKey, newUnitPrice);

                // 入参的售卖价单价 和 数据库中的售卖价单价 的比较
                if (dbUnitThirdPriceByBaseIdMap.containsKey(carModelIdsKey)) {
                    BigDecimal unitThirdPrice = dbUnitThirdPriceByBaseIdMap.get(carModelIdsKey);
                    if (unitThirdPrice.compareTo(newUnitPrice) != 0) {
                        log.error("填写的第三方售卖价单价{}和已保存的单价不一致{},newUnitPrice={},dbUnitThirdPriceByBaseIdMap={}", newUnitPrice.toPlainString(), unitThirdPrice.toPlainString(),JSON.toJSONString(newUnitPrice),JSON.toJSONString(dbUnitThirdPriceByBaseIdMap));
                        baseResponse.setCode(-1);
                        baseResponse.setMessage("填写的售卖价"+thirdSalesPrice.toPlainString()+"，单价"+newUnitPrice.toPlainString()+"，与已保存的售卖价单价"+unitThirdPrice.toPlainString()+"不一致");
                        return generateSuiXiangCardCdkOutput;
                    }
                }
            }
        }
        if (sum <= 0 || sum > 100000) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("发放总数不在1-100000之间");
            return generateSuiXiangCardCdkOutput;
        }
        // 开始保存cdk相关信息
        try {
            long suiXiangCardCdkConfigId = suixiangCardCdkService.saveSuixiangCardCdk(generateSuiXiangCardCdkInput, sum);
            baseResponse.setCode(0);
            baseResponse.setMessage("兑换码生成成功");
            generateSuiXiangCardCdkOutput.setSuiXiangCardCdkConfigId(suiXiangCardCdkConfigId);
            log.info("兑换码生成成功.入参:{}", JSON.toJSONString(generateSuiXiangCardCdkInput));
            return generateSuiXiangCardCdkOutput;
        } catch (Exception e) {
            baseResponse.setCode(-1);
            baseResponse.setMessage(e.getMessage());
            return generateSuiXiangCardCdkOutput;
        }
    }

    @Override
    public ActivateSuixiangCardCdkOutput activateSuixiangCardCdk(ActivateSuixiangCardCdkInput activateSuixiangCardCdkInput) {
        log.info("activateSuixiangCardCdk input:{}", JSON.toJSONString(activateSuixiangCardCdkInput));
        if (StringUtils.isNotEmpty(activateSuixiangCardCdkInput.getMid())) {
            return activateSuixiangCardCdkByMid(activateSuixiangCardCdkInput);
        }
        else if (StringUtils.isNotEmpty(activateSuixiangCardCdkInput.getMobile())) {
            return activateSuixiangCardCdkByMobile(activateSuixiangCardCdkInput);
        }
        else {
            ActivateSuixiangCardCdkOutput activateSuixiangCardCdkOutput = new ActivateSuixiangCardCdkOutput();
            BaseResponse baseResponse = new BaseResponse();
            activateSuixiangCardCdkOutput.setBaseResponse(baseResponse);
            baseResponse.setCode(-1);
            baseResponse.setMessage("兑换码不存在");
            return activateSuixiangCardCdkOutput;
        }
    }


    private ActivateSuixiangCardCdkOutput activateSuixiangCardCdkByMid(ActivateSuixiangCardCdkInput activateSuixiangCardCdkInput) {
        ActivateSuixiangCardCdkOutput output = new ActivateSuixiangCardCdkOutput();
        BaseResponse baseResponse = new BaseResponse();
        output.setBaseResponse(baseResponse);

        // step1: 兑换码是否正确，否：该兑换码不存在，请重新输⼊
        String cdkey = activateSuixiangCardCdkInput.getCdkey();
        String mid = activateSuixiangCardCdkInput.getMid();
        if (StringUtils.isBlank(cdkey) || StringUtils.isBlank(mid)) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("兑换码不能为空");
            return output;
        }
        String key = "lock_activateSuixiangCardCdk_" + cdkey;
        JedisLock lock = new JedisLock(key, 50 * 1000);
        Jedis jedis = JedisUtil.getJedis();
        try {
            log.info("用户mid:{}尝试获取激活随享卡cdk锁.key:{}", mid, key);
            if (!lock.acquire(jedis)) {
                log.error("用户mid:{}尝试获取激活随享卡cdk锁失败.key:{}", mid,  key);
                baseResponse.setCode(ActivateErrorEnum.ACTIVATED_FREQUENT.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.ACTIVATED_FREQUENT.getDesc());
                return output;
            }
            // step1: 兑换码是否正确，否：该兑换码不存在，请重新输⼊
            if (cdkey.length() != 16) {
                baseResponse.setCode(ActivateErrorEnum.CDKEY_ERROR.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_ERROR.getDesc());
                return output;
            }
            MembershipBasicInfo userBasicInfo = memberShipService.getUserBasicInfo(mid);
            if (userBasicInfo == null) {
                baseResponse.setCode(ActivateErrorEnum.CDKEY_ERROR.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_ERROR.getDesc());
                return output;
            }
            SuixiangCardCdkExample suixiangCardCdkExample = new SuixiangCardCdkExample();
            suixiangCardCdkExample.createCriteria().andCdkeyEqualTo(cdkey);
            List<SuixiangCardCdk> suixiangCardCdks = suixiangCardCdkMapper.selectByExample(suixiangCardCdkExample);
            if (CollectionUtils.isEmpty(suixiangCardCdks)) {
                log.error("该兑换码不存在，请重新输⼊.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.CDKEY_ERROR.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_ERROR.getDesc());
                return output;
            }
            SuixiangCardCdk suixiangCardCdk = suixiangCardCdks.get(0);
            SuixiangCardRentDays suixiangCardRentDays = suixiangCardRentDaysMapper.selectByPrimaryKey(suixiangCardCdk.getCardRentId());
            if (suixiangCardRentDays == null) {
                log.error("suixiangCardRentDays为空.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.CDKEY_ERROR.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_ERROR.getDesc());
                return output;
            }
            // 用户 已购买的随享卡数
            Integer suiXiangCardNum = suiXiangCardService.getPurchaseSuiXiangCardNum(userBasicInfo.getPkId(), suixiangCardCdk.getCardBaseId());
            if (suiXiangCardNum == null) {
                log.error("查询用户已购买的随享卡数量异常.suiXiangCardNum为空.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.CDKEY_ERROR.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_ERROR.getDesc());
                return output;
            }

            // cdk随享卡价格表
            SuixiangCardPrice cdkSuiXiangCardPrice = suixiangCardPriceMapper.selectByPrimaryKey(suixiangCardCdk.getCardPriceId());
            if (cdkSuiXiangCardPrice == null) {
                log.error("cdkSuiXiangCardPrice 为空.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.CDKEY_ERROR.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_ERROR.getDesc());
                return output;
            }


            // step2: 兑换码是否活动已失效？ 否：该活动的兑换码已失效！
            Date nowDate = new Date();
            SuixiangCardBase suixiangCardBase = suixiangCardBaseMapper.selectByPrimaryKey(suixiangCardCdk.getCardBaseId());
            if (suixiangCardBase == null
                    || suixiangCardBase.getCardStatus() != SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus()
                    || suixiangCardBase.getSaleStartTime().after(nowDate)
                    || nowDate.after(suixiangCardBase.getSaleEndTime())) {
                log.error("兑换码不存在/该活动兑换码已失效.cdkey:{}, suixiangCardBase:{}", cdkey, JSON.toJSONString(suixiangCardBase));
                baseResponse.setCode(ActivateErrorEnum.CDKEY_EXPIRED.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_EXPIRED.getDesc());
                return output;
            }


            // step3: 兑换码是否已被使⽤？ 否：该兑换码已被领取
            if (StringUtils.equals(suixiangCardCdk.getActivatedMid(), mid)) {
                baseResponse.setCode(ActivateErrorEnum.ACTIVATED_REPEAT.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.ACTIVATED_REPEAT.getDesc());
                return output;
            }

            if (suixiangCardCdk.getIsActivated() == 1) {
                log.error("兑换码已被领取.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.ACTIVATED.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.ACTIVATED.getDesc());
                return output;
            }


            // step4: 该账号是否已成功兑换过其他随享卡兑换码, 暂时cdk兑换只兑换一张卡
            if (suixiangCardBase.getPurchaseLimitNum() > 0
                    && (suiXiangCardNum >= suixiangCardBase.getPurchaseLimitNum() || suixiangCardBase.getPurchaseLimitNum() < 1 + suiXiangCardNum)) {
                log.error("该次卡活动一个用户仅可兑换" + suixiangCardBase.getPurchaseLimitNum() + "张，已兑换" + suiXiangCardNum + "张.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.ACTIVATED_LIMIT.getStatus());
                baseResponse.setMessage("该次卡活动⼀个⽤户仅可兑换" + suixiangCardBase.getPurchaseLimitNum() + "张");
                return output;
            }

            // 获取可以合并的随享卡
            SuixiangCardUse canMergeSuixiangCardUse = getCanMergeSuixiangCardUse(userBasicInfo.getPkId(), suixiangCardCdk.getCardBaseId(),cdkSuiXiangCardPrice.getCarModelIds());

            if (canMergeSuixiangCardUse != null) {
                // 合并卡
                mergeSuixiangCardByCdk(canMergeSuixiangCardUse, suixiangCardCdk, suixiangCardBase, suixiangCardRentDays, userBasicInfo, activateSuixiangCardCdkInput.getObtainType());
                output.setCardOperateType(1);
                output.setCardUseId(canMergeSuixiangCardUse.getId());
            }else{
                // 发卡
                offerSuixiangCardByCdk(suixiangCardCdk, suixiangCardBase, suixiangCardRentDays, userBasicInfo, activateSuixiangCardCdkInput.getObtainType());
                output.setCardOperateType(0);
                // 通过兑换码id 获取到随享卡的id
                SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByCdkId(suixiangCardCdk.getId());
                output.setCardUseId(suixiangCardUse != null ? suixiangCardUse.getId() : 0);
            }

        } catch (Exception e) {
            log.error("兑换码兑换失败.cdkey:{}", cdkey, e);
            baseResponse.setCode(-1);
            baseResponse.setMessage("兑换码兑换失败，请稍后重试");
            return output;
        } finally {
            if (jedis != null) {
                lock.releaseExcu(jedis);
                jedis.close();
            }
        }

        baseResponse.setCode(0);
        baseResponse.setMessage("兑换成功");
        return output;
    }


    /**
     *
     * 获取可以合并的随享卡
     *
     * @param userPkId
     * @param cardBaseId
     * @param carModelIds
     * @return
     */
    private SuixiangCardUse getCanMergeSuixiangCardUse(Long userPkId, Long cardBaseId, String carModelIds ) {
        if (StringUtils.isBlank(carModelIds)) {
            return null;
        }
        // 判断是否有可以合并的随享卡
        List<SuixiangCardUse> suixiangCardUses = suixiangCardUseMapper.selectCanMergeCardList(userPkId, cardBaseId);
        if (CollectionUtils.isNotEmpty(suixiangCardUses)) {
            for (SuixiangCardUse suixiangCardUse : suixiangCardUses) {
                Long cardPriceId = suixiangCardUse.getCardPriceId();
                SuixiangCardPrice suixiangCardPrice = suixiangCardPriceMapper.selectByPrimaryKey(cardPriceId);
                if (suixiangCardPrice != null) {
                    String oldCarModelIds = suixiangCardPrice.getCarModelIds();
                    if (StringUtils.isNotBlank(oldCarModelIds)) {
                        if(ComUtils.stringIsEquals(carModelIds, oldCarModelIds) || (carModelIds.contains("-1") && oldCarModelIds.contains("-1"))){
                            return suixiangCardUse;
                        }
                    }
                }
            }
        }
        return null;
    }



    /**
     * 合并已存在的随享卡
     * @param canMergeSuixiangCardUse  老随享卡（合并随享卡）
     * @param suixiangCardCdk   新随享卡信息（被合并随享卡）
     * @param suixiangCardBase
     * @param suixiangCardRentDays
     * @param membershipBasicInfo
     */
    private void mergeSuixiangCardByCdk(SuixiangCardUse canMergeSuixiangCardUse, SuixiangCardCdk suixiangCardCdk, SuixiangCardBase suixiangCardBase, SuixiangCardRentDays suixiangCardRentDays, MembershipBasicInfo membershipBasicInfo,  int obtainType) {
        Date nowDate = new Date();
        // 充值天数
        Integer mergeDay = suixiangCardRentDays.getRentDays();
        Long userPkId = membershipBasicInfo.getPkId();
        String name = membershipBasicInfo.getName();
        Long canMergeSuixiangCardUseId = canMergeSuixiangCardUse.getId();

        if (StringUtils.isBlank(name)) {
            name = suixiangCardCdk.getCreateOperName();
        }

        // 激活信息
        suixiangCardCdk.setIsActivated(1);
        suixiangCardCdk.setActivatedMid(membershipBasicInfo.getMid());
        suixiangCardCdk.setActivatedUserName(name);
        suixiangCardCdk.setActivatedUserMobile(membershipBasicInfo.getMobilePhone());
        suixiangCardCdk.setActivatedTime(nowDate);
        suixiangCardCdk.setUpdateOperId(userPkId);
        suixiangCardCdk.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        // 这里购买记录 直接存 老随享卡（合并随享卡）id
        suixiangCardCdk.setCardUseId(canMergeSuixiangCardUseId);
        suixiangCardCdk.setUpdateTime(nowDate);

        // 购买信息
        SuixiangCardPurchaseRecord suixiangCardPurchaseRecord = new SuixiangCardPurchaseRecord();
        suixiangCardPurchaseRecord.setOrderSeq(ComUtils.createOrderSeq(userPkId));
        suixiangCardPurchaseRecord.setOrgId(suixiangCardBase.getOrgId());
        suixiangCardPurchaseRecord.setUserId(userPkId);
        suixiangCardPurchaseRecord.setUserMobile(membershipBasicInfo.getMobilePhone());
        suixiangCardPurchaseRecord.setCardBaseId(suixiangCardBase.getId());
        suixiangCardPurchaseRecord.setCardName(suixiangCardBase.getCardName());
        suixiangCardPurchaseRecord.setCardRentId(suixiangCardRentDays.getId());
        // 这里购买记录 直接存 老随享卡（合并随享卡）id
        suixiangCardPurchaseRecord.setCardUseIds(""+canMergeSuixiangCardUseId);
        suixiangCardPurchaseRecord.setCardUseId(canMergeSuixiangCardUseId);

        suixiangCardPurchaseRecord.setCardPriceId(suixiangCardCdk.getCardPriceId());
        suixiangCardPurchaseRecord.setIssueType(SuiXiangCardIssueTypeEnum.EXCHANGE.getType());
        suixiangCardPurchaseRecord.setPaymentStatus(SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus());
        suixiangCardPurchaseRecord.setQuantity(1);
        suixiangCardPurchaseRecord.setPayTime(nowDate);
        suixiangCardPurchaseRecord.setRealAmount(BigDecimal.ZERO);
        suixiangCardPurchaseRecord.setCreateTime(nowDate);
        suixiangCardPurchaseRecord.setCdkId(suixiangCardCdk.getId());
        suixiangCardPurchaseRecord.setObtainType(obtainType);
        if (obtainType == SuiXiangCardObtainTypeEnum.SCAN.getType()) {
            suixiangCardPurchaseRecord.setRemindStatus(1);
        }
        suixiangCardPurchaseRecord.setCreateOperId(userPkId);
        suixiangCardPurchaseRecord.setUpdateOperId(userPkId);
        suixiangCardPurchaseRecord.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        suixiangCardPurchaseRecord.setCreateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));

        // 准备新增suixiang_card_purchase_record_log表
        SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
        recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.GIVE_OUT.getOperationType());
        recordLog.setContent("兑换随享卡兑换码-合并随享卡");
        recordLog.setCreateTime(nowDate);
        recordLog.setCreateOperId(userPkId);
        recordLog.setUpdateOperId(userPkId);
        recordLog.setCreateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        recordLog.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));

        // 合并卡 准备更新suixiang_card_use表

        /*Date expiresTime = canMergeSuixiangCardUse.getExpiresTime();
        LocalDateTime startLocalDateTime = DateUtil.dateToLocalDateTime(expiresTime);*/
        LocalDateTime startLocalDateTime = DateUtil.dateToLocalDateTime(nowDate);
        LocalDateTime expiresLocalDateTime = startLocalDateTime.plusMonths(suixiangCardBase.getValidDaysType());

        SuiXiangCardMergeDto suixiangCardMergeDto = new SuiXiangCardMergeDto();
        suixiangCardMergeDto.setSuiXiangCardUseId(canMergeSuixiangCardUseId);
        suixiangCardMergeDto.setExpiresTime(DateUtil.localDateTimeToDate(expiresLocalDateTime));
        suixiangCardMergeDto.setOperateDay(mergeDay);
        suixiangCardMergeDto.setUpdateOperId(userPkId);
        suixiangCardMergeDto.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));


        // 准备新增suixiang_card_use_opreation_log表
        // 老卡，待合并的随想卡日志
        SuixiangCardUseOpreationLog useLog = new SuixiangCardUseOpreationLog();
        useLog.setCardPriceId(canMergeSuixiangCardUse.getCardPriceId());
        useLog.setCardGroup(1); // 卡类别：1-随享卡
        useLog.setOperationType(SuiXiangCardUseLogOperationTypeEnum.MERGE.getOperationType());
        useLog.setOriginSystem("storeManagementSystem"); // 门店管理系统
        useLog.setInitDays(canMergeSuixiangCardUse.getInitDays());
        useLog.setAvailableDays(canMergeSuixiangCardUse.getAvailableDays());
        useLog.setUsedDays(canMergeSuixiangCardUse.getUsedDays());
        useLog.setFrozenDays(canMergeSuixiangCardUse.getFrozenDays());
        useLog.setOrderOperationDays(mergeDay);
        useLog.setCreateTime(nowDate);
        useLog.setCreateOperId(userPkId);
        useLog.setCreateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        useLog.setUpdateOperId(userPkId);
        useLog.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        useLog.setCardUseId(canMergeSuixiangCardUseId);
        useLog.setPurchaseId(canMergeSuixiangCardUse.getPurchaseId());

        // 新卡，被合并的随想卡日志
        SuixiangCardUseOpreationLog newUseLog = new SuixiangCardUseOpreationLog();
        newUseLog.setCardPriceId(canMergeSuixiangCardUse.getCardPriceId());
        newUseLog.setCardGroup(1); // 卡类别：1-随享卡
        newUseLog.setOperationType(SuiXiangCardUseLogOperationTypeEnum.GIVE_OUT.getOperationType());
        newUseLog.setOriginSystem("storeManagementSystem"); // 门店管理系统
        newUseLog.setInitDays(suixiangCardRentDays.getRentDays());
        newUseLog.setAvailableDays(suixiangCardRentDays.getRentDays());
        newUseLog.setUsedDays(0);
        newUseLog.setFrozenDays(0);
        newUseLog.setOrderOperationDays(suixiangCardRentDays.getRentDays());
        newUseLog.setCreateTime(nowDate);
        newUseLog.setCreateOperId(userPkId);
        newUseLog.setCreateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        newUseLog.setUpdateOperId(userPkId);
        newUseLog.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        newUseLog.setCardUseId(canMergeSuixiangCardUseId);

        // 开启事务，新增记录
        suixiangCardBaseManager.mergeSuiXiangCard(suixiangCardCdk, suixiangCardPurchaseRecord, recordLog, suixiangCardMergeDto, useLog,newUseLog);

        log.info("兑换随享卡，随享卡合并数据处理完毕！suixiangCardCdk[{}]", JSON.toJSONString(suixiangCardCdk));
    }


    /**
     * 合并已存在的随享卡
     * @param canMergeSuixiangCardUse  老随享卡（合并随享卡）
     * @param suixiangCardCdk   新随享卡信息（被合并随享卡）
     * @param suixiangCardBase
     * @param suixiangCardRentDays
     * @param membershipBasicInfo
     */
    private void mergeSuixiangCardByPreCdk(SuixiangCardUseTemp suixiangCardUseTemp, SuixiangCardUse canMergeSuixiangCardUse, SuixiangCardCdk suixiangCardCdk, SuixiangCardBase suixiangCardBase, SuixiangCardRentDays suixiangCardRentDays, MembershipBasicInfo membershipBasicInfo) {
        Date nowDate = new Date();
        // 充值天数
        Integer mergeDay = suixiangCardRentDays.getRentDays();
        Long userPkId = membershipBasicInfo.getPkId();
        String name = membershipBasicInfo.getName();
        Long canMergeSuixiangCardUseId = canMergeSuixiangCardUse.getId();

        if (StringUtils.isBlank(name)) {
            name = suixiangCardCdk.getCreateOperName();
        }

        // 激活信息
        suixiangCardCdk.setIsActivated(1);
        suixiangCardCdk.setActivatedMid(membershipBasicInfo.getMid());
        suixiangCardCdk.setActivatedUserName(name);
        suixiangCardCdk.setActivatedUserMobile(membershipBasicInfo.getMobilePhone());
        suixiangCardCdk.setActivatedTime(nowDate);
        suixiangCardCdk.setUpdateOperId(userPkId);
        suixiangCardCdk.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        // 这里购买记录 直接存 老随享卡（合并随享卡）id
        suixiangCardCdk.setCardUseId(canMergeSuixiangCardUseId);
        suixiangCardCdk.setUpdateTime(nowDate);

        // 购买信息
        SuixiangCardPurchaseRecord suixiangCardPurchaseRecord = new SuixiangCardPurchaseRecord();
        suixiangCardPurchaseRecord.setOrderSeq(ComUtils.createOrderSeq(userPkId));
        suixiangCardPurchaseRecord.setOrgId(suixiangCardBase.getOrgId());
        suixiangCardPurchaseRecord.setUserId(userPkId);
        suixiangCardPurchaseRecord.setUserMobile(membershipBasicInfo.getMobilePhone());
        suixiangCardPurchaseRecord.setCardBaseId(suixiangCardBase.getId());
        suixiangCardPurchaseRecord.setCardName(suixiangCardBase.getCardName());
        suixiangCardPurchaseRecord.setCardRentId(suixiangCardRentDays.getId());
        // 这里购买记录 直接存 老随享卡（合并随享卡）id
        suixiangCardPurchaseRecord.setCardUseIds(""+canMergeSuixiangCardUseId);
        suixiangCardPurchaseRecord.setCardUseId(canMergeSuixiangCardUseId);

        suixiangCardPurchaseRecord.setCardPriceId(suixiangCardCdk.getCardPriceId());
        suixiangCardPurchaseRecord.setIssueType(SuiXiangCardIssueTypeEnum.EXCHANGE.getType());
        suixiangCardPurchaseRecord.setPaymentStatus(SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus());
        suixiangCardPurchaseRecord.setQuantity(1);
        suixiangCardPurchaseRecord.setPayTime(nowDate);
        suixiangCardPurchaseRecord.setRealAmount(BigDecimal.ZERO);
        suixiangCardPurchaseRecord.setCreateTime(nowDate);
        suixiangCardPurchaseRecord.setCdkId(suixiangCardCdk.getId());
        suixiangCardPurchaseRecord.setObtainType(suixiangCardUseTemp.getObtainType());

        suixiangCardPurchaseRecord.setCreateOperId(userPkId);
        suixiangCardPurchaseRecord.setUpdateOperId(userPkId);
        suixiangCardPurchaseRecord.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        suixiangCardPurchaseRecord.setCreateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));

        // 准备新增suixiang_card_purchase_record_log表
        SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
        recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.GIVE_OUT.getOperationType());
        recordLog.setContent("兑换随享卡兑换码-合并随享卡");
        recordLog.setCreateTime(nowDate);
        recordLog.setCreateOperId(userPkId);
        recordLog.setUpdateOperId(userPkId);
        recordLog.setCreateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        recordLog.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));

        // 合并卡 准备更新suixiang_card_use表

        /*Date expiresTime = canMergeSuixiangCardUse.getExpiresTime();
        LocalDateTime startLocalDateTime = DateUtil.dateToLocalDateTime(expiresTime);*/
        LocalDateTime startLocalDateTime = DateUtil.dateToLocalDateTime(nowDate);
        LocalDateTime expiresLocalDateTime = startLocalDateTime.plusMonths(suixiangCardBase.getValidDaysType());

        SuiXiangCardMergeDto suixiangCardMergeDto = new SuiXiangCardMergeDto();
        suixiangCardMergeDto.setSuiXiangCardUseId(canMergeSuixiangCardUseId);
        suixiangCardMergeDto.setExpiresTime(DateUtil.localDateTimeToDate(expiresLocalDateTime));
        suixiangCardMergeDto.setOperateDay(mergeDay);
        suixiangCardMergeDto.setUpdateOperId(userPkId);
        suixiangCardMergeDto.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));


        // 准备新增suixiang_card_use_opreation_log表
        // 老卡，待合并的随想卡日志
        SuixiangCardUseOpreationLog useLog = new SuixiangCardUseOpreationLog();
        useLog.setCardPriceId(canMergeSuixiangCardUse.getCardPriceId());
        useLog.setCardGroup(1); // 卡类别：1-随享卡
        useLog.setOperationType(SuiXiangCardUseLogOperationTypeEnum.MERGE.getOperationType());
        useLog.setOriginSystem("storeManagementSystem"); // 门店管理系统
        useLog.setInitDays(canMergeSuixiangCardUse.getInitDays());
        useLog.setAvailableDays(canMergeSuixiangCardUse.getAvailableDays());
        useLog.setUsedDays(canMergeSuixiangCardUse.getUsedDays());
        useLog.setFrozenDays(canMergeSuixiangCardUse.getFrozenDays());
        useLog.setOrderOperationDays(mergeDay);
        useLog.setCreateTime(nowDate);
        useLog.setCreateOperId(userPkId);
        useLog.setCreateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        useLog.setUpdateOperId(userPkId);
        useLog.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        useLog.setCardUseId(canMergeSuixiangCardUseId);
        useLog.setPurchaseId(canMergeSuixiangCardUse.getPurchaseId());

        // 新卡，被合并的随想卡日志
        SuixiangCardUseOpreationLog newUseLog = new SuixiangCardUseOpreationLog();
        newUseLog.setCardPriceId(canMergeSuixiangCardUse.getCardPriceId());
        newUseLog.setCardGroup(1); // 卡类别：1-随享卡
        newUseLog.setOperationType(SuiXiangCardUseLogOperationTypeEnum.GIVE_OUT.getOperationType());
        newUseLog.setOriginSystem("storeManagementSystem"); // 门店管理系统
        newUseLog.setInitDays(suixiangCardRentDays.getRentDays());
        newUseLog.setAvailableDays(suixiangCardRentDays.getRentDays());
        newUseLog.setUsedDays(0);
        newUseLog.setFrozenDays(0);
        newUseLog.setOrderOperationDays(suixiangCardRentDays.getRentDays());
        newUseLog.setCreateTime(nowDate);
        newUseLog.setCreateOperId(userPkId);
        newUseLog.setCreateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        newUseLog.setUpdateOperId(userPkId);
        newUseLog.setUpdateOperName(ComUtils.splitStr(name, Constants.OPER_NAME_LENGTH));
        newUseLog.setCardUseId(canMergeSuixiangCardUseId);
        suixiangCardUseTemp.setIsOffer(1);
        // 开启事务，新增记录
        suixiangCardBaseManager.mergeSuiXiangCard(suixiangCardUseTemp, suixiangCardCdk, suixiangCardPurchaseRecord, recordLog, suixiangCardMergeDto, useLog,newUseLog);

        log.info("兑换随享卡，随享卡合并数据处理完毕！suixiangCardCdk[{}]", JSON.toJSONString(suixiangCardCdk));
    }



    @Transactional(rollbackFor = Exception.class)
    public long saveSuixiangCardCdk(GenerateSuiXiangCardCdkInput generateSuiXiangCardCdkInput, int sum) throws BusinessException {
        long cardBaseId = generateSuiXiangCardCdkInput.getCardBaseId();
        OperatorDto operatorDto = generateSuiXiangCardCdkInput.getOperatorDto();
        List<SuiXiangCardCdkDto> suiXiangCardCdkDtos = generateSuiXiangCardCdkInput.getSuiXiangCardCdkDtos();
        /**
         * 全局加锁
         */
        String key = "lock_generateSuiXiangCdK";
        JedisLock lock = new JedisLock(key, 100 * 1000);
        Jedis jedis = JedisUtil.getJedis();
        try {
            log.info("尝试获取生成随享卡cdk锁.key:{}", key);
            if (!lock.acquire(jedis)) {
                log.warn("尝试获取生成随享卡cdk锁失败.key:{}", key);
                throw new BusinessException(-1, "其他用户正在批量生成券码，请稍后重试");
            }
            log.info("尝试获取生成随享卡cdk锁成功.key:{}", key);
            long id = 0;
            String couponCode = suixiangCardCdkMapper.getLatestKey();
            if (StringUtils.isNotBlank(couponCode) && couponCode.length() == 16) {
                id = CdkGeneratorUtil.decode(couponCode);
            }
            // 重新要查一下
            SuixiangCardBase suixiangCardBase = suixiangCardBaseMapper.selectByPrimaryKey(cardBaseId);
            if (operatorDto != null) {
                suixiangCardBase.setUpdateOperId(operatorDto.getOperatorId());
                suixiangCardBase.setUpdateOperName(operatorDto.getOperatorName());
            }
            int stock = suixiangCardBase.getStock();
            if (stock < sum) {
                log.error("随享卡库存不足.cardBaseId:{},stock:{},sum:{}", cardBaseId, stock, sum);
                throw new BusinessException(-1, "库存不足");
            }

            // 先减库存，减库存成功后再生成兑换码
            suixiangCardBase.setStock(sum); // 这里借用stock字段作为需要赠送的数量，在sql中使用减法
            suixiangCardBase.setSales(sum); // 这里借用sales字段作为需要赠送的数量，在sql中使用加法
            if (suixiangCardBaseMapper.updateStock(suixiangCardBase) <= 0) {
                log.error("更新库存失败.suixiangCardBase:{}", JSON.toJSONString(suixiangCardBase));
                throw new BusinessException(-1, "生成兑换码失败，请重试");
            }

            // 兑换码配置
            SuixiangCardCdkConfig suixiangCardCdkConfig = new SuixiangCardCdkConfig();
            suixiangCardCdkConfig.setCardBaseId(cardBaseId);
            suixiangCardCdkConfig.setActName(DateUtil.getSystemDate(DateUtil.dtLong));
            suixiangCardCdkConfig.setCardName(suixiangCardBase.getCardName());
            suixiangCardCdkConfig.setTotalQuantity(sum);
            suixiangCardCdkConfig.setPurpose(generateSuiXiangCardCdkInput.getPurpose());
            if (operatorDto != null) {
                suixiangCardCdkConfig.setCreateOperId(operatorDto.getOperatorId());
                suixiangCardCdkConfig.setCreateOperName(operatorDto.getOperatorName());
                suixiangCardCdkConfig.setUpdateOperId(operatorDto.getOperatorId());
                suixiangCardCdkConfig.setUpdateOperName(operatorDto.getOperatorName());
            }
            if (suixiangCardCdkConfigMapper.insertSelective(suixiangCardCdkConfig) <= 0) {
                log.error("保存随享卡兑换码配置表失败.suixiangCardCdkConfig:{}", JSON.toJSONString(suixiangCardCdkConfig));
                throw new BusinessException(-1, "生成兑换码失败，请重试");
            }

            // 生成兑换码
            for (SuiXiangCardCdkDto suiXiangCardCdkDto : suiXiangCardCdkDtos) {
                long cardRentId = suiXiangCardCdkDto.getCardRentId();
                long cardPriceId = suiXiangCardCdkDto.getCardPriceId();
                int num = suiXiangCardCdkDto.getNum();
                if (num == 0) continue;
                int batchSize = 1000;

                // 插入兑换码配置详情表
                SuixiangCardCdkConfigDetail suixiangCardCdkConfigDetail = new SuixiangCardCdkConfigDetail();
                suixiangCardCdkConfigDetail.setCardCdkConfigId(suixiangCardCdkConfig.getId());
                suixiangCardCdkConfigDetail.setCardBaseId(cardBaseId);
                suixiangCardCdkConfigDetail.setCardRentId(cardRentId);
                suixiangCardCdkConfigDetail.setCardPriceId(cardPriceId);
                suixiangCardCdkConfigDetail.setQuantity(num);
                suixiangCardCdkConfigDetail.setActDesc(suiXiangCardCdkDto.getActDesc());
                suixiangCardCdkConfigDetail.setThirdSalesPrice(suiXiangCardCdkDto.getThirdSalesPrice());
                if (operatorDto != null) {
                    suixiangCardCdkConfigDetail.setCreateOperId(operatorDto.getOperatorId());
                    suixiangCardCdkConfigDetail.setCreateOperName(operatorDto.getOperatorName());
                    suixiangCardCdkConfigDetail.setUpdateOperId(operatorDto.getOperatorId());
                    suixiangCardCdkConfigDetail.setUpdateOperName(operatorDto.getOperatorName());
                }
                if (suixiangCardCdkConfigDetailMapper.insertSelective(suixiangCardCdkConfigDetail) <= 0) {
                    log.error("保存随享卡兑换码配置详情表失败.suixiangCardCdkConfigDetail:{}", JSON.toJSONString(suixiangCardCdkConfigDetail));
                    throw new BusinessException("生成兑换码失败，请重试");
                }

                // 批量生成
                log.info("开始生成兑换码.cardBaseId:{},cardRentId:{},cardPriceId:{},num:{}", cardBaseId, cardRentId, cardPriceId, num);
                int totalBatch = (int) Math.ceil((double) num / batchSize);

                for (int i = 0; i < totalBatch; i++) {
                    int start = i * batchSize; // 开始位置
                    int end = Math.min((i + 1) * batchSize, num); // 结束位置
                    // 极小的可能会有重复，重试3次
                    int retries = 3;
                    for (; retries >= 0; retries--) {
                        try {
                            log.info("开始批量生成随享卡兑换码.起始id:{},数量:{}", id, end - start);
                            if (suixiangCardCdkService.saveBatchCdk(suixiangCardCdkConfigDetail.getId(), cardBaseId, cardRentId, cardPriceId, id, end - start, operatorDto)) {
                                break;
                            }
                        } catch (Exception e) {
                            log.error("批量生成兑换码失败，剩余尝试次数:{}", retries, e);
                            if (retries == 0) {
                                throw new BusinessException("生成兑换码失败，请重试");
                            }
                        }
                    }
                    id += end - start;
                }
            }
            return suixiangCardCdkConfig.getId();
        } catch (Exception e) {
            log.error("生成兑换码异常", e);
            return -1;
        } finally {
            if (jedis != null) {
                lock.releaseExcu(jedis);
                log.info("释放生成随享卡cdk锁成功.key:{}", key);
                jedis.close();
            }
        }
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public boolean saveBatchCdk(long cardCdkConfigDetailId, long cardBaseId, long cardRentId, long cardPriceId, long id, int num, OperatorDto operatorDto) throws BusinessException {
        List<SuixiangCardCdk> suixiangCardCdks = new ArrayList<>();
        for (int i = 0; i < num; i++) {
            SuixiangCardCdk suixiangCardCdk = new SuixiangCardCdk();
            suixiangCardCdk.setCardCdkConfigDetailId(cardCdkConfigDetailId);
            suixiangCardCdk.setCardBaseId(cardBaseId);
            suixiangCardCdk.setCardRentId(cardRentId);
            suixiangCardCdk.setCardPriceId(cardPriceId);
            suixiangCardCdk.setCdkey(CdkGeneratorUtil.encode(++id));
            if (operatorDto != null) {
                suixiangCardCdk.setCreateOperId(operatorDto.getOperatorId());
                suixiangCardCdk.setCreateOperName(operatorDto.getOperatorName());
                suixiangCardCdk.setUpdateOperId(operatorDto.getOperatorId());
                suixiangCardCdk.setUpdateOperName(operatorDto.getOperatorName());
            }
            else {
                suixiangCardCdk.setCreateOperId(-1L);
                suixiangCardCdk.setCreateOperName("");
                suixiangCardCdk.setUpdateOperId(-1L);
                suixiangCardCdk.setUpdateOperName("");
            }
            suixiangCardCdks.add(suixiangCardCdk);
        }
        if (suixiangCardCdkMapper.batchSaveCdk(suixiangCardCdks) < num) {
            throw new BusinessException("兑换码有冲突");
        }
        return true;
    }


    private static final String TEMP_FILE_DIR = "/data/";
    //private static final String TEMP_FILE_DIR = "C:\\Users\\<USER>\\Desktop\\work\\ttt\\";
    private static final String[] EXPORT_TITLES = {"卡活动ID", "卡名称", "随享卡", "兑换码", "有效开始时间", "有效结束时间", "兑换码用途", "第三方售卖价", "领取状态", "累计订单数", "领取手机号", "领取姓名", "发放人"};

    private CsvWriter getWriter(String filePath) {
        CsvWriter csvWriter = null;
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                file.createNewFile();
            }
            csvWriter = new CsvWriter(filePath, ',', Charset.forName("GBK"));
            csvWriter.writeRecord(EXPORT_TITLES);
        } catch (Exception e) {
            log.error("导出随享卡购买记录！创建数据文件失败，filePath={}", filePath, e);
        }
        return csvWriter;
    }

    @Override
    public QueryAndExportSuiXiangCardCdkOutput queryAndExportSuiXiangCardCdk(QueryAndExportSuiXiangCardCdkInput input) {
        QueryAndExportSuiXiangCardCdkOutput resp = new QueryAndExportSuiXiangCardCdkOutput();
        BaseResponse baseResponse = new BaseResponse();
        resp.setBaseResponse(baseResponse);

        // 入参校验
        if (input == null || input.getSuiXiangCardCdkConfigId() <= 0) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("入参校验不通过！");
            return resp;
        }
        long suixiangCardCdkConfigId = input.getSuiXiangCardCdkConfigId();
        SuixiangCardCdkConfig suixiangCardCdkConfig = suixiangCardCdkConfigMapper.selectByPrimaryKey(suixiangCardCdkConfigId);
        if (input.isNeedQrFlag() && StringUtils.isEmpty(suixiangCardCdkConfig.getQrCodeZipUrl())) {
            String key = "lock_exportSuiXiangCdK";
            JedisLock lock = new JedisLock(key, 90 * 60 * 1000);
            Jedis jedis = JedisUtil.getJedis();
            try {
                log.info("尝试获取导出随享卡二维码cdk锁.key:{}", key);
                if (!lock.acquire(jedis)) {
                    log.warn("尝试获取导出随享卡二维码cdk锁失败.key:{}", key);
                    baseResponse.setCode(-1);
                    baseResponse.setMessage("有兑换码正在生成中：请稍后再试。");
                    return resp;
                }
                taskExecutor.execute(() -> {
                    try {
                        log.info("生成二维码开始");
                        doGenerateQrCode(input);
                        log.info("生成二维码结束");
                    } catch (BusinessException e) {
                        throw new RuntimeException(e);
                    } finally {
                        if (jedis != null) {
                            lock.releaseExcu(jedis);
                            log.info("释放生成随享卡cdk锁成功.key:{}", key);
                            jedis.close();
                        }
                    }
                });
            } catch (Exception e) {
                log.error("导出随享卡二维码异常！", e);
            }
            baseResponse.setCode(-1);
            baseResponse.setMessage("由于二维码生成速度较慢，请稍后点击「兑换码记录」按钮手动下载。");
            return resp;
        }

        // 文件名 命名规则为名称+时间戳(当前时间) 例<随享卡名称20240404090701>
        StringBuffer newFileName = new StringBuffer();
        if (suixiangCardCdkConfig != null) {
            String cardName = suixiangCardCdkConfig.getCardName();
            try {
                // 对cardName 中特殊字符替换为空
                cardName = cardName.replaceAll("[\\\\/:*?\"<>|+-]", "");
            } catch (Exception e) {
            }

            newFileName.append(cardName);
        } else {
            newFileName.append("随享卡兑换码");
        }
        newFileName.append(DateUtil.getFormatDate(new Date(), DateUtil.dtLong));
        newFileName.append(".csv");
        // 检查并创建临时目录
        ComUtils.checkAndMkDir(TEMP_FILE_DIR);
        String fileName = newFileName.toString();
        String filePath = TEMP_FILE_DIR + fileName;
        CsvWriter csvWriter = getWriter(filePath);
        if (csvWriter == null) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("创建数据文件失败");
            log.info("导出随享卡兑换码记录，应答参数：{}", JSON.toJSONString(resp));
            return resp;
        }

        Jedis jedis = JedisUtil.getJedis();
        String redisKey = "lock_queryAndExportSuixiangCardCdkDetail_" + suixiangCardCdkConfigId;
        try {
            // 用redis 控制最多同时有3个请求进去
            if (SimpleMethodLimiter.allowMethod(jedis, redisKey, 3, 0)) {
                try {
                    // 写入excel
                    doWriteToExcel(suixiangCardCdkConfigId, csvWriter);
                } catch (Exception e) {
                    log.error("导出随享卡兑换码记录！数据导出失败，fileName={},suixiangCardCdkConfigId={}", fileName, suixiangCardCdkConfigId, e);
                    baseResponse.setCode(-1);
                    baseResponse.setMessage("导出随享卡兑换码记录失败");
                    return resp;
                } finally {
                    if (csvWriter != null) {
                        csvWriter.flush();
                        csvWriter.close();
                    }
                }
            } else {
                log.error("其他用户正在导出数据，请稍后重试,suixiangCardCdkConfigId={}", suixiangCardCdkConfigId);
                baseResponse.setCode(-1);
                baseResponse.setMessage("其他用户正在导出数据，请稍后重试");
                return resp;
            }
        } finally {
            SimpleMethodLimiter.releaseMethod(jedis, redisKey);
        }

        // 上传阿里云
        log.info("suixiangCardCdkConfigId={} 导出随享卡兑换码文件，上传阿里云开始", suixiangCardCdkConfigId);
        String fileUrl = FileUtil.uploadSynByFilePath(filePath, "/export/suixiangcard/cdk/" + fileName);
        if (fileUrl == null) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("导出随享卡兑换码文件，上传到阿里云失败！");
            return resp;
        }
        log.info("suixiangCardCdkConfigId={} 导出随享卡兑换码文件，上传阿里云结束", suixiangCardCdkConfigId);

        if (input.isNeedQrFlag() && StringUtils.isNotEmpty(suixiangCardCdkConfig.getQrCodeZipUrl())) {
            resp.setZipUrl(FileUtil.WEB_URL + suixiangCardCdkConfig.getQrCodeZipUrl());
        }
        // 删除本地临时文件
        FileUtil.deleteTmpFile(new File(filePath));
        resp.setCsvUrl(fileUrl);
        baseResponse.setCode(0);
        baseResponse.setMessage("导出成功");
        log.info("suixiangCardCdkConfigId={} 导出随享卡兑换码文件，应答参数：{}", suixiangCardCdkConfigId, JSON.toJSONString(resp));
        return resp;
    }

    private void offerSuixiangCardByCdk(SuixiangCardCdk suixiangCardCdk, SuixiangCardBase suixiangCardBase, SuixiangCardRentDays suixiangCardRentDays,  MembershipBasicInfo membershipBasicInfo, int obtainType) throws BusinessException {
        Date nowDate = new Date();
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setOperatorId(suixiangCardCdk.getCreateOperId());
        operatorDto.setOperatorName(suixiangCardCdk.getCreateOperName());
        // 激活信息
        suixiangCardCdk.setIsActivated(1);
        suixiangCardCdk.setActivatedMid(membershipBasicInfo.getMid());
        suixiangCardCdk.setActivatedUserName(membershipBasicInfo.getName());
        suixiangCardCdk.setActivatedUserMobile(membershipBasicInfo.getMobilePhone());
        suixiangCardCdk.setActivatedTime(nowDate);
        suixiangCardCdk.setUpdateOperId(membershipBasicInfo.getPkId());
        suixiangCardCdk.setUpdateOperName(ComUtils.splitStr(membershipBasicInfo.getName(), Constants.OPER_NAME_LENGTH));
        suixiangCardCdk.setUpdateTime(nowDate);
        // 购买信息
        SuixiangCardPurchaseRecord suixiangCardPurchaseRecord = new SuixiangCardPurchaseRecord();
        suixiangCardPurchaseRecord.setOrderSeq(ComUtils.createOrderSeq(membershipBasicInfo.getPkId()));
        suixiangCardPurchaseRecord.setOrgId(suixiangCardBase.getOrgId());
        suixiangCardPurchaseRecord.setUserId(membershipBasicInfo.getPkId());
        suixiangCardPurchaseRecord.setUserMobile(membershipBasicInfo.getMobilePhone());
        suixiangCardPurchaseRecord.setCardBaseId(suixiangCardBase.getId());
        suixiangCardPurchaseRecord.setCardName(suixiangCardBase.getCardName());
        suixiangCardPurchaseRecord.setCardRentId(suixiangCardRentDays.getId());
        suixiangCardPurchaseRecord.setCardUseIds("-1");
        suixiangCardPurchaseRecord.setObtainType(obtainType);
        suixiangCardPurchaseRecord.setCardPriceId(suixiangCardCdk.getCardPriceId());
        suixiangCardPurchaseRecord.setIssueType(SuiXiangCardIssueTypeEnum.EXCHANGE.getType());
        suixiangCardPurchaseRecord.setPaymentStatus(SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus());
        suixiangCardPurchaseRecord.setQuantity(1);
        suixiangCardPurchaseRecord.setPayTime(nowDate);
        suixiangCardPurchaseRecord.setRealAmount(BigDecimal.ZERO);
        suixiangCardPurchaseRecord.setCreateTime(nowDate);
        suixiangCardPurchaseRecord.setCdkId(suixiangCardCdk.getId());
        if (obtainType == SuiXiangCardObtainTypeEnum.SCAN.getType()) {
            suixiangCardPurchaseRecord.setRemindStatus(1);
        }
        if (operatorDto != null) {
            suixiangCardPurchaseRecord.setCreateOperId(operatorDto.getOperatorId());
            suixiangCardPurchaseRecord.setCreateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
            suixiangCardPurchaseRecord.setUpdateOperId(operatorDto.getOperatorId());
            suixiangCardPurchaseRecord.setUpdateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
        }
        // 准备新增suixiang_card_purchase_record_log表
        SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
        recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.GIVE_OUT.getOperationType());
        recordLog.setContent("兑换随享卡兑换码");
        recordLog.setCreateTime(nowDate);
        if (operatorDto != null) {
            recordLog.setCreateOperId(operatorDto.getOperatorId());
            recordLog.setCreateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
            recordLog.setUpdateOperId(operatorDto.getOperatorId());
            recordLog.setUpdateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
        }
        List<SuixiangCardUse> useList = new ArrayList<>();
        List<SuixiangCardUseOpreationLog> useLogList = new ArrayList<>();
        for(int i=0; i< suixiangCardPurchaseRecord.getQuantity(); i++) {
            // 准备新增suixiang_card_use表
            SuixiangCardUse use = new SuixiangCardUse();
            use.setCardBaseId(suixiangCardBase.getId());
            use.setCardPriceId(suixiangCardPurchaseRecord.getCardPriceId());
            use.setUserId(membershipBasicInfo.getPkId());
            use.setCardType(1); // 1-随享卡
            use.setCardName(suixiangCardPurchaseRecord.getCardName());
            use.setCardStatus(SuiXiangCardStatusEnum.EFFECTIVE.getStatus());
            use.setStartTime(nowDate);
            LocalDateTime startLocalDateTime = DateUtil.dateToLocalDateTime(nowDate);
            LocalDateTime expiresLocalDateTime = startLocalDateTime.plusMonths(suixiangCardBase.getValidDaysType());
            use.setExpiresTime(DateUtil.localDateTimeToDate(expiresLocalDateTime));

            use.setInitDays(suixiangCardRentDays.getRentDays());
            use.setAvailableDays(suixiangCardRentDays.getRentDays());
            use.setUsedDays(0);
            use.setFrozenDays(0);
            use.setCreateTime(nowDate);
            if (operatorDto != null) {
                use.setCreateOperId(operatorDto.getOperatorId());
                use.setCreateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
                use.setUpdateOperId(operatorDto.getOperatorId());
                use.setUpdateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
            }
            useList.add(use);

            // 准备新增suixiang_card_use_opreation_log表
            SuixiangCardUseOpreationLog useLog = new SuixiangCardUseOpreationLog();
            useLog.setCardPriceId(suixiangCardPurchaseRecord.getCardPriceId());
            useLog.setCardGroup(1); // 卡类别：1-随享卡
            useLog.setOperationType(SuiXiangCardUseLogOperationTypeEnum.GIVE_OUT.getOperationType());
            useLog.setOriginSystem("storeManagementSystem"); // 门店管理系统
            useLog.setInitDays(suixiangCardRentDays.getRentDays());
            useLog.setAvailableDays(suixiangCardRentDays.getRentDays());
            useLog.setUsedDays(0);
            useLog.setFrozenDays(0);
            useLog.setCreateTime(nowDate);
            if (operatorDto != null) {
                useLog.setCreateOperId(operatorDto.getOperatorId());
                useLog.setCreateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
                useLog.setUpdateOperId(operatorDto.getOperatorId());
                useLog.setUpdateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
            }
            useLogList.add(useLog);
        }
        // 开启事务，新增记录
        suixiangCardBaseManager.giveSuiXiangCard(suixiangCardCdk, suixiangCardPurchaseRecord, recordLog, useList, useLogList);

        // 走到这里，就是赠送成功了
        //log.info("兑换随享卡，单个用户数据处理完毕！userInfo[{}]", giveCardDto.getUserInfo());
    }

    @Override
    public QuerySuiXiangCardCdkConfigDetailOutput querySuiXiangCardCdkConfigDetail(QuerySuiXiangCardCdkConfigDetailInput input) {
        QuerySuiXiangCardCdkConfigDetailOutput resp = new QuerySuiXiangCardCdkConfigDetailOutput();
        BaseResponse baseResponse = new BaseResponse();
        resp.setBaseResponse(baseResponse);

        //参数校验
        if (input == null || input.getCardBaseId() <= 0) {
            log.error("查询随享卡兑换码记录，入参异常，input={}", JSON.toJSONString(input));
            baseResponse.setCode(-1);
            baseResponse.setMessage("参数异常");
            return resp;
        }

        // 第三方售卖价
        Map<String, BigDecimal> rentIdAndThirdPriceMap = suiXiangCardInnerService.getThirdPriceByBaseId(input.getCardBaseId());

        try {
            List<SuixiangCardInfoDto> suixiangCardInfoDtos = suixiangCardPriceMapper.selectInfoByBaseId(input.getCardBaseId());
            List<QuerySuiXiangCardCdkConfigDetailDto> lists = suixiangCardInfoDtos.stream().map(
                    suixiangCardInfoDto -> {
                        Long cardRentId = suixiangCardInfoDto.getCardRentId();
                        Long cardPriceId = suixiangCardInfoDto.getCardPriceId();
                        String key = cardRentId + Constants.STR_COMMA + cardPriceId;

                        QuerySuiXiangCardCdkConfigDetailDto detailDto = new QuerySuiXiangCardCdkConfigDetailDto();
                        detailDto.setCardBaseId(suixiangCardInfoDto.getCardBaseId());
                        detailDto.setCardPriceId(cardPriceId);
                        detailDto.setCardRentId(cardRentId);
                        String actDesc = "租期" + suixiangCardInfoDto.getRentDays() + "天 - 售价" + suixiangCardInfoDto.getSalesPrice().toPlainString() + "元";
                        detailDto.setActDesc(actDesc);
                        detailDto.setThirdSalePrice(rentIdAndThirdPriceMap.getOrDefault(key,null));
                        return detailDto;
                    }
            ).collect(Collectors.toList());
            resp.setList(lists);
        } catch (Exception e) {
            log.error("查询随享卡兑换码记录！异常，suixiangCardBaseId={}", input.getCardBaseId(), e);
            baseResponse.setCode(-1);
            baseResponse.setMessage("查询随享卡兑换码记录失败");
        }
        return resp;
    }



    @Override
    public QuerySuiXiangCardCdkConfigOutput querySuiXiangCardCdkConfig(QuerySuiXiangCardCdkConfigDetailInput input) {
        QuerySuiXiangCardCdkConfigOutput resp = new QuerySuiXiangCardCdkConfigOutput();
        BaseResponse baseResponse = new BaseResponse();
        resp.setBaseResponse(baseResponse);

        //参数校验
        if (input == null || input.getCardBaseId() <= 0) {
            log.error("查询随享卡兑换码生成记录，入参异常，input={}", JSON.toJSONString(input));
            baseResponse.setCode(-1);
            baseResponse.setMessage("参数异常");
            return resp;
        }
        long cardBaseId = input.getCardBaseId();

        try {
            SuixiangCardCdkConfigExample example = new SuixiangCardCdkConfigExample();
            example.createCriteria().andCardBaseIdEqualTo(cardBaseId);
            List<SuixiangCardCdkConfig> suixiangCardCdkConfigs = suixiangCardCdkConfigMapper.selectByExample(example);
            List<QuerySuiXiangCardCdkConfigDto> lists = suixiangCardCdkConfigs.stream().map(
                    cardCdkConfig -> {
                        QuerySuiXiangCardCdkConfigDto dto = new QuerySuiXiangCardCdkConfigDto();
                        dto.setActName(cardCdkConfig.getActName());
                        dto.setCdkConfigId(cardCdkConfig.getId());
                        dto.setCreateOperName(cardCdkConfig.getCreateOperName());
                        dto.setTotalQuantity(cardCdkConfig.getTotalQuantity());
                        return dto;
                    }
            ).collect(Collectors.toList());
            resp.setList(lists);
        } catch (Exception e) {
            log.error("查询随享卡兑换码生成记录！异常，suixiangCardBaseId={}", cardBaseId, e);
            baseResponse.setCode(-1);
            baseResponse.setMessage("查询随享卡兑换码生成记录失败");
        }
        return resp;
    }

    private ActivateSuixiangCardCdkOutput activateSuixiangCardCdkByMobile(ActivateSuixiangCardCdkInput activateSuixiangCardCdkInput) {
        ActivateSuixiangCardCdkOutput output = new ActivateSuixiangCardCdkOutput();
        BaseResponse baseResponse = new BaseResponse();
        output.setBaseResponse(baseResponse);

        // step1: 兑换码是否正确，否：该兑换码不存在，请重新输⼊
        String cdkey = activateSuixiangCardCdkInput.getCdkey();
        String mobile = activateSuixiangCardCdkInput.getMobile();
        if (StringUtils.isBlank(cdkey) || StringUtils.isBlank(mobile)) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("参数错误");
            return output;
        }
        String key = "lock_activateSuixiangCardCdk_" + cdkey;
        JedisLock lock = new JedisLock(key, 50 * 1000);
        Jedis jedis = JedisUtil.getJedis();
        try {
            log.info("用户mobile:{}尝试获取激活随享卡cdk锁.key:{}", mobile, key);
            if (!lock.acquire(jedis)) {
                log.error("用户mobile:{}尝试获取激活随享卡cdk锁失败.key:{}", mobile,  key);
                baseResponse.setCode(ActivateErrorEnum.ACTIVATED_FREQUENT.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.ACTIVATED_FREQUENT.getDesc());
                return output;
            }
            // step1: 兑换码是否正确，否：该兑换码不存在，请重新输⼊
            if (cdkey.length() != 16) {
                baseResponse.setCode(ActivateErrorEnum.CDKEY_EXPIRED.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_EXPIRED.getDesc());
                return output;
            }
            SuixiangCardCdkExample suixiangCardCdkExample = new SuixiangCardCdkExample();
            suixiangCardCdkExample.createCriteria().andCdkeyEqualTo(cdkey);
            List<SuixiangCardCdk> suixiangCardCdks = suixiangCardCdkMapper.selectByExample(suixiangCardCdkExample);
            if (CollectionUtils.isEmpty(suixiangCardCdks)) {
                log.error("该兑换码不存在，请重新输⼊.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.CDKEY_EXPIRED.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_EXPIRED.getDesc());
                return output;
            }
            SuixiangCardCdk suixiangCardCdk = suixiangCardCdks.get(0);
            SuixiangCardRentDays suixiangCardRentDays = suixiangCardRentDaysMapper.selectByPrimaryKey(suixiangCardCdk.getCardRentId());
            if (suixiangCardRentDays == null) {
                log.error("suixiangCardRentDays为空.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.CDKEY_EXPIRED.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_EXPIRED.getDesc());
                return output;
            }

            // cdk随享卡价格表
            SuixiangCardPrice cdkSuiXiangCardPrice = suixiangCardPriceMapper.selectByPrimaryKey(suixiangCardCdk.getCardPriceId());
            if (cdkSuiXiangCardPrice == null) {
                log.error("cdkSuiXiangCardPrice 为空.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.CDKEY_EXPIRED.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_EXPIRED.getDesc());
                return output;
            }


            // step2: 兑换码是否活动已失效？ 否：该活动的兑换码已失效！
            Date nowDate = new Date();
            SuixiangCardBase suixiangCardBase = suixiangCardBaseMapper.selectByPrimaryKey(suixiangCardCdk.getCardBaseId());
            if (suixiangCardBase == null
                    || suixiangCardBase.getCardStatus() != SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus()
                    || suixiangCardBase.getSaleStartTime().after(nowDate)
                    || nowDate.after(suixiangCardBase.getSaleEndTime())) {
                log.error("兑换码不存在/该活动兑换码已失效.cdkey:{}, suixiangCardBase:{}", cdkey, JSON.toJSONString(suixiangCardBase));
                baseResponse.setCode(ActivateErrorEnum.CDKEY_EXPIRED.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_EXPIRED.getDesc());
                return output;
            }

            if (StringUtils.equals(mobile, suixiangCardCdk.getActivatedUserMobile())) {
                log.error("已领取过该卡.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.ACTIVATED_REPEAT.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.ACTIVATED_REPEAT.getDesc());
                return output;
            }

            // step3: 兑换码是否已被使⽤？ 否：该兑换码已被领取
            if (suixiangCardCdk.getIsActivated() == 1) {
                log.error("兑换码已被领取.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.ACTIVATED.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.ACTIVATED.getDesc());
                return output;
            }
            // 用户 已购买的随享卡数
            Integer suiXiangCardNum = suiXiangCardService.getPurchaseSuiXiangCardNum(mobile, suixiangCardCdk.getCardBaseId());
            if (suiXiangCardNum == null) {
                log.error("查询用户已购买的随享卡数量异常.suiXiangCardNum为空.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.CDKEY_EXPIRED.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.CDKEY_EXPIRED.getDesc());
                return output;
            }

            // step4: 该账号是否已成功兑换过其他随享卡兑换码, 暂时cdk兑换只兑换一张卡
            if (suixiangCardBase.getPurchaseLimitNum() > 0
                    && (suiXiangCardNum >= suixiangCardBase.getPurchaseLimitNum() || suixiangCardBase.getPurchaseLimitNum() < 1 + suiXiangCardNum)) {
                log.error("该次卡活动一个用户仅可兑换" + suixiangCardBase.getPurchaseLimitNum() + "张，已兑换" + suiXiangCardNum + "张.cdkey:{}", cdkey);
                baseResponse.setCode(ActivateErrorEnum.ACTIVATED_LIMIT.getStatus());
                baseResponse.setMessage(ActivateErrorEnum.ACTIVATED_LIMIT.getDesc());
                return output;
            }

            suixiangCardCdk.setIsActivated(1);
            suixiangCardCdk.setActivatedUserMobile(mobile);
            suixiangCardCdk.setActivatedTime(nowDate);
            suixiangCardCdk.setUpdateOperId(-1L);
            suixiangCardCdk.setUpdateOperName(ComUtils.splitStr("手机号预激活", Constants.OPER_NAME_LENGTH));
            suixiangCardCdk.setUpdateTime(nowDate);
            suixiangCardCdkMapper.updateByPrimaryKeySelective(suixiangCardCdk);

            // 操作人取生成cdkey的人
            OperatorDto operatorDto = new OperatorDto();
            operatorDto.setOperatorId(suixiangCardCdk.getCreateOperId());
            operatorDto.setOperatorName(suixiangCardCdk.getCreateOperName());

            // 预发卡
            SuixiangCardUseTemp suixiangCardUseTemp = new SuixiangCardUseTemp();
            suixiangCardUseTemp.setCardBaseId(suixiangCardCdk.getCardBaseId());
            suixiangCardUseTemp.setCardPriceId(suixiangCardCdk.getCardPriceId());
            suixiangCardUseTemp.setCardType(1);
            suixiangCardUseTemp.setCardName(suixiangCardBase.getCardName());
            suixiangCardUseTemp.setCdkey(cdkey);
            suixiangCardUseTemp.setMobilePhone(mobile);
            suixiangCardUseTemp.setQuantity(1);
            suixiangCardUseTemp.setCdkey(cdkey);
            suixiangCardUseTemp.setCdkId(suixiangCardCdk.getId());
            suixiangCardUseTemp.setStartTime(nowDate);
            LocalDateTime startLocalDateTime = DateUtil.dateToLocalDateTime(nowDate);
            LocalDateTime expiresLocalDateTime = startLocalDateTime.plusMonths(suixiangCardBase.getValidDaysType());
            suixiangCardUseTemp.setExpiresTime(DateUtil.localDateTimeToDate(expiresLocalDateTime));
            suixiangCardUseTemp.setIssueType(SuiXiangCardIssueTypeEnum.EXCHANGE.getType());
            suixiangCardUseTemp.setObtainType(SuiXiangCardObtainTypeEnum.SCAN.getType());
            if (operatorDto != null) {
                suixiangCardUseTemp.setCreateOperId(operatorDto.getOperatorId());
                suixiangCardUseTemp.setCreateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
                suixiangCardUseTemp.setUpdateOperId(operatorDto.getOperatorId());
                suixiangCardUseTemp.setUpdateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
            }
            suixiangCardUseTempMapper.insertSelective(suixiangCardUseTemp);
        } catch (Exception e) {
            log.error("兑换码兑换失败.cdkey:{}", cdkey, e);
            baseResponse.setCode(-1);
            baseResponse.setMessage("兑换码兑换失败，请稍后重试");
            return output;
        } finally {
            if (jedis != null) {
                lock.releaseExcu(jedis);
                jedis.close();
            }
        }

        baseResponse.setCode(0);
        baseResponse.setMessage("兑换成功");
        return output;
    }

    @Override
    public OfferSuixiangCardPreCdkOutput offerSuixiangCardPreCdk(OfferSuixiangCardPreCdkInput input) {
        OfferSuixiangCardPreCdkOutput offerSuixiangCardPreCdkOutput = new OfferSuixiangCardPreCdkOutput();
        BaseResponse baseResponse = new BaseResponse();
        offerSuixiangCardPreCdkOutput.setBaseResponse(baseResponse);

        MembershipBasicInfo membershipBasicInfo = memberShipService.getUserBasicInfo(input.getMid());

        SuixiangCardUseTempExample suixiangCardUseTempExample = new SuixiangCardUseTempExample();
        suixiangCardUseTempExample.createCriteria().andMobilePhoneEqualTo(membershipBasicInfo.getMobilePhone());
        List<SuixiangCardUseTemp> suixiangCardUseTemps = suixiangCardUseTempMapper.selectByExample(suixiangCardUseTempExample);
        for (SuixiangCardUseTemp suixiangCardUseTemp : suixiangCardUseTemps) {
            try {
                log.info("预激活发放随享卡,id:{}", suixiangCardUseTemp.getId());
                if (suixiangCardUseTemp.getIsOffer() == 1) {
                    log.error("预激活发放随享卡，随享卡已发放过了");
                    continue;
                }
                SuixiangCardCdk suixiangCardCdk = suixiangCardCdkMapper.selectByPrimaryKey(suixiangCardUseTemp.getCdkId());
                SuixiangCardBase suixiangCardBase = suixiangCardBaseMapper.selectByPrimaryKey(suixiangCardUseTemp.getCardBaseId());
                if (suixiangCardBase == null) {
                    log.error("预激活发放随享卡，随享卡基础信息不存在,cardBaseId:{}.", suixiangCardUseTemp.getCardBaseId());
                    throw new RuntimeException("随享卡基础信息不存在");
                }
                SuixiangCardRentDays suixiangCardRentDays = suixiangCardRentDaysMapper.selectByPrimaryKey(suixiangCardCdk.getCardRentId());
                if (suixiangCardRentDays == null) {
                    log.error("预激活发放随享卡，随享卡租期信息不存在,cardRentId:{}.", suixiangCardCdk.getCardRentId());
                    throw new RuntimeException("随享卡租期信息不存在");
                }


                SuixiangCardPrice cdkSuiXiangCardPrice = suixiangCardPriceMapper.selectByPrimaryKey(suixiangCardCdk.getCardPriceId());
                if (cdkSuiXiangCardPrice == null) {
                    log.error("预激活发放随享卡，随享卡价格信息不存在,cardPriceId:{}.", suixiangCardCdk.getCardPriceId());
                    throw new RuntimeException("随享卡价格信息不存在");
                }

                // 获取可以合并的随享卡
                SuixiangCardUse canMergeSuixiangCardUse = getCanMergeSuixiangCardUse(membershipBasicInfo.getPkId(), suixiangCardCdk.getCardBaseId(), cdkSuiXiangCardPrice.getCarModelIds());
                if (suixiangCardBase.getCardStatus() == SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus() && canMergeSuixiangCardUse != null) {
                    // 合并卡 TODO 过期卡
                    mergeSuixiangCardByPreCdk(suixiangCardUseTemp, canMergeSuixiangCardUse, suixiangCardCdk, suixiangCardBase, suixiangCardRentDays, membershipBasicInfo);
                }else {
                    offerSuixiangCardByPreCdk(suixiangCardUseTemp, suixiangCardCdk, suixiangCardBase, suixiangCardRentDays, membershipBasicInfo);
                }
            } catch (Exception e) {
                log.error("兑换码兑换失败！", e);
            }
            log.info("预激活发放随享卡，兑换码兑换成功,cdkey:{}.", suixiangCardUseTemp.getCdkey());
        }
        return offerSuixiangCardPreCdkOutput;
    }


    private void offerSuixiangCardByPreCdk(SuixiangCardUseTemp suixiangCardUseTemp, SuixiangCardCdk suixiangCardCdk, SuixiangCardBase suixiangCardBase, SuixiangCardRentDays suixiangCardRentDays,  MembershipBasicInfo membershipBasicInfo) {
        Date nowDate = new Date();
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setOperatorId(suixiangCardCdk.getCreateOperId());
        operatorDto.setOperatorName(suixiangCardCdk.getCreateOperName());

        // 激活信息
        suixiangCardCdk.setActivatedMid(membershipBasicInfo.getMid());
        suixiangCardCdk.setActivatedUserName(membershipBasicInfo.getName());
        suixiangCardCdk.setActivatedUserMobile(membershipBasicInfo.getMobilePhone());
        suixiangCardCdk.setUpdateOperId(membershipBasicInfo.getPkId());
        suixiangCardCdk.setUpdateOperName(ComUtils.splitStr(membershipBasicInfo.getName(), Constants.OPER_NAME_LENGTH));
        suixiangCardCdk.setUpdateTime(nowDate);

        // 预发临时表
        suixiangCardUseTemp.setIsOffer(1);
        // 购买信息
        SuixiangCardPurchaseRecord suixiangCardPurchaseRecord = new SuixiangCardPurchaseRecord();
        suixiangCardPurchaseRecord.setOrderSeq(ComUtils.createOrderSeq(membershipBasicInfo.getPkId()));
        suixiangCardPurchaseRecord.setOrgId(suixiangCardBase.getOrgId());
        suixiangCardPurchaseRecord.setUserId(membershipBasicInfo.getPkId());
        suixiangCardPurchaseRecord.setUserMobile(membershipBasicInfo.getMobilePhone());
        suixiangCardPurchaseRecord.setCardBaseId(suixiangCardBase.getId());
        suixiangCardPurchaseRecord.setCardName(suixiangCardBase.getCardName());
        suixiangCardPurchaseRecord.setCardRentId(suixiangCardRentDays.getId());
        suixiangCardPurchaseRecord.setCardUseIds("-1");
        suixiangCardPurchaseRecord.setCardPriceId(suixiangCardCdk.getCardPriceId());
        suixiangCardPurchaseRecord.setIssueType(suixiangCardUseTemp.getIssueType());
        suixiangCardPurchaseRecord.setObtainType(suixiangCardUseTemp.getObtainType());
        suixiangCardPurchaseRecord.setPaymentStatus(SuiXiangCardPaymentStatusEnum.PAY.getPaymentStatus());
        suixiangCardPurchaseRecord.setQuantity(suixiangCardUseTemp.getQuantity());
        suixiangCardPurchaseRecord.setPayTime(nowDate);
        suixiangCardPurchaseRecord.setRealAmount(BigDecimal.ZERO);
        suixiangCardPurchaseRecord.setCreateTime(nowDate);
        suixiangCardPurchaseRecord.setCdkId(suixiangCardCdk.getId());
        if (operatorDto != null) {
            suixiangCardPurchaseRecord.setCreateOperId(operatorDto.getOperatorId());
            suixiangCardPurchaseRecord.setCreateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
            suixiangCardPurchaseRecord.setUpdateOperId(operatorDto.getOperatorId());
            suixiangCardPurchaseRecord.setUpdateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
        }
        // 准备新增suixiang_card_purchase_record_log表
        SuixiangCardPurchaseRecordLog recordLog = new SuixiangCardPurchaseRecordLog();
        recordLog.setOperationType(SuiXiangCardRecordLogOperationTypeEnum.GIVE_OUT.getOperationType());
        recordLog.setContent("扫码兑换随享卡");
        recordLog.setCreateTime(nowDate);
        if (operatorDto != null) {
            recordLog.setCreateOperId(operatorDto.getOperatorId());
            recordLog.setCreateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
            recordLog.setUpdateOperId(operatorDto.getOperatorId());
            recordLog.setUpdateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
        }
        List<SuixiangCardUse> useList = new ArrayList<>();
        List<SuixiangCardUseOpreationLog> useLogList = new ArrayList<>();
        for (int i = 0; i < suixiangCardPurchaseRecord.getQuantity(); i++) {
            // 准备新增suixiang_card_use表
            SuixiangCardUse use = new SuixiangCardUse();
            use.setCardBaseId(suixiangCardBase.getId());
            use.setCardPriceId(suixiangCardPurchaseRecord.getCardPriceId());
            use.setUserId(membershipBasicInfo.getPkId());
            use.setCardType(1); // 1-随享卡
            use.setCardName(suixiangCardPurchaseRecord.getCardName());
            // 校验临时表的卡活动状态有效
            if (suixiangCardBase.getCardStatus() == SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus()) {
                use.setCardStatus(SuiXiangCardStatusEnum.EFFECTIVE.getStatus());
            }
            else {
                use.setCardStatus(SuiXiangCardStatusEnum.EXPIRED.getStatus());
            }
            use.setStartTime(nowDate);
            LocalDateTime startLocalDateTime = DateUtil.dateToLocalDateTime(nowDate);
            LocalDateTime expiresLocalDateTime = startLocalDateTime.plusMonths(suixiangCardBase.getValidDaysType());
            use.setExpiresTime(DateUtil.localDateTimeToDate(expiresLocalDateTime));

            use.setInitDays(suixiangCardRentDays.getRentDays());
            use.setAvailableDays(suixiangCardRentDays.getRentDays());
            use.setUsedDays(0);
            use.setFrozenDays(0);
            use.setCreateTime(nowDate);
            if (operatorDto != null) {
                use.setCreateOperId(operatorDto.getOperatorId());
                use.setCreateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
                use.setUpdateOperId(operatorDto.getOperatorId());
                use.setUpdateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
            }
            useList.add(use);

            // 准备新增suixiang_card_use_opreation_log表
            SuixiangCardUseOpreationLog useLog = new SuixiangCardUseOpreationLog();
            useLog.setCardPriceId(suixiangCardPurchaseRecord.getCardPriceId());
            useLog.setCardGroup(1); // 卡类别：1-随享卡
            useLog.setOperationType(SuiXiangCardUseLogOperationTypeEnum.GIVE_OUT.getOperationType());
            useLog.setOriginSystem("storeManagementSystem"); // 门店管理系统
            useLog.setInitDays(suixiangCardRentDays.getRentDays());
            useLog.setAvailableDays(suixiangCardRentDays.getRentDays());
            useLog.setUsedDays(0);
            useLog.setFrozenDays(0);
            useLog.setCreateTime(nowDate);
            if (operatorDto != null) {
                useLog.setCreateOperId(operatorDto.getOperatorId());
                useLog.setCreateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
                useLog.setUpdateOperId(operatorDto.getOperatorId());
                useLog.setUpdateOperName(ComUtils.splitStr(operatorDto.getOperatorName(), Constants.OPER_NAME_LENGTH));
            }
            useLogList.add(useLog);
        }
        // 开启事务，新增记录
        suixiangCardBaseManager.giveSuiXiangCard(suixiangCardUseTemp, suixiangCardCdk, suixiangCardPurchaseRecord, recordLog, useList, useLogList);
    }


    /**
     * 将数据写入Excel文件
     *
     * @param suixiangCardCdkConfigId 随享卡配置ID
     * @param csvWriter               CSV写入器，用于写入Excel文件
     */
    private void doWriteToExcel(long suixiangCardCdkConfigId, CsvWriter csvWriter) {
        int pageNum = 1;
        int pageSize = 1000;
        while (true) {
            // 查询兑换记录，分页获取数据
            Page page = new Page(pageNum, pageSize);
            List<SuixiangCardCdkExportDto> suixiangCardCdkExportDtos = suixiangCardCdkMapper.queryExportListBySuixiangCardCdkConfigId(suixiangCardCdkConfigId, page);
            if (CollectionUtils.isEmpty(suixiangCardCdkExportDtos)) {
                // 数据查询完毕，结束循环
                log.info("导出随享卡兑换码记录！数据查询写入完毕，结束查询,pageNum={},suixiangCardCdkConfigId={}", pageNum, suixiangCardCdkConfigId);
                break;
            }
            // 处理并写入当前分页的数据
            int size = suixiangCardCdkExportDtos.size();
            log.info("导出随享卡兑换码记录！本分页查询完成, pageNum={}, pageSize={}, size={},suixiangCardCdkConfigId={}", pageNum, pageSize, size, suixiangCardCdkConfigId);
            suixiangCardCdkExportDtos.stream().forEach(dto -> {
                try {
                    // 根据是否激活，设置日期格式
                    String startDate;
                    String endDate;
                    String activatedMid = dto.getActivatedMid();
                    if (StringUtils.isBlank(activatedMid)) {
                        // 未激活
                        startDate = DateUtil.getFormatDate(dto.getSaleStartTime(), DateUtil.simple);
                        endDate = DateUtil.getFormatDate(dto.getSaleEndTime(), DateUtil.simple);
                    } else {
                        // 已激活
                        startDate = DateUtil.getFormatDate(dto.getCardStartTime(), DateUtil.simple);
                        endDate = DateUtil.getFormatDate(dto.getCardExpiresTime(), DateUtil.simple);
                    }

                    String actviatedUserMobile = StringUtils.isNotBlank(dto.getActivatedUserMobile()) ? dto.getActivatedUserMobile() : "";
                    String actviatedUserName = StringUtils.isNotBlank(dto.getActivatedUserName()) ? dto.getActivatedUserName() : "";
                    String createOperName = StringUtils.isNotBlank(dto.getCreateOperName()) ? dto.getCreateOperName() : "";


                    // 准备数据数组，包含随享卡的详细信息
                    String[] data = new String[]{
                            "\t" + dto.getCardBaseId(),
                            "\t" + dto.getCardName(),
                            "\t" + dto.getCdkDesc(),
                            "\t" + dto.getCdkey(),
                            "\t" + startDate,
                            "\t" + endDate,
                            "\t" + dto.getPurpose(),
                            "\t" + dto.getThirdSalesPrice(),
                            "\t" + dto.getIsActivatedDesc(),
                            "\t" + dto.getTotalOrder(),
                            "\t" + actviatedUserMobile,
                            "\t" + actviatedUserName,
                            "\t" + createOperName,
                    };
                    // 将数据写入CSV文件
                    csvWriter.writeRecord(data, true);
                } catch (Exception e) {
                    // 写入数据失败时记录错误日志
                    log.error("导出随享卡兑换码记录！数据写入失败，dto={},suixiangCardCdkConfigId={}", JSON.toJSONString(dto), suixiangCardCdkConfigId, e);
                }
            });
            // 刷新写入器缓冲区
            csvWriter.flush();
            log.info("导出随享卡兑换码记录！本分页数据写入完成, pageNum={}, pageSize={},  size={},suixiangCardCdkConfigId={}", pageNum, pageSize, size, suixiangCardCdkConfigId);
            pageNum++;
        }
    }

    private String zipCsvAndQrCode(SuixiangCardCdkConfig suixiangCardCdkConfig, String csvUrl, String qrCodeZipUrl) throws BusinessException {
        Path csvPath = Paths.get(csvUrl);
        String csvFileName = csvPath.getFileName().toString();
        OSSObject csv = FileUtil.downloadStream(FileUtil.ENV + csvUrl);
        Path qrCodeZipPath = Paths.get(qrCodeZipUrl);
        String qrCodeZipFileName = qrCodeZipPath.getFileName().toString();
        OSSObject qrCodeZip = FileUtil.downloadStream(FileUtil.ENV + qrCodeZipUrl);
        Map<String, InputStream> map = new HashMap<>();
        map.put(csvFileName, csv.getObjectContent());
        map.put(qrCodeZipFileName, qrCodeZip.getObjectContent());
        String zipName = getCardExportName(suixiangCardCdkConfig);
        try {
            return ZipUtil.zipStreamsAndUpload(map, TEMP_FILE_DIR + zipName + ".zip", "/export/suixiangcard/cdk/qr/" + zipName + ".zip");
            //ZipUtil.zipStreams(map, TEMP_FILE_DIR + zipName + ".zip");
            //File file = new File(TEMP_FILE_DIR + zipName + ".zip");
            //return Files.newInputStream(file.toPath());
        } catch (Exception e) {
            throw new BusinessException("压缩失败，请稍后尝试");
        }
    }

    private void doGenerateQrCode(QueryAndExportSuiXiangCardCdkInput input) throws BusinessException {
        long suiXiangCardCdkConfigId = input.getSuiXiangCardCdkConfigId();
        SuixiangCardCdkConfig suixiangCardCdkConfig = suixiangCardCdkConfigMapper.selectByPrimaryKey(suiXiangCardCdkConfigId);
        String zipName = "二维码" + getCardExportName(suixiangCardCdkConfig);
        String fileDir = TEMP_FILE_DIR + zipName;
		String zipPath = fileDir + ".zip";
		Path path = Paths.get(fileDir);
		Path parentDir = path.getParent();
		try {
			if (!Files.exists(parentDir)) {
				Files.createDirectories(parentDir);
			}
			if (Files.exists(path)) {
				throw new BusinessException("目录已存在，请稍后尝试");
			}
			if (!Files.exists(path)) {
				Files.createDirectories(path);
			}
		} catch (IOException e) {
			throw new BusinessException("创建目录失败");
		}

        com.extracme.evcard.membership.core.dto.OperatorDto operatorDto = new com.extracme.evcard.membership.core.dto.OperatorDto();
        operatorDto.setOperatorId(input.getOperatorDto().getOperatorId());
        operatorDto.setOperatorName(input.getOperatorDto().getOperatorName());
        SuixiangCardCdkConfigDetailExample suixiangCardCdkConfigDetailExample = new SuixiangCardCdkConfigDetailExample();
        suixiangCardCdkConfigDetailExample.createCriteria().andCardCdkConfigIdEqualTo(suiXiangCardCdkConfigId);
        List<SuixiangCardCdkConfigDetail> suixiangCardCdkConfigDetails = suixiangCardCdkConfigDetailMapper.selectByExample(suixiangCardCdkConfigDetailExample);
        for (SuixiangCardCdkConfigDetail suixiangCardCdkConfigDetail : suixiangCardCdkConfigDetails) {
            // TODO 创建目录
            String actDesc = suixiangCardCdkConfigDetail.getActDesc();
            String dir = fileDir + "/" + actDesc;
			Path wxQrCodePath = Paths.get(dir);
			try {
				if (!Files.exists(wxQrCodePath)) {
					Files.createDirectories(wxQrCodePath);
				}
			} catch (IOException e) {
				throw new BusinessException("创建目录失败");
			}

            SuixiangCardCdkExample suixiangCardCdkExample = new SuixiangCardCdkExample();
            suixiangCardCdkExample.createCriteria().andCardCdkConfigDetailIdEqualTo(suixiangCardCdkConfigDetail.getId());
            List<SuixiangCardCdk> suixiangCardCdks = suixiangCardCdkMapper.selectByExample(suixiangCardCdkExample);

            int size = suixiangCardCdks.size();
            int batchSize = 100;
            int totalBatch = (int) Math.ceil((double) size / batchSize);
            for (int i = 0; i < totalBatch; i++) {
                int start = i * batchSize; // 开始位置
                int end = Math.min((i + 1) * batchSize, size); // 结束位置
                List<SuixiangCardCdk> batchSuixiangCardCdks = suixiangCardCdks.subList(start, end);
                List<CreateWxQrCodeDto> createWxQrCodeDtos = new ArrayList<>();
                for (SuixiangCardCdk suixiangCardCdk : batchSuixiangCardCdks) {
                    if (StringUtils.isEmpty(suixiangCardCdk.getWechatCdkQrUrl())) {
                        CreateWxQrCodeDto createWxQrCodeDto = new CreateWxQrCodeDto();
                        createWxQrCodeDto.setShortLinkName(suixiangCardCdk.getCdkey());
                        createWxQrCodeDto.setWeChatPage(wechatPage);
                        String key = "?type=1&id=" + suixiangCardCdk.getCdkey() + "&source=1";
                        createWxQrCodeDto.setOriginalUrl(key);
                        createWxQrCodeDtos.add(createWxQrCodeDto);
                    }
                }

                Map<String, WxQrCodeDto> cdkQrCodeMap = new HashMap<>();
                for (int retries = 1; retries <= 3; retries++) {
                    try {
                        cdkQrCodeMap = shortlinkManagementService.batchCreateWxQrCode(createWxQrCodeDtos, "/qrCode/wechat/sxk/", operatorDto);
                        break;
                    } catch (Exception e) {
                        log.error("第{}次尝试，创建微信二维码失败", retries, e);
                    }
                }

                for (SuixiangCardCdk suixiangCardCdk : batchSuixiangCardCdks) {
                    String wxQrCode = "";
                    if (StringUtils.isEmpty(suixiangCardCdk.getWechatCdkQrUrl())) {
                        WxQrCodeDto wxQrCodeDto = cdkQrCodeMap.getOrDefault(suixiangCardCdk.getCdkey(), new WxQrCodeDto());
                        if (StringUtils.isEmpty(wxQrCodeDto.getKey())) {
                            log.error("创建微信二维码失败:{}", suixiangCardCdk.getCdkey());
                            int retries = 3;
                            for (; retries >= 0; retries--) {
                                try {
                                    List<CreateWxQrCodeDto> retryCreateWxQrCodeDtos = new ArrayList<>();
                                    CreateWxQrCodeDto retryDto = new CreateWxQrCodeDto();
                                    retryDto.setShortLinkName(suixiangCardCdk.getCdkey());
                                    retryDto.setWeChatPage(wechatPage);
                                    String key = "?type=1&id=" + suixiangCardCdk.getCdkey() + "&source=1";
                                    retryDto.setOriginalUrl(key);
                                    retryCreateWxQrCodeDtos.add(retryDto);
                                    Map<String, WxQrCodeDto> retryWxQrCodeDtoMap = shortlinkManagementService.batchCreateWxQrCode(retryCreateWxQrCodeDtos, "/qrCode/wechat/sxk/", operatorDto);
                                    wxQrCodeDto = retryWxQrCodeDtoMap.get(suixiangCardCdk.getCdkey());
                                    if (StringUtils.isEmpty(wxQrCodeDto.getKey())) {
                                        throw new BusinessException("创建微信二维码失败");
                                    }
                                    else {
                                        break;
                                    }
                                } catch (Exception e) {
                                    log.error("创建微信二维码失败，剩余尝试次数:{}", retries, e);
                                    if (retries == 0) {
                                        throw new BusinessException("生成兑换码失败，请重试");
                                    }
                                }
                            }
                        }
                        wxQrCode = wxQrCodeDto.getKey();


                        suixiangCardCdk.setWechatCdkQrUrl(wxQrCode);
                        suixiangCardCdk.setUpdateTime(new Date());
                        suixiangCardCdkMapper.updateByPrimaryKeySelective(suixiangCardCdk);
                    }
                    else {
                        wxQrCode = suixiangCardCdk.getWechatCdkQrUrl();
                    }
                    InputStream inputStream = null;
                    try (FileOutputStream outputStream = new FileOutputStream(dir + "/" + suixiangCardCdk.getCdkey() + ".png")) {
                        log.info("下载：{}到{}", wxQrCode, dir + "/" + suixiangCardCdk.getCdkey() + ".png");
                        OSSObject ossObject = FileUtil.downloadStream(FileUtil.ENV + wxQrCode);
                        //OSSObject ossObject = FileUtil.downloadStream("test" + wxQrCode);
                        inputStream = ossObject.getObjectContent();
                        IOUtils.copy(inputStream, outputStream);
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new BusinessException("保存二维码失败");
                    } finally {
                        if (inputStream != null) {
                            try {
                                inputStream.close();
                            } catch (IOException e) {
                                throw new BusinessException("保存二维码失败");
                            }
                        }
                    }
                }
            }

        }

        try {
            log.info("压缩文件地址:{}", zipPath);
			String url = ZipUtil.compressAndUpload(fileDir, zipPath, "/qrCode/wechat/" + zipName + ".zip");
            log.info("生成微信太阳码压缩包地址:{}", url);
            suixiangCardCdkConfig.setQrCodeZipUrl(url);
            suixiangCardCdkConfig.setUpdateTime(new Date());
            suixiangCardCdkConfigMapper.updateByPrimaryKeySelective(suixiangCardCdkConfig);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("压缩失败");
			throw new BusinessException("压缩失败");
		}
    }

    private String getCardExportName(SuixiangCardCdkConfig suixiangCardCdkConfig) throws BusinessException {
        if (suixiangCardCdkConfig == null) {
            throw new BusinessException("随享卡配置不能为空");
        }

        StringBuilder zipName = new StringBuilder();
        String cardName = suixiangCardCdkConfig.getCardName();
        try {
            // 对cardName 中特殊字符替换为空
            cardName = cardName.replaceAll("[\\\\/:*?\"<>|+-]", "");
        } catch (Exception e) {
            log.error("cardName 中特殊字符替换为空异常", e);
        }

        zipName.append(cardName).append(DateUtil.getFormatDate(new Date(), DateUtil.dtLong));
        return zipName.toString();
    }
}
