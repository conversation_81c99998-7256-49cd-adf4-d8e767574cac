package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.dto.CardDiscountInfoQueryDto;
import com.extracme.evcard.rpc.vipcard.dto.UserCardDiscountViewDto;
import com.extracme.evcard.rpc.vipcard.model.MmpUserCardDiscountInfo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface MmpUserCardDiscountInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_card_discount_info
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_card_discount_info
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    int insert(MmpUserCardDiscountInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_card_discount_info
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    int insertSelective(MmpUserCardDiscountInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_card_discount_info
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    MmpUserCardDiscountInfo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_card_discount_info
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    int updateByPrimaryKeySelective(MmpUserCardDiscountInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_card_discount_info
     *
     * @mbggenerated Mon May 17 16:12:05 CST 2021
     */
    int updateByPrimaryKey(MmpUserCardDiscountInfo record);

    /**
     *
     * @param userCardNo
     */
    MmpUserCardDiscountInfo userCardDiscount(@Param("userCardNo") Long userCardNo);

    List<UserCardDiscountViewDto> selectPage(CardDiscountInfoQueryDto queryDto);

    /**
     * 作废卡片
     * @param userCardNo
     */
    void cancelUserCard(Long userCardNo);

    /**
     * 根据时间+会员卡号，查询用户会员卡使用详情
     */
    MmpUserCardDiscountInfo getUserDiscountByTime(@Param("userCardNo") Long userCardNo, @Param("date") Date date);

    /**
     * 冻结 会员卡周期内的折扣金额
     */
    int freezeDiscountAmount(@Param("id") Long id,
                             @Param("frozenAmount") BigDecimal frozenAmount,
                             @Param("discountAmount") BigDecimal discountAmount,
                             @Param("userId") Long userId,
                             @Param("userName") String userName,
                             @Param("updateTime") Date updateTime);

    /**
     * 解冻 会员卡周期内的折扣金额
     */
    int unfreezeDiscountAmount(@Param("id") Long id,
                               @Param("unfreezeAmount") BigDecimal unfreezeAmount,
                               @Param("discountAmount") BigDecimal discountAmount,
                               @Param("userId") Long userId,
                               @Param("userName") String userName,
                               @Param("updateTime") Date updateTime);

    /**
     * 扣除 会员卡周期内的冻结金额
     */
    int deductFrozenDiscountAmount(@Param("id") Long id,
                                    @Param("deductAmount") BigDecimal deductAmount,
                                    @Param("frozenAmount") BigDecimal frozenAmount,
                                    @Param("userId") Long userId,
                                    @Param("userName") String userName,
                                    @Param("updateTime") Date updateTime);

    MmpUserCardDiscountInfo userCardFrozenDiscounts(@Param("userCardNo") Long userCardNo);
}