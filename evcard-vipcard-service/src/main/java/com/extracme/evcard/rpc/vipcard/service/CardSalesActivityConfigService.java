package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.vipcard.dao.MmpCardDefMapper;
import com.extracme.evcard.rpc.vipcard.dao.MmpCardSalesActivityMapper;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.enums.CardActivityOperateEnum;
import com.extracme.evcard.rpc.vipcard.enums.CardActivityStatusEnum;
import com.extracme.evcard.rpc.vipcard.model.MmpCardDef;
import com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity;
import com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivityInfo;
import com.extracme.evcard.rpc.vipcard.service.inner.MemberCardInnerService;
import com.extracme.evcard.rpc.vipcard.service.inner.OrgService;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/29
 */
@Slf4j
@Service
public class CardSalesActivityConfigService implements ICardSalesActivityConfigService {

    @Resource
    private MemberCardInnerService memberCardInnerService;

    @Resource
    private OrgService orgService;

    @Resource
    ICardModelService cardModelService;

    @Autowired
    private MmpCardSalesActivityMapper mmpCardSalesActivityMapper;

    @Resource
    private MmpCardDefMapper mmpCardDefMapper;

    // 折扣卡审核允许的城市
    @Value("${audit.allow.orgcode:004Y}")
    private String auditAllowOrgCode;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse add(CardActivityConfigDto configDTO, OperatorDto operateDTO) {
        /**
         * 1 检查活动配置参数
         */
        BaseResponse checkResp = checkConfigInfo(configDTO, true);
        if(!Constants.STATUS_OK.equals(checkResp.getCode())) {
            return checkResp;
        }
        //校验活动所属机构与卡片所属机构的关系
        checkResp = checkCardOrg(configDTO.getCardId(), configDTO.getOrgId());
        if(!Constants.STATUS_OK.equals(checkResp.getCode())) {
            return checkResp;
        }
        //校验时间冲突
        checkResp = queryOrgTimeConflict(null, configDTO.getOrgId(),
                configDTO.getCardId(), configDTO.getStartTime(), configDTO.getEndTime());
        if(checkResp.getCode() != 0) {
            return checkResp;
        }

        BaseResponse resp = new BaseResponse();
        /**
         * 2 活动数据保存
         */
        MmpCardSalesActivity mmpCardSalesActivity = new MmpCardSalesActivity();
        BeanCopyUtils.copyProperties(configDTO, mmpCardSalesActivity);
        createCustomSettings(mmpCardSalesActivity, configDTO);
        mmpCardSalesActivity.setCreateOperId(operateDTO.getOperatorId());
        mmpCardSalesActivity.setCreateOperName(operateDTO.getOperatorName());
        mmpCardSalesActivity.setCreateTime(new Date());
        mmpCardSalesActivity.setUpdateOperId(operateDTO.getOperatorId());
        mmpCardSalesActivity.setUpdateOperName(operateDTO.getOperatorName());
        mmpCardSalesActivity.setUpdateTime(mmpCardSalesActivity.getCreateTime());
        mmpCardSalesActivity.setActivityStatus(CardActivityStatusEnum.CREATED.getStatus());
        mmpCardSalesActivity.setStatus(0);
        mmpCardSalesActivity.setMiscDesc(StringUtils.EMPTY);
        mmpCardSalesActivityMapper.add(mmpCardSalesActivity);
        /**
         * 3 后处理
         */
        operateDTO.setRemark("创建");
        //createPostProcess(taskCreateInputDTO, taskInfo, taskConfig);
        postProcess(mmpCardSalesActivity.getId(), mmpCardSalesActivity, CardActivityOperateEnum.CREATE, operateDTO);
        resp.setMessage("提交成功");
        return resp;
    }


    /**
     * 校验同类活动同公司有效期区间是否有活动冲突
     * @param id
     * @param orgId
     * @param startDate
     * @param endDate
     * @return
     */
    protected BaseResponse queryOrgTimeConflict(Long id, String orgId, Long cardId,
                                                String startDate, String endDate) {
        BaseResponse resp = new BaseResponse();
        List<Long> activityIds = mmpCardSalesActivityMapper.queryOrgTimeConflict(id, orgId, cardId, startDate, endDate);
        if (CollectionUtils.isNotEmpty(activityIds)) {
            resp.setCode(-1);
            resp.setMessage("该卡片当前已关联其他活动，请重新选择");
            return resp;
        }
        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse update(CardActivityConfigDto configDTO, OperatorDto operateDTO) {
        /**
         * TODO 待上架状态，仅允许变更预告时间
         */
        BaseResponse resp = new BaseResponse();
        /**
         * 1 检查活动状态
         */
        Long id = configDTO.getId();
        MmpCardSalesActivity oldActivity = mmpCardSalesActivityMapper.selectByPrimaryKey(id);
        if (oldActivity == null || !oldActivity.getStatus().equals(0)) {
            resp.setCode(-1);
            resp.setMessage("活动不存在");
            return resp;
        }
        //TODO状态检查
        if (CardActivityStatusEnum.RUNNING.getStatus().compareTo(oldActivity.getActivityStatus()) < 0) {
            resp.setCode(-1);
            resp.setMessage("活动不可编辑");
            return resp;
        }
        //修改进行中的活动, 增加库存
        if(CardActivityStatusEnum.RUNNING.getStatus().equals(oldActivity.getActivityStatus())) {
            return postUpdateStock(oldActivity, configDTO, operateDTO);
        }
        //修改已发布的活动, 修改预告时间
        if(CardActivityStatusEnum.PUBLISHED.getStatus().equals(oldActivity.getActivityStatus())) {
            return postUpdateAdvanceTime(oldActivity, configDTO, operateDTO);
        }
        /**
         * 2 检查活动配置参数
         */
        BaseResponse checkResp = checkConfigInfo(configDTO, false);
        if(!Constants.STATUS_OK.equals(checkResp.getCode())) {
            return checkResp;
        }
        //校验活动所属机构与卡片所属机构的关系
        Long cardId = configDTO.getCardId() == null ? oldActivity.getCardId() : configDTO.getCardId();
        checkResp = checkCardOrg(cardId, configDTO.getOrgId());
        if(!Constants.STATUS_OK.equals(checkResp.getCode())) {
            return checkResp;
        }
        //校验时间冲突
        checkResp = queryOrgTimeConflict(configDTO.getId(), configDTO.getOrgId(),
                configDTO.getCardId(), configDTO.getStartTime(), configDTO.getEndTime());
        if(checkResp.getCode() != 0) {
            return checkResp;
        }

        /**
         * 3 数据保存
         */
        MmpCardSalesActivity mmpCardSalesActivity = new MmpCardSalesActivity();
        BeanCopyUtils.copyProperties(configDTO, mmpCardSalesActivity);
        createCustomSettings(mmpCardSalesActivity, configDTO);
        mmpCardSalesActivity.setUpdateOperId(operateDTO.getOperatorId());
        mmpCardSalesActivity.setUpdateOperName(operateDTO.getOperatorName());
        mmpCardSalesActivity.setUpdateTime(new Date());
        mmpCardSalesActivityMapper.updateByPrimaryKeySelective(mmpCardSalesActivity);
        /**
         * 4 后处理
         */
        operateDTO.setRemark("修改活动" + buildUpdateLogs(oldActivity, configDTO));
        //createPostProcess(taskCreateInputDTO, taskInfo, taskConfig);
        postProcess(mmpCardSalesActivity.getId(), mmpCardSalesActivity, CardActivityOperateEnum.UPDATE, operateDTO);
        resp.setMessage("提交成功");
        return resp;
    }

    private BaseResponse checkCardOrg(Long cardId, String activityOrgId){
        BaseResponse resp = new BaseResponse();
        MmpCardDef cardDef = mmpCardDefMapper.selectByPrimaryKey(cardId);
        if(cardDef == null || cardDef.getStatus() != 0) {
            resp.setCode(-1);
            resp.setMessage("卡片【" + cardId + " 】不存在或被禁用，暂不可使用");
            return resp;
        }
        if(!StringUtils.startsWith(cardDef.getOrgId(), activityOrgId)) {
            resp.setCode(-1);
            resp.setMessage("仅限使用本公司或下级公司的卡片，请重新选择");
            return resp;
        }
        return resp;
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseResponse postUpdateStock(MmpCardSalesActivity oldActivity,
                                            CardActivityConfigDto configDTO, OperatorDto operateDTO) {
        BaseResponse resp = new BaseResponse();
        Long stock = configDTO.getStock();
        //仅允许修改预告时间
        if (stock == null) {
            resp.setCode(-1);
            resp.setMessage("请指定库存");
            return resp;
        }
        if(oldActivity.getStock() >= stock) {
            resp.setCode(-1);
            resp.setMessage("上架中活动仅支持增加库存");
            return resp;
        }
        //3.1 活动数据保存
        MmpCardSalesActivity mmpCardSalesActivity = new MmpCardSalesActivity();
        mmpCardSalesActivity.setId(oldActivity.getId());
        mmpCardSalesActivity.setStock(stock);
        mmpCardSalesActivity.setUpdateOperId(operateDTO.getOperatorId());
        mmpCardSalesActivity.setUpdateOperName(operateDTO.getOperatorName());
        mmpCardSalesActivity.setUpdateTime(new Date());
        mmpCardSalesActivityMapper.updateByPrimaryKeySelective(mmpCardSalesActivity);
        //4 后处理
        operateDTO.setRemark("修改活动，库存由" + oldActivity.getStock() + "改为" + stock
            + "，原初始库存为" + oldActivity.getOpeningStock());
        postProcess(oldActivity.getId(), oldActivity, CardActivityOperateEnum.UPDATE, operateDTO);
        resp.setMessage("提交成功");
        return resp;
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseResponse postUpdateAdvanceTime(MmpCardSalesActivity oldActivity,
                                              CardActivityConfigDto configDTO, OperatorDto operateDTO) {
        BaseResponse resp = new BaseResponse();
        Date stateDateTime = DateUtil.getDateFromStrEx(configDTO.getStartTime(), DateUtil.dtLong);
        Date preNoticeDateTime =DateUtil.getDateFromStrEx(configDTO.getAdvanceNoticeTime(), DateUtil.dtLong);
        //仅允许修改预告时间
        if (preNoticeDateTime == null) {
            resp.setCode(-1);
            resp.setMessage("请选择活动预告开始时间");
            return resp;
        }
        if(preNoticeDateTime.getTime() > stateDateTime.getTime()) {
            resp.setCode(-1);
            resp.setMessage("预告开始时间应早于卡片上架时间");
            return resp;
        }
        if(preNoticeDateTime.getTime() <= System.currentTimeMillis()) {
            resp.setCode(-1);
            resp.setMessage("预告开始时间应晚于当前时间");
            return resp;
        }
        //3.1 活动数据保存
        MmpCardSalesActivity mmpCardSalesActivity = new MmpCardSalesActivity();
        mmpCardSalesActivity.setId(oldActivity.getId());
        mmpCardSalesActivity.setAdvanceNoticeTime(preNoticeDateTime);
        mmpCardSalesActivity.setUpdateOperId(operateDTO.getOperatorId());
        mmpCardSalesActivity.setUpdateOperName(operateDTO.getOperatorName());
        mmpCardSalesActivity.setUpdateTime(new Date());
        mmpCardSalesActivityMapper.updateByPrimaryKeySelective(mmpCardSalesActivity);
        //4 后处理
        String originPreDateStr = DateUtil.getFormatDate(oldActivity.getAdvanceNoticeTime(), DateUtil.simple);
        String preDateStr = DateUtil.getFormatDate(preNoticeDateTime, DateUtil.simple);
        operateDTO.setRemark("修改活动，预告开始时间由" + originPreDateStr + "改为" + preDateStr);
        postProcess(oldActivity.getId(), oldActivity, CardActivityOperateEnum.UPDATE, operateDTO);
        resp.setMessage("提交成功");
        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse delete(Long id, OperatorDto operateDTO) {
        Long operatorId = operateDTO.getOperatorId();
        String operatorName = operateDTO.getOperatorName();
        String remark = "删除";
        if(StringUtils.isBlank(operateDTO.getRemark())) {
            operateDTO.setRemark(remark);
        }
        //1. 检查活动状态
        MmpCardSalesActivity oldActivity = mmpCardSalesActivityMapper.selectByPrimaryKey(id);
        if (oldActivity == null || oldActivity.getStatus() != 0) {
            return new BaseResponse(-1,"卡片不存在，不可删除");
        }
        if (CardActivityStatusEnum.RUNNING.getStatus().compareTo(oldActivity.getActivityStatus()) <= 0) {
            return new BaseResponse(-1,"活动已上架，不可删除");
        }
        //2. 逻辑删除活动
        int count = mmpCardSalesActivityMapper.disable(id, operatorId, operatorName);
        if (count < 1) {
            return new BaseResponse(-1,remark + "失败");
        }
        //4. 后处理
        return postProcess(id, oldActivity, CardActivityOperateEnum.DELETE, operateDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse publish(Long id, OperatorDto operateDTO) {
        Long operatorId = operateDTO.getOperatorId();
        String operatorName = operateDTO.getOperatorName();
        String remark = "审核通过";
        if(StringUtils.isBlank(operateDTO.getRemark())) {
            operateDTO.setRemark(remark);
        }
        //1. 检查活动状态
        MmpCardSalesActivity oldActivity = mmpCardSalesActivityMapper.selectByPrimaryKey(id);
        if (oldActivity == null || oldActivity.getStatus() != 0) {
            return new BaseResponse(-1,"活动不存在，不可删除");
        }
        if (!CardActivityStatusEnum.CREATED.getStatus().equals(oldActivity.getActivityStatus())) {
            return new BaseResponse(-1,"活动状态状态已变更，请刷新后重试");
        }

        // app5.7 折扣卡活动审核时，卡的运营城市只 允许南京
        Long cardId = oldActivity.getCardId();
        if(!cardAcitvityOrgIdCheck(cardId)){
            return new BaseResponse(-1,"审核不通过，折扣卡不可用于南京以外的运营公司，如有问题可联系总部市场");
        }

        //判断是否需要立即开始
        Date now = new Date();
        if (now.compareTo(oldActivity.getStartTime()) >= 0) {
            operateDTO.setRemark("审核通过并上架");
            return start(id, operateDTO, true);
        }
        //2. 变更活动状态
        int count = mmpCardSalesActivityMapper.updateStatus(id, CardActivityStatusEnum.PUBLISHED.getStatus(),
                operatorId, operatorName);
        if (count < 1) {
            return new BaseResponse(-1,remark + "失败");
        }
        //4. 后处理
        return postProcess(id, oldActivity, CardActivityOperateEnum.PUBLISH, operateDTO);
    }

    /**
     * 折扣卡活动 审核 城市限制
     * @param cardId
     * @return
     */
    private boolean cardAcitvityOrgIdCheck(Long cardId) {
        if (cardId != null) {
            CardModelDetailDto cardModel = cardModelService.getCardModelById(cardId);
            if (cardModel != null) {
                String orgId = cardModel.getOrgId();
                if (StringUtils.isNotBlank(orgId) && (orgId.equals(auditAllowOrgCode))) {
                   return true;
                }
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse start(Long id, OperatorDto operateDTO) {
        return start(id, operateDTO, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseResponse start(Long id, OperatorDto operateDTO, Boolean isPublish) {
        Long operatorId = operateDTO.getOperatorId();
        String operatorName = operateDTO.getOperatorName();
        String remark = "立即上架";
        if(StringUtils.isBlank(operateDTO.getRemark())) {
            operateDTO.setRemark(remark);
        }
        //1. 检查活动状态
        MmpCardSalesActivity oldActivity = mmpCardSalesActivityMapper.selectByPrimaryKey(id);
        if (oldActivity == null || oldActivity.getStatus() != 0) {
            return new BaseResponse(-1,"活动不存在，不可上架");
        }
        if(!isPublish && !CardActivityStatusEnum.PUBLISHED.getStatus().equals(oldActivity.getActivityStatus())) {
            return new BaseResponse(-1,"活动状态已变更，请刷新后重试");
        }
        //活动已过截止时间
        Date now = new Date();
        if (now.compareTo(oldActivity.getEndTime()) > 0) {
            return new BaseResponse(-1,"活动时间已截止，无法上架");
        }
        //检查活动时间重叠问题
        //TODO 校验存在相同进行中活动 校验时间冲突
        MmpCardSalesActivity runningActivity = mmpCardSalesActivityMapper.selectRunningActivityfOrg(oldActivity.getOrgId(), oldActivity.getCardId());
        if(runningActivity != null) {
            return new BaseResponse(-1,"当前卡片该公司已有活动[" + runningActivity.getId() + "]上架中，暂不可上架");
        }

        //2. 变更活动状态及活动开始时间
        int count = mmpCardSalesActivityMapper.updateStatusStart(id, operatorId, operatorName);
        if (count < 1) {
            return new BaseResponse(-1,remark + "失败");
        }
        //4. 后处理
        return postProcess(id, oldActivity, CardActivityOperateEnum.START, operateDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse stop(Long id, OperatorDto operateDTO) {
        Long operatorId = operateDTO.getOperatorId();
        String operatorName = operateDTO.getOperatorName();
        String remark = "立即下架";
        if(StringUtils.isBlank(operateDTO.getRemark())) {
            operateDTO.setRemark(remark);
        }
        //1. 检查活动状态
        MmpCardSalesActivity oldActivity = mmpCardSalesActivityMapper.selectByPrimaryKey(id);
        if (oldActivity == null || oldActivity.getStatus() != 0) {
            return new BaseResponse(-1,"活动不存在，不可下架");
        }
        if (!CardActivityStatusEnum.RUNNING.getStatus().equals(oldActivity.getActivityStatus())) {
            return new BaseResponse(-1,"活动状态已变更，请刷新后重试");
        }
        //2. 变更活动状态及活动结束时间
        int count = mmpCardSalesActivityMapper.updateStatusStop(id, operatorId, operatorName);
        if (count < 1) {
            return new BaseResponse(-1,remark + "失败");
        }
        //4. 后处理
        return postProcess(id, oldActivity, CardActivityOperateEnum.STOP, operateDTO);
    }

    @Override
    public CardActivityConfigFullDto getDetail(Long id) {
        MmpCardSalesActivity activity = mmpCardSalesActivityMapper.selectByPrimaryKey(id);
        if(activity == null) {
            return null;
        }
        CardActivityConfigFullDto activityDto = new CardActivityConfigFullDto();
        BeanCopyUtils.copyProperties(activity, activityDto);
        if(BigDecimal.ZERO.equals(activityDto.getUnderlinePrice())) {
            activityDto.setUnderlinePrice(null);
        }
        // 机构名称
        String orgName = orgService.getOrgNameByOrgId(activity.getOrgId());
        activityDto.setOrgName(orgName);
        activityDto.setStartTime(DateUtil.getFormatDate(activity.getStartTime(), DateUtil.DATE_TYPE8));
        activityDto.setEndTime(DateUtil.getFormatDate(activity.getEndTime(), DateUtil.DATE_TYPE8));
        activityDto.setAdvanceNoticeTime(DateUtil.getFormatDate(activity.getAdvanceNoticeTime(), DateUtil.DATE_TYPE8));
        activityDto.setSalesVolume(activityDto.getOpeningStock() - activityDto.getStock());
        // 卡片信息
        CardModelListViewDto cardInfo = cardModelService.getCardModelDetailById(activity.getCardId());
        activityDto.setCard(cardInfo);
        return activityDto;
    }

    @Override
    public PageBeanBO<CardActivityListViewDto> queryPage(CardActivityQueryDto queryDto) {
        /**
         * TODO 开始时间范围临界
         */
        PageBeanBO<CardActivityListViewDto> pageBeanBO = new PageBeanBO<>();
        if(queryDto.getPageNum() == null) {
            queryDto.setPageNum(1);
        }
        if(queryDto.getPageSize() == null) {
            queryDto.setPageSize(10);
        }
        Page page;
        if (queryDto.getIsAll() == null || queryDto.getIsAll() == 0) {
            page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize(), false);
        } else {
            page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        }
        List<MmpCardSalesActivityInfo> list = mmpCardSalesActivityMapper.selectList(queryDto);

        List<CardActivityListViewDto> result = new ArrayList<>();
        for(MmpCardSalesActivityInfo activityInfo : list) {
            CardActivityListViewDto dto = new CardActivityListViewDto();
            BeanCopyUtils.copyProperties(activityInfo, dto);
            dto.setStartTime(DateUtil.getFormatDate(activityInfo.getStartTime(), DateUtil.DATE_TYPE8));
            dto.setEndTime(DateUtil.getFormatDate(activityInfo.getEndTime(), DateUtil.DATE_TYPE8));
            dto.setAdvanceNoticeTime(DateUtil.getFormatDate(activityInfo.getAdvanceNoticeTime(), DateUtil.DATE_TYPE8));
            //dto.setSalesVolume(activityInfo.getOpeningStock() - activityInfo.getStock());
            dto.setSalesVolume(activityInfo.getSalesVolume());
            dto.setCreateTime(DateUtil.getFormatDate(activityInfo.getCreateTime(), DateUtil.simple));
            dto.setUpdateTime(DateUtil.getFormatDate(activityInfo.getUpdateTime(), DateUtil.simple));
            dto.setCityNames(memberCardInnerService.getCityDesc(dto.getCityLimit()));
            //变更为商品车型描述
            //dto.setVehicleModelNames(memberCardInnerService.getVehicleModelDesc(dto.getVehicleModelLimit()));
            dto.setVehicleModelNames(memberCardInnerService.getGoodsVehicleModelDesc(dto.getGoodsModelId()));
            dto.setRentMethodNames(memberCardInnerService.getRentMethodDesc(dto.getRentMethod()));
            dto.setRentMethodGroupNames(memberCardInnerService.getRentMethodGroupDesc(dto.getRentMethodGroup()));
            result.add(dto);
        }
        PageBO pageBO = new PageBO();
        pageBO.setTotal(page.getTotal());
        pageBO.setPageNum(page.getPageNum());
        pageBO.setPageSize(page.getPageSize());
        pageBeanBO.setPage(pageBO);
        pageBeanBO.setList(result);
        return pageBeanBO;
    }

    @Override
    public PageBeanBO<CardActivityConfigLogDto> queryLogs(Long configId, Integer pageNum, Integer pageSize, Integer isAll) {
        return memberCardInnerService.queryLogs(Arrays.asList(configId), 1, pageNum, pageSize, isAll);
    }

    private void createCustomSettings(MmpCardSalesActivity activity, CardActivityConfigDto configDTO) {
        /**
         * 处理上下架及预告时间格式
         */
        activity.setOpeningStock(configDTO.getStock());
        activity.setStartTime(DateUtil.getDateFromStrEx(configDTO.getStartTime(), DateUtil.dtLong));
        activity.setEndTime(DateUtil.getDateFromStrEx(configDTO.getEndTime(), DateUtil.dtLong));
        activity.setAdvanceNoticeTime(DateUtil.getDateFromStrEx(configDTO.getAdvanceNoticeTime(), DateUtil.dtLong));
    }

    public BaseResponse postProcess(Long id, MmpCardSalesActivity activity,
                                    CardActivityOperateEnum opType, OperatorDto operateDTO) {
        //1 记录操作日志
        memberCardInnerService.saveCardActivityConfigLog(operateDTO.getRemark(), id, opType, operateDTO);
        //2 后处理-各活动自定义
        //customPostProcess(taskInfo, operator);
        //3 更新活动redis缓存
        //updateCache(id, taskInfo, operator);
        return new BaseResponse(0, operateDTO.getRemark() + "成功");
    }

    @Override
    public void saveCardActivityLog(Long activityId, CardActivityOperateEnum opType,
                                    String content, OperatorDto operateDTO) {
        memberCardInnerService.saveCardActivityConfigLog(content, activityId, opType, operateDTO);
    }

    private static final String LOG_FORMAT_STR = "，【%s】由【%s】改为【%s】";
    private static final String LOG_FORMAT_NUM = "，【%s】由【%d】改为【%d】";
    private String buildUpdateLogs(MmpCardSalesActivity oldActivity, CardActivityConfigDto configDTO){
        StringBuffer sb = new StringBuffer();
        try {
            if (NumberUtils.compare(oldActivity.getCardId(), configDTO.getCardId()) != 0) {
                sb.append(String.format(LOG_FORMAT_NUM, "卡片ID", oldActivity.getCardId(), configDTO.getCardId()));
            }
            if (!StringUtils.equals(oldActivity.getActivityName(), configDTO.getActivityName())) {
                sb.append(String.format(LOG_FORMAT_STR, "活动名称", oldActivity.getActivityName(), configDTO.getActivityName()));
            }
            if (!StringUtils.equals(oldActivity.getOrgId(), configDTO.getOrgId())) {
                String oriOrgName = oldActivity.getOrgId() + orgService.getOrgNameByOrgId(oldActivity.getOrgId());
                String orgName = configDTO.getOrgId() + orgService.getOrgNameByOrgId(configDTO.getOrgId());
                sb.append(String.format(LOG_FORMAT_STR, "运营公司", oriOrgName, orgName));
            }
            String oriSalesPrice = ComUtils.decimalFormat(oldActivity.getSalesPrice(), 2);
            String salesPrice = ComUtils.decimalFormat(configDTO.getSalesPrice(), 2);
            if (!StringUtils.equals(oriSalesPrice, salesPrice)) {
                sb.append(String.format(LOG_FORMAT_STR, "实际售价", oriSalesPrice, salesPrice));
            }
            String oriUnderlinePrice = ComUtils.decimalFormat(oldActivity.getUnderlinePrice(), 2);
            String underlinePrice = ComUtils.decimalFormat(configDTO.getUnderlinePrice(), 2);
            if (!StringUtils.equals(oriUnderlinePrice, underlinePrice)) {
                sb.append(String.format(LOG_FORMAT_STR, "划线价格", oriUnderlinePrice, underlinePrice));
            }
            String oriActivityTime = DateUtil.getFormatDate(oldActivity.getStartTime(), DateUtil.DATE_TYPE8) + "~" +
                    DateUtil.getFormatDate(oldActivity.getEndTime(), DateUtil.DATE_TYPE8);
            String activityTime = DateUtil.getFormatDate(configDTO.getStartTime(), DateUtil.dtLong, DateUtil.DATE_TYPE8) + "~" +
                    DateUtil.getFormatDate(configDTO.getEndTime(), DateUtil.dtLong, DateUtil.DATE_TYPE8);
            if (!StringUtils.equals(oriActivityTime, activityTime)) {
                sb.append(String.format(LOG_FORMAT_STR, "上架时间", oriActivityTime, activityTime));
            }
            String oriPreNoticeTime = DateUtil.getFormatDate(oldActivity.getAdvanceNoticeTime(), DateUtil.DATE_TYPE8);
            String preNoticeTime = DateUtil.getFormatDate(configDTO.getAdvanceNoticeTime(), DateUtil.dtLong, DateUtil.DATE_TYPE8);
            if (!StringUtils.equals(oriPreNoticeTime, preNoticeTime)) {
                sb.append(String.format(LOG_FORMAT_STR, "预告开始时间", oriPreNoticeTime, preNoticeTime));
            }
            if (NumberUtils.compare(oldActivity.getPersonPurchasesLimit(), configDTO.getPersonPurchasesLimit()) != 0) {
                sb.append(String.format(LOG_FORMAT_NUM, "最多可购买张数", oldActivity.getPersonPurchasesLimit(), configDTO.getPersonPurchasesLimit()));
            }
            if (NumberUtils.compare(oldActivity.getStock(), configDTO.getStock()) != 0) {
                sb.append(String.format(LOG_FORMAT_NUM, "库存", oldActivity.getStock(), configDTO.getStock()));
            }
            if (NumberUtils.compare(oldActivity.getPlatformType(), configDTO.getPlatformType()) != 0) {
                sb.append(String.format(LOG_FORMAT_NUM, "活动平台", getPlatformName(oldActivity.getPlatformType()),
                        getPlatformName(configDTO.getPlatformType())));
            }
            if (!StringUtils.equals(oldActivity.getRules(), configDTO.getRules())) {
                sb.append("，活动规则变更");
            }
        }catch (Exception e) {
            log.error("buildActivityUpdateLogs failed, id=" + configDTO.getId(), e);
        }
        return sb.toString();
    }

    private static final String[] PLATFORM_NAMES = {"EVCARD", "公众不可见"};
    private static String getPlatformName(Integer platformType) {
        if (platformType == null || platformType >= PLATFORM_NAMES.length || platformType < 0) {
            platformType = 0;
        }
        return PLATFORM_NAMES[platformType];
    }

    private BaseResponse checkConfigInfo(CardActivityConfigDto configDTO, boolean isCreate) {
        BaseResponse vo = new BaseResponse();
        if (null == configDTO) {
            vo.setCode(-1);
            vo.setMessage("活动配置参数不完整");
            return vo;
        }
        if (StringUtils.isBlank(configDTO.getActivityName())) {
            vo.setCode(-1);
            vo.setMessage("请填写活动名称");
            return vo;
        }
        if (StringUtils.isNotBlank(configDTO.getActivityName()) &&
                configDTO.getActivityName().length() > 50) {
            vo.setCode(-1);
            vo.setMessage("活动名称长度不满足要求，长度不能超过50个字符");
            return vo;
        }
        if (StringUtils.isBlank(configDTO.getOrgId())) {
            vo.setCode(-1);
            vo.setMessage("请选择运营公司");
            return vo;
        }
        if(isCreate && configDTO.getCardId() == null) {
            vo.setCode(-1);
            vo.setMessage("请选择卡片");
            return vo;
        }
        if(configDTO.getPlatformType() == null) {
            configDTO.setPlatformType(0);
        }
        if (configDTO.getSalesPrice() == null || BigDecimal.ZERO.compareTo(configDTO.getSalesPrice()) > 0) {
            vo.setCode(-1);
            vo.setMessage("请指定实际售价");
            return vo;
        }
        if (new BigDecimal(10000000).compareTo(configDTO.getSalesPrice()) <= 0) {
            vo.setCode(-1);
            vo.setMessage("实际售价限制不应超过7位整数");
            return vo;
        }
        if (configDTO.getUnderlinePrice() == null || configDTO.getPlatformType() == 1) {
            configDTO.setUnderlinePrice(BigDecimal.ZERO);
        } else {
            if (BigDecimal.ZERO.compareTo(configDTO.getUnderlinePrice()) >= 0) {
                vo.setCode(-1);
                vo.setMessage("划线价格应大于0");
            }
            if (new BigDecimal(10000000).compareTo(configDTO.getUnderlinePrice()) <= 0) {
                vo.setCode(-1);
                vo.setMessage("划线价格不应超过7位整数");
                return vo;
            }
            if (configDTO.getUnderlinePrice().compareTo(configDTO.getSalesPrice()) <= 0) {
                vo.setCode(-1);
                vo.setMessage("划线价格不可小于等于实际售价");
                return vo;
            }
        }

        if (configDTO.getPersonPurchasesLimit() == null || configDTO.getPersonPurchasesLimit() < 1 || configDTO.getPersonPurchasesLimit() >= 100) {
            vo.setCode(-1);
            vo.setMessage("请单人最多可购买张数，范围为1~99的整数");
            return vo;
        }
        if(configDTO.getStock() == null) {
            vo.setCode(-1);
            vo.setMessage("请填写活动库存");
            return vo;
        }
        if(configDTO.getStock() <= 0 || configDTO.getStock() > 10000000) {
            vo.setCode(-1);
            vo.setMessage("库存不应超过7位整数");
            return vo;
        }
        Date stateDateTime = DateUtil.getDateFromStrEx(configDTO.getStartTime(), DateUtil.dtLong);
        Date endDateTime = DateUtil.getDateFromStrEx(configDTO.getEndTime(), DateUtil.dtLong);
        Date preNoticeDateTime = DateUtil.getDateFromStrEx(configDTO.getAdvanceNoticeTime(), DateUtil.dtLong);
        if (stateDateTime == null || endDateTime == null) {
            vo.setCode(-1);
            vo.setMessage("请选择卡片上下架时间");
            return vo;
        }
        if (preNoticeDateTime == null) {
            if(configDTO.getPlatformType() == 1) {
                preNoticeDateTime = DateUtil.addMin(stateDateTime, -60);
                configDTO.setAdvanceNoticeTime(DateUtil.getFormatDate(preNoticeDateTime, DateUtil.dtLong));
            }else {
                vo.setCode(-1);
                vo.setMessage("请选择活动预告开始时间");
                return vo;
            }
        }
        if(endDateTime.getTime() <= stateDateTime.getTime()) {
            vo.setCode(-1);
            vo.setMessage("卡片上架结束时间需晚于开始时间");
            return vo;
        }
        if(endDateTime.getTime() <= System.currentTimeMillis()) {
            vo.setCode(-1);
            vo.setMessage("卡片上架结束时间需晚于当前时间");
            return vo;
        }
        if(configDTO.getPlatformType() != 1) {
            if (preNoticeDateTime.getTime() >= stateDateTime.getTime()) {
                vo.setCode(-1);
                vo.setMessage("预告开始时间应早于卡片上架时间");
                return vo;
            }
            if (preNoticeDateTime.getTime() <= System.currentTimeMillis()) {
                vo.setCode(-1);
                vo.setMessage("预告开始时间应晚于当前时间");
                return vo;
            }
        }
        //活动规则
        //vipcard s2 去除活动规则字段
//        if (isCreate && StringUtils.isBlank(configDTO.getRules())) {
//            vo.setCode(-1);
//            vo.setMessage("请填写活动规则");
//            return vo;
//        }
        if (StringUtils.isNotBlank(configDTO.getRules())
                && configDTO.getRules().length() > 100) {
            vo.setCode(-1);
            vo.setMessage("活动规则不满足要求，长度不能超过100个字符");
            return vo;
        }
        if (isCreate && configDTO.getPlatformType() == null) {
            //默认可见
            configDTO.setPlatformType(0);
        }

        return vo;
    }
}
