package com.extracme.evcard.rpc.vipcard.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SuixiangCardPurchaseRecordExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public SuixiangCardPurchaseRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNull() {
            addCriterion("org_id is null");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNotNull() {
            addCriterion("org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualTo(String value) {
            addCriterion("org_id =", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualTo(String value) {
            addCriterion("org_id <>", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThan(String value) {
            addCriterion("org_id >", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("org_id >=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThan(String value) {
            addCriterion("org_id <", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualTo(String value) {
            addCriterion("org_id <=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLike(String value) {
            addCriterion("org_id like", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotLike(String value) {
            addCriterion("org_id not like", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIn(List<String> values) {
            addCriterion("org_id in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotIn(List<String> values) {
            addCriterion("org_id not in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdBetween(String value1, String value2) {
            addCriterion("org_id between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotBetween(String value1, String value2) {
            addCriterion("org_id not between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserMobileIsNull() {
            addCriterion("user_mobile is null");
            return (Criteria) this;
        }

        public Criteria andUserMobileIsNotNull() {
            addCriterion("user_mobile is not null");
            return (Criteria) this;
        }

        public Criteria andUserMobileEqualTo(String value) {
            addCriterion("user_mobile =", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileNotEqualTo(String value) {
            addCriterion("user_mobile <>", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileGreaterThan(String value) {
            addCriterion("user_mobile >", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileGreaterThanOrEqualTo(String value) {
            addCriterion("user_mobile >=", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileLessThan(String value) {
            addCriterion("user_mobile <", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileLessThanOrEqualTo(String value) {
            addCriterion("user_mobile <=", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileLike(String value) {
            addCriterion("user_mobile like", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileNotLike(String value) {
            addCriterion("user_mobile not like", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileIn(List<String> values) {
            addCriterion("user_mobile in", values, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileNotIn(List<String> values) {
            addCriterion("user_mobile not in", values, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileBetween(String value1, String value2) {
            addCriterion("user_mobile between", value1, value2, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileNotBetween(String value1, String value2) {
            addCriterion("user_mobile not between", value1, value2, "userMobile");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdIsNull() {
            addCriterion("card_base_id is null");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdIsNotNull() {
            addCriterion("card_base_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdEqualTo(Long value) {
            addCriterion("card_base_id =", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdNotEqualTo(Long value) {
            addCriterion("card_base_id <>", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdGreaterThan(Long value) {
            addCriterion("card_base_id >", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_base_id >=", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdLessThan(Long value) {
            addCriterion("card_base_id <", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("card_base_id <=", value, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdIn(List<Long> values) {
            addCriterion("card_base_id in", values, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdNotIn(List<Long> values) {
            addCriterion("card_base_id not in", values, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdBetween(Long value1, Long value2) {
            addCriterion("card_base_id between", value1, value2, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("card_base_id not between", value1, value2, "cardBaseId");
            return (Criteria) this;
        }

        public Criteria andCardNameIsNull() {
            addCriterion("card_name is null");
            return (Criteria) this;
        }

        public Criteria andCardNameIsNotNull() {
            addCriterion("card_name is not null");
            return (Criteria) this;
        }

        public Criteria andCardNameEqualTo(String value) {
            addCriterion("card_name =", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotEqualTo(String value) {
            addCriterion("card_name <>", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameGreaterThan(String value) {
            addCriterion("card_name >", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameGreaterThanOrEqualTo(String value) {
            addCriterion("card_name >=", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameLessThan(String value) {
            addCriterion("card_name <", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameLessThanOrEqualTo(String value) {
            addCriterion("card_name <=", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameLike(String value) {
            addCriterion("card_name like", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotLike(String value) {
            addCriterion("card_name not like", value, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameIn(List<String> values) {
            addCriterion("card_name in", values, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotIn(List<String> values) {
            addCriterion("card_name not in", values, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameBetween(String value1, String value2) {
            addCriterion("card_name between", value1, value2, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardNameNotBetween(String value1, String value2) {
            addCriterion("card_name not between", value1, value2, "cardName");
            return (Criteria) this;
        }

        public Criteria andCardRentIdIsNull() {
            addCriterion("card_rent_id is null");
            return (Criteria) this;
        }

        public Criteria andCardRentIdIsNotNull() {
            addCriterion("card_rent_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardRentIdEqualTo(Long value) {
            addCriterion("card_rent_id =", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdNotEqualTo(Long value) {
            addCriterion("card_rent_id <>", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdGreaterThan(Long value) {
            addCriterion("card_rent_id >", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_rent_id >=", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdLessThan(Long value) {
            addCriterion("card_rent_id <", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdLessThanOrEqualTo(Long value) {
            addCriterion("card_rent_id <=", value, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdIn(List<Long> values) {
            addCriterion("card_rent_id in", values, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdNotIn(List<Long> values) {
            addCriterion("card_rent_id not in", values, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdBetween(Long value1, Long value2) {
            addCriterion("card_rent_id between", value1, value2, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardRentIdNotBetween(Long value1, Long value2) {
            addCriterion("card_rent_id not between", value1, value2, "cardRentId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIsNull() {
            addCriterion("card_price_id is null");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIsNotNull() {
            addCriterion("card_price_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdEqualTo(Long value) {
            addCriterion("card_price_id =", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotEqualTo(Long value) {
            addCriterion("card_price_id <>", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdGreaterThan(Long value) {
            addCriterion("card_price_id >", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_price_id >=", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdLessThan(Long value) {
            addCriterion("card_price_id <", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdLessThanOrEqualTo(Long value) {
            addCriterion("card_price_id <=", value, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdIn(List<Long> values) {
            addCriterion("card_price_id in", values, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotIn(List<Long> values) {
            addCriterion("card_price_id not in", values, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdBetween(Long value1, Long value2) {
            addCriterion("card_price_id between", value1, value2, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCardPriceIdNotBetween(Long value1, Long value2) {
            addCriterion("card_price_id not between", value1, value2, "cardPriceId");
            return (Criteria) this;
        }

        public Criteria andCdkIdIsNull() {
            addCriterion("cdk_id is null");
            return (Criteria) this;
        }

        public Criteria andCdkIdIsNotNull() {
            addCriterion("cdk_id is not null");
            return (Criteria) this;
        }

        public Criteria andCdkIdEqualTo(Long value) {
            addCriterion("cdk_id =", value, "cdkId");
            return (Criteria) this;
        }

        public Criteria andCdkIdNotEqualTo(Long value) {
            addCriterion("cdk_id <>", value, "cdkId");
            return (Criteria) this;
        }

        public Criteria andCdkIdGreaterThan(Long value) {
            addCriterion("cdk_id >", value, "cdkId");
            return (Criteria) this;
        }

        public Criteria andCdkIdGreaterThanOrEqualTo(Long value) {
            addCriterion("cdk_id >=", value, "cdkId");
            return (Criteria) this;
        }

        public Criteria andCdkIdLessThan(Long value) {
            addCriterion("cdk_id <", value, "cdkId");
            return (Criteria) this;
        }

        public Criteria andCdkIdLessThanOrEqualTo(Long value) {
            addCriterion("cdk_id <=", value, "cdkId");
            return (Criteria) this;
        }

        public Criteria andCdkIdIn(List<Long> values) {
            addCriterion("cdk_id in", values, "cdkId");
            return (Criteria) this;
        }

        public Criteria andCdkIdNotIn(List<Long> values) {
            addCriterion("cdk_id not in", values, "cdkId");
            return (Criteria) this;
        }

        public Criteria andCdkIdBetween(Long value1, Long value2) {
            addCriterion("cdk_id between", value1, value2, "cdkId");
            return (Criteria) this;
        }

        public Criteria andCdkIdNotBetween(Long value1, Long value2) {
            addCriterion("cdk_id not between", value1, value2, "cdkId");
            return (Criteria) this;
        }

        public Criteria andIssueTypeIsNull() {
            addCriterion("issue_type is null");
            return (Criteria) this;
        }

        public Criteria andIssueTypeIsNotNull() {
            addCriterion("issue_type is not null");
            return (Criteria) this;
        }

        public Criteria andIssueTypeEqualTo(Integer value) {
            addCriterion("issue_type =", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeNotEqualTo(Integer value) {
            addCriterion("issue_type <>", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeGreaterThan(Integer value) {
            addCriterion("issue_type >", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("issue_type >=", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeLessThan(Integer value) {
            addCriterion("issue_type <", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeLessThanOrEqualTo(Integer value) {
            addCriterion("issue_type <=", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeIn(List<Integer> values) {
            addCriterion("issue_type in", values, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeNotIn(List<Integer> values) {
            addCriterion("issue_type not in", values, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeBetween(Integer value1, Integer value2) {
            addCriterion("issue_type between", value1, value2, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("issue_type not between", value1, value2, "issueType");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusIsNull() {
            addCriterion("payment_status is null");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusIsNotNull() {
            addCriterion("payment_status is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusEqualTo(Integer value) {
            addCriterion("payment_status =", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusNotEqualTo(Integer value) {
            addCriterion("payment_status <>", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusGreaterThan(Integer value) {
            addCriterion("payment_status >", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("payment_status >=", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusLessThan(Integer value) {
            addCriterion("payment_status <", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusLessThanOrEqualTo(Integer value) {
            addCriterion("payment_status <=", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusIn(List<Integer> values) {
            addCriterion("payment_status in", values, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusNotIn(List<Integer> values) {
            addCriterion("payment_status not in", values, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusBetween(Integer value1, Integer value2) {
            addCriterion("payment_status between", value1, value2, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("payment_status not between", value1, value2, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Integer value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Integer value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Integer value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Integer value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Integer> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Integer> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Integer value1, Integer value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andPayTimeIsNull() {
            addCriterion("pay_time is null");
            return (Criteria) this;
        }

        public Criteria andPayTimeIsNotNull() {
            addCriterion("pay_time is not null");
            return (Criteria) this;
        }

        public Criteria andPayTimeEqualTo(Date value) {
            addCriterion("pay_time =", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotEqualTo(Date value) {
            addCriterion("pay_time <>", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThan(Date value) {
            addCriterion("pay_time >", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("pay_time >=", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThan(Date value) {
            addCriterion("pay_time <", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThanOrEqualTo(Date value) {
            addCriterion("pay_time <=", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeIn(List<Date> values) {
            addCriterion("pay_time in", values, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotIn(List<Date> values) {
            addCriterion("pay_time not in", values, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeBetween(Date value1, Date value2) {
            addCriterion("pay_time between", value1, value2, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotBetween(Date value1, Date value2) {
            addCriterion("pay_time not between", value1, value2, "payTime");
            return (Criteria) this;
        }

        public Criteria andRealAmountIsNull() {
            addCriterion("real_amount is null");
            return (Criteria) this;
        }

        public Criteria andRealAmountIsNotNull() {
            addCriterion("real_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRealAmountEqualTo(BigDecimal value) {
            addCriterion("real_amount =", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountNotEqualTo(BigDecimal value) {
            addCriterion("real_amount <>", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountGreaterThan(BigDecimal value) {
            addCriterion("real_amount >", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("real_amount >=", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountLessThan(BigDecimal value) {
            addCriterion("real_amount <", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("real_amount <=", value, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountIn(List<BigDecimal> values) {
            addCriterion("real_amount in", values, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountNotIn(List<BigDecimal> values) {
            addCriterion("real_amount not in", values, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_amount between", value1, value2, "realAmount");
            return (Criteria) this;
        }

        public Criteria andRealAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_amount not between", value1, value2, "realAmount");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqIsNull() {
            addCriterion("out_trade_seq is null");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqIsNotNull() {
            addCriterion("out_trade_seq is not null");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqEqualTo(String value) {
            addCriterion("out_trade_seq =", value, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqNotEqualTo(String value) {
            addCriterion("out_trade_seq <>", value, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqGreaterThan(String value) {
            addCriterion("out_trade_seq >", value, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqGreaterThanOrEqualTo(String value) {
            addCriterion("out_trade_seq >=", value, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqLessThan(String value) {
            addCriterion("out_trade_seq <", value, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqLessThanOrEqualTo(String value) {
            addCriterion("out_trade_seq <=", value, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqLike(String value) {
            addCriterion("out_trade_seq like", value, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqNotLike(String value) {
            addCriterion("out_trade_seq not like", value, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqIn(List<String> values) {
            addCriterion("out_trade_seq in", values, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqNotIn(List<String> values) {
            addCriterion("out_trade_seq not in", values, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqBetween(String value1, String value2) {
            addCriterion("out_trade_seq between", value1, value2, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andOutTradeSeqNotBetween(String value1, String value2) {
            addCriterion("out_trade_seq not between", value1, value2, "outTradeSeq");
            return (Criteria) this;
        }

        public Criteria andCardUseIdIsNull() {
            addCriterion("card_use_id is null");
            return (Criteria) this;
        }

        public Criteria andCardUseIdIsNotNull() {
            addCriterion("card_use_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardUseIdEqualTo(Long value) {
            addCriterion("card_use_id =", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdNotEqualTo(Long value) {
            addCriterion("card_use_id <>", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdGreaterThan(Long value) {
            addCriterion("card_use_id >", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_use_id >=", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdLessThan(Long value) {
            addCriterion("card_use_id <", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdLessThanOrEqualTo(Long value) {
            addCriterion("card_use_id <=", value, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdIn(List<Long> values) {
            addCriterion("card_use_id in", values, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdNotIn(List<Long> values) {
            addCriterion("card_use_id not in", values, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdBetween(Long value1, Long value2) {
            addCriterion("card_use_id between", value1, value2, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andCardUseIdNotBetween(Long value1, Long value2) {
            addCriterion("card_use_id not between", value1, value2, "cardUseId");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIsNull() {
            addCriterion("order_seq is null");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIsNotNull() {
            addCriterion("order_seq is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSeqEqualTo(String value) {
            addCriterion("order_seq =", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotEqualTo(String value) {
            addCriterion("order_seq <>", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThan(String value) {
            addCriterion("order_seq >", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThanOrEqualTo(String value) {
            addCriterion("order_seq >=", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThan(String value) {
            addCriterion("order_seq <", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThanOrEqualTo(String value) {
            addCriterion("order_seq <=", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLike(String value) {
            addCriterion("order_seq like", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotLike(String value) {
            addCriterion("order_seq not like", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIn(List<String> values) {
            addCriterion("order_seq in", values, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotIn(List<String> values) {
            addCriterion("order_seq not in", values, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqBetween(String value1, String value2) {
            addCriterion("order_seq between", value1, value2, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotBetween(String value1, String value2) {
            addCriterion("order_seq not between", value1, value2, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoIsNull() {
            addCriterion("pay_order_no is null");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoIsNotNull() {
            addCriterion("pay_order_no is not null");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoEqualTo(String value) {
            addCriterion("pay_order_no =", value, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoNotEqualTo(String value) {
            addCriterion("pay_order_no <>", value, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoGreaterThan(String value) {
            addCriterion("pay_order_no >", value, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("pay_order_no >=", value, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoLessThan(String value) {
            addCriterion("pay_order_no <", value, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoLessThanOrEqualTo(String value) {
            addCriterion("pay_order_no <=", value, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoLike(String value) {
            addCriterion("pay_order_no like", value, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoNotLike(String value) {
            addCriterion("pay_order_no not like", value, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoIn(List<String> values) {
            addCriterion("pay_order_no in", values, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoNotIn(List<String> values) {
            addCriterion("pay_order_no not in", values, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoBetween(String value1, String value2) {
            addCriterion("pay_order_no between", value1, value2, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andPayOrderNoNotBetween(String value1, String value2) {
            addCriterion("pay_order_no not between", value1, value2, "payOrderNo");
            return (Criteria) this;
        }

        public Criteria andRemindStatusIsNull() {
            addCriterion("remind_status is null");
            return (Criteria) this;
        }

        public Criteria andRemindStatusIsNotNull() {
            addCriterion("remind_status is not null");
            return (Criteria) this;
        }

        public Criteria andRemindStatusEqualTo(Integer value) {
            addCriterion("remind_status =", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusNotEqualTo(Integer value) {
            addCriterion("remind_status <>", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusGreaterThan(Integer value) {
            addCriterion("remind_status >", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("remind_status >=", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusLessThan(Integer value) {
            addCriterion("remind_status <", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusLessThanOrEqualTo(Integer value) {
            addCriterion("remind_status <=", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusIn(List<Integer> values) {
            addCriterion("remind_status in", values, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusNotIn(List<Integer> values) {
            addCriterion("remind_status not in", values, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusBetween(Integer value1, Integer value2) {
            addCriterion("remind_status between", value1, value2, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("remind_status not between", value1, value2, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andCancelTimeIsNull() {
            addCriterion("cancel_time is null");
            return (Criteria) this;
        }

        public Criteria andCancelTimeIsNotNull() {
            addCriterion("cancel_time is not null");
            return (Criteria) this;
        }

        public Criteria andCancelTimeEqualTo(Date value) {
            addCriterion("cancel_time =", value, "cancelTime");
            return (Criteria) this;
        }

        public Criteria andCancelTimeNotEqualTo(Date value) {
            addCriterion("cancel_time <>", value, "cancelTime");
            return (Criteria) this;
        }

        public Criteria andCancelTimeGreaterThan(Date value) {
            addCriterion("cancel_time >", value, "cancelTime");
            return (Criteria) this;
        }

        public Criteria andCancelTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("cancel_time >=", value, "cancelTime");
            return (Criteria) this;
        }

        public Criteria andCancelTimeLessThan(Date value) {
            addCriterion("cancel_time <", value, "cancelTime");
            return (Criteria) this;
        }

        public Criteria andCancelTimeLessThanOrEqualTo(Date value) {
            addCriterion("cancel_time <=", value, "cancelTime");
            return (Criteria) this;
        }

        public Criteria andCancelTimeIn(List<Date> values) {
            addCriterion("cancel_time in", values, "cancelTime");
            return (Criteria) this;
        }

        public Criteria andCancelTimeNotIn(List<Date> values) {
            addCriterion("cancel_time not in", values, "cancelTime");
            return (Criteria) this;
        }

        public Criteria andCancelTimeBetween(Date value1, Date value2) {
            addCriterion("cancel_time between", value1, value2, "cancelTime");
            return (Criteria) this;
        }

        public Criteria andCancelTimeNotBetween(Date value1, Date value2) {
            addCriterion("cancel_time not between", value1, value2, "cancelTime");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginIsNull() {
            addCriterion("merge_pay_origin is null");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginIsNotNull() {
            addCriterion("merge_pay_origin is not null");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginEqualTo(Integer value) {
            addCriterion("merge_pay_origin =", value, "mergePayOrigin");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginNotEqualTo(Integer value) {
            addCriterion("merge_pay_origin <>", value, "mergePayOrigin");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginGreaterThan(Integer value) {
            addCriterion("merge_pay_origin >", value, "mergePayOrigin");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginGreaterThanOrEqualTo(Integer value) {
            addCriterion("merge_pay_origin >=", value, "mergePayOrigin");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginLessThan(Integer value) {
            addCriterion("merge_pay_origin <", value, "mergePayOrigin");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginLessThanOrEqualTo(Integer value) {
            addCriterion("merge_pay_origin <=", value, "mergePayOrigin");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginIn(List<Integer> values) {
            addCriterion("merge_pay_origin in", values, "mergePayOrigin");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginNotIn(List<Integer> values) {
            addCriterion("merge_pay_origin not in", values, "mergePayOrigin");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginBetween(Integer value1, Integer value2) {
            addCriterion("merge_pay_origin between", value1, value2, "mergePayOrigin");
            return (Criteria) this;
        }

        public Criteria andMergePayOriginNotBetween(Integer value1, Integer value2) {
            addCriterion("merge_pay_origin not between", value1, value2, "mergePayOrigin");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNull() {
            addCriterion("create_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNotNull() {
            addCriterion("create_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdEqualTo(Long value) {
            addCriterion("create_oper_id =", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotEqualTo(Long value) {
            addCriterion("create_oper_id <>", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThan(Long value) {
            addCriterion("create_oper_id >", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_oper_id >=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThan(Long value) {
            addCriterion("create_oper_id <", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("create_oper_id <=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIn(List<Long> values) {
            addCriterion("create_oper_id in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotIn(List<Long> values) {
            addCriterion("create_oper_id not in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdBetween(Long value1, Long value2) {
            addCriterion("create_oper_id between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("create_oper_id not between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNull() {
            addCriterion("create_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNotNull() {
            addCriterion("create_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameEqualTo(String value) {
            addCriterion("create_oper_name =", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotEqualTo(String value) {
            addCriterion("create_oper_name <>", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThan(String value) {
            addCriterion("create_oper_name >", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_name >=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThan(String value) {
            addCriterion("create_oper_name <", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThanOrEqualTo(String value) {
            addCriterion("create_oper_name <=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLike(String value) {
            addCriterion("create_oper_name like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotLike(String value) {
            addCriterion("create_oper_name not like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIn(List<String> values) {
            addCriterion("create_oper_name in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotIn(List<String> values) {
            addCriterion("create_oper_name not in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameBetween(String value1, String value2) {
            addCriterion("create_oper_name between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotBetween(String value1, String value2) {
            addCriterion("create_oper_name not between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNull() {
            addCriterion("update_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNotNull() {
            addCriterion("update_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdEqualTo(Long value) {
            addCriterion("update_oper_id =", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotEqualTo(Long value) {
            addCriterion("update_oper_id <>", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThan(Long value) {
            addCriterion("update_oper_id >", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_oper_id >=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThan(Long value) {
            addCriterion("update_oper_id <", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("update_oper_id <=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIn(List<Long> values) {
            addCriterion("update_oper_id in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotIn(List<Long> values) {
            addCriterion("update_oper_id not in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdBetween(Long value1, Long value2) {
            addCriterion("update_oper_id between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("update_oper_id not between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNull() {
            addCriterion("update_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNotNull() {
            addCriterion("update_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameEqualTo(String value) {
            addCriterion("update_oper_name =", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotEqualTo(String value) {
            addCriterion("update_oper_name <>", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThan(String value) {
            addCriterion("update_oper_name >", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_oper_name >=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThan(String value) {
            addCriterion("update_oper_name <", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThanOrEqualTo(String value) {
            addCriterion("update_oper_name <=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLike(String value) {
            addCriterion("update_oper_name like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotLike(String value) {
            addCriterion("update_oper_name not like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIn(List<String> values) {
            addCriterion("update_oper_name in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotIn(List<String> values) {
            addCriterion("update_oper_name not in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameBetween(String value1, String value2) {
            addCriterion("update_oper_name between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotBetween(String value1, String value2) {
            addCriterion("update_oper_name not between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated do_not_delete_during_merge Wed Aug 28 15:43:47 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Wed Aug 28 15:43:47 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}