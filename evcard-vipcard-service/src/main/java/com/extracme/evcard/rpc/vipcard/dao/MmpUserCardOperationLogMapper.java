package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.dto.UserCardHistoryQueryDto;
import com.extracme.evcard.rpc.vipcard.model.MmpUserCardOperationLog;

import java.util.List;

public interface MmpUserCardOperationLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpUserCardOperationLog record);

    int insertSelective(MmpUserCardOperationLog record);

    MmpUserCardOperationLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpUserCardOperationLog record);

    int updateByPrimaryKey(MmpUserCardOperationLog record);

    /**
     * 批量保存日志
     * @param records
     * @return
     */
    int batchSave(List<MmpUserCardOperationLog> records);

    /**
     * 查询
     * @param queryDto
     * @return
     */
    List<MmpUserCardOperationLog> selectCardHistoryPage(UserCardHistoryQueryDto queryDto);
}