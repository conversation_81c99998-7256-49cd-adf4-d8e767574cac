package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.model.MmpCardPurchaseRecordLog;

public interface MmpCardPurchaseRecordLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_purchase_record_log
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_purchase_record_log
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    int insert(MmpCardPurchaseRecordLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_purchase_record_log
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    int insertSelective(MmpCardPurchaseRecordLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_purchase_record_log
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    MmpCardPurchaseRecordLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_purchase_record_log
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    int updateByPrimaryKeySelective(MmpCardPurchaseRecordLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_card_purchase_record_log
     *
     * @mbggenerated Tue Jun 22 16:27:09 CST 2021
     */
    int updateByPrimaryKey(MmpCardPurchaseRecordLog record);
}