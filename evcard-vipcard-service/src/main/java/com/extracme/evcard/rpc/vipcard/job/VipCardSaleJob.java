package com.extracme.evcard.rpc.vipcard.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.vipcard.dao.MmpCardRemindMapper;
import com.extracme.evcard.rpc.vipcard.dao.MmpCardSalesActivityMapper;
import com.extracme.evcard.rpc.vipcard.dto.UserRemindActivityDto;
import com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Component
@ElasticSimpleJob(jobName = "vipcard-rpc-vipCardSaleJob",
        cron = "0 0/1 * * * ?", description = "会员卡开售提醒", overwrite = true)
public class VipCardSaleJob implements SimpleJob {

    @Autowired
    private MmpCardRemindMapper mmpCardRemindMapper;

    @Resource
    private IMessagepushServ messageServ;

    @Resource
    private IMemberShipService memberShipService;

    @Resource
    private MmpCardSalesActivityMapper mmpCardSalesActivityMapper;

    @Override
    public void execute(ShardingContext shardingContext) {

        /**
         * 查询即将开售的活动
         */
        List<MmpCardSalesActivity> mmpCardSalesActivities =  mmpCardSalesActivityMapper.queryActivityNow();
        if (CollectionUtils.isEmpty(mmpCardSalesActivities) ){
            return;
        }

        List<Long> activityIds =  mmpCardSalesActivities.stream().map(MmpCardSalesActivity::getId).collect(Collectors.toList());


        List<UserRemindActivityDto> userRemindActivityDtos =  mmpCardRemindMapper.queryByIdAndActivity(null,null);
        if (CollectionUtils.isEmpty(userRemindActivityDtos)){
            return;
        }

        log.info("卡销售提醒：活动id{},待提醒{}", JSON.toJSONString(activityIds),JSON.toJSONString(userRemindActivityDtos));
        userRemindActivityDtos.stream().forEach(p->{
            if (activityIds.contains(p.getActivityId())){
                MembershipBasicInfo membershipBasicInfo = memberShipService.getUserBasicInfoByPkId(p.getUserId());
                if (membershipBasicInfo != null){
                    messageServ.push(membershipBasicInfo.getAuthId(),membershipBasicInfo.getMembershipType(),213,2,"vipCard-rpc");
                    messageServ.asyncSendSMSTemplateForMarket(membershipBasicInfo.getMobilePhone(), 227, null,"vipCard-rpc");
                }
                mmpCardRemindMapper.updateRdminByActivityAndCard(p.getUserId(),activityIds);
            }
        });


    }
}
