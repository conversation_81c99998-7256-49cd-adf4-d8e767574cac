package com.extracme.evcard.rpc.vipcard.service.store;

import com.extracme.evcard.rpc.vipcard.model.VehicleModel;
import com.extracme.evcard.rpc.vipcard.rest.MdRestClient;
import com.extracme.evcard.rpc.vipcard.rest.entity.GetMappedGoodVehicleModelRes;
import com.extracme.evcard.rpc.vipcard.rest.entity.GoodsVehicleModelInfo;
import com.extracme.evcard.rpc.vipcard.rest.entity.MappedGoodVehicleModelInfo;
import com.extracme.evcard.rpc.vipcard.service.store.ConfigLoader;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GoodsModelService {

    @Resource
    private ConfigLoader configLoader;

    @Resource
    private MdRestClient mdRestClient;


    public List<VehicleModel> getVehicleModelList() {
        Map<Long, String> goodsModels = configLoader.goodsModelMap;
        if(goodsModels == null || goodsModels.isEmpty()) {
            return new ArrayList<>();
        }
        List<VehicleModel> list = new ArrayList<>();
        for(Long key : goodsModels.keySet()) {
            VehicleModel veh = new VehicleModel();
            veh.setVehicleModelSeq(String.valueOf(key));
            veh.setVehicleModelName(goodsModels.get(key));
            list.add(veh);
        }
        return list;
    }

    public Map<Long, String> getVehicleNames(List<Long> vehicleModelSeqs) {
        Map<Long, String> goodsModels = configLoader.goodsModelMap;
        if(goodsModels == null || goodsModels.isEmpty() || CollectionUtils.isEmpty(vehicleModelSeqs)) {
            return new HashMap<>();
        }
        Map<Long, String> data = new HashMap<>();
        for(Long vehicleModelSeq : vehicleModelSeqs) {
            String modelName = goodsModels.get(vehicleModelSeq);
            if(StringUtils.isNotBlank(modelName)) {
                data.put(vehicleModelSeq, modelName);
            }
        }
        return data;
    }

    public MappedGoodVehicleModelInfo getGoodsVehicleModel(Integer vehicleModel) {
        if(vehicleModel == null) {
            return null;
        }
        try {
            GetMappedGoodVehicleModelRes res = mdRestClient.getGoodsModelByVehicleModel(Long.valueOf(vehicleModel));
            if(res != null && res.getCode() == 0) {
                return res.getData();
            }
        }catch (Exception ex) {
            log.error("getGoodsModelByVehicleModel：获取资产车型对应的商品车型异常, vehicleModel=" + vehicleModel, ex);
        }
        return null;
    }

    /**
     *  获取商品 详情
     * @param
     * @return
     */
    public Map<Long,GoodsVehicleModelInfo> getGoodsDetailModelMap(List<Long> goodModelIds,boolean isAll) {
        Map<Long, GoodsVehicleModelInfo> goodsDetailModelMap = configLoader.goodsDetailModelMap;
        if(goodsDetailModelMap == null || goodsDetailModelMap.isEmpty() || (!isAll && CollectionUtils.isEmpty(goodModelIds))) {
            return null;
        }

        Map<Long, GoodsVehicleModelInfo> data = new HashMap<>();
        if(isAll){
            for (Long id : goodsDetailModelMap.keySet()) {
                GoodsVehicleModelInfo oldInfo = goodsDetailModelMap.get(id);
                if(oldInfo != null){
                    GoodsVehicleModelInfo newInfo = new GoodsVehicleModelInfo();
                    newInfo.setAndroidPicUrl(oldInfo.getAndroidPicUrl());
                    newInfo.setGoodsModelId(oldInfo.getGoodsModelId());
                    newInfo.setIosPicUrl(oldInfo.getIosPicUrl());
                    newInfo.setGoodsModelName(oldInfo.getGoodsModelName());
                    data.put(id, newInfo);
                }
            }
        }else{
            for(Long id : goodModelIds) {
                GoodsVehicleModelInfo oldInfo = goodsDetailModelMap.get(id);
                if(oldInfo != null){
                    GoodsVehicleModelInfo newInfo = new GoodsVehicleModelInfo();
                    newInfo.setAndroidPicUrl(oldInfo.getAndroidPicUrl());
                    newInfo.setGoodsModelId(oldInfo.getGoodsModelId());
                    newInfo.setIosPicUrl(oldInfo.getIosPicUrl());
                    newInfo.setGoodsModelName(oldInfo.getGoodsModelName());
                    data.put(id, newInfo);
                }
            }
        }
        return data;
    }


    public List<GoodsVehicleModelInfo> getGoodsDetailModelList(List<Long> goodModelIds,boolean isAll) {
        Map<Long, GoodsVehicleModelInfo> goodsDetailModelMap = configLoader.goodsDetailModelMap;
        if(goodsDetailModelMap == null || goodsDetailModelMap.isEmpty() || (!isAll && CollectionUtils.isEmpty(goodModelIds))) {
            return null;
        }

        Set<Long> hasExistedId = new HashSet<>();
        List<GoodsVehicleModelInfo> data = Lists.newArrayList();
        if(isAll){
            for (Long id : goodsDetailModelMap.keySet()) {
                if (!hasExistedId.contains(id)) {
                    hasExistedId.add(id);
                    GoodsVehicleModelInfo oldInfo = goodsDetailModelMap.get(id);
                    if(oldInfo != null){
                        GoodsVehicleModelInfo newInfo = new GoodsVehicleModelInfo();
                        newInfo.setAndroidPicUrl(oldInfo.getAndroidPicUrl());
                        newInfo.setGoodsModelId(oldInfo.getGoodsModelId());
                        newInfo.setIosPicUrl(oldInfo.getIosPicUrl());
                        newInfo.setGoodsModelName(oldInfo.getGoodsModelName());
                        data.add(newInfo);
                    }
                }
            }
        }else{
            for(Long id : goodModelIds) {
                if (!hasExistedId.contains(id)) {
                    hasExistedId.add(id);
                    GoodsVehicleModelInfo oldInfo = goodsDetailModelMap.get(id);
                    if(oldInfo != null){
                        GoodsVehicleModelInfo newInfo = new GoodsVehicleModelInfo();
                        newInfo.setAndroidPicUrl(oldInfo.getAndroidPicUrl());
                        newInfo.setGoodsModelId(oldInfo.getGoodsModelId());
                        newInfo.setIosPicUrl(oldInfo.getIosPicUrl());
                        newInfo.setGoodsModelName(oldInfo.getGoodsModelName());
                        data.add(newInfo);
                    }
                }
            }
        }
        return data;
    }
}
