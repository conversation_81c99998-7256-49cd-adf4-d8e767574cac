package com.extracme.evcard.rpc.vipcard.service.inner;

import com.extracme.evcard.rpc.vipcard.dao.OrgInfoMapper;
import com.extracme.evcard.rpc.vipcard.model.OrgInfo;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/4/7
 */
@Component
public class OrgService {

    @Resource
    private OrgInfoMapper orgInfoMapper;

    /**
     * id, orgInfo
     */
    private Cache<Long, OrgInfo> orgCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10 * DateUtils.MILLIS_PER_MINUTE, TimeUnit.MILLISECONDS).maximumSize(2000).build();

    /**
     * id, orgInfo
     */
    private Cache<String, String> orgIdCache = CacheBuilder.newBuilder()
            .expireAfterWrite(1 * DateUtils.MILLIS_PER_HOUR, TimeUnit.MILLISECONDS).maximumSize(1000).build();

    /**
     * 根据机构主键(id)查询机构信息
     * @param id
     * @return
     */
    public OrgInfo getOrgInfoByPrimaryKey(Long id) {
        OrgInfo orgInfo = orgCache.getIfPresent(id);
        if(orgInfo == null) {
            orgInfo = orgInfoMapper.selectByPrimaryKey(id);
            if (null == orgInfo) {
                return null;
            }
            orgCache.put(id, orgInfo);
        }
        return orgInfo;
    }

    /**
     * 根据机构主键(id)查询机构名称
     * @param id
     * @return
     */
    public String getOrgNameByPrimaryKey(Long id){
        OrgInfo orgInfo = getOrgInfoByPrimaryKey(id);
        if(orgInfo == null) {
            return StringUtils.EMPTY;
        }
        return orgInfo.getOrgName();
    }

    /**
     * 根据orgId查询机构名称
     * @param orgId
     * @return
     */
    public String getOrgNameByOrgId(String orgId){
        String orgName = orgIdCache.getIfPresent(orgId);
        if(orgName == null) {
            OrgInfo orgInfo = orgInfoMapper.getOrgSeqByOrgId(orgId);
            if (null == orgInfo) {
                return null;
            }
            orgName = orgInfo.getOrgName();
            orgIdCache.put(orgId, orgInfo.getOrgName());
        }
        return orgName;
    }
}
