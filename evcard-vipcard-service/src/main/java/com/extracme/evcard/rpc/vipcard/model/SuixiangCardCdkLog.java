package com.extracme.evcard.rpc.vipcard.model;

import java.util.Date;

public class SuixiangCardCdkLog {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.card_base_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private Long cardBaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.card_rent_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private Long cardRentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.card_price_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private Long cardPriceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.num
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private Long num;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.create_time
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.create_oper_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.create_oper_name
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.update_time
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.update_oper_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.update_oper_name
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_cdk_log.is_deleted
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    private Integer isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.id
     *
     * @return the value of suixiang_card_cdk_log.id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.id
     *
     * @param id the value for suixiang_card_cdk_log.id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.card_base_id
     *
     * @return the value of suixiang_card_cdk_log.card_base_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public Long getCardBaseId() {
        return cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.card_base_id
     *
     * @param cardBaseId the value for suixiang_card_cdk_log.card_base_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setCardBaseId(Long cardBaseId) {
        this.cardBaseId = cardBaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.card_rent_id
     *
     * @return the value of suixiang_card_cdk_log.card_rent_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public Long getCardRentId() {
        return cardRentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.card_rent_id
     *
     * @param cardRentId the value for suixiang_card_cdk_log.card_rent_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setCardRentId(Long cardRentId) {
        this.cardRentId = cardRentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.card_price_id
     *
     * @return the value of suixiang_card_cdk_log.card_price_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public Long getCardPriceId() {
        return cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.card_price_id
     *
     * @param cardPriceId the value for suixiang_card_cdk_log.card_price_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setCardPriceId(Long cardPriceId) {
        this.cardPriceId = cardPriceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.num
     *
     * @return the value of suixiang_card_cdk_log.num
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public Long getNum() {
        return num;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.num
     *
     * @param num the value for suixiang_card_cdk_log.num
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setNum(Long num) {
        this.num = num;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.create_time
     *
     * @return the value of suixiang_card_cdk_log.create_time
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.create_time
     *
     * @param createTime the value for suixiang_card_cdk_log.create_time
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.create_oper_id
     *
     * @return the value of suixiang_card_cdk_log.create_oper_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.create_oper_id
     *
     * @param createOperId the value for suixiang_card_cdk_log.create_oper_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.create_oper_name
     *
     * @return the value of suixiang_card_cdk_log.create_oper_name
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.create_oper_name
     *
     * @param createOperName the value for suixiang_card_cdk_log.create_oper_name
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.update_time
     *
     * @return the value of suixiang_card_cdk_log.update_time
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.update_time
     *
     * @param updateTime the value for suixiang_card_cdk_log.update_time
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.update_oper_id
     *
     * @return the value of suixiang_card_cdk_log.update_oper_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_cdk_log.update_oper_id
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.update_oper_name
     *
     * @return the value of suixiang_card_cdk_log.update_oper_name
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_cdk_log.update_oper_name
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_cdk_log.is_deleted
     *
     * @return the value of suixiang_card_cdk_log.is_deleted
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_cdk_log.is_deleted
     *
     * @param isDeleted the value for suixiang_card_cdk_log.is_deleted
     *
     * @mbggenerated Fri Aug 23 14:17:07 CST 2024
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}