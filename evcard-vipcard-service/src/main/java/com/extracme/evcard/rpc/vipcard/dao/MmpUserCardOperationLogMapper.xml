<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.MmpUserCardOperationLogMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.MmpUserCardOperationLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_card_no" property="userCardNo" jdbcType="BIGINT" />
    <result column="card_id" property="cardId" jdbcType="BIGINT" />
    <result column="card_group" property="cardGroup" jdbcType="INTEGER" />
    <result column="operation_type" property="operationType" jdbcType="BIGINT" />
    <result column="origin_system" property="originSystem" jdbcType="VARCHAR" />
    <result column="ref_key" property="refKey" jdbcType="VARCHAR" />
    <result column="order_seq" property="orderSeq" jdbcType="VARCHAR" />
    <result column="discount_amount" property="discountAmount" jdbcType="DECIMAL" />
    <result column="amount" property="amount" jdbcType="DECIMAL" />
    <result column="real_amount" property="realAmount" jdbcType="DECIMAL" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_card_no, card_id, card_group, operation_type, origin_system, ref_key, order_seq, 
    discount_amount, amount, real_amount, misc_desc, status, create_time, create_oper_id, 
    create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_card_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ${issSchema}.mmp_user_card_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardOperationLog" >
    insert into ${issSchema}.mmp_user_card_operation_log (id, user_card_no, card_id,
      card_group, operation_type, origin_system, 
      ref_key, order_seq, discount_amount, 
      amount, real_amount, misc_desc, 
      status, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{userCardNo,jdbcType=BIGINT}, #{cardId,jdbcType=BIGINT}, 
      #{cardGroup,jdbcType=INTEGER}, #{operationType,jdbcType=BIGINT}, #{originSystem,jdbcType=VARCHAR}, 
      #{refKey,jdbcType=VARCHAR}, #{orderSeq,jdbcType=VARCHAR}, #{discountAmount,jdbcType=DECIMAL}, 
      #{amount,jdbcType=DECIMAL}, #{realAmount,jdbcType=DECIMAL}, #{miscDesc,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardOperationLog" >
    insert into  ${issSchema}.mmp_user_card_operation_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userCardNo != null" >
        user_card_no,
      </if>
      <if test="cardId != null" >
        card_id,
      </if>
      <if test="cardGroup != null" >
        card_group,
      </if>
      <if test="operationType != null" >
        operation_type,
      </if>
      <if test="originSystem != null" >
        origin_system,
      </if>
      <if test="refKey != null" >
        ref_key,
      </if>
      <if test="orderSeq != null" >
        order_seq,
      </if>
      <if test="discountAmount != null" >
        discount_amount,
      </if>
      <if test="amount != null" >
        amount,
      </if>
      <if test="realAmount != null" >
        real_amount,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userCardNo != null" >
        #{userCardNo,jdbcType=BIGINT},
      </if>
      <if test="cardId != null" >
        #{cardId,jdbcType=BIGINT},
      </if>
      <if test="cardGroup != null" >
        #{cardGroup,jdbcType=INTEGER},
      </if>
      <if test="operationType != null" >
        #{operationType,jdbcType=BIGINT},
      </if>
      <if test="originSystem != null" >
        #{originSystem,jdbcType=VARCHAR},
      </if>
      <if test="refKey != null" >
        #{refKey,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null" >
        #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="discountAmount != null" >
        #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="realAmount != null" >
        #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardOperationLog" >
    update ${issSchema}.mmp_user_card_operation_log
    <set >
      <if test="userCardNo != null" >
        user_card_no = #{userCardNo,jdbcType=BIGINT},
      </if>
      <if test="cardId != null" >
        card_id = #{cardId,jdbcType=BIGINT},
      </if>
      <if test="cardGroup != null" >
        card_group = #{cardGroup,jdbcType=INTEGER},
      </if>
      <if test="operationType != null" >
        operation_type = #{operationType,jdbcType=BIGINT},
      </if>
      <if test="originSystem != null" >
        origin_system = #{originSystem,jdbcType=VARCHAR},
      </if>
      <if test="refKey != null" >
        ref_key = #{refKey,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null" >
        order_seq = #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="discountAmount != null" >
        discount_amount = #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="amount != null" >
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="realAmount != null" >
        real_amount = #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpUserCardOperationLog" >
    update ${issSchema}.mmp_user_card_operation_log
    set user_card_no = #{userCardNo,jdbcType=BIGINT},
      card_id = #{cardId,jdbcType=BIGINT},
      card_group = #{cardGroup,jdbcType=INTEGER},
      operation_type = #{operationType,jdbcType=BIGINT},
      origin_system = #{originSystem,jdbcType=VARCHAR},
      ref_key = #{refKey,jdbcType=VARCHAR},
      order_seq = #{orderSeq,jdbcType=VARCHAR},
      discount_amount = #{discountAmount,jdbcType=DECIMAL},
      amount = #{amount,jdbcType=DECIMAL},
      real_amount = #{realAmount,jdbcType=DECIMAL},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchSave">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    insert into ${issSchema}.mmp_user_card_operation_log (user_card_no, card_id,
    card_group, operation_type, origin_system,
    ref_key, order_seq, misc_desc,
    create_time, create_oper_id,
    create_oper_name)
    values
    <foreach collection="list" item="item" separator=","  index="index">
      (#{item.userCardNo,jdbcType=BIGINT}, #{item.cardId,jdbcType=BIGINT},
      #{item.cardGroup,jdbcType=INTEGER}, #{item.operationType,jdbcType=BIGINT}, #{item.originSystem,jdbcType=VARCHAR},
      #{item.refKey,jdbcType=VARCHAR}, #{item.orderSeq,jdbcType=VARCHAR}, #{item.miscDesc,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.createOperId,jdbcType=BIGINT},
      #{item.createOperName,jdbcType=VARCHAR})
    </foreach>

  </insert>


  <select id="selectCardHistoryPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_card_operation_log
    where user_card_no = #{userCardNo}
    <if test="cardGroup != null" >
      and card_group = #{cardGroup}
    </if>
    <if test="operationType != null" >
      and operation_type = #{operationType}
    </if>
    <if test="endTime != null" >
      and create_time &lt;= #{endTime}
    </if>
    <if test="startTime != null" >
      and create_time &gt;= #{startTime}
    </if>
    <if test="orderSeq != null and orderSeq != ''" >
      and order_seq = #{orderSeq}
    </if>
    order by id desc
  </select>


</mapper>