package com.extracme.evcard.rpc.vipcard.model;

public class OrgInfo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.id
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_ID
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_NAME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String orgName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_KIND
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String orgKind;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_CLASS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double orgClass;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CONTACT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String contact;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.TEL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String tel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.MOBILE_PHONE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String mobilePhone;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ADDRESS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String address;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.MAIL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String mail;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.LICENSE_NO
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String licenseNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.FAX
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String fax;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.COUNTY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String county;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CITY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double city;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.PROVINCE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double province;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CORPORATE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String corporate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.LOCATION
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String location;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.RTOLN
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String rtoln;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.LICENSE_NO_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String licenseNoImgUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.TAX_REGISTRATION_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String taxRegistrationImgUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_CODE_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String orgCodeImgUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.REMARK
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.PAY_WAY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double payWay;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CREATED_USER
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String createdUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CREATED_TIME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String createdTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.UPDATED_USER
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String updatedUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.UPDATED_TIME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String updatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.STATUS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORIGIN
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double origin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.DEPOSIT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double deposit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.RESERVE_AMOUNT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double reserveAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.RENT_MINS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double rentMins;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.AGENCY_ID
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String agencyId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CITY_SHORT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String cityShort;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.INSIDE_FLAG
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Integer insideFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_ALIAS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String orgAlias;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CHECK_DATE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String checkDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CHECK_ALERT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Integer checkAlert;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.BALANCE_MAIL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String balanceMail;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_PROPERTY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Byte orgProperty;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_PROTRETY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Boolean orgProtrety;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.id
     *
     * @return the value of org_info.id
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.id
     *
     * @param id the value for org_info.id
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.ORG_ID
     *
     * @return the value of org_info.ORG_ID
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.ORG_ID
     *
     * @param orgId the value for org_info.ORG_ID
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.ORG_NAME
     *
     * @return the value of org_info.ORG_NAME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.ORG_NAME
     *
     * @param orgName the value for org_info.ORG_NAME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.ORG_KIND
     *
     * @return the value of org_info.ORG_KIND
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getOrgKind() {
        return orgKind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.ORG_KIND
     *
     * @param orgKind the value for org_info.ORG_KIND
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setOrgKind(String orgKind) {
        this.orgKind = orgKind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.ORG_CLASS
     *
     * @return the value of org_info.ORG_CLASS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Double getOrgClass() {
        return orgClass;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.ORG_CLASS
     *
     * @param orgClass the value for org_info.ORG_CLASS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setOrgClass(Double orgClass) {
        this.orgClass = orgClass;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.CONTACT
     *
     * @return the value of org_info.CONTACT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getContact() {
        return contact;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.CONTACT
     *
     * @param contact the value for org_info.CONTACT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setContact(String contact) {
        this.contact = contact;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.TEL
     *
     * @return the value of org_info.TEL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getTel() {
        return tel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.TEL
     *
     * @param tel the value for org_info.TEL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setTel(String tel) {
        this.tel = tel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.MOBILE_PHONE
     *
     * @return the value of org_info.MOBILE_PHONE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getMobilePhone() {
        return mobilePhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.MOBILE_PHONE
     *
     * @param mobilePhone the value for org_info.MOBILE_PHONE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.ADDRESS
     *
     * @return the value of org_info.ADDRESS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getAddress() {
        return address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.ADDRESS
     *
     * @param address the value for org_info.ADDRESS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.MAIL
     *
     * @return the value of org_info.MAIL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getMail() {
        return mail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.MAIL
     *
     * @param mail the value for org_info.MAIL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setMail(String mail) {
        this.mail = mail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.LICENSE_NO
     *
     * @return the value of org_info.LICENSE_NO
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getLicenseNo() {
        return licenseNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.LICENSE_NO
     *
     * @param licenseNo the value for org_info.LICENSE_NO
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setLicenseNo(String licenseNo) {
        this.licenseNo = licenseNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.FAX
     *
     * @return the value of org_info.FAX
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getFax() {
        return fax;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.FAX
     *
     * @param fax the value for org_info.FAX
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setFax(String fax) {
        this.fax = fax;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.COUNTY
     *
     * @return the value of org_info.COUNTY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getCounty() {
        return county;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.COUNTY
     *
     * @param county the value for org_info.COUNTY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setCounty(String county) {
        this.county = county;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.CITY
     *
     * @return the value of org_info.CITY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Double getCity() {
        return city;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.CITY
     *
     * @param city the value for org_info.CITY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setCity(Double city) {
        this.city = city;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.PROVINCE
     *
     * @return the value of org_info.PROVINCE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Double getProvince() {
        return province;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.PROVINCE
     *
     * @param province the value for org_info.PROVINCE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setProvince(Double province) {
        this.province = province;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.CORPORATE
     *
     * @return the value of org_info.CORPORATE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getCorporate() {
        return corporate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.CORPORATE
     *
     * @param corporate the value for org_info.CORPORATE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setCorporate(String corporate) {
        this.corporate = corporate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.LOCATION
     *
     * @return the value of org_info.LOCATION
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getLocation() {
        return location;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.LOCATION
     *
     * @param location the value for org_info.LOCATION
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.RTOLN
     *
     * @return the value of org_info.RTOLN
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getRtoln() {
        return rtoln;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.RTOLN
     *
     * @param rtoln the value for org_info.RTOLN
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setRtoln(String rtoln) {
        this.rtoln = rtoln;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.LICENSE_NO_IMG_URL
     *
     * @return the value of org_info.LICENSE_NO_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getLicenseNoImgUrl() {
        return licenseNoImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.LICENSE_NO_IMG_URL
     *
     * @param licenseNoImgUrl the value for org_info.LICENSE_NO_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setLicenseNoImgUrl(String licenseNoImgUrl) {
        this.licenseNoImgUrl = licenseNoImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.TAX_REGISTRATION_IMG_URL
     *
     * @return the value of org_info.TAX_REGISTRATION_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getTaxRegistrationImgUrl() {
        return taxRegistrationImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.TAX_REGISTRATION_IMG_URL
     *
     * @param taxRegistrationImgUrl the value for org_info.TAX_REGISTRATION_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setTaxRegistrationImgUrl(String taxRegistrationImgUrl) {
        this.taxRegistrationImgUrl = taxRegistrationImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.ORG_CODE_IMG_URL
     *
     * @return the value of org_info.ORG_CODE_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getOrgCodeImgUrl() {
        return orgCodeImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.ORG_CODE_IMG_URL
     *
     * @param orgCodeImgUrl the value for org_info.ORG_CODE_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setOrgCodeImgUrl(String orgCodeImgUrl) {
        this.orgCodeImgUrl = orgCodeImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.REMARK
     *
     * @return the value of org_info.REMARK
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.REMARK
     *
     * @param remark the value for org_info.REMARK
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.PAY_WAY
     *
     * @return the value of org_info.PAY_WAY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Double getPayWay() {
        return payWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.PAY_WAY
     *
     * @param payWay the value for org_info.PAY_WAY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setPayWay(Double payWay) {
        this.payWay = payWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.CREATED_USER
     *
     * @return the value of org_info.CREATED_USER
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getCreatedUser() {
        return createdUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.CREATED_USER
     *
     * @param createdUser the value for org_info.CREATED_USER
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.CREATED_TIME
     *
     * @return the value of org_info.CREATED_TIME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.CREATED_TIME
     *
     * @param createdTime the value for org_info.CREATED_TIME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.UPDATED_USER
     *
     * @return the value of org_info.UPDATED_USER
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getUpdatedUser() {
        return updatedUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.UPDATED_USER
     *
     * @param updatedUser the value for org_info.UPDATED_USER
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.UPDATED_TIME
     *
     * @return the value of org_info.UPDATED_TIME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.UPDATED_TIME
     *
     * @param updatedTime the value for org_info.UPDATED_TIME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.STATUS
     *
     * @return the value of org_info.STATUS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Double getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.STATUS
     *
     * @param status the value for org_info.STATUS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setStatus(Double status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.ORIGIN
     *
     * @return the value of org_info.ORIGIN
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Double getOrigin() {
        return origin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.ORIGIN
     *
     * @param origin the value for org_info.ORIGIN
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setOrigin(Double origin) {
        this.origin = origin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.DEPOSIT
     *
     * @return the value of org_info.DEPOSIT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Double getDeposit() {
        return deposit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.DEPOSIT
     *
     * @param deposit the value for org_info.DEPOSIT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setDeposit(Double deposit) {
        this.deposit = deposit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.RESERVE_AMOUNT
     *
     * @return the value of org_info.RESERVE_AMOUNT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Double getReserveAmount() {
        return reserveAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.RESERVE_AMOUNT
     *
     * @param reserveAmount the value for org_info.RESERVE_AMOUNT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setReserveAmount(Double reserveAmount) {
        this.reserveAmount = reserveAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.RENT_MINS
     *
     * @return the value of org_info.RENT_MINS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Double getRentMins() {
        return rentMins;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.RENT_MINS
     *
     * @param rentMins the value for org_info.RENT_MINS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setRentMins(Double rentMins) {
        this.rentMins = rentMins;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.AGENCY_ID
     *
     * @return the value of org_info.AGENCY_ID
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getAgencyId() {
        return agencyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.AGENCY_ID
     *
     * @param agencyId the value for org_info.AGENCY_ID
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.CITY_SHORT
     *
     * @return the value of org_info.CITY_SHORT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getCityShort() {
        return cityShort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.CITY_SHORT
     *
     * @param cityShort the value for org_info.CITY_SHORT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setCityShort(String cityShort) {
        this.cityShort = cityShort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.INSIDE_FLAG
     *
     * @return the value of org_info.INSIDE_FLAG
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Integer getInsideFlag() {
        return insideFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.INSIDE_FLAG
     *
     * @param insideFlag the value for org_info.INSIDE_FLAG
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setInsideFlag(Integer insideFlag) {
        this.insideFlag = insideFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.ORG_ALIAS
     *
     * @return the value of org_info.ORG_ALIAS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getOrgAlias() {
        return orgAlias;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.ORG_ALIAS
     *
     * @param orgAlias the value for org_info.ORG_ALIAS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setOrgAlias(String orgAlias) {
        this.orgAlias = orgAlias;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.CHECK_DATE
     *
     * @return the value of org_info.CHECK_DATE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getCheckDate() {
        return checkDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.CHECK_DATE
     *
     * @param checkDate the value for org_info.CHECK_DATE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.CHECK_ALERT
     *
     * @return the value of org_info.CHECK_ALERT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Integer getCheckAlert() {
        return checkAlert;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.CHECK_ALERT
     *
     * @param checkAlert the value for org_info.CHECK_ALERT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setCheckAlert(Integer checkAlert) {
        this.checkAlert = checkAlert;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.BALANCE_MAIL
     *
     * @return the value of org_info.BALANCE_MAIL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public String getBalanceMail() {
        return balanceMail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.BALANCE_MAIL
     *
     * @param balanceMail the value for org_info.BALANCE_MAIL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setBalanceMail(String balanceMail) {
        this.balanceMail = balanceMail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.ORG_PROPERTY
     *
     * @return the value of org_info.ORG_PROPERTY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Byte getOrgProperty() {
        return orgProperty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.ORG_PROPERTY
     *
     * @param orgProperty the value for org_info.ORG_PROPERTY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setOrgProperty(Byte orgProperty) {
        this.orgProperty = orgProperty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column org_info.ORG_PROTRETY
     *
     * @return the value of org_info.ORG_PROTRETY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public Boolean getOrgProtrety() {
        return orgProtrety;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column org_info.ORG_PROTRETY
     *
     * @param orgProtrety the value for org_info.ORG_PROTRETY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    public void setOrgProtrety(Boolean orgProtrety) {
        this.orgProtrety = orgProtrety;
    }
}