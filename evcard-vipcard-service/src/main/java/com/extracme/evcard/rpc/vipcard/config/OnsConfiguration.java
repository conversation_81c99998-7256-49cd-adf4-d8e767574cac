package com.extracme.evcard.rpc.vipcard.config;

import com.aliyun.openservices.ons.api.PropertyKeyConst;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Data
@Configuration
@ConfigurationProperties(prefix = "ons")
public class OnsConfiguration {

    private String accessKey;
    private String secretKey;
    private String nameSrvAddr;

    @Value("${ons.contract.normal.topic}")
    private String contractNormalTopic;
    @Value("${ons.contract.groupId}")
    private String contractGroupId;

    public Properties getMqProperties() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.AccessKey, this.accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, this.secretKey);
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, this.nameSrvAddr);
        return properties;
    }
}