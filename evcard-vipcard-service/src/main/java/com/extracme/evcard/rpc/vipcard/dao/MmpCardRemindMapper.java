package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.dto.UserRemindActivityDto;
import com.extracme.evcard.rpc.vipcard.model.MmpCardRemind;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpCardRemindMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpCardRemind record);

    int insertSelective(MmpCardRemind record);

    MmpCardRemind selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpCardRemind record);

    int updateByPrimaryKey(MmpCardRemind record);


    List<UserRemindActivityDto> queryByIdAndActivity(@Param("userId") Long userId, @Param("list") List<Long> list);

    int updateRdminByActivityAndCard(@Param("userId") Long userId, @Param("list") List<Long> list);
}