package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.dto.MemberCardQueryDto;
import com.extracme.evcard.rpc.vipcard.model.MemberCardListInfo;
import com.extracme.evcard.rpc.vipcard.model.MmpUserCardInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface MmpUserCardInfoMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_card_info
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    int insert(MmpUserCardInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_card_info
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    int insertSelective(MmpUserCardInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_card_info
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    MmpUserCardInfo selectByPrimaryKey(Long userCardNo);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_card_info
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    int updateByPrimaryKeySelective(MmpUserCardInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_card_info
     *
     * @mbggenerated Thu Dec 24 14:00:12 CST 2020
     */
    int updateByPrimaryKey(MmpUserCardInfo record);


    /**
     * 根据会员id查询会员卡信息
     * @param id
     */
    List<MmpUserCardInfo> queryUserVipCardById(Long id);

    /**
     *
     * @param id
     * @param cardId
     */
    MmpUserCardInfo queryUserVipCardByIdAndCard(@Param("id") Long id, @Param("cardId") Long cardId);

    /**
     * 查询即将过期卡
     * @return
     */
    List<MmpUserCardInfo> queryUserOverTimeCardInfo();

    /**
     * 根据会员id查询生效中的会员卡
     * @param userId
     * @return
     */
    List<MmpUserCardInfo> queryEffectiveUserVipCardById(@Param("userId") Long userId);

    /**
     * 批量发放卡片
     * @param list
     * @return
     */
    int batchInsert(@Param("list")List<MmpUserCardInfo> list);

    /**
     * 禁用指定机构和用户的企业/个人卡
     * @param userIds
     * @param agencyId
     * @return
     */
    int batchDisable(@Param("userIds")Set<Long> userIds, @Param("agencyId") String agencyId,
                     @Param("updateOperId") Long operId,
                     @Param("updateOperName") String operUser);


    /**
     * 查询用户的企业卡
     * @param userIds
     * @param agencyId
     * @return
     */
    List<MmpUserCardInfo> queryUserCorporateCards(@Param("userIds")Set<Long> userIds, @Param("agencyId") String agencyId);

    /**
     * 查询用户的会员卡列表
     * @param userId
     * @param queryDto
     * @return
     */
    List<MemberCardListInfo> selectUserCardPage(@Param("userId") Long userId, @Param("condition") MemberCardQueryDto queryDto);

    /**
     * 作废用户的会员卡
     * @param userId
     * @param cardId
     * @param userCardNo
     * @param operId
     * @param operUser
     * @return
     */
    int cancelUserCard(@Param("userId") Long userId, @Param("cardId") Long cardId,
                       @Param("userCardNo") Long userCardNo,
                       @Param("updateOperId") Long operId,
                       @Param("updateOperName") String operUser);

    int updateUseCounts(MmpUserCardInfo record);

    /**
     * 更新已过期用户卡
     * @return
     */
    int expireUserCards();

    /**
     * 获取用户当前拥有的生效中的指定卡片
     * @param id
     * @param cardId
     * @return
     */
    MmpUserCardInfo selectOneUserActiveCardByCardId(@Param("id") Long id, @Param("cardId") Long cardId);
}