<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.MmpCardSalesActivityMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="card_id" property="cardId" jdbcType="BIGINT" />
    <result column="activity_name" property="activityName" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="VARCHAR" />
    <result column="sales_price" property="salesPrice" jdbcType="DECIMAL" />
    <result column="underline_price" property="underlinePrice" jdbcType="DECIMAL" />
    <result column="activity_status" property="activityStatus" jdbcType="INTEGER" />
    <result column="person_purchases_limit" property="personPurchasesLimit" jdbcType="INTEGER" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="advance_notice_time" property="advanceNoticeTime" jdbcType="TIMESTAMP" />
    <result column="rules" property="rules" jdbcType="VARCHAR" />
    <result column="opening_stock" property="openingStock" jdbcType="DECIMAL" />
    <result column="stock" property="stock" jdbcType="DECIMAL" />
    <result column="sales_volume" property="salesVolume" jdbcType="DECIMAL" />
    <result column="platform_type" property="platformType" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, card_id, activity_name, org_id, sales_price, underline_price, activity_status, 
    person_purchases_limit, start_time, end_time, advance_notice_time, rules, opening_stock, 
    stock, sales_volume, platform_type, misc_desc, status, create_time, create_oper_id, create_oper_name, update_time,
    update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_sales_activity
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    delete from ${issSchema}.mmp_card_sales_activity
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    insert into ${issSchema}.mmp_card_sales_activity (id, card_id, activity_name, 
      org_id, sales_price, underline_price, 
      activity_status, person_purchases_limit, start_time, 
      end_time, advance_notice_time, rules, 
      opening_stock, stock, platform_type, misc_desc,
      status, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{cardId,jdbcType=BIGINT}, #{activityName,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=VARCHAR}, #{salesPrice,jdbcType=DECIMAL}, #{underlinePrice,jdbcType=DECIMAL}, 
      #{activityStatus,jdbcType=INTEGER}, #{personPurchasesLimit,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{advanceNoticeTime,jdbcType=TIMESTAMP}, #{rules,jdbcType=VARCHAR}, 
      #{openingStock,jdbcType=DECIMAL}, #{stock,jdbcType=DECIMAL}, #{platformType,jdbcType=INTEGER},
      #{miscDesc,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    insert into ${issSchema}.mmp_card_sales_activity
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="cardId != null" >
        card_id,
      </if>
      <if test="activityName != null" >
        activity_name,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="salesPrice != null" >
        sales_price,
      </if>
      <if test="underlinePrice != null" >
        underline_price,
      </if>
      <if test="activityStatus != null" >
        activity_status,
      </if>
      <if test="personPurchasesLimit != null" >
        person_purchases_limit,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="advanceNoticeTime != null" >
        advance_notice_time,
      </if>
      <if test="rules != null" >
        rules,
      </if>
      <if test="openingStock != null" >
        opening_stock,
      </if>
      <if test="stock != null" >
        stock,
      </if>
      <if test="platformType != null" >
        platform_type,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cardId != null" >
        #{cardId,jdbcType=BIGINT},
      </if>
      <if test="activityName != null" >
        #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="salesPrice != null" >
        #{salesPrice,jdbcType=DECIMAL},
      </if>
      <if test="underlinePrice != null" >
        #{underlinePrice,jdbcType=DECIMAL},
      </if>
      <if test="activityStatus != null" >
        #{activityStatus,jdbcType=INTEGER},
      </if>
      <if test="personPurchasesLimit != null" >
        #{personPurchasesLimit,jdbcType=INTEGER},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="advanceNoticeTime != null" >
        #{advanceNoticeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rules != null" >
        #{rules,jdbcType=VARCHAR},
      </if>
      <if test="openingStock != null" >
        #{openingStock,jdbcType=DECIMAL},
      </if>
      <if test="stock != null" >
        #{stock,jdbcType=DECIMAL},
      </if>
      <if test="platformType != null" >
        #{platformType,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    update ${issSchema}.mmp_card_sales_activity
    <set >
      <if test="cardId != null" >
        card_id = #{cardId,jdbcType=BIGINT},
      </if>
      <if test="activityName != null" >
        activity_name = #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="salesPrice != null" >
        sales_price = #{salesPrice,jdbcType=DECIMAL},
      </if>
      <if test="underlinePrice != null" >
        underline_price = #{underlinePrice,jdbcType=DECIMAL},
      </if>
      <if test="activityStatus != null" >
        activity_status = #{activityStatus,jdbcType=INTEGER},
      </if>
      <if test="personPurchasesLimit != null" >
        person_purchases_limit = #{personPurchasesLimit,jdbcType=INTEGER},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="advanceNoticeTime != null" >
        advance_notice_time = #{advanceNoticeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rules != null" >
        rules = #{rules,jdbcType=VARCHAR},
      </if>
      <if test="openingStock != null" >
        opening_stock = #{openingStock,jdbcType=DECIMAL},
      </if>
      <if test="stock != null" >
        stock = #{stock,jdbcType=DECIMAL},
      </if>
      <if test="platformType != null" >
        platform_type = #{platformType,jdbcType=DECIMAL},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    update ${issSchema}.mmp_card_sales_activity
    set card_id = #{cardId,jdbcType=BIGINT},
      activity_name = #{activityName,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=VARCHAR},
      sales_price = #{salesPrice,jdbcType=DECIMAL},
      underline_price = #{underlinePrice,jdbcType=DECIMAL},
      activity_status = #{activityStatus,jdbcType=INTEGER},
      person_purchases_limit = #{personPurchasesLimit,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      advance_notice_time = #{advanceNoticeTime,jdbcType=TIMESTAMP},
      rules = #{rules,jdbcType=VARCHAR},
      opening_stock = #{openingStock,jdbcType=DECIMAL},
      stock = #{stock,jdbcType=DECIMAL},
      platform_type = #{platformType,jdbcType=INTEGER},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="add" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    insert into ${issSchema}.mmp_card_sales_activity (card_id, activity_name,
    org_id, sales_price, underline_price,
    activity_status, person_purchases_limit, start_time,
    end_time, advance_notice_time, rules,
    opening_stock, stock, platform_type,
    misc_desc,
    status, create_time, create_oper_id,
    create_oper_name, update_time, update_oper_id,
    update_oper_name)
    values (#{cardId,jdbcType=BIGINT}, #{activityName,jdbcType=VARCHAR},
    #{orgId,jdbcType=VARCHAR}, #{salesPrice,jdbcType=DECIMAL}, #{underlinePrice,jdbcType=DECIMAL},
    #{activityStatus,jdbcType=INTEGER}, #{personPurchasesLimit,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP},
    #{endTime,jdbcType=TIMESTAMP}, #{advanceNoticeTime,jdbcType=TIMESTAMP}, #{rules,jdbcType=VARCHAR},
    #{openingStock,jdbcType=DECIMAL}, #{stock,jdbcType=DECIMAL}, #{platformType,jdbcType=INTEGER},
    #{miscDesc,jdbcType=VARCHAR},
    #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT},
    #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT},
    #{updateOperName,jdbcType=VARCHAR})
  </insert>

  <update id="disable">
    UPDATE
    ${issSchema}.mmp_card_sales_activity
    SET status = 1,
    update_time = now(), update_oper_id = #{operId}, update_oper_name = #{operUser}
    WHERE id = #{id} AND status = 0
  </update>

  <update id="updateStatus">
    UPDATE
    ${issSchema}.mmp_card_sales_activity
    SET activity_status = #{status},
    update_time = now(), update_oper_id = #{operId}, update_oper_name = #{operUser}
    WHERE id = #{id} AND status = 0
  </update>

  <update id="updateStatusStart">
    UPDATE
    ${issSchema}.mmp_card_sales_activity
    SET activity_status = 2,
    start_time = now(),
    update_time = now(), update_oper_id = #{operId}, update_oper_name = #{operUser}
    WHERE id = #{id} AND status = 0 AND activity_status in (0, 1)
  </update>

  <update id="updateStatusStop">
    UPDATE
    ${issSchema}.mmp_card_sales_activity
    SET activity_status = 3,
    end_time = now(),
    update_time = now(), update_oper_id = #{operId}, update_oper_name = #{operUser}
    WHERE id = #{id} AND status = 0 AND activity_status = 2
  </update>

  <resultMap id="fullResultMap" type="com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivityInfo" extends="BaseResultMap">
    <result column="org_name" property="orgName" jdbcType="VARCHAR" />
    <result column="card_name" property="cardName" jdbcType="VARCHAR" />
    <result column="card_type" property="cardType" jdbcType="INTEGER" />
    <result column="discount" property="discount" jdbcType="INTEGER" />
    <result column="max_value" property="maxValue" jdbcType="DECIMAL" />
    <result column="duration_limit" property="durationLimit" jdbcType="INTEGER" />
    <result column="city_limit" property="cityLimit" jdbcType="VARCHAR" />
    <result column="vehicle_model_limit" property="vehicleModelLimit" jdbcType="VARCHAR" />
    <result column="goods_model_id" property="goodsModelId" jdbcType="VARCHAR" />
    <result column="store_ids" property="storeIds" jdbcType="VARCHAR" />
    <result column="rent_method" property="rentMethod" jdbcType="VARCHAR" />
    <result column="rent_method_group" property="rentMethodGroup" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectList" resultMap="fullResultMap"
          parameterType="com.extracme.evcard.rpc.vipcard.dto.CardActivityQueryDto" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:01:23 CST 2020.
    -->
    select
    a.id, a.card_id, a.activity_name, a.org_id, a.sales_price, a.underline_price, a.activity_status,
    a.person_purchases_limit, a.start_time, a.end_time, a.advance_notice_time, a.rules, a.opening_stock,
    a.stock, a.sales_volume, a.platform_type, a.misc_desc, a.status, a.create_time, a.create_oper_id, a.create_oper_name, a.update_time,
    a.update_oper_id, a.update_oper_name,
    o.org_name,
    c.card_name, c.card_type, c.discount, c.max_value, c.duration_limit, c.city_limit,
    c.vehicle_model_limit, c.goods_model_id, c.store_ids, c.rent_method, c.rent_method_group

    from ${issSchema}.mmp_card_sales_activity a
    LEFT JOIN ${issSchema}.mmp_card_def c ON c.card_id = a.card_id
    LEFT JOIN ${isvSchema}.org_info o ON a.org_id = o.ORG_ID
    where 1 = 1 and a.status = 0
    <if test="id != null">
      and a.id = #{id}
    </if>
    <if test="cardId != null">
      and a.card_id = #{cardId}
    </if>
    <if test="activityName != null and activityName != '' ">
      and a.activity_name like concat('%',#{activityName},'%')
    </if>
    <if test="cardName != null and cardName != '' ">
      and c.card_name like concat('%',#{cardName},'%')
    </if>
    <if test="cardType != null">
      and c.card_type = #{cardType}
    </if>
    <if test="activityStatus != null">
      and a.activity_status = #{activityStatus}
    </if>
    <if test="orgId != null and orgId != '' ">
      and a.org_id like concat(#{orgId},'%')
    </if>
    <if test="startDate != null and startDate != '' ">
      and a.end_time &gt;= #{startDate}
    </if>
    <if test="endDate != null and endDate != '' ">
      and a.start_time &lt; #{endDate}
    </if>
    <if test="preAnnounceEnable != null and preAnnounceEnable == 0 ">
      and a.advance_notice_time &lt;= now()
    </if>
    <if test="preAnnounceEnable != null and preAnnounceEnable == 1 ">
      and a.advance_notice_time &gt; now()
    </if>
    <if test="stockMin != null">
      and a.stock &gt;= #{stockMin}
    </if>
    <if test="stockMax != null">
      and a.stock &lt; #{stockMax}
    </if>
    <if test="salesVolumeMin != null">
      and a.opening_stock - a.stock &gt;= #{salesVolumeMin}
    </if>
    <if test="salesVolumeMax != null">
      and a.opening_stock - a.stock &lt; #{salesVolumeMax}
    </if>
    <if test="platformType != null">
      and a.platform_type = #{platformType}
    </if>
    order by a.create_time desc
  </select>

  <select id="getEffectiveActivityCardList" resultType="com.extracme.evcard.rpc.vipcard.model.CardSalesActivityCardModel">
    SELECT
        a.id as activityId,
        a.activity_name as activityName,
        a.card_id as cardId,
        a.sales_price as salesPrice,
        a.underline_price as underlinePrice,
        a.person_purchases_limit as activityPersonPurchasesLimit,
        a.start_time as startTime,
        a.activity_status as activityStatus,
        a.advance_notice_time as advanceNoticeTime,
        a.stock as stock,
        b.card_name as cardName,
        b.card_type as cardType,
        b.discount as discount,
        b.max_value as `maxValue`,
        b.duration_limit as durationLimit,
        b.city_limit as cityLimit,
        b.vehicle_model_limit as vehicleModelLimit,
        b.goods_model_id as goodsModelId,
        b.store_ids as storeIds,
        b.rent_method as rentMethod,
        b.rent_method_group as rentMethodGroup,
        b.start_time as pickUpStartTime,
        b.end_time as pickUpEndTime,
        b.available_days_of_week as availableDaysOfWeek,
        b.valid_days as validDays,
        b.rules as rules,
        b.purchase_type as purchaseType,
        b.back_url as backUrl,
        b.total_discount_amount as totalDiscountAmount
    FROM
        ${issSchema}.mmp_card_sales_activity a
    LEFT JOIN ${issSchema}.mmp_card_def b ON a.card_id = b.card_id
    WHERE
        a.status = 0
    and a.activity_status IN (1, 2)
    and a.platform_type = 0
    AND a.stock >= 0
    AND (b.city_limit = '' OR b.city_limit LIKE CONCAT('%', #{cityId}, '%'))
    order by a.activity_status DESC, a.start_time ASC
  </select>

  <select id="getEffectiveActivityCardDetailById" resultType="com.extracme.evcard.rpc.vipcard.model.CardSalesActivityCardModel">
    SELECT
        a.id as activityId,
        a.activity_name as activityName,
        a.card_id as cardId,
        a.sales_price as salesPrice,
        a.underline_price as underlinePrice,
        a.person_purchases_limit as activityPersonPurchasesLimit,
        a.start_time as startTime,
        a.end_time as endTime,
        a.activity_status as activityStatus,
        a.stock as stock,
        a.platform_type as platformType,
        b.card_name as cardName,
        b.card_type as cardType,
        b.discount as discount,
        b.max_value as `maxValue`,
        b.duration_limit as durationLimit,
        b.city_limit as cityLimit,
        b.vehicle_model_limit as vehicleModelLimit,
        b.goods_model_id as goodsModelId,
        b.store_ids as storeIds,
        b.rent_method as rentMethod,
        b.rent_method_group as rentMethodGroup,
        b.start_time as pickUpStartTime,
        b.end_time as pickUpEndTime,
        b.available_days_of_week as availableDaysOfWeek,
        b.valid_days as validDays,
        b.rules as rules,
        b.purchase_type as purchaseType,
        b.back_url as backUrl,
        b.total_discount_amount as totalDiscountAmount
    FROM
        ${issSchema}.mmp_card_sales_activity a
    LEFT JOIN iss.mmp_card_def b ON a.card_id = b.card_id
    WHERE
        a.status = 0
    AND a.id = #{id}
  </select>
  <select id="getActivityByCardIdAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_sales_activity
    where card_id = #{cardId} and activity_status =#{status}
    AND platform_type = 0
  </select>
  <select id="getEffectiveActivityByOrder" resultType="com.extracme.evcard.rpc.vipcard.model.CardSalesActivityCardModel">
    SELECT
        a.id AS activityId,
        a.activity_name as activityName,
        a.card_id AS cardId,
        a.sales_price AS salesPrice,
        a.underline_price AS underlinePrice,
        a.person_purchases_limit AS activityPersonPurchasesLimit,
        a.start_time AS startTime,
        a.activity_status AS activityStatus,
        a.stock as stock,
        a.platform_type as platformType,
        b.card_name AS cardName,
        b.card_type AS cardType,
        b.discount AS discount,
        b.max_value AS `maxValue`,
        b.duration_limit AS durationLimit,
        b.city_limit AS cityLimit,
        b.vehicle_model_limit AS vehicleModelLimit,
        b.goods_model_id AS goodsModelId,
        b.store_ids as storeIds,
        b.rent_method AS rentMethod,
        b.rent_method_group AS rentMethodGroup,
        b.start_time AS pickUpStartTime,
        b.end_time AS pickUpEndTime,
        b.available_days_of_week AS availableDaysOfWeek,
        b.rules AS rules,
        b.purchase_type as purchaseType,
        b.back_url as backUrl,
        b.total_discount_amount as totalDiscountAmount
    FROM
        ${issSchema}.mmp_card_sales_activity a
    LEFT JOIN iss.mmp_card_def b ON a.card_id = b.card_id
    WHERE
        a.status = 0
    AND a.activity_status = 2
    AND a.stock > 0
    AND a.platform_type = 0
  </select>


  <!-- 待上线任务列表（待上线） -->
  <select id="selectReadyToStartActivityList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from ${issSchema}.mmp_card_sales_activity
      where activity_status = 1
      and start_time &lt;= #{date}
      and status = 0
  </select>

  <!-- 待下线任务列表（上线中&暂停中） -->
  <select id="selectReadyToStopActivityList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from ${issSchema}.mmp_card_sales_activity
      where activity_status = 2
      and end_time &lt;= #{date}
      and status = 0
  </select>
    <select id="queryActivityNow" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from ${issSchema}.mmp_card_sales_activity
      where
      date_format(DATE_SUB(start_time, INTERVAL 3 MINUTE), '%Y-%c-%d %H:%i') = date_format(NOW(), '%Y-%c-%d %H:%i')
      and activity_status = 1
      and status = 0
    </select>

    <!--活动批量上线/下线  -->
  <update id="batchUpdateActivityStatusStart">
    UPDATE ${issSchema}.mmp_card_sales_activity
    SET activity_status=#{activityStatus},
    start_time = now(),
    update_time = now(),
    update_oper_id = #{operId}, update_oper_name = #{operUser}
    WHERE
      activity_status = #{oriStatus}
      and id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
        #{item}
        </foreach>
  </update>

  <update id="batchUpdateActivityStatusStop">
    UPDATE ${issSchema}.mmp_card_sales_activity
    SET activity_status=#{activityStatus},
    end_time = now(),
    update_time = now(),
    update_oper_id = #{operId}, update_oper_name = #{operUser}
    WHERE
    activity_status = #{oriStatus}
    and id in
    <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </update>


  <update id="updateActivityStock" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:39:34 CST 2020.
    -->
    update ${issSchema}.mmp_card_sales_activity
    <set >
      <if test="stock != null" >
        stock = stock - #{stock,jdbcType=DECIMAL},
      </if>
      <if test="salesVolume != null" >
        sales_volume = sales_volume + #{salesVolume,jdbcType=DECIMAL},
      </if>
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      <if test="activityStatus != null" >
        activity_status = #{activityStatus,jdbcType=INTEGER},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 创建任务时，查询任务时间冲突 -->
  <select id="queryOrgTimeConflict" resultType="java.lang.Long">
    SELECT id
    FROM ${issSchema}.mmp_card_sales_activity
    WHERE activity_status != 3
    AND status = 0
    AND org_id = #{orgId} AND card_id = #{cardId}
    AND end_time &gt;=#{startDate} AND start_time &lt;=#{endDate}
    <if test="id !=null ">
      AND id != #{id}
    </if>
  </select>


  <!-- 获取当前时间范围内进行中的指定类型的活动, 进行中或暂停中 -->
  <select id="selectRunningActivityfOrg" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from ${issSchema}.mmp_card_sales_activity
    where org_id = #{orgId} AND card_id = #{cardId} AND activity_status in(2)
    AND status = 0
    limit 1
  </select>


</mapper>