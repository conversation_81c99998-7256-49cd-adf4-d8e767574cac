package com.extracme.evcard.rpc.vipcard.rest.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PaySuiXiangCardOrderReq {
    // 当前用户
    private String mid;

    private String payOrderNo; // 支付订单号


    private String payChannel; // 支付渠道 1: 支付宝app 2:银联app 3:账户余额 5:微信app 6:微信公众号 7:支付宝wap 8:银联wap 10:apple pay 11: 小米pay 12:华为pay 13:三星pay 14:魅族pay 15:支付宝小程序 16:银联app预授权 17:银联wap预授权  18:微信wap 19:微信小程序 20:支付宝app2.0 21:支付宝代扣 22:电信翼支付
    private String wapUrl;//网站地址，微信H5支付使用
    private String wapName;//网站名称，微信H5支付使用
    private String frontNotifyUrl;
    // 通过支付渠道要支付的金额
    private BigDecimal realAmount;
    // 是否允许免密支付 1=是 2=否
    private int densityFree;

    private String openId;   // 支付宝/微信用户id 小程序
}
