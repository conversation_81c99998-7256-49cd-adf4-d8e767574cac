<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.SuixiangCardPriceMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPrice">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_base_id" jdbcType="BIGINT" property="cardBaseId" />
    <result column="card_rent_id" jdbcType="BIGINT" property="cardRentId" />
    <result column="sales_price" jdbcType="DECIMAL" property="salesPrice" />
    <result column="underline_price" jdbcType="DECIMAL" property="underlinePrice" />
    <result column="car_model_group" jdbcType="VARCHAR" property="carModelGroup" />
    <result column="car_model_ids" jdbcType="VARCHAR" property="carModelIds" />
    <result column="sales" jdbcType="INTEGER" property="sales" />

    <result column="landing_page_pic_url" jdbcType="VARCHAR" property="landingPagePicUrl" />
    <result column="landing_page_head_pic_url" jdbcType="VARCHAR" property="landingPageHeadPicUrl" />

    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    id, card_base_id, card_rent_id, sales_price, underline_price, car_model_group, car_model_ids, 
    sales, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, 
    update_oper_name, is_deleted, landing_page_pic_url, landing_page_head_pic_url
  </sql>
  <select id="selectByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPriceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from suixiang_card_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from suixiang_card_price
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    delete from suixiang_card_price
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPrice">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    insert into suixiang_card_price (id, card_base_id, card_rent_id, 
      sales_price, underline_price, car_model_group, 
      car_model_ids, sales, create_time, 
      create_oper_id, create_oper_name, update_time, 
      update_oper_id, update_oper_name, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{cardBaseId,jdbcType=BIGINT}, #{cardRentId,jdbcType=BIGINT}, 
      #{salesPrice,jdbcType=DECIMAL}, #{underlinePrice,jdbcType=DECIMAL}, #{carModelGroup,jdbcType=VARCHAR}, 
      #{carModelIds,jdbcType=VARCHAR}, #{sales,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPrice" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    insert into suixiang_card_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cardBaseId != null">
        card_base_id,
      </if>
      <if test="cardRentId != null">
        card_rent_id,
      </if>
      <if test="salesPrice != null">
        sales_price,
      </if>
      <if test="underlinePrice != null">
        underline_price,
      </if>
      <if test="carModelGroup != null">
        car_model_group,
      </if>
      <if test="carModelIds != null">
        car_model_ids,
      </if>
      <if test="sales != null">
        sales,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="landingPagePicUrl != null">
        landing_page_pic_url,
      </if>
      <if test="landingPageHeadPicUrl != null">
        landing_page_head_pic_url,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cardBaseId != null">
        #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardRentId != null">
        #{cardRentId,jdbcType=BIGINT},
      </if>
      <if test="salesPrice != null">
        #{salesPrice,jdbcType=DECIMAL},
      </if>
      <if test="underlinePrice != null">
        #{underlinePrice,jdbcType=DECIMAL},
      </if>
      <if test="carModelGroup != null">
        #{carModelGroup,jdbcType=VARCHAR},
      </if>
      <if test="carModelIds != null">
        #{carModelIds,jdbcType=VARCHAR},
      </if>
      <if test="sales != null">
        #{sales,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>

      <if test="landingPagePicUrl != null">
        #{landingPagePicUrl,jdbcType=VARCHAR},
      </if>

      <if test="landingPageHeadPicUrl != null">
        #{landingPageHeadPicUrl,jdbcType=VARCHAR},
      </if>

    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPriceExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    select count(*) from suixiang_card_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    update suixiang_card_price
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardBaseId != null">
        card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.cardRentId != null">
        card_rent_id = #{record.cardRentId,jdbcType=BIGINT},
      </if>
      <if test="record.salesPrice != null">
        sales_price = #{record.salesPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.underlinePrice != null">
        underline_price = #{record.underlinePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.carModelGroup != null">
        car_model_group = #{record.carModelGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.carModelIds != null">
        car_model_ids = #{record.carModelIds,jdbcType=VARCHAR},
      </if>
      <if test="record.sales != null">
        sales = #{record.sales,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null">
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null">
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null">
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null">
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    update suixiang_card_price
    set id = #{record.id,jdbcType=BIGINT},
      card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      card_rent_id = #{record.cardRentId,jdbcType=BIGINT},
      sales_price = #{record.salesPrice,jdbcType=DECIMAL},
      underline_price = #{record.underlinePrice,jdbcType=DECIMAL},
      car_model_group = #{record.carModelGroup,jdbcType=VARCHAR},
      car_model_ids = #{record.carModelIds,jdbcType=VARCHAR},
      sales = #{record.sales,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPrice">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    update suixiang_card_price
    <set>
      <if test="cardBaseId != null">
        card_base_id = #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardRentId != null">
        card_rent_id = #{cardRentId,jdbcType=BIGINT},
      </if>
      <if test="salesPrice != null">
        sales_price = #{salesPrice,jdbcType=DECIMAL},
      </if>
      <if test="underlinePrice != null">
        underline_price = #{underlinePrice,jdbcType=DECIMAL},
      </if>
      <if test="carModelGroup != null">
        car_model_group = #{carModelGroup,jdbcType=VARCHAR},
      </if>
      <if test="carModelIds != null">
        car_model_ids = #{carModelIds,jdbcType=VARCHAR},
      </if>
      <if test="sales != null">
        sales = #{sales,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPrice">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jan 11 20:33:47 CST 2023.
    -->
    update suixiang_card_price
    set card_base_id = #{cardBaseId,jdbcType=BIGINT},
      card_rent_id = #{cardRentId,jdbcType=BIGINT},
      sales_price = #{salesPrice,jdbcType=DECIMAL},
      underline_price = #{underlinePrice,jdbcType=DECIMAL},
      car_model_group = #{carModelGroup,jdbcType=VARCHAR},
      car_model_ids = #{carModelIds,jdbcType=VARCHAR},
      sales = #{sales,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <sql id="Base_Column_List3">
    t3.id as cardPriceId, t3.card_base_id as cardBaseId,t3.card_rent_id as cardRentId,t3.sales_price as salesPrice,t3.underline_price as underlinePrice,
    t3.car_model_group as carModelGroup,t3.car_model_ids as carModelIds,t3.sales as sales,t3.landing_page_pic_url as landingPagePicUrl,t3.landing_page_head_pic_url as landingPageHeadPicUrl,
    t1.card_name as cardName,t1.org_id as orgId,t1.city_id as cityId,t1.advance_notice_time as advanceNoticeTime,t1.unavailable_date as unavailableDate,t1.purchase_limit_num as purchaseLimitNum,
    t1.sale_start_time as saleStartTime,t1.sale_end_time as saleEndTime,t1.valid_days_type as validDaysType ,t1.init_stock as initStock,t1.holiday_available as holidayAvailable,t1.vehicle_brand_ids as vehicleBrandIds,t1.merge_flag as mergeFlag,t1.landing_page_flag as landingPageFlag,
    t1.stock,t1.sales as totalSales,t1.display_flag as displayFlag,t1.single_order_duration as singleOrderDuration,t1.style_type as styleType,
    t1.back_url as backUrl,t1.rules,t1.card_status as cardStatus,t2.rent_days as rentDays,t2.service_fees as serviceFees,t2.total_service_fees_amout as totalServiceFeesAmout
  </sql>

  <!-- 根据价格id 查询随享卡基础信息 -->
  <select id="selectInfoByPriceId" parameterType="java.lang.Long" resultType="com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardInfoDto">
    select
    <include refid="Base_Column_List3" />
    from suixiang_card_price t3
    left join suixiang_card_base t1 on t1.id = t3.card_base_id
    left join suixiang_card_rent_days t2 on t2.id = t3.card_rent_id
    where t3.id = #{priceId,jdbcType=BIGINT}
    and t1.is_deleted=0
    and t2.is_deleted=0
    and t3.is_deleted=0
  </select>

  <select id="selectInfoListByPriceIds"  resultType="com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardInfoDto">
    select
    <include refid="Base_Column_List3" />
    from suixiang_card_price t3
    left join suixiang_card_base t1 on t1.id = t3.card_base_id
    left join suixiang_card_rent_days t2 on t2.id = t3.card_rent_id
    where t1.is_deleted=0
    and t2.is_deleted=0
    and t3.is_deleted=0
    <if test="priceIds!=null and priceIds.size &gt; 0">
      and t3.id in
      <foreach close=")" collection="priceIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="getListByCityIdAndCarModelId" resultType="com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardInfoDto">
    select
    <include refid="Base_Column_List3" />
    from suixiang_card_price t3
    left join suixiang_card_base t1 on t1.id = t3.card_base_id
    left join suixiang_card_rent_days t2 on t2.id = t3.card_rent_id
    where t1.card_status = 3
    and t1.is_deleted=0
    and t2.is_deleted=0
    and t3.is_deleted=0
    and t1.display_flag = 1
    and t1.stock > 0
    <if test="carModelId != null">
      and (find_in_set(#{carModelId},t3.car_model_ids) or t3.car_model_ids like concat('%','-1','%'))
    </if>
    <if test="cityId != null">
      and (find_in_set(#{cityId},t1.city_id) or t1.city_id like concat('%','-1','%'))
    </if>
  </select>


  <!-- 根据基础表id 查询随享卡基础信息 -->
  <select id="selectInfoByBaseId" parameterType="java.lang.Long" resultType="com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardInfoDto">
    select
    <include refid="Base_Column_List3" />
    from suixiang_card_price t3
    left join suixiang_card_base t1 on t1.id = t3.card_base_id
    left join suixiang_card_rent_days t2 on t2.id = t3.card_rent_id
    where t1.is_deleted=0
    and t2.is_deleted=0
    and t3.is_deleted=0
    and t3.card_base_id = #{cardBaseId}
    order by t1.id asc,t2.rent_days asc,t3.sales_price asc
  </select>
</mapper>