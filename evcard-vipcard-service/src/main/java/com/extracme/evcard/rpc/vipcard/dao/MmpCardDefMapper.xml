<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.MmpCardDefMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.MmpCardDef" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:01:23 CST 2020.
    -->
    <id column="card_id" property="cardId" jdbcType="BIGINT" />
    <result column="card_name" property="cardName" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="VARCHAR" />
    <result column="card_type" property="cardType" jdbcType="INTEGER" />

    <result column="purchase_type" property="purchaseType" jdbcType="INTEGER" />
    <result column="style_type" property="styleType" jdbcType="INTEGER" />
    <result column="back_url" property="backUrl" jdbcType="VARCHAR" />

    <result column="discount" property="discount" jdbcType="INTEGER" />
    <result column="total_discount_amount" property="totalDiscountAmount" jdbcType="DECIMAL" />
    <result column="max_value" property="maxValue" jdbcType="DECIMAL" />
    <result column="duration_limit" property="durationLimit" jdbcType="INTEGER" />
    <result column="city_limit" property="cityLimit" jdbcType="VARCHAR" />
    <result column="vehicle_model_limit" property="vehicleModelLimit" jdbcType="VARCHAR" />
    <result column="goods_model_id" property="goodsModelId" jdbcType="VARCHAR" />
    <result column="store_ids" property="storeIds" jdbcType="VARCHAR" />
    <result column="rent_method" property="rentMethod" jdbcType="VARCHAR" />
    <result column="rent_method_group" property="rentMethodGroup" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime" jdbcType="VARCHAR" />
    <result column="end_time" property="endTime" jdbcType="VARCHAR" />
    <result column="available_days_of_week" property="availableDaysOfWeek" jdbcType="VARCHAR" />
    <result column="rules" property="rules" jdbcType="VARCHAR" />
    <result column="valid_time_type" property="validTimeType" jdbcType="INTEGER" />
    <result column="effective_days" property="effectiveDays" jdbcType="INTEGER" />
    <result column="valid_days" property="validDays" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:01:23 CST 2020.
    -->
    card_id, card_name, org_id, card_type, purchase_type, style_type, back_url,
    discount, total_discount_amount, max_value, duration_limit, city_limit,
    vehicle_model_limit, goods_model_id, store_ids, rent_method, rent_method_group, start_time, end_time, available_days_of_week, rules,
    valid_time_type, effective_days, valid_days, status, create_time,
    create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:01:23 CST 2020.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_def
    where card_id = #{cardId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:01:23 CST 2020.
    -->
    delete from ${issSchema}.mmp_card_def
    where card_id = #{cardId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardDef" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:01:23 CST 2020.
    -->
    insert into ${issSchema}.mmp_card_def (card_id, card_name, org_id,
      card_type, purchase_type, style_type, back_url,
      discount, total_discount_amount, max_value,
      duration_limit, city_limit, vehicle_model_limit, goods_model_id, store_ids,
      rent_method, rent_method_group, start_time, end_time,
      available_days_of_week, rules, valid_time_type,
      effective_days, valid_days,
      status, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{cardId,jdbcType=BIGINT}, #{cardName,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, 
      #{cardType,jdbcType=INTEGER},
      #{purchaseType,jdbcType=INTEGER}, #{styleType,jdbcType=INTEGER}, #{backUrl,jdbcType=VARCHAR},
      #{discount,jdbcType=INTEGER}, #{totalDiscountAmount,jdbcType=DECIMAL},
      #{maxValue,jdbcType=DECIMAL},
      #{durationLimit,jdbcType=INTEGER}, #{cityLimit,jdbcType=VARCHAR}, #{vehicleModelLimit,jdbcType=VARCHAR},
      #{goodsModelId,jdbcType=VARCHAR}, #{storeIds,jdbcType=VARCHAR},
      #{rentMethod,jdbcType=VARCHAR},#{rentMethodGroup,jdbcType=VARCHAR}, #{startTime,jdbcType=VARCHAR}, #{endTime,jdbcType=VARCHAR},
      #{availableDaysOfWeek,jdbcType=VARCHAR}, #{rules,jdbcType=VARCHAR}, #{validTimeType,jdbcType=INTEGER},
      #{effectiveDays,jdbcType=INTEGER}, #{validDays,jdbcType=INTEGER},
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardDef" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:01:23 CST 2020.
    -->
    insert into ${issSchema}.mmp_card_def
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="cardId != null" >
        card_id,
      </if>
      <if test="cardName != null" >
        card_name,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="cardType != null" >
        card_type,
      </if>

      <if test="purchaseType != null" >
        purchase_type,
      </if>
      <if test="styleType != null" >
        style_type,
      </if>
      <if test="backUrl != null" >
        back_url,
      </if>

      <if test="discount != null" >
        discount,
      </if>
      <if test="totalDiscountAmount != null" >
        total_discount_amount,
      </if>
      <if test="maxValue != null" >
        max_value,
      </if>
      <if test="durationLimit != null" >
        duration_limit,
      </if>
      <if test="cityLimit != null" >
        city_limit,
      </if>
      <if test="vehicleModelLimit != null" >
        vehicle_model_limit,
      </if>
      <if test="goodsModelId != null" >
        goods_model_id,
      </if>
      <if test="storeIds != null" >
        store_ids,
      </if>
      <if test="rentMethod != null" >
        rent_method,
      </if>
      <if test="rentMethodGroup != null" >
        rent_method_group,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="availableDaysOfWeek != null" >
        available_days_of_week,
      </if>
      <if test="rules != null" >
        rules,
      </if>
      <if test="validTimeType != null" >
        valid_time_type,
      </if>
      <if test="effectiveDays != null" >
        effective_days,
      </if>
      <if test="validDays != null" >
        valid_days,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="cardId != null" >
        #{cardId,jdbcType=BIGINT},
      </if>
      <if test="cardName != null" >
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null" >
        #{cardType,jdbcType=INTEGER},
      </if>

      <if test="purchaseType != null" >
        #{purchaseType,jdbcType=INTEGER},
      </if>
      <if test="styleType != null" >
        #{styleType,jdbcType=INTEGER},
      </if>
      <if test="backUrl != null" >
        #{backUrl,jdbcType=VARCHAR},
      </if>
      <if test="discount != null" >
        #{discount,jdbcType=INTEGER},
      </if>
      <if test="totalDiscountAmount != null" >
        #{totalDiscountAmount,jdbcType=INTEGER},
      </if>
      <if test="maxValue != null" >
        #{maxValue,jdbcType=DECIMAL},
      </if>
      <if test="durationLimit != null" >
        #{durationLimit,jdbcType=INTEGER},
      </if>
      <if test="cityLimit != null" >
        #{cityLimit,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelLimit != null" >
        #{vehicleModelLimit,jdbcType=VARCHAR},
      </if>
      <if test="goodsModelId != null" >
        #{goodsModelId,jdbcType=VARCHAR},
      </if>
      <if test="storeIds != null" >
        #{storeIds,jdbcType=VARCHAR},
      </if>
      <if test="rentMethod != null" >
        #{rentMethod,jdbcType=VARCHAR},
      </if>
      <if test="rentMethodGroup != null" >
        #{rentMethodGroup,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="availableDaysOfWeek != null" >
        #{availableDaysOfWeek,jdbcType=VARCHAR},
      </if>
      <if test="rules != null" >
        #{rules,jdbcType=VARCHAR},
      </if>
      <if test="validTimeType != null" >
        #{validTimeType,jdbcType=INTEGER},
      </if>
      <if test="effectiveDays != null" >
        #{effectiveDays,jdbcType=INTEGER},
      </if>
      <if test="validDays != null" >
        #{validDays,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardDef" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:01:23 CST 2020.
    -->
    update ${issSchema}.mmp_card_def
    <set >
      <if test="cardName != null" >
        card_name = #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null" >
        card_type = #{cardType,jdbcType=INTEGER},
      </if>

      <if test="purchaseType != null" >
        purchase_type = #{purchaseType,jdbcType=INTEGER},
      </if>
      <if test="styleType != null" >
        style_type = #{styleType,jdbcType=INTEGER},
      </if>
      <if test="backUrl != null" >
        back_url = #{backUrl,jdbcType=INTEGER},
      </if>

      <if test="discount != null" >
        discount = #{discount,jdbcType=INTEGER},
      </if>
      <if test="totalDiscountAmount != null" >
        total_discount_amount = #{totalDiscountAmount,jdbcType=DECIMAL},
      </if>
      <if test="maxValue != null" >
        max_value = #{maxValue,jdbcType=DECIMAL},
      </if>
      <if test="durationLimit != null" >
        duration_limit = #{durationLimit,jdbcType=INTEGER},
      </if>
      <if test="cityLimit != null" >
        city_limit = #{cityLimit,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelLimit != null" >
        vehicle_model_limit = #{vehicleModelLimit,jdbcType=VARCHAR},
      </if>
      <if test="goodsModelId != null" >
        goods_model_id = #{goodsModelId,jdbcType=VARCHAR},
      </if>
      <if test="storeIds != null" >
        store_ids = #{storeIds,jdbcType=VARCHAR},
      </if>
      <if test="rentMethod != null" >
        rent_method = #{rentMethod,jdbcType=VARCHAR},
      </if>
      <if test="rentMethodGroup != null" >
        rent_method_group = #{rentMethodGroup,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="availableDaysOfWeek != null" >
        available_days_of_week = #{availableDaysOfWeek,jdbcType=VARCHAR},
      </if>
      <if test="rules != null" >
        rules = #{rules,jdbcType=VARCHAR},
      </if>
      <if test="validTimeType != null" >
        valid_time_type = #{validTimeType,jdbcType=INTEGER},
      </if>
      <if test="effectiveDays != null" >
        effective_days = #{effectiveDays,jdbcType=INTEGER},
      </if>
      <if test="validDays != null" >
        valid_days = #{validDays,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where card_id = #{cardId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardDef" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:01:23 CST 2020.
    -->
    update ${issSchema}.mmp_card_def
    set card_name = #{cardName,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=VARCHAR},
      card_type = #{cardType,jdbcType=INTEGER},

      purchase_type = #{purchaseType,jdbcType=INTEGER},
      style_type = #{styleType,jdbcType=INTEGER},
      back_url = #{backUrl,jdbcType=VARCHAR},

      discount = #{discount,jdbcType=INTEGER},
      total_discount_amount = #{totalDiscountAmount,jdbcType=DECIMAL},
      max_value = #{maxValue,jdbcType=DECIMAL},
      duration_limit = #{durationLimit,jdbcType=INTEGER},
      city_limit = #{cityLimit,jdbcType=VARCHAR},
      vehicle_model_limit = #{vehicleModelLimit,jdbcType=VARCHAR},
      goods_model_id = #{goodsModelId,jdbcType=VARCHAR},
      store_ids = #{storeIds,jdbcType=VARCHAR},
      rent_method = #{rentMethod,jdbcType=VARCHAR},
      rent_method_group = #{rentMethodGroup,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=VARCHAR},
      end_time = #{endTime,jdbcType=VARCHAR},
      available_days_of_week = #{availableDaysOfWeek,jdbcType=VARCHAR},
      rules = #{rules,jdbcType=VARCHAR},
      valid_time_type = #{validTimeType,jdbcType=INTEGER},
      effective_days = #{effectiveDays,jdbcType=INTEGER},
      valid_days = #{validDays,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where card_id = #{cardId,jdbcType=BIGINT}
  </update>


  <select id="selectMatchCardModel" resultMap="BaseResultMap" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardDef" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:01:23 CST 2020.
    -->
    SELECT
    <include refid="Base_Column_List"/>
    FROM ${issSchema}.mmp_card_def
    <where>
      1 = 1
      <if test=" cardId!=null ">
        AND card_id!=#{cardId}
      </if>
      <if test=" cardType!=null ">
        AND card_type=#{cardType}
      </if>

      <if test=" purchaseType!=null ">
        AND purchase_type=#{purchaseType}
      </if>
      <if test=" styleType!=null ">
        AND style_type=#{styleType}
      </if>
      <if test=" backUrl!=null ">
        AND back_url=#{backUrl}
      </if>
      <if test=" cardName!=null ">
        AND card_name=#{cardName}
      </if>
      <if test=" rules!=null ">
        AND rules=#{rules}
      </if>
      <if test=" orgId!=null ">
        AND org_id =#{orgId}
      </if>
      <if test=" validTimeType!=null ">
        AND valid_time_type=#{validTimeType}
      </if>
      <if test="validDays != null ">
        AND valid_days=#{validDays}
      </if>
      <if test="effectiveDays != null ">
        AND effective_days=#{effectiveDays}
      </if>
      <if test=" maxValue!=null  ">
        AND max_value=#{maxValue}
      </if>
      <if test="discount!=null">
        AND discount = #{discount}
      </if>
      <if test=" totalDiscountAmount!=null ">
        AND total_discount_amount=#{totalDiscountAmount}
      </if>
      <if test="durationLimit != null">
        AND duration_limit = #{durationLimit}
      </if>
      <if test=" cityLimit!=null ">
        AND city_limit = #{cityLimit}
      </if>
      <if test=" vehicleModelLimit!=null ">
        AND vehicle_model_limit = #{vehicleModelLimit}
      </if>
      <if test=" goodsModelId!=null ">
        AND goods_model_id = #{goodsModelId}
      </if>
      <if test=" storeIds!=null ">
        AND store_ids = #{storeIds}
      </if>
      <if test="rentMethod != null">
        AND rent_method = #{rentMethod}
      </if>
      <if test="rentMethodGroup != null">
        AND rent_method_group = #{rentMethodGroup}
      </if>
      <if test="availableDaysOfWeek != null">
        AND available_days_of_week = #{availableDaysOfWeek}
      </if>
      <if test=" startTime!=null ">
        AND start_time=#{startTime}
      </if>
      <if test=" endTime!=null ">
        AND end_time=#{endTime}
      </if>
    </where>
    ORDER BY CREATE_TIME DESC limit 1
  </select>


    <insert id="add" parameterType="com.extracme.evcard.rpc.vipcard.model.MmpCardDef" useGeneratedKeys="true" keyProperty="cardId">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Dec 24 14:01:23 CST 2020.
        -->
        insert into ${issSchema}.mmp_card_def (card_name, org_id,
        card_type, purchase_type, style_type, back_url,
        discount, total_discount_amount, max_value,
        duration_limit, city_limit, vehicle_model_limit, goods_model_id, store_ids,
        rent_method, rent_method_group, start_time, end_time,
        available_days_of_week, rules, valid_time_type,
        effective_days, valid_days,
        status, create_time, create_oper_id,
        create_oper_name, update_time, update_oper_id,
        update_oper_name)
        values (#{cardName,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR},
        #{cardType,jdbcType=INTEGER},
        #{purchaseType,jdbcType=INTEGER}, #{styleType,jdbcType=INTEGER}, #{backUrl,jdbcType=VARCHAR},
        #{discount,jdbcType=INTEGER}, #{totalDiscountAmount,jdbcType=DECIMAL},
        #{maxValue,jdbcType=DECIMAL},
        #{durationLimit,jdbcType=INTEGER}, #{cityLimit,jdbcType=VARCHAR}, #{vehicleModelLimit,jdbcType=VARCHAR},
        #{goodsModelId,jdbcType=VARCHAR}, #{storeIds,jdbcType=VARCHAR},
        #{rentMethod,jdbcType=VARCHAR},#{rentMethodGroup,jdbcType=VARCHAR}, #{startTime,jdbcType=VARCHAR}, #{endTime,jdbcType=VARCHAR},
        #{availableDaysOfWeek,jdbcType=VARCHAR}, #{rules,jdbcType=VARCHAR}, #{validTimeType,jdbcType=INTEGER},
        #{effectiveDays,jdbcType=INTEGER}, #{validDays,jdbcType=INTEGER},
        #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT},
        #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT},
        #{updateOperName,jdbcType=VARCHAR})
    </insert>


    <select id="selectOneActivityByCard" resultType="java.lang.Long" parameterType="java.lang.Long" >
      <!--
        WARNING - @mbggenerated
        This element is automatically generated by MyBatis Generator, do not modify.
        This element was generated on Thu Dec 24 14:39:34 CST 2020.
      -->
      select id
      from ${issSchema}.mmp_card_sales_activity
      where card_id = #{cardId,jdbcType=BIGINT} and status = 0
      limit 1
  </select>

  <select id="batchSelectActivityByCardIds" resultType="java.lang.Long">
    select distinct card_id
    from ${issSchema}.mmp_card_sales_activity
    where status = 0 and card_id in
    <foreach collection="cardIds" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    limit 100
  </select>


  <resultMap id="ModelResultMap" type="com.extracme.evcard.rpc.vipcard.model.MmpCardDefInfo" extends="BaseResultMap">
    <result column="org_name" property="orgName" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectList" resultMap="ModelResultMap"
          parameterType="com.extracme.evcard.rpc.vipcard.dto.CardModelQueryParamDto" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 24 14:01:23 CST 2020.
    -->
    select
    c.card_id, c.card_name, c.org_id, c.card_type, c.purchase_type, c.style_type, c.back_url,
    c.discount, c.total_discount_amount, c.max_value, c.duration_limit, c.city_limit,
    c.vehicle_model_limit, c.goods_model_id, c.store_ids, c.rent_method, c.rent_method_group,c.start_time, c.end_time, c.available_days_of_week, c.rules,
    c.valid_time_type, c.effective_days, c.valid_days, c.status, c.create_time,
    c.create_oper_id, c.create_oper_name, c.update_time, c.update_oper_id, c.update_oper_name,
    o.org_name
    from ${issSchema}.mmp_card_def c
    LEFT JOIN ${isvSchema}.org_info o ON c.org_id = o.ORG_ID
    where 1 = 1
    <if test="cardId != null">
      and c.card_id = #{cardId}
    </if>
    <if test="cardName != null and cardName != '' ">
      and c.card_name like concat('%',#{cardName},'%')
    </if>
    <if test="cardType != null">
      and c.card_type = #{cardType}
    </if>
    <if test="purchaseType != null">
      and c.purchase_type = #{purchaseType}
    </if>
    <if test="orgId != null and orgId != '' ">
      and c.org_id like concat(#{orgId},'%')
    </if>
    <if test="cityId != null">
      and c.city_limit like concat('%',#{cityId},',%')
    </if>
    <if test="status != null">
      and c.status = #{status}
    </if>
<!--    <if test="rentMethodStr!=null and rentMethodStr != '' ">-->
<!--      and c.rent_method = #{rentMethodStr}-->
<!--    </if>-->
    <if test="rentMethods!=null and rentMethods.size > 0">
       and
      <foreach item="item" collection="rentMethods" open="(" separator=" or " close=")">
        <if test="item == -1">
          c.rent_method = ''
        </if>
        <if test="item != -1">
          c.rent_method like concat('%',#{item},',%')
        </if>
      </foreach>
    </if>

    <if test="rentMethodGroups!=null and rentMethodGroups.size > 0">
      and
      <foreach item="item" collection="rentMethodGroups" open="(" separator=" or " close=")">
        <if test="item == -1">
          c.rent_method_group = ''
        </if>
        <if test="item != -1">
          c.rent_method_group like concat('%',#{item},',%')
        </if>
      </foreach>
    </if>
      order by c.create_time desc
  </select>

  <!-- 禁用 -->
  <update id="updateStatusStop">
    UPDATE
    ${issSchema}.mmp_card_def
    SET status = 1,
    update_time = now(), update_oper_id = #{operId}, update_oper_name = #{operUser}
    WHERE card_id = #{cardId} AND status in (0)
  </update>

  <update id="updateStatusStart">
    UPDATE
    ${issSchema}.mmp_card_def
    SET status = 0,
    update_time = now(), update_oper_id = #{operId}, update_oper_name = #{operUser}
    WHERE card_id = #{cardId} AND status in (1)
  </update>

  <select id="batchQueryByIdList" resultMap="BaseResultMap" >
    select
        <include refid="Base_Column_List" />
    from ${issSchema}.mmp_card_def
    where card_id in
    <foreach collection="cardIdList" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>