package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecordLog;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecordLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardPurchaseRecordLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record_log
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    int countByExample(SuixiangCardPurchaseRecordLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record_log
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record_log
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    int insert(SuixiangCardPurchaseRecordLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record_log
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    int insertSelective(SuixiangCardPurchaseRecordLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record_log
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    List<SuixiangCardPurchaseRecordLog> selectByExample(SuixiangCardPurchaseRecordLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record_log
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    SuixiangCardPurchaseRecordLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record_log
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    int updateByExampleSelective(@Param("record") SuixiangCardPurchaseRecordLog record, @Param("example") SuixiangCardPurchaseRecordLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record_log
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    int updateByExample(@Param("record") SuixiangCardPurchaseRecordLog record, @Param("example") SuixiangCardPurchaseRecordLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record_log
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    int updateByPrimaryKeySelective(SuixiangCardPurchaseRecordLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record_log
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    int updateByPrimaryKey(SuixiangCardPurchaseRecordLog record);
}