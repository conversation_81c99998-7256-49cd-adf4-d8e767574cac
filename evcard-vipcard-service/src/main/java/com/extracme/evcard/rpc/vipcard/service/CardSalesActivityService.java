package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.MemberWrapInfoDto;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.MmpUserTagDto;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.IMembershipWrapService;
import com.extracme.evcard.rpc.order.dto.OrderInfoDto;
import com.extracme.evcard.rpc.order.service.IOrderService;
import com.extracme.evcard.rpc.shop.service.IShopService;
import com.extracme.evcard.rpc.vehicle.dto.VehicleModelDTO;
import com.extracme.evcard.rpc.vehicle.service.IVehicleModelService;
import com.extracme.evcard.rpc.vipcard.dao.CityMapper;
import com.extracme.evcard.rpc.vipcard.dao.MmpCardSalesActivityMapper;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.model.CardSalesActivityCardModel;
import com.extracme.evcard.rpc.vipcard.model.MmpCardSalesActivity;
import com.extracme.evcard.rpc.vipcard.service.inner.CityServ;
import com.extracme.evcard.rpc.vipcard.service.inner.MemberCardInnerService;
import com.extracme.evcard.rpc.vipcard.service.store.StoreServ;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.extracme.evcard.rpc.vipcard.util.ConfigUtil;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 16:39 2020/12/24
 */

@Slf4j
@Service
public class CardSalesActivityService implements ICardSalesActivityService {

    @Autowired
    private CityServ cityServ;
    @Autowired
    private MmpCardSalesActivityMapper cardSalesActivityMapper;
    @Autowired
    private ICardTradeService cardTradeService;
    @Autowired
    private IVehicleModelService vehicleModelService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IShopService shopService;
    @Autowired
    private CityMapper cityMapper;
    @Autowired
    private IMemberShipService memberShipService;
    @Resource
    IMembershipWrapService membershipWrapService;
    @Autowired
    private MemberCardService memberCardService;

    @Autowired
    private MemberCardInnerService memberCardInnerService;

    @Autowired
    private StoreServ storeServ;

    @Override
    public List<EffectiveActivityCardInfoDto> getEffectiveActivityCardList(Long pkId, String cityName) {
        /**
         * 1.根据城市名称，获取城市id
         */
        Long cityId = cityServ.getCityIdByCityName(cityName);
        if (cityId == null) {
            return null;
        }

        /**
         * 2.查询活动列表
         */
        Date nowTime = new Date();
        List<EffectiveActivityCardInfoDto> resultList = new ArrayList<>();
        List<CardSalesActivityCardModel> effectiveActivityCardList = cardSalesActivityMapper.getEffectiveActivityCardList(cityId);
        if (CollectionUtils.isNotEmpty(effectiveActivityCardList)) {
            /**
             * 3.根据活动id，批量查询购买记录
             */
            Map<Long, List<MemberCardPurchaseRecordDto>> activityPurchaseMap = new HashMap<>();
            List<Long> remindActivityIdList = new ArrayList<>();
            int userAge = 0;
            if (pkId != null) {
                List<Long> activityIdList = effectiveActivityCardList.stream().filter(p -> p.getActivityStatus() == 2).
                        map(CardSalesActivityCardModel::getActivityId).collect(Collectors.toList());
                List<MemberCardPurchaseRecordDto> memberCardPurchaseRecord = cardTradeService.getMemberCardPurchaseRecord(pkId, activityIdList);
                activityPurchaseMap = getPurchaseMapOnly(memberCardPurchaseRecord);
                //activityPurchaseMap = memberCardPurchaseRecord.stream().collect(Collectors.groupingBy(MemberCardPurchaseRecordDto::getCardActivityId));

                /**
                 * 4.根据活动id，批量查询开售提醒设置
                 */
                List<Long> waitSaleActivityIdList = effectiveActivityCardList.stream().filter(p -> p.getActivityStatus() == 1).
                        map(CardSalesActivityCardModel::getActivityId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(waitSaleActivityIdList)) {
                    remindActivityIdList = memberCardService.queryCardRemind(pkId, waitSaleActivityIdList);
                }
                MemberWrapInfoDto member = membershipWrapService.getMemberWrapInfoByPkId(pkId);
                if(member != null && member.getAge() != null) {
                    userAge = member.getAge();
                }
            }

            for (CardSalesActivityCardModel activityCardModel : effectiveActivityCardList) {
                if (activityCardModel.getActivityStatus() == 1 && activityCardModel.getAdvanceNoticeTime().after(nowTime)) {
                    continue;
                }
                //判断，如果是学生卡，年龄>=24，则不返回学生卡活动
                if (activityCardModel.getPurchaseType() == 1) {
                    if (userAge <=0 || userAge >= 24) {
                        continue;
                    }
                }
                EffectiveActivityCardInfoDto infoDto = formatActivityInfo(activityCardModel, pkId, activityPurchaseMap, remindActivityIdList);
                resultList.add(infoDto);
            }
        }
        return resultList;
    }


    @Override
    public EffectiveActivityCardDetailDto getEffectiveActivityCardDetail(Long pkId, Long activityId) {
        EffectiveActivityCardDetailDto detailDto = null;
        /**
         * 1.查询活动信息
         */
        CardSalesActivityCardModel activityCardModel = cardSalesActivityMapper.getEffectiveActivityCardDetailById(activityId);
        if (activityCardModel != null) {
            /**
             * 2.根据活动id，批量查询购买记录
             */
            Map<Long, List<MemberCardPurchaseRecordDto>> activityPurchaseList = new HashMap<>();
            List<Long> remindActivityIdList = new ArrayList<>();
            if (pkId != null) {
                List<MemberCardPurchaseRecordDto> memberCardPurchaseRecord = cardTradeService.getMemberCardPurchaseRecord(pkId, Arrays.asList(activityId));
                activityPurchaseList = getPurchaseMapOnly(memberCardPurchaseRecord);
                //activityPurchaseList = memberCardPurchaseRecord.stream().collect(Collectors.groupingBy(MemberCardPurchaseRecordDto::getCardActivityId));

                /**
                 * 3.根据活动id，批量查询开售提醒设置
                 */
                remindActivityIdList = memberCardService.queryCardRemind(pkId, Arrays.asList(activityId));
            }
            detailDto = formatActivityInfo(activityCardModel, pkId, activityPurchaseList, remindActivityIdList);
            /**
             * 判断学生卡年龄和是否上传过学生证照片
             */
            if (activityCardModel.getPurchaseType() == 1) {
                int userAge = 0;
                MemberWrapInfoDto member = membershipWrapService.getMemberWrapInfoByPkId(pkId);
                if(member != null && member.getAge() != null) {
                    userAge = member.getAge();
                }
                if (0 < userAge && userAge < 24) {
                    MembershipBasicInfo userBasicInfoByPkId = memberShipService.getUserBasicInfoByPkId(pkId);
                    if (userBasicInfoByPkId != null) {
                        MmpUserTagDto mmpUserTagDto = memberShipService.queryUserTagByAuthId(userBasicInfoByPkId.getAuthId());
                        String studentCardUrl = mmpUserTagDto.getStudentCardUrl();
                        if (StringUtils.isNotBlank(studentCardUrl)) {
                            detailDto.setStudentCardFlag(1);
                        }
                    }
                }
            }
        }
        return detailDto;
    }

    @Override
    public List<CanUseActivityCardInfoDto> getEffectiveActivityCardByOrder(String orderSeq, BigDecimal rentAmount) {
        List<CanUseActivityCardInfoDto> resultList = new ArrayList<>();
        /**
         * 1.查询订单信息
         */
        OrderInfoDto orderInfo = orderService.getOrderInfoById(orderSeq);
        if (orderInfo != null) {
            if (orderInfo.getPaymentStatus() >= 5) {
                /**
                 * 2.获取用户信息
                 */
                MembershipBasicInfo memberInfo = memberShipService.getUserBasicInfo(orderInfo.getAuthId(), orderInfo.getOrderType().shortValue());
                Long pkId = memberInfo.getPkId();
                /**
                 * 3.获取订单的取车时间、用车时长、用车区域、车型、产品线
                 */
                String orderPickUpDateTime = orderInfo.getPickupdatetime();
                Date orderPickUpDate = DateUtil.getDateFromStr(orderPickUpDateTime, DateUtil.dtLong);
                Date orderReturnDate =  DateUtil.getDateFromStr(orderPickUpDateTime, DateUtil.dtLong);
                Integer orderCostTime = orderInfo.getCostTime();
                String pickUpShopSeq = orderInfo.getPickupStoreSeq();
                String returnShopSeq = orderInfo.getReturnStoreSeq();
                Integer orderVehicleModelSeq = orderInfo.getPayItem();
                Integer orderRentMethod = orderInfo.getRentMethod();
                /**
                 * 4.获取可用套餐
                 */
                GetEffectiveActivityCardInput cardInput = new GetEffectiveActivityCardInput(pkId, rentAmount, orderPickUpDate, orderCostTime, pickUpShopSeq, returnShopSeq, orderVehicleModelSeq, orderRentMethod);
                resultList = getEffectiveActivityCard(cardInput);
            }
        }
        return resultList;
    }

    @Override
    public CardActivityConfigDto queryActivityBuId(Long activityId) {

        MmpCardSalesActivity mmpCardSalesActivity = cardSalesActivityMapper.selectByPrimaryKey(activityId);
        if (mmpCardSalesActivity == null){
            return null;
        }
        CardActivityConfigDto cardDetailDto = new CardActivityConfigDto();
        BeanUtils.copyProperties(mmpCardSalesActivity,cardDetailDto);
        return cardDetailDto;
    }

    @Override
    public List<CanUseActivityCardInfoDto> getEffectiveActivityCard(GetEffectiveActivityCardInput input) {
        List<CanUseActivityCardInfoDto> resultList = new ArrayList<>();
        /**
         * 预处理：  取车时间、用车时长、用车区域、车型、产品线
         */
        memberCardInnerService.preProcessOrderCondition(input);

        /**
         * 1.获取用户年龄信息
         */
        Long pkId = input.getUserId();
        MemberWrapInfoDto memberWrapInfo = membershipWrapService.getMemberWrapInfoByPkId(pkId);
        Integer userAge = Optional.ofNullable(memberWrapInfo).map(MemberWrapInfoDto::getAge).orElse(0);
        /**
         * 2.取车时间、用车时长、用车区域、车型、产品线
         */
        Date orderPickUpDate = input.getOrderPickUpDate();
        Integer orderCostTime = input.getOrderCostTime();
        String pickUpShopSeq = ComUtils.getValidSeq(input.getPickUpShopSeq());
        String returnShopSeq = ComUtils.getValidSeq(input.getReturnShopSeq());
        Integer orderVehicleModelSeq = input.getVehicleModelSeq();
        Integer orderGoodsVehicleModelSeq = input.getGoodsModelId();
        Integer orderRentMethod = input.getRentMethod();
        BigDecimal rentAmount = input.getRentAmount();
        boolean isMdOrder = Constants.RENT_METHOD_MD.equals(orderRentMethod);

        //取车城市、还车城市
        Long returnCityId = input.getReturnCity();
        Long pickCityId = input.getPickUpCity();

        /**
         * 4.使用城市查询进行中的活动
         */
        List<CardSalesActivityCardModel> effectiveActivityList = cardSalesActivityMapper.getEffectiveActivityByOrder();
        if (CollectionUtils.isNotEmpty(effectiveActivityList)) {
            /**
             * 5.根据活动id，批量查询购买记录
             */
            Map<Long, List<MemberCardPurchaseRecordDto>> purchaseList = new HashMap<>();
            int studentCardFlag = 0;
            if (pkId != null) {
                List<Long> activityIdList = effectiveActivityList.stream().map(CardSalesActivityCardModel::getActivityId).collect(Collectors.toList());
                List<MemberCardPurchaseRecordDto> memberCardPurchaseRecord = cardTradeService.getMemberCardPurchaseRecord(pkId, activityIdList);
                purchaseList = getPurchaseMapOnly(memberCardPurchaseRecord);
                if (0 < userAge && userAge < 24) {
                    MmpUserTagDto mmpUserTagDto = memberShipService.queryUserTagByAuthId(memberWrapInfo.getAuthId());
                    String studentCardUrl = mmpUserTagDto.getStudentCardUrl();
                    if (StringUtils.isNotBlank(studentCardUrl)) {
                        studentCardFlag = 1;
                    }
                }
            }
            for (CardSalesActivityCardModel activityCardModel : effectiveActivityList) {
                //判断，如果是学生卡，年龄>=24，则不返回学生卡活动
                if (activityCardModel.getPurchaseType() == 1) {
                    if (userAge <=0 || userAge >= 24) {
                        continue;
                    }
                }
                /**
                 * 4.检查是否以达到购买上限
                 */
                if (purchaseList.containsKey(activityCardModel.getActivityId())) {
                    List<MemberCardPurchaseRecordDto> recordDtoList = purchaseList.get(activityCardModel.getActivityId());
                    int alreadyPurchaseCount = recordDtoList.stream().map(MemberCardPurchaseRecordDto::getQuantity).reduce(0, ComUtils::add);
                    Integer activityPersonPurchasesLimit = activityCardModel.getActivityPersonPurchasesLimit();
                    if (alreadyPurchaseCount >= activityPersonPurchasesLimit) {
                        continue;
                    }
                }

                /**
                 * 5.校验限制条件，订单是否能使用此会员卡
                 */
                //用车区域
                String cityLimit = activityCardModel.getCityLimit();
                if (StringUtils.isNotBlank(cityLimit)) {
                    List<Integer> cityIdList = ComUtils.turnToList(cityLimit, ",");
                    //TODO 待确认： 确认是取车还车均要满足用车条件？
                    if (pickCityId == null || returnCityId == null || !cityIdList.contains(pickCityId.intValue()) || !cityIdList.contains(returnCityId.intValue())) {
                    //if (returnCityId == null || !cityIdList.contains(pickCityId.intValue()) || !cityIdList.contains(returnCityId.intValue())) {
                        continue;
                    }
                }

                //新增： 若指定了取车门店，则校验可用门店限制(限制取车门店)
                String orderPickUpStore = ComUtils.getValidSeq(input.getPickUpStoreId());
                String storeId = activityCardModel.getStoreIds();
                if (StringUtils.isNotBlank(storeId) && StringUtils.isNotBlank(orderPickUpStore) && isMdOrder) {
                    List<Integer> storeIdList = ComUtils.turnToList(storeId, ",");
                    if (!storeIdList.contains(orderVehicleModelSeq)) {
                        continue;
                    }
                }
                //取车时间 星期
                String availableDaysOfWeek = activityCardModel.getAvailableDaysOfWeek();
                if (StringUtils.isNotBlank(availableDaysOfWeek)) {
                    Instant instant = orderPickUpDate.toInstant();
                    LocalDate now = instant.atZone(ZoneId.systemDefault()).toLocalDate();
                    int dayOfWeek = now.getDayOfWeek().getValue();
                    if (!availableDaysOfWeek.contains(String.valueOf(dayOfWeek))) {
                        continue;
                    }
                }
                //取车时间 时间段-->取消时段限制
                String pickUpStartTime = activityCardModel.getPickUpStartTime();
                String pickUpEndTime = activityCardModel.getPickUpEndTime();
//                if (StringUtils.isNotBlank(pickUpStartTime) && StringUtils.isNotBlank(pickUpEndTime)) {
//                    String orderPickUpDateTime = DateUtil.getFormatDate(orderPickUpDate, DateUtil.dtLong);
//                    String pickDateDay = orderPickUpDateTime.substring(0, 8);
//                    String pickStartTime = pickDateDay + pickUpStartTime;
//                    String pickEndTime = pickDateDay + pickUpEndTime;
//                    Date pickStartDate = DateUtil.getDateFromStr(pickStartTime, DateUtil.dtLong);
//                    Date pickEndDate = DateUtil.getDateFromStr(pickEndTime, DateUtil.dtLong);
//                    if (orderPickUpDate.before(pickStartDate) || orderPickUpDate.after(pickEndDate)) {
//                        continue;
//                    }
//                }
                //用车时长
                Integer durationLimit = activityCardModel.getDurationLimit();
                if (durationLimit > 0) {
                    if (orderCostTime < durationLimit) {
                        continue;
                    }
                }
                //可用车型
                String vehicleModelLimit = activityCardModel.getVehicleModelLimit();
                if (StringUtils.isNotBlank(vehicleModelLimit)) {
                    List<Integer> vehicleModelSeqList = ComUtils.turnToList(vehicleModelLimit, ",");
                    if (!vehicleModelSeqList.contains(orderVehicleModelSeq)) {
                        continue;
                    }
                }
                //处理商品车型校验
                String goodsVehicleModelLimit = activityCardModel.getGoodsModelId();
                if (StringUtils.isNotBlank(goodsVehicleModelLimit)) {
                    if(orderGoodsVehicleModelSeq != null) {
                        List<Integer> vehicleModelSeqList = ComUtils.turnToList(goodsVehicleModelLimit, ",");
                        if (!vehicleModelSeqList.contains(orderGoodsVehicleModelSeq)) {
                            continue;
                        }
                    }

                }
                //产品线大类校验
                String rentMethodGroup = activityCardModel.getRentMethodGroup();
                if (StringUtils.isNotBlank(rentMethodGroup)) {
                    List<Integer> rentMethodList = ComUtils.turnToList(rentMethodGroup, ",");
                    Integer orderRentMethodGroup = memberCardInnerService.getRentMethodGroup(orderRentMethod);
                    if(!rentMethodList.contains(orderRentMethodGroup)) {
                        continue;
                    }
                } else {
                    //未指定大类，则校验具体产品线
                    String rentMethod = activityCardModel.getRentMethod();
                    if (StringUtils.isNotBlank(rentMethod)) {
                        List<Integer> rentMethodList = ComUtils.turnToList(rentMethod, ",");
                        if (!rentMethodList.contains(orderRentMethod)) {
                            continue;
                        }
                    }
                }
                /**
                 * 6.计算折扣减免金额
                 */
                Integer discount = activityCardModel.getDiscount();
                BigDecimal discountRate = new BigDecimal(discount).divide(new BigDecimal(100));
                BigDecimal payAmount = rentAmount.multiply(discountRate).setScale(2, BigDecimal.ROUND_UP);
                BigDecimal deductionAmount = rentAmount.subtract(payAmount).setScale(2, BigDecimal.ROUND_UP);
                BigDecimal maxValue = activityCardModel.getMaxValue();
                if (maxValue.compareTo(BigDecimal.ZERO) > 0) {
                    if (deductionAmount.compareTo(maxValue) > 0) {
                        deductionAmount = maxValue;
                    }
                }
                //折扣上限
                BigDecimal totalDiscountAmount = activityCardModel.getTotalDiscountAmount();
                if (totalDiscountAmount != null &&totalDiscountAmount.compareTo(BigDecimal.ZERO) > 0) {
                    if (deductionAmount.compareTo(totalDiscountAmount) > 0) {
                        deductionAmount = totalDiscountAmount;
                    }
                }
                /**
                 * 7.组装返回数据
                 */
                EffectiveActivityCardDetailDto detailDto = formatActivityInfo(activityCardModel, null, null, null);
                CanUseActivityCardInfoDto infoDto = new CanUseActivityCardInfoDto();
                BeanUtils.copyProperties(detailDto, infoDto);
                infoDto.setDeductionAmount(deductionAmount);
                if (activityCardModel.getPurchaseType() == 1) {
                    infoDto.setStudentCardFlag(studentCardFlag);
                }
                resultList.add(infoDto);
            }
            Collections.sort(resultList);
        }
        log.info("getEffectiveActivityCard: req={}, result={}", JSON.toJSONString(input), JSON.toJSONString(resultList));
        return resultList;
    }

    /**
     * 返回值
     * @param activityCardModel
     * @param pkId
     * @param activityPurchaseList
     * @param remindActivityIdList
     * @return
     */
    private EffectiveActivityCardDetailDto formatActivityInfo(CardSalesActivityCardModel activityCardModel, Long pkId,
                                                              Map<Long, List<MemberCardPurchaseRecordDto>> activityPurchaseList,
                                                              List<Long> remindActivityIdList) {
        EffectiveActivityCardDetailDto infoDto = new EffectiveActivityCardDetailDto();
        BeanUtils.copyProperties(activityCardModel, infoDto);
        infoDto.setStartTime(DateUtil.getFormatDate(activityCardModel.getStartTime(), DateUtil.dtLong));
        infoDto.setEndTime(DateUtil.getFormatDate(activityCardModel.getEndTime(), DateUtil.dtLong));
        if (StringUtils.isNotBlank(activityCardModel.getBackUrl())){
            infoDto.setBackUrl(ConfigUtil.getConfigPropertiesStr("web_url") + activityCardModel.getBackUrl());
        }
        /**
         * 4.判断活动限制条件，并拼接限制条件
         */
        StringBuffer limitConditionBuffer = new StringBuffer();
        List<String> limitDetail = new ArrayList<>();
        //最高可抵扣金额
        BigDecimal maxValue = activityCardModel.getMaxValue();
        if (maxValue.compareTo(BigDecimal.ZERO) > 0) {
            String maxValueStr = maxValue.toString();
            if (maxValueStr.endsWith(".00")) {
                maxValueStr = maxValueStr.replace(".00", "");
            }
            limitConditionBuffer.append("最高可抵").append(maxValueStr).append("元/");
            limitDetail.add("单笔最高可抵：" + maxValueStr + "元");
        }
        //取车时间段
        String availableDaysOfWeek = activityCardModel.getAvailableDaysOfWeek();
        String pickUpStartTime = activityCardModel.getPickUpStartTime();
        String pickUpEndTime = activityCardModel.getPickUpEndTime();
        if (StringUtils.isNotBlank(pickUpStartTime) && StringUtils.isNotBlank(pickUpEndTime)
                && "000000".equals(pickUpStartTime) && "240000".equals(pickUpEndTime)) {
            pickUpStartTime = StringUtils.EMPTY;
            pickUpEndTime = StringUtils.EMPTY;
        }
        if (StringUtils.isNotBlank(availableDaysOfWeek)
                || StringUtils.isNotBlank(pickUpStartTime)
                || StringUtils.isNotBlank(pickUpEndTime)) {
            limitConditionBuffer.append("取车时段/");
            StringBuffer pickTimeLimitDesc = new StringBuffer("取车时段：限");
            if (StringUtils.isNotBlank(availableDaysOfWeek)) {
                StringBuffer weekLimit = new StringBuffer();
                String[] weekDescArray = new String[]{"周一、", "周二、", "周三、", "周四、", "周五、", "周六、", "周日"};
                String[] weekArray = availableDaysOfWeek.split(",");
                for (String s : weekArray) {
                    weekLimit.append(weekDescArray[Integer.valueOf(s) - 1]);
                }
                String weekDesc = ComUtils.formatEndChar(weekLimit.toString(),"、");
                pickTimeLimitDesc.append(weekDesc);
            }
            //不再显示时段限制
//            if (StringUtils.isNotBlank(pickUpStartTime)) {
//                pickTimeLimitDesc.append(DateUtil.getFormatDate(pickUpStartTime, DateUtil.timeShort, DateUtil.DATE_TYPE6));
//            }
//            if (StringUtils.isNotBlank(pickUpEndTime)) {
//                pickTimeLimitDesc.append("-").append(pickUpEndTime.substring(0,2) + ":" + pickUpEndTime.substring(2,4));
//            }
            limitDetail.add(pickTimeLimitDesc.toString());
        }
        //用车时长
        Integer durationLimit = activityCardModel.getDurationLimit();
        if (durationLimit > 0) {
            limitConditionBuffer.append("用车时长/");
            limitDetail.add("用车时长：" + DateUtil.formatTimeDesc(durationLimit) + "及以上");
        }
        //用车区域
        String cityLimit = activityCardModel.getCityLimit();
        if (StringUtils.isNotBlank(cityLimit)) {
            limitConditionBuffer.append("用车区域/");
            String[] split = cityLimit.split(",");
            StringBuffer cityLimitBuffer = new StringBuffer("用车区域：限");
            for (String s : split) {
                cityLimitBuffer.append(cityServ.getCitynameByCityid(Long.valueOf(s))).append("、");
            }
            String cityLimitDesc = ComUtils.formatEndChar(cityLimitBuffer.toString(),"、");
            limitDetail.add(cityLimitDesc);
        }
        //车型
        String vehicleModelLimit = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(activityCardModel.getGoodsModelId())){
            vehicleModelLimit = memberCardInnerService.getGoodsVehicleModelDesc(activityCardModel.getGoodsModelId());
        }
        if(StringUtils.isNotBlank(vehicleModelLimit)) {
            limitConditionBuffer.append("车型/");
            StringBuffer vehicleModelLimitBuffer = new StringBuffer("可用车型：限");
            vehicleModelLimitBuffer.append(vehicleModelLimit.replace(Constants.STR_COMMA_ZH, Constants.STR_SPLIT_ZH));
            limitDetail.add(vehicleModelLimitBuffer.toString());
        }
        //门店限制描述组织
        String storeLimit = memberCardInnerService.getStoreDesc(activityCardModel.getStoreIds());
        if (StringUtils.isNotBlank(storeLimit)) {
            limitConditionBuffer.append("门店/");
            StringBuffer storeLimitBuffer = new StringBuffer("可用取车门店：限");
            storeLimitBuffer.append(storeLimit.replace(Constants.STR_COMMA_ZH, Constants.STR_SPLIT_ZH));
            limitDetail.add(storeLimitBuffer.toString());
        }
        //产品线
        String rentMethodGroupLimit = memberCardInnerService.getRentMethodGroupDesc(activityCardModel.getRentMethodGroup());
        if (StringUtils.isNotBlank(rentMethodGroupLimit)) {
            limitConditionBuffer.append("产品线/");
            StringBuffer rentMethodLimitBuffer = new StringBuffer("产品线： 限");
            rentMethodLimitBuffer.append(rentMethodGroupLimit.replace(Constants.STR_COMMA_ZH, Constants.STR_SPLIT_ZH));
            limitDetail.add(rentMethodLimitBuffer.toString());
        }
        //累计总折扣上限
        BigDecimal totalDiscountAmount = activityCardModel.getTotalDiscountAmount();
        if (totalDiscountAmount != null && totalDiscountAmount.compareTo(BigDecimal.ZERO) > 0) {
            String totalDiscountAmountStr = totalDiscountAmount.toString();
            if (totalDiscountAmountStr.endsWith(".00")) {
                totalDiscountAmountStr = totalDiscountAmountStr.replace(".00", "");
            }
            limitConditionBuffer.append("总折扣上限").append(totalDiscountAmountStr).append("元/");
            limitDetail.add("该卡单位时间周期内累计总折扣上限：" + totalDiscountAmountStr + "元");
        }
        String limitCondition = ComUtils.formatEndChar(limitConditionBuffer.toString(),"/");
        infoDto.setLimitCondition(limitCondition);
        infoDto.setLimitDetail(limitDetail);

        //会员卡有效期说明
        infoDto.setCardDesc("生效后" + activityCardModel.getValidDays() + "天过期，可累加购买" + activityCardModel.getActivityPersonPurchasesLimit() + "张");

        /**
         * 5.已上架活动，判断活动是否已达购买上限
         */
        int canSaleNum = activityCardModel.getActivityPersonPurchasesLimit();
        if (pkId != null && activityCardModel.getActivityStatus() == 2) {
            int alreadyPurchaseCount = 0;
            if (activityPurchaseList != null && activityPurchaseList.containsKey(activityCardModel.getActivityId())) {
                List<MemberCardPurchaseRecordDto> recordDtoList = activityPurchaseList.get(activityCardModel.getActivityId());
                alreadyPurchaseCount = recordDtoList.stream().map(MemberCardPurchaseRecordDto::getQuantity).reduce(0, ComUtils::add);
                Integer activityPersonPurchasesLimit = activityCardModel.getActivityPersonPurchasesLimit();
                if (alreadyPurchaseCount >= activityPersonPurchasesLimit) {
                    infoDto.setPurchaseLimitFlag(1);
                }
                canSaleNum = activityPersonPurchasesLimit - alreadyPurchaseCount;
                if (canSaleNum < 0) {
                    canSaleNum = 0;
                }
            }
            Integer stock = activityCardModel.getStock();
            if (canSaleNum > stock) {
                canSaleNum = stock;
            }
        }
        if (infoDto.getPurchaseLimitFlag() == 0) {
            if (activityCardModel.getStock() == 0) {
                infoDto.setPurchaseLimitFlag(2);
            }
        }
        infoDto.setCanSaleNum(canSaleNum);

        /**
         * 6.若是预售活动，查询是否已设置开售提醒
         */
        if (pkId != null && activityCardModel.getActivityStatus() == 1) {
            if (remindActivityIdList != null && remindActivityIdList.contains(activityCardModel.getActivityId())) {
                infoDto.setSaleRemind(1);
            }
        }
        return infoDto;
    }

    public Map<Long, List<MemberCardPurchaseRecordDto>> getPurchaseMapOnly(List<MemberCardPurchaseRecordDto> purchaseRecordDtos){
        return purchaseRecordDtos.stream().filter(p->p.getIssueType() == 0).filter(p->Arrays.asList(0,1).contains(p.getPaymentStatus()))
                .collect(Collectors.groupingBy(MemberCardPurchaseRecordDto::getCardActivityId));
    }

}
