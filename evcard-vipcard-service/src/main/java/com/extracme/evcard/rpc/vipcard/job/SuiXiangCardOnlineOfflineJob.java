package com.extracme.evcard.rpc.vipcard.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardBaseMapper;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardConfigOperationLogMapper;
import com.extracme.evcard.rpc.vipcard.enums.IsDeleteEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardBaseCardStatusEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardConfigOperateTypeEnum;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardBase;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardBaseExample;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardConfigOperationLog;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 随享卡上架下架任务，每分钟触发一次
 *
 * <AUTHOR>
 * @date 2023/1/18
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "vipcard-rpc-suiXiangCardOnlineOfflineJob",
        cron = "0 * * * * ?", description = "随享卡上架下架任务", overwrite = true)
public class SuiXiangCardOnlineOfflineJob implements SimpleJob {

    @Resource
    private SuixiangCardBaseMapper suixiangCardBaseMapper;

    @Resource
    private SuixiangCardConfigOperationLogMapper suixiangCardConfigOperationLogMapper;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("随享卡上架下架任务begin");

        Date nowDate = new Date();

        // 自动上架
        processOnline(nowDate);

        // 自动下架
        processOffline(nowDate);

        log.info("随享卡上架下架任务end");
    }

    private void processOffline(Date nowDate) {
        // 查询需要下架的记录：到了售卖结束时间的 3-已上架 记录
        SuixiangCardBaseExample offExample = new SuixiangCardBaseExample();
        offExample.createCriteria()
                .andSaleEndTimeLessThanOrEqualTo(nowDate)
                .andCardStatusEqualTo(SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus())
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardBase> offList = suixiangCardBaseMapper.selectByExample(offExample);
        if (CollectionUtils.isNotEmpty(offList)) {
            // 根据offList，更新对应记录的状态为 4-已下架，带原状态 3-已上架 作为条件
            List<Long> offIdList = offList.stream().map(item -> item.getId()).collect(Collectors.toList());
            log.info("需要下架的记录数为{}，offIdList：{}", offIdList.size(), JSON.toJSONString(offIdList));

            SuixiangCardBase offTarget = new SuixiangCardBase();
            offTarget.setCardStatus(SuiXiangCardBaseCardStatusEnum.OFF_SHELF.getStatus());
            offTarget.setUpdateOperId(Constants.OPER_ID_SYSTEM);
            offTarget.setUpdateOperName(Constants.OPER_NAME_SYSTEM);
            SuixiangCardBaseExample offCond = new SuixiangCardBaseExample();
            offCond.createCriteria()
                    .andIdIn(offIdList)
                    .andCardStatusEqualTo(SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus())
                    .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
            int ret = suixiangCardBaseMapper.updateByExampleSelective(offTarget, offCond);
            log.info("需要下架的更新成功的记录数为{}", ret);

            // 记录操作日志
            List<SuixiangCardConfigOperationLog> logList = new ArrayList<>();
            for (Long cardBaseId : offIdList) {
                SuixiangCardConfigOperationLog operationLog = new SuixiangCardConfigOperationLog();
                operationLog.setCardBaseId(cardBaseId);
                operationLog.setOperateType(SuiXiangCardConfigOperateTypeEnum.OFFLINE.getOperateType());
                operationLog.setOperationContent("自动下架随享卡");
                operationLog.setCreateOperId(Constants.OPER_ID_SYSTEM);
                operationLog.setCreateOperName(Constants.OPER_NAME_SYSTEM);
                logList.add(operationLog);
            }
            suixiangCardConfigOperationLogMapper.batchInsert(logList);
        } else {
            log.info("没有需要下架的记录");
        }

    }

    private void processOnline(Date nowDate) {
        List<SuixiangCardBase> upList = new ArrayList<>();

        // 查询需要上架的记录1：有预告时间，且到了预告时间的 2-待上架 记录
        SuixiangCardBaseExample upExample1 = new SuixiangCardBaseExample();
        upExample1.createCriteria()
                .andAdvanceNoticeTimeLessThanOrEqualTo(nowDate)
                .andCardStatusEqualTo(SuiXiangCardBaseCardStatusEnum.TO_BE_PUT_ON_SHELF.getStatus())
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardBase> upList1 = suixiangCardBaseMapper.selectByExample(upExample1);
        if (CollectionUtils.isNotEmpty(upList1)) {
            upList.addAll(upList1);
        }

        // 查询需要上架的记录2：没有预告时间，且到了售卖开始时间的 2-待上架 记录
        SuixiangCardBaseExample upExample2 = new SuixiangCardBaseExample();
        upExample2.createCriteria()
                .andAdvanceNoticeTimeIsNull()
                .andSaleStartTimeLessThanOrEqualTo(nowDate)
                .andCardStatusEqualTo(SuiXiangCardBaseCardStatusEnum.TO_BE_PUT_ON_SHELF.getStatus())
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardBase> upList2 = suixiangCardBaseMapper.selectByExample(upExample2);
        if (CollectionUtils.isNotEmpty(upList2)) {
            upList.addAll(upList2);
        }

        if (CollectionUtils.isNotEmpty(upList)) {
            // 根据upList，更新对应记录的状态为 3-已上架，带原状态 2-待上架 作为条件
            List<Long> upIdList = upList.stream().map(item -> item.getId()).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
            log.info("需要上架的记录数为{}，upIdList：{}", upIdList.size(), JSON.toJSONString(upIdList));

            SuixiangCardBase upTarget = new SuixiangCardBase();
            upTarget.setCardStatus(SuiXiangCardBaseCardStatusEnum.EFFECTIVE.getStatus());
            upTarget.setUpdateOperId(Constants.OPER_ID_SYSTEM);
            upTarget.setUpdateOperName(Constants.OPER_NAME_SYSTEM);
            SuixiangCardBaseExample upCond = new SuixiangCardBaseExample();
            upCond.createCriteria()
                    .andIdIn(upIdList)
                    .andCardStatusEqualTo(SuiXiangCardBaseCardStatusEnum.TO_BE_PUT_ON_SHELF.getStatus())
                    .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
            int ret = suixiangCardBaseMapper.updateByExampleSelective(upTarget, upCond);
            log.info("需要上架的更新成功的记录数为{}", ret);

            // 记录操作日志
            List<SuixiangCardConfigOperationLog> logList = new ArrayList<>();
            for (Long cardBaseId : upIdList) {
                SuixiangCardConfigOperationLog operationLog = new SuixiangCardConfigOperationLog();
                operationLog.setCardBaseId(cardBaseId);
                operationLog.setOperateType(SuiXiangCardConfigOperateTypeEnum.ONLINE.getOperateType());
                operationLog.setOperationContent("自动上架随享卡（状态：待上架->已上架）");
                operationLog.setCreateOperId(Constants.OPER_ID_SYSTEM);
                operationLog.setCreateOperName(Constants.OPER_NAME_SYSTEM);
                logList.add(operationLog);
            }
            suixiangCardConfigOperationLogMapper.batchInsert(logList);
        } else {
            log.info("没有需要上架的记录");
        }
    }
}
