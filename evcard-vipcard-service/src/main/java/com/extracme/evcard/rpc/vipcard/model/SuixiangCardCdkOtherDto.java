package com.extracme.evcard.rpc.vipcard.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SuixiangCardCdkOtherDto implements Serializable {

    /**
     *  suixiang_card_cdk 主键
     */
    private Long cdkId;

    /**
     *  suixiang_card_cdk_config_detail 主键
     */
    private Long cdkConfigDetailId;

    /**
     *  suixiang_card_cdk_config 主键
     */
    private Long cdkConfigId;

    /**
     * 用途
     */
    private Integer purpose;

    /**
     * 第三方售价
     */
    private String thirdSalesPrice;
}
