package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdk;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkExample;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkExportDto;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardCdkOtherDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SuixiangCardCdkMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    int countByExample(SuixiangCardCdkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    int insert(SuixiangCardCdk record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    int insertSelective(SuixiangCardCdk record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    List<SuixiangCardCdk> selectByExample(SuixiangCardCdkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    SuixiangCardCdk selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    int updateByExampleSelective(@Param("record") SuixiangCardCdk record, @Param("example") SuixiangCardCdkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    int updateByExample(@Param("record") SuixiangCardCdk record, @Param("example") SuixiangCardCdkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    int updateByPrimaryKeySelective(SuixiangCardCdk record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_cdk
     *
     * @mbggenerated Mon Nov 18 10:20:00 CST 2024
     */
    int updateByPrimaryKey(SuixiangCardCdk record);


    /**
     * 通过ckd配置表id 查询cdk记录集合
     *
     * @param suixiangCardCdkConfigId
     * @return
     */
    List<SuixiangCardCdkExportDto> queryExportListBySuixiangCardCdkConfigId(@Param("suixiangCardCdkConfigId") Long suixiangCardCdkConfigId, @Param("page") Page page);

    String getLatestKey();

    int batchSaveCdk(List<SuixiangCardCdk> records);

    /**
     * 通过cdkId 查询cdk配置详情
     * @param cdkId
     * @return
     */
    SuixiangCardCdkOtherDto getCkdOtherDtoByCkdId(Long cdkId);

}