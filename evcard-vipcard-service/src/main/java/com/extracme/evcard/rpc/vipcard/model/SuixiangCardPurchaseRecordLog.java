package com.extracme.evcard.rpc.vipcard.model;

import java.util.Date;

public class SuixiangCardPurchaseRecordLog {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.purchase_id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private Long purchaseId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.operation_type
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private Integer operationType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.pay_order_no
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private String payOrderNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.content
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private String content;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.misc_desc
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.create_time
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.update_time
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_purchase_record_log.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    private Integer isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.id
     *
     * @return the value of suixiang_card_purchase_record_log.id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.id
     *
     * @param id the value for suixiang_card_purchase_record_log.id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.purchase_id
     *
     * @return the value of suixiang_card_purchase_record_log.purchase_id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public Long getPurchaseId() {
        return purchaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.purchase_id
     *
     * @param purchaseId the value for suixiang_card_purchase_record_log.purchase_id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setPurchaseId(Long purchaseId) {
        this.purchaseId = purchaseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.operation_type
     *
     * @return the value of suixiang_card_purchase_record_log.operation_type
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public Integer getOperationType() {
        return operationType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.operation_type
     *
     * @param operationType the value for suixiang_card_purchase_record_log.operation_type
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.pay_order_no
     *
     * @return the value of suixiang_card_purchase_record_log.pay_order_no
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public String getPayOrderNo() {
        return payOrderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.pay_order_no
     *
     * @param payOrderNo the value for suixiang_card_purchase_record_log.pay_order_no
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.content
     *
     * @return the value of suixiang_card_purchase_record_log.content
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.content
     *
     * @param content the value for suixiang_card_purchase_record_log.content
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.misc_desc
     *
     * @return the value of suixiang_card_purchase_record_log.misc_desc
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.misc_desc
     *
     * @param miscDesc the value for suixiang_card_purchase_record_log.misc_desc
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.create_time
     *
     * @return the value of suixiang_card_purchase_record_log.create_time
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.create_time
     *
     * @param createTime the value for suixiang_card_purchase_record_log.create_time
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.create_oper_id
     *
     * @return the value of suixiang_card_purchase_record_log.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.create_oper_id
     *
     * @param createOperId the value for suixiang_card_purchase_record_log.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.create_oper_name
     *
     * @return the value of suixiang_card_purchase_record_log.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.create_oper_name
     *
     * @param createOperName the value for suixiang_card_purchase_record_log.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.update_time
     *
     * @return the value of suixiang_card_purchase_record_log.update_time
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.update_time
     *
     * @param updateTime the value for suixiang_card_purchase_record_log.update_time
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.update_oper_id
     *
     * @return the value of suixiang_card_purchase_record_log.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_purchase_record_log.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.update_oper_name
     *
     * @return the value of suixiang_card_purchase_record_log.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_purchase_record_log.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_purchase_record_log.is_deleted
     *
     * @return the value of suixiang_card_purchase_record_log.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_purchase_record_log.is_deleted
     *
     * @param isDeleted the value for suixiang_card_purchase_record_log.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:37:25 CST 2023
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}