package com.extracme.evcard.rpc.vipcard.service.inner;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.extracme.evcard.rpc.vipcard.dao.CityMapper;
import com.extracme.evcard.rpc.vipcard.model.City;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Slf4j
public class CityServ {
	
	@Resource
    CityMapper cityMapper;
	
	private static final ConcurrentHashMap<Long, String> citys = new ConcurrentHashMap<Long, String>();
	private static final ConcurrentHashMap<String, Long> cityIdMap = new ConcurrentHashMap<String, Long>();

	public String getCitynameByCityid(Long cityId) {
		String cityname = citys.get(cityId);
		if(StringUtils.isNotBlank(cityname)) {
			return cityname;
		}
		City city = cityMapper.selectByCityid(cityId);
		if(city == null) {
			return null;
		}
		citys.put(city.getCityid(), city.getCity());
		return city.getCity();
	}

	/**
	 * 根据城市名称获取城市id
	 * @param cityName
	 * @return
	 */
	public Long getCityIdByCityName(String cityName) {
		Long cityId = cityIdMap.get(cityName);
		if(cityId != null) {
			return cityId;
		}
		City city = cityMapper.selectByCityName(cityName);
		if(city == null) {
			return null;
		}
		cityIdMap.put(city.getCity(), city.getCityid());
		return city.getCityid();
	}


	private Cache<Long, String> cityNameCache = CacheBuilder.newBuilder()
			.expireAfterWrite(1 * DateUtils.MILLIS_PER_DAY, TimeUnit.MILLISECONDS).maximumSize(500).build();

	public Map<Long, String> getCityNames(List<Long> cityIds) {
		if(CollectionUtils.isEmpty(cityIds)) {
			return null;
		}
		Map<Long, String> map = new HashMap<>();
		Set<Long> cityIdSets = new HashSet<>();
		for(Long cityId : cityIds) {
			String cityName = cityNameCache.getIfPresent(cityId);
			if(cityName != null) {
				map.put(cityId, cityName);
			}else {
				cityIdSets.add(cityId);
			}

		}
		if(!CollectionUtils.isEmpty(cityIdSets)) {
			try {
				List<City> list = cityMapper.selectByCityIds(cityIdSets);
				if (!CollectionUtils.isEmpty(list)) {
					for (City city : list) {
						map.put(city.getCityid(), city.getCity());
						cityNameCache.put(city.getCityid(), city.getCity());
					}
				}
			}catch (Exception ex) {
				log.error("查询城市名称失败,", ex);
			}
		}
		return map;
	}

}
