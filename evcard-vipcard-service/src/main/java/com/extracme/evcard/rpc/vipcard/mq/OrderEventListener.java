package com.extracme.evcard.rpc.vipcard.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.order.ConsumeOrderContext;
import com.aliyun.openservices.ons.api.order.MessageOrderListener;
import com.aliyun.openservices.ons.api.order.OrderAction;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.mq.bean.md.MdCardPay;
import com.extracme.evcard.mq.bean.md.MdEventEnum;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.messagepush.service.ISensorsdataService;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardPriceMapper;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardPurchaseRecordMapper;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardUseMapper;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardInfoDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.pay.PaySuiXiangCardPurchaseDto;
import com.extracme.evcard.rpc.vipcard.enums.IsDeleteEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardStatusEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuiXiangCardUseLogOperationTypeEnum;
import com.extracme.evcard.rpc.vipcard.enums.SuixiangCardRefundSuccessEnum;
import com.extracme.evcard.rpc.vipcard.manager.SuixiangCardBaseManager;
import com.extracme.evcard.rpc.vipcard.model.*;
import com.extracme.evcard.rpc.vipcard.service.ISuiXiangCardTradeService;
import com.extracme.evcard.rpc.vipcard.service.SuixiangCardRefundService;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单普通事件
 */
@Slf4j
@Service
public class OrderEventListener implements MessageOrderListener {

    @Resource
    private SuixiangCardPurchaseRecordMapper suixiangCardPurchaseRecordMapper;

    @Resource
    private SuixiangCardUseMapper suixiangCardUseMapper;

    @Resource
    private SuixiangCardBaseManager suixiangCardBaseManager;

    @Resource
    private ISuiXiangCardTradeService suiXiangCardTradeService;

    @Resource
    private ISensorsdataService sensorsdataService;

    @Resource
    private IMemberShipService memberShipService;

    @Resource
    private SuixiangCardPriceMapper suixiangCardPriceMapper;

    @Resource
    private SuixiangCardRefundService suixiangCardRefundService;

    @Override
    public OrderAction consume(Message message, ConsumeOrderContext context) {
        log.info("接收消息！message={},context={}", JSON.toJSONString(message), JSON.toJSONString(context));
        try {
            String tag = message.getTag();
            if (MdEventEnum.CARD_REFUND_SUCCESS.getTag().equals(tag)) {
                processCardRefund(message.getBody());
            } else if (MdEventEnum.CARD_PAY.getTag().equals(tag)) {
                paySuccessCallBack(message.getBody());
            }
        } catch (Exception e) {
            log.error("消费消息异常！message={},context={}",
                    JSON.toJSONString(message), JSON.toJSONString(context), e);
        }
        return OrderAction.Success;
    }


    /**
     * 处理 随享卡支付成功事件 消息
     *
     * @param message
     */
    public void paySuccessCallBack(byte[] message) {
        MdCardPay mdCardPay = JSON.parseObject(message, MdCardPay.class);
        if (mdCardPay == null) {
            log.error("支付随享卡成功事件中，返回应答为空");
            return;
        }

        String payOrderNo = mdCardPay.getPayOrderNo();
        if (StringUtils.isBlank(payOrderNo)) {
            log.error("支付随享卡成功事件中，未找到对应的支付订单号: {}", JSON.toJSONString(mdCardPay));
            return;
        }

        // 根据payOrderNo查询suixiang_card_purchase_record表
        SuixiangCardPurchaseRecordExample recordExample = new SuixiangCardPurchaseRecordExample();
        recordExample.createCriteria()
                .andPayOrderNoEqualTo(payOrderNo)
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardPurchaseRecord> recordList = suixiangCardPurchaseRecordMapper.selectByExample(recordExample);
        if (CollectionUtils.isEmpty(recordList)) {
            log.error("支付随享卡成功事件中，根据payOrderNo[{}]查询不到suixiang_card_purchase_record表记录！", payOrderNo);
            return;
        }

        Long purchaseId = recordList.get(0).getId();
        try {
            // 发卡
            PaySuiXiangCardPurchaseDto dto = suiXiangCardTradeService.paySuiXiangCardPurchaseCallBack(purchaseId);

            try {
                // 埋点
                List<String> useCardId = dto.getUseCardId();
                if (purchaseId != null) {
//                    SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(useCardId);
                    SuixiangCardPurchaseRecord suixiangCardPurchaseRecord = suixiangCardPurchaseRecordMapper.selectByPrimaryKey(purchaseId);
                    if (suixiangCardPurchaseRecord != null) {
                        // 查询随享卡
                        SuixiangCardInfoDto suixiangCardInfoDto = suixiangCardPriceMapper.selectInfoByPriceId(suixiangCardPurchaseRecord.getCardPriceId());
                        if (suixiangCardInfoDto != null) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("order_id", useCardId + "");
                            map.put("is_success", true);
                            map.put("type", "随享卡");
                            // 支付方式
                            if (mdCardPay.getPaymentType() == 1) {
                                map.put("payment_type", "支付宝");
                            } else if (mdCardPay.getPaymentType() == 2) {
                                map.put("payment_type", "微信");
                            } else if (mdCardPay.getPaymentType() == 3) {
                                map.put("payment_type", "银联");
                            }

                            // 实际支付金额
                            map.put("payment_amount", suixiangCardInfoDto.getSalesPrice().intValue());
                            map.put("is_pw_free", false);
                            // 随享订单优惠价
                            map.put("order_amout", suixiangCardInfoDto.getSalesPrice().intValue());
                            map.put("total_payment", suixiangCardInfoDto.getSalesPrice().intValue());

                            map.put("e_amount", 0);
                            map.put("operation_model", "门店");
                            // 查询用户
                            MembershipBasicInfo membershipBasicInfo = memberShipService.getUserBasicInfoByPkId(suixiangCardPurchaseRecord.getUserId());
                            if (membershipBasicInfo != null) {
                                sensorsdataService.track(membershipBasicInfo.getAuthId(), true, "pay_complete", map);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("支付完成 pay_complete 埋点异常，dto[{}]", JSON.toJSONString(dto), e);
            }
        } catch (BusinessException e) {
            log.error("支付随享卡成功事件中，paySuiXiangCardPurchaseCallBack异常.mdCardPay[{}]: e[{}]", JSON.toJSONString(mdCardPay), e);
        }
    }

    public void processCardRefund(byte[] message){
        MdCardPay cardRefundDto = JSON.parseObject(message, MdCardPay.class);
        if (cardRefundDto == null) {
            log.error("退卡费成功事件中，返回应答为空");
            return;
        }

        String payOrderNo = cardRefundDto.getPayOrderNo();
        if (StringUtils.isBlank(payOrderNo)) {
            log.error("退卡费成功事件中，未找到对应的支付订单号: {}", JSON.toJSONString(cardRefundDto));
            return;
        }

        Long cardUseId = null;
        int type = 0;
        try {
            String sxkUseId = cardRefundDto.getSxkUseId();
            // 兼容旧前缀
            if (sxkUseId.startsWith(Constants.SXK_USEID_PREFIX)) {
                String stringSxkUseId = sxkUseId.substring(Constants.SXK_USEID_PREFIX.length());
                cardUseId = Long.valueOf(stringSxkUseId);
            }
            else if (sxkUseId.startsWith("TYPE_1_" + Constants.SXK_USEID_PREFIX)) {
                type = 1;
                String stringSxkUseId = sxkUseId.substring(("TYPE_1_" + Constants.SXK_USEID_PREFIX).length());
                cardUseId = Long.valueOf(stringSxkUseId);
            }
            else if (sxkUseId.startsWith("TYPE_2_" + Constants.SXK_USEID_PREFIX)) {
                type = 2;
                String stringSxkUseId = sxkUseId.substring(("TYPE_2_" + Constants.SXK_USEID_PREFIX).length());
                cardUseId = Long.valueOf(stringSxkUseId);
            }
            else {
                log.error("退卡费成功事件中，获取cardUseId失败: {}", JSON.toJSONString(cardRefundDto));
                return;
            }
        } catch (Exception e) {
            log.error("退卡费成功事件中，获取cardUseId失败: {}", JSON.toJSONString(cardRefundDto));
            return;
        }

        // 根据payOrderNo查询suixiang_card_purchase_record表
        SuixiangCardPurchaseRecordExample recordExample = new SuixiangCardPurchaseRecordExample();
        recordExample.createCriteria()
                .andPayOrderNoEqualTo(payOrderNo)
                .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        List<SuixiangCardPurchaseRecord> recordList = suixiangCardPurchaseRecordMapper.selectByExample(recordExample);
        if (CollectionUtils.isEmpty(recordList)) {
            log.error("根据payOrderNo[{}]查询不到suixiang_card_purchase_record表记录！", payOrderNo);
            return;
        }

        // 根据cardUseId查询suixiang_card_use表
        SuixiangCardUse use = suixiangCardUseMapper.selectByPrimaryKey(cardUseId);
        if (use == null) {
            log.error("根据cardUseId[{}]查询不到suixiang_card_use表！payOrderNo[{}]", cardUseId, payOrderNo);
            return;
        }

        // 接受到退卡成功mq后，直接处理 更新状态 变成 4-已退卡，记录log
        try {
            suixiangCardBaseManager.updateCardUseAndLog(use, null, SuiXiangCardStatusEnum.RETURN,
                    SuiXiangCardUseLogOperationTypeEnum.RETURNED);
        } catch (Exception e) {
            log.error("更新suixiang_card_use表状态为[4-已退卡]和记录log失败", e);
            return;
        }
        suixiangCardRefundService.saveSuixiangCardRefundLog(cardUseId, SuixiangCardRefundSuccessEnum.REFUND_SUCCESS.getStatus(), type);
    }

}
