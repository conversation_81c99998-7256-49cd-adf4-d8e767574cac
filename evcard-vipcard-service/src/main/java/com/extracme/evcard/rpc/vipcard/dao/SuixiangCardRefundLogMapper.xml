<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.SuixiangCardRefundLogMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_base_id" jdbcType="BIGINT" property="cardBaseId" />
    <result column="card_use_id" jdbcType="BIGINT" property="cardUseId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="order_seq" jdbcType="VARCHAR" property="orderSeq" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="success" jdbcType="INTEGER" property="success" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    id, card_base_id, card_use_id, user_id, mobile_phone, username, order_seq, refund_amount, 
    refund_time, type, success, create_time, create_oper_id, create_oper_name, update_time, 
    update_oper_id, update_oper_name, remark, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from suixiang_card_refund_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from suixiang_card_refund_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    delete from suixiang_card_refund_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    insert into suixiang_card_refund_log (id, card_base_id, card_use_id, 
      user_id, mobile_phone, username, 
      order_seq, refund_amount, refund_time, 
      type, success, create_time, 
      create_oper_id, create_oper_name, update_time, 
      update_oper_id, update_oper_name, remark, 
      is_deleted)
    values (#{id,jdbcType=BIGINT}, #{cardBaseId,jdbcType=BIGINT}, #{cardUseId,jdbcType=BIGINT}, 
      #{userId,jdbcType=BIGINT}, #{mobilePhone,jdbcType=VARCHAR}, #{username,jdbcType=VARCHAR}, 
      #{orderSeq,jdbcType=VARCHAR}, #{refundAmount,jdbcType=DECIMAL}, #{refundTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=INTEGER}, #{success,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    insert into suixiang_card_refund_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cardBaseId != null">
        card_base_id,
      </if>
      <if test="cardUseId != null">
        card_use_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="mobilePhone != null">
        mobile_phone,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="orderSeq != null">
        order_seq,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="success != null">
        success,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cardBaseId != null">
        #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardUseId != null">
        #{cardUseId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="mobilePhone != null">
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null">
        #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="success != null">
        #{success,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLogExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    select count(*) from suixiang_card_refund_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    update suixiang_card_refund_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardBaseId != null">
        card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.cardUseId != null">
        card_use_id = #{record.cardUseId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.mobilePhone != null">
        mobile_phone = #{record.mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSeq != null">
        order_seq = #{record.orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="record.refundAmount != null">
        refund_amount = #{record.refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.refundTime != null">
        refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.success != null">
        success = #{record.success,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null">
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null">
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null">
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null">
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    update suixiang_card_refund_log
    set id = #{record.id,jdbcType=BIGINT},
      card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      card_use_id = #{record.cardUseId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      mobile_phone = #{record.mobilePhone,jdbcType=VARCHAR},
      username = #{record.username,jdbcType=VARCHAR},
      order_seq = #{record.orderSeq,jdbcType=VARCHAR},
      refund_amount = #{record.refundAmount,jdbcType=DECIMAL},
      refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      type = #{record.type,jdbcType=INTEGER},
      success = #{record.success,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    update suixiang_card_refund_log
    <set>
      <if test="cardBaseId != null">
        card_base_id = #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardUseId != null">
        card_use_id = #{cardUseId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="mobilePhone != null">
        mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null">
        order_seq = #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="success != null">
        success = #{success,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 17 14:38:43 CST 2024.
    -->
    update suixiang_card_refund_log
    set card_base_id = #{cardBaseId,jdbcType=BIGINT},
      card_use_id = #{cardUseId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      username = #{username,jdbcType=VARCHAR},
      order_seq = #{orderSeq,jdbcType=VARCHAR},
      refund_amount = #{refundAmount,jdbcType=DECIMAL},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=INTEGER},
      success = #{success,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLog" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from suixiang_card_refund_log
    where is_deleted = 0
    <if test="cardBaseId != null and cardBaseId != ''">
      and card_base_id = #{cardBaseId,jdbcType=BIGINT},
    </if>
    <if test="cardUseId != null and cardUseId != ''">
      and card_use_id = #{cardUseId,jdbcType=BIGINT},
    </if>
    <if test="userId != null and userId != ''">
      and user_id = #{userId,jdbcType=BIGINT},
    </if>
    <if test="mobilePhone != null and mobilePhone != ''">
      and mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
    </if>
    <if test="username != null and username != ''">
      and username = #{username,jdbcType=VARCHAR},
    </if>
    <if test="orderSeq != null and orderSeq != ''">
      and order_seq = #{orderSeq,jdbcType=VARCHAR},
    </if>
    <if test="refundAmount != null and refundAmount != ''">
      and refund_amount = #{refundAmount,jdbcType=DECIMAL},
    </if>
    <if test="refundTime != null and refundTime != ''">
      and refund_time = #{refundTime,jdbcType=TIMESTAMP},
    </if>
    <if test="type != null and type != ''">
      and type = #{type,jdbcType=INTEGER},
    </if>
    <if test="success != null and success != ''">
      and success = #{success,jdbcType=INTEGER},
    </if>
    <if test="createTime != null and createTime != ''">
      and create_time = #{createTime,jdbcType=TIMESTAMP},
    </if>
    <if test="createOperId != null and createOperId != ''">
      and create_oper_id = #{createOperId,jdbcType=INTEGER},
    </if>
    <if test="createOperName != null and createOperName != ''">
      and create_oper_name = #{createOperName,jdbcType=VARCHAR},
    </if>
    <if test="updateTime != null and updateTime != ''">
      and update_time = #{updateTime,jdbcType=TIMESTAMP},
    </if>
    <if test="updateOperId != null and updateOperId != ''">
      and update_oper_id = #{updateOperId,jdbcType=INTEGER},
    </if>
    <if test="updateOperName != null and updateOperName != ''">
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
    </if>
    <if test="remark != null and remark != ''">
      remark = #{remark,jdbcType=VARCHAR},
    </if>
  </select>
</mapper>