package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuiXiangCardRentDaysMinDto;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardRentDays;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardRentDaysExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardRentDaysMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    int countByExample(SuixiangCardRentDaysExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    int insert(SuixiangCardRentDays record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    int insertSelective(SuixiangCardRentDays record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    List<SuixiangCardRentDays> selectByExampleWithBLOBs(SuixiangCardRentDaysExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    List<SuixiangCardRentDays> selectByExample(SuixiangCardRentDaysExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    SuixiangCardRentDays selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    int updateByExampleSelective(@Param("record") SuixiangCardRentDays record, @Param("example") SuixiangCardRentDaysExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    int updateByExampleWithBLOBs(@Param("record") SuixiangCardRentDays record, @Param("example") SuixiangCardRentDaysExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    int updateByExample(@Param("record") SuixiangCardRentDays record, @Param("example") SuixiangCardRentDaysExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    int updateByPrimaryKeySelective(SuixiangCardRentDays record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    int updateByPrimaryKeyWithBLOBs(SuixiangCardRentDays record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_rent_days
     *
     * @mbggenerated Wed Jan 11 20:39:23 CST 2023
     */
    int updateByPrimaryKey(SuixiangCardRentDays record);

    List<SuiXiangCardRentDaysMinDto> selectMinRentDaysGroupByCardBaseId(@Param("cardBaseIdList")List<Long> cardBaseIdList);
}