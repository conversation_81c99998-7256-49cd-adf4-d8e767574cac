package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.MemberWrapInfoDto;
import com.extracme.evcard.membership.core.service.IMembershipWrapService;
import com.extracme.evcard.redis.JedisLock;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardPurchaseRecordMapper;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardRefundLogMapper;
import com.extracme.evcard.rpc.vipcard.dao.SuixiangCardUseMapper;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;
import com.extracme.evcard.rpc.vipcard.enums.*;
import com.extracme.evcard.rpc.vipcard.manager.SuixiangCardBaseManager;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecord;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLog;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardRefundLogExample;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUse;
import com.extracme.evcard.rpc.vipcard.rest.MdRestClient;
import com.extracme.evcard.rpc.vipcard.rest.entity.ReturnCardFeeReq;
import com.extracme.evcard.rpc.vipcard.rest.entity.ReturnCardFeeRes;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
@Slf4j
public class SuixiangCardRefundService implements ISuixiangCardRefundService {

    @Autowired
    private SuixiangCardPurchaseRecordMapper suixiangCardPurchaseRecordMapper;

    @Autowired
    private SuixiangCardRefundLogMapper suixiangCardRefundLogMapper;

    @Autowired
    private SuixiangCardUseMapper suixiangCardUseMapper;

    @Autowired
    private SuixiangCardBaseManager suixiangCardBaseManager;

    @Autowired
    private IMembershipWrapService membershipWrapService;

    @Autowired
    private MdRestClient mdRestClient;

    @Override
    public QuerySuixiangCardRefundLogOutput querySuixiangCardRefundLog(QuerySuixiangCardRefundLogInput input) {
        BaseResponse baseResponse = new BaseResponse();
        QuerySuixiangCardRefundLogOutput output = new QuerySuixiangCardRefundLogOutput();
        output.setBaseResponse(baseResponse);

        if (StringUtils.isNotEmpty(input.getRefundStartTime()) && !DateUtil.isValidDateString(input.getRefundStartTime(), DateUtil.DATE_TYPE1)) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("退款开始日期格式非法，正确格式为 yyyy-MM-dd");
            return output;
        }
        if (StringUtils.isNotEmpty(input.getRefundEndTime()) && !DateUtil.isValidDateString(input.getRefundEndTime(), DateUtil.DATE_TYPE1)) {
            baseResponse.setCode(-1);
            baseResponse.setMessage("退款结束日期格式非法，正确格式为 yyyy-MM-dd");
            return output;
        }

        SuixiangCardRefundLogExample suixiangCardRefundLogExample = new SuixiangCardRefundLogExample();
        SuixiangCardRefundLogExample.Criteria criteria = suixiangCardRefundLogExample.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
        if (input.getCardBaseId() != null) {
            criteria.andCardBaseIdEqualTo(input.getCardBaseId());
        }
        if (input.getCardUseId() != null) {
            criteria.andCardUseIdEqualTo(input.getCardUseId());
        }
        if (StringUtils.isNotEmpty(input.getMobilePhone())) {
            criteria.andMobilePhoneEqualTo(input.getMobilePhone());
        }
        if (StringUtils.isNotEmpty(input.getOrderSeq())) {
            criteria.andOrderSeqEqualTo(input.getOrderSeq());
        }
        if (StringUtils.isNotEmpty(input.getRefundStartTime())) {
            Date refundStartTime = DateUtil.getDateFromStr(input.getRefundStartTime() + " 00:00:00", DateUtil.simple);
            criteria.andRefundTimeGreaterThanOrEqualTo(refundStartTime);
        }
        if (StringUtils.isNotEmpty(input.getRefundEndTime())) {
            Date refundEndTime = DateUtil.getDateFromStr(input.getRefundEndTime() + " 23:59:59", DateUtil.simple);
            criteria.andRefundTimeLessThanOrEqualTo(refundEndTime);
        }
        if (input.getType() != null && input.getType() != 0) {
            criteria.andTypeEqualTo(input.getType());
        }
        if (input.getSuccess() != null && input.getSuccess() != 0) {
            criteria.andSuccessEqualTo(input.getSuccess());
        }
        else {
            List<Integer> successList = new ArrayList<>();
            successList.add(1);
            successList.add(2);
            criteria.andSuccessIn(successList);
        }

        try (Page<SuixiangCardRefundLog> page = PageHelper.startPage(input.getPageNum(), input.getPageSize()).doSelectPage(() -> suixiangCardRefundLogMapper.selectByExample(suixiangCardRefundLogExample))) {
            List<SuixiangCardRefundLogDto> suixiangCardRefundLogDtoList = new ArrayList<>();
            for (SuixiangCardRefundLog suixiangCardRefundLog : page.getResult()) {
                SuixiangCardRefundLogDto suixiangCardRefundLogDto = new SuixiangCardRefundLogDto();
                BeanUtils.copyProperties(suixiangCardRefundLog, suixiangCardRefundLogDto);
                suixiangCardRefundLogDto.setRefundTime(DateUtil.dateToString(suixiangCardRefundLog.getRefundTime(), DateUtil.simple));
                suixiangCardRefundLogDto.setCreateTime(DateUtil.dateToString(suixiangCardRefundLog.getCreateTime(), DateUtil.simple));
                suixiangCardRefundLogDto.setUpdateTime(DateUtil.dateToString(suixiangCardRefundLog.getUpdateTime(), DateUtil.simple));
                suixiangCardRefundLogDtoList.add(suixiangCardRefundLogDto);
            }
            output.setTotal(page.getTotal());
            output.setList(suixiangCardRefundLogDtoList);
        }
        return output;
    }

    @Override
    public BaseResponse manualRefundSuixiangCard(Long cardUseId, OperatorDto operatorDto) {
        BaseResponse baseResponse = checkAllowedRefund(cardUseId);
        if (baseResponse.getCode() != 0) {
            return baseResponse;
        }
        try (Jedis jedis = JedisUtil.getJedis()) {
            int expireMillis = 60000;
            String key = "lock_refund_cardUseId_" + cardUseId;
            JedisLock lock = new JedisLock(key, expireMillis);
            try {
                if (lock.acquire(jedis)) {
                    // 临界区，需要再次检查退款条件，避免随享卡性质变化
                    baseResponse = checkAllowedRefund(cardUseId);
                    if (baseResponse.getCode() != 0) {
                        return baseResponse;
                    }
                    // 退款动作
                    log.info("手动退款开始, cardUseId:{}", cardUseId);
                    baseResponse = refund(cardUseId, 1, operatorDto);
                    log.info("手动退款结束, cardUseId:{}, 结果:{}", cardUseId, JSON.toJSONString(baseResponse));
                    return baseResponse;
                }
                else {
                    log.warn("手动退款，redis锁获取失败，key:{}", key);
                    return new BaseResponse(-1, "正在退款中，请勿频繁操作");
                }
            } catch (Exception e) {
                log.error("手动退款失败，cardUseId:{},异常:{}", cardUseId, e.getMessage());
                return new BaseResponse(-1, e.getMessage());
            }
            finally {
                lock.releaseLua(jedis);
            }
        } catch (Exception e) {
            log.error("手动退款，获取redis异常:{}", e.getMessage());
            return new BaseResponse(-1, "手动退款失败，请稍后再试");
        }
    }

    // 退款
    public BaseResponse refund(Long cardUseId, int type, OperatorDto operatorDto) throws BusinessException {
        SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(cardUseId);
        if (suixiangCardUse == null) {
            throw new BusinessException("未查询到会员随享卡信息");
        }
        SuixiangCardPurchaseRecord suixiangCardPurchaseRecord = suixiangCardPurchaseRecordMapper.selectByPrimaryKey(suixiangCardUse.getPurchaseId());
        if (suixiangCardPurchaseRecord == null) {
            throw new BusinessException("未查询到随享卡购买记录");
        }
        // 冻结
        suixiangCardBaseManager.updateCardUseAndLog(suixiangCardUse, SuiXiangCardStatusEnum.EFFECTIVE, SuiXiangCardStatusEnum.AUTO_FROZENED,
                SuiXiangCardUseLogOperationTypeEnum.AUTO_FREEZED);

        // 调用支付的returnCardFee退卡费（同步应答只是受理），然后接收异步的mq应答，变成 4-已退卡；
        ReturnCardFeeReq req = new ReturnCardFeeReq();
        req.setPayOrderNo(suixiangCardPurchaseRecord.getPayOrderNo());

        // 获得单价 兼容的是 一次购买多张随享卡，每次退卡，退单张卡
        BigDecimal realAmount = suixiangCardPurchaseRecord.getRealAmount();
        int quantity = (suixiangCardPurchaseRecord.getQuantity() == null || suixiangCardPurchaseRecord.getQuantity() == 0) ? 1 : suixiangCardPurchaseRecord.getQuantity();
        BigDecimal unitPrice = realAmount.divide(new BigDecimal(quantity), 2, RoundingMode.HALF_DOWN);
        req.setAmount(unitPrice);
        req.setSxkUseId("TYPE_" + type + "_" + Constants.SXK_USEID_PREFIX + suixiangCardUse.getId());
        log.info("调用支付的returnCardFee退卡费，请求参数：{}", JSON.toJSONString(req));
        try {
            // 记录一条退款中的操作日志
            this.saveSuixiangCardRefundLog(cardUseId, type, SuixiangCardRefundSuccessEnum.REFUNDING.getStatus(), operatorDto);
            ReturnCardFeeRes resp = mdRestClient.returnCardFee(req);
            log.info("调用支付的returnCardFee退卡费，应答参数：{}", JSON.toJSONString(resp));
            if (resp == null) {
                log.error("调用支付的returnCardFee退卡费，同步受理返回为空！id:[{}],payOrderNo:[{}]", suixiangCardUse.getId(), req.getPayOrderNo());
                throw new BusinessException(-1, "退卡接口返回为空");
            }
            if (resp.getCode() != 0) {
                log.error("调用支付的returnCardFee退卡费，同步受理失败！id:[{}],payOrderNo:[{}],retCode:[{}]", suixiangCardUse.getId(), req.getPayOrderNo(), resp.getCode());
                throw new BusinessException(resp.getCode(), resp.getMessage());
            }
        } catch (Exception e) {
            log.error("手动退款，调用支付的returnCardFee退卡费，同步受理有异常！id:[{}],payOrderNo:[{}]", suixiangCardUse.getId(), suixiangCardPurchaseRecord.getPayOrderNo(), e);
            // 状态回滚 刷成正常，记录log
            suixiangCardBaseManager.updateCardUseAndLog(suixiangCardUse, SuiXiangCardStatusEnum.AUTO_FROZENED, SuiXiangCardStatusEnum.EFFECTIVE,
                    SuiXiangCardUseLogOperationTypeEnum.RETURNED_FAIL);
            // 记录退款失败的日志
            this.saveSuixiangCardRefundLog(cardUseId, type, SuixiangCardRefundSuccessEnum.REFUND_FAILURE.getStatus(), operatorDto);
            throw new BusinessException("发起退款操作失败，请稍后尝试");
        }
        return new BaseResponse(0, "退款操作成功，请等待退款完成");
    }

    /**
     * 卡片生效状态为生效中，卡片无累计订单数无累计节省金额，无进行中使用改随享卡的订单，购买记录支付金额>0
     * @return
     */
    @Override
    public BaseResponse checkAllowedRefund(Long cardUseId) {
        SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(cardUseId);
        // 卡片生效状态不为生效中
        if (!SuiXiangCardStatusEnum.EFFECTIVE.getStatus().equals(suixiangCardUse.getCardStatus())) {
            return new BaseResponse(-1, "卡片生效状态不为生效中！");
        }
        // 存在累计使用订单
        if (suixiangCardUse.getTotalOrder() > 0) {
            return new BaseResponse(-1, "卡片存在累计使用订单！");
        }
        // 存在累计节省金额
        if (suixiangCardUse.getTotalDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
            return new BaseResponse(-1, "卡片存在累计节省金额！");
        }
        // 存在进行中使用随享卡的订单，已使用或有冻结天数（就是有进行中的订单）
        if ((suixiangCardUse.getUsedDays() != null && suixiangCardUse.getUsedDays() > 0) || (suixiangCardUse.getFrozenDays() != null && suixiangCardUse.getFrozenDays() > 0)) {
            return new BaseResponse(-1, "卡片存在进行中使用随享卡的订单，已使用或有冻结天数！");
        }
        // 查询购买表
        SuixiangCardPurchaseRecord purchaseRecord = suixiangCardPurchaseRecordMapper.selectByPrimaryKey(suixiangCardUse.getPurchaseId());
        if (purchaseRecord == null) {
            log.error("查询不到suixiang_card_purchase_record表记录！id:[{}],purchaseId:[{}]", suixiangCardUse.getId(), suixiangCardUse.getPurchaseId());
            return new BaseResponse(-1, "查询不到购买记录！");
        }
        // 不是购买的卡
        if (!SuiXiangCardIssueTypeEnum.PURCHASE.getType().equals(purchaseRecord.getIssueType())) {
            return new BaseResponse(-1, "卡片不是购买的卡！");
        }
        // 无实付金额
        if (purchaseRecord.getRealAmount() == null || purchaseRecord.getRealAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return new BaseResponse(-1, "卡片无实付金额！");
        }
        if (purchaseRecord.getQuantity() == null || purchaseRecord.getQuantity() <= 0) {
            return new BaseResponse(-1, "卡片无购买张数");
        }
        if (StringUtils.isBlank(purchaseRecord.getPayOrderNo())) {
            log.error("查到suixiang_card_purchase_record表记录的payOrderNo为空！id:[{}],purchaseId:[{}]", suixiangCardUse.getId(), suixiangCardUse.getPurchaseId());
            return new BaseResponse(-1, "卡片不存在支付订单号！");
        }
        return new BaseResponse(0, "");
    }

    @Override
    public boolean saveSuixiangCardRefundLog(Long cardUseId, int type, int success, OperatorDto operatorDto) {
        SuixiangCardUse suixiangCardUse = suixiangCardUseMapper.selectByPrimaryKey(cardUseId);
        // 不抛出异常，不影响其他操作
        if (suixiangCardUse == null) {
            log.error("未查询到会员随享卡信息, cardUseId:{}", cardUseId);
            return false;
        }
        SuixiangCardPurchaseRecord suixiangCardPurchaseRecord = suixiangCardPurchaseRecordMapper.selectByPrimaryKey(suixiangCardUse.getPurchaseId());
        if (suixiangCardPurchaseRecord == null) {
            log.error("未查询到随享卡购买记录, purchaseId:{}", suixiangCardUse.getPurchaseId());
            return false;
        }
        SuixiangCardRefundLog suixiangCardRefundLog = new SuixiangCardRefundLog();
        suixiangCardRefundLog.setCardUseId(suixiangCardUse.getId());
        suixiangCardRefundLog.setCardBaseId(suixiangCardUse.getCardBaseId());
        suixiangCardRefundLog.setOrderSeq(suixiangCardPurchaseRecord.getOrderSeq());
        suixiangCardRefundLog.setRefundAmount(suixiangCardPurchaseRecord.getRealAmount().divide(new BigDecimal(suixiangCardPurchaseRecord.getQuantity()), 2, RoundingMode.UNNECESSARY));
        // 用户信息
        suixiangCardRefundLog.setUserId(suixiangCardUse.getUserId());
        MemberWrapInfoDto memberWrapInfoDto = membershipWrapService.getMemberWrapInfoByPkId(suixiangCardUse.getUserId());
        if (memberWrapInfoDto != null) {
            suixiangCardRefundLog.setMobilePhone(memberWrapInfoDto.getMobilePhone());
            suixiangCardRefundLog.setUsername(memberWrapInfoDto.getName());
        }
        if (operatorDto != null) {
            suixiangCardRefundLog.setCreateOperId(operatorDto.getOperatorId());
            suixiangCardRefundLog.setCreateOperName(operatorDto.getOperatorName());
            suixiangCardRefundLog.setUpdateOperId(operatorDto.getOperatorId());
            suixiangCardRefundLog.setUpdateOperName(operatorDto.getOperatorName());
        }
        suixiangCardRefundLog.setType(type);
        suixiangCardRefundLog.setSuccess(success);
        if (suixiangCardRefundLogMapper.insertSelective(suixiangCardRefundLog) > 0) {
            return true;
        }
        else {
            log.error("保存随享卡退款日志失败，suixiangCardRefundLog：[{}]", JSON.toJSON(suixiangCardRefundLog));
            return false;
        }
    }

    @Override
    public boolean saveSuixiangCardRefundLog(Long cardUseId, int success, int type) {
        SuixiangCardRefundLogExample suixiangCardRefundLogExample = new SuixiangCardRefundLogExample();
        // 查询最近的一条记录
        suixiangCardRefundLogExample.createCriteria().andCardUseIdEqualTo(cardUseId);
        suixiangCardRefundLogExample.setOrderByClause("refund_time DESC LIMIT 1");
        List<SuixiangCardRefundLog> suixiangCardRefundLogs = suixiangCardRefundLogMapper.selectByExample(suixiangCardRefundLogExample);
        if (CollectionUtils.isEmpty(suixiangCardRefundLogs)) {
            log.error("随享卡退款，未查询到最近的退款日志，cardUseId:{}", cardUseId);
        }
        // 如果是退款中的日志，就插入一条退款结果日志（保留退款中日志）
        if (SuixiangCardRefundSuccessEnum.REFUNDING.getStatus() == suixiangCardRefundLogs.get(0).getSuccess()) {
            SuixiangCardRefundLog suixiangCardRefundLog = suixiangCardRefundLogs.get(0);
            suixiangCardRefundLog.setId(null);
            suixiangCardRefundLog.setType(type);
            suixiangCardRefundLog.setSuccess(success);
            suixiangCardRefundLogMapper.insertSelective(suixiangCardRefundLog);
        }
        else {
            log.error("随享卡退款，未查询到退款中的日志，card");
        }
        return true;
    }
}
