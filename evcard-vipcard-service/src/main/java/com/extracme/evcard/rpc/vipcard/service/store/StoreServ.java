package com.extracme.evcard.rpc.vipcard.service.store;

import com.extracme.evcard.rpc.vipcard.dto.StoreBasicDto;
import com.extracme.evcard.rpc.vipcard.rest.entity.StoreInfo;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class StoreServ {

    @Resource
    ConfigLoader configLoader;

    private Cache<Long, String> storeCache = CacheBuilder.newBuilder()
            .expireAfterWrite(1 * DateUtils.MILLIS_PER_HOUR, TimeUnit.MILLISECONDS).maximumSize(500).build();

    public Map<Long, String> getStoreNames(List<Long> storeIds) {
        List<StoreBasicDto> list = getStoreByIds(storeIds);
        if(CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Long, String> map = new HashMap<>();
        for(StoreBasicDto store : list) {
            map.put(store.getStoreId(), store.getStoreName());
        }
        return map;
    }

    public StoreInfo getStoreById(String storeId) {
        Long lStoreId = ComUtils.getLong(storeId);
        if(storeId != null) {
            return configLoader.storeInfoMap.get(lStoreId);
        }
        return null;
    }


    public List<StoreBasicDto> getStoreByIds(Set<Long> storeIds) {
        Map<Long, StoreInfo> storeMap = configLoader.storeInfoMap;
        if(storeMap == null || storeMap.isEmpty()) {
            return new ArrayList<>();
        }
        List<StoreBasicDto> list = new ArrayList<>();
        for(Long storeId : storeIds) {
            StoreInfo storeInfo = storeMap.get(storeId);
            if(storeInfo != null) {
                StoreBasicDto store = new StoreBasicDto();
                store.setStoreId(storeInfo.getId());
                store.setStoreName(storeInfo.getStoreName());
                store.setCityCode(Long.valueOf(storeInfo.getOperCityId()));
                store.setOrgCode(storeInfo.getOperOrgCode());
                list.add(store);
            }
        }
        return list;
    }

    public List<StoreBasicDto> getStoreByIds(Long[] storeIds) {
        Set<Long> set = Arrays.stream(storeIds).collect(Collectors.toSet());
        return getStoreByIds(set);
    }

    public List<StoreBasicDto> getStoreByIds(List<Long> storeIds) {
        if(CollectionUtils.isEmpty(storeIds)) {
            return null;
        }
        Set<Long> set = storeIds.stream().collect(Collectors.toSet());
        return getStoreByIds(set);
    }
}
