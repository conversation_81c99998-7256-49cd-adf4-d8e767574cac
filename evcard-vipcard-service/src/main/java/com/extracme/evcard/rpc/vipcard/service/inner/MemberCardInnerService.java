package com.extracme.evcard.rpc.vipcard.service.inner;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.evcard.rpc.shop.dto.ShopInfoDto;
import com.extracme.evcard.rpc.shop.service.IShopService;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.vipcard.dao.*;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.enums.CardActivityOperateEnum;
import com.extracme.evcard.rpc.vipcard.model.*;
import com.extracme.evcard.rpc.vipcard.rest.entity.MappedGoodVehicleModelInfo;
import com.extracme.evcard.rpc.vipcard.rest.entity.StoreInfo;
import com.extracme.evcard.rpc.vipcard.service.store.GoodsModelService;
import com.extracme.evcard.rpc.vipcard.service.store.StoreServ;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/1/17
 */
@Slf4j
@Component("memberCardInnerService")
public class MemberCardInnerService {
    @Autowired
    private IShopService shopService;
    @Autowired
    private CityMapper cityMapper;

    @Resource
    private CityServ cityServ;

    @Resource
    private OrgService orgService;

    @Resource
    private VehicleModelServ vehicleModelServ;

    @Resource
    private StoreServ storeServ;

    @Resource
    private GoodsModelService goodsModelService;

    @Resource
    private MmpCardConfigOperationLogMapper mmpCardConfigOperationLogMapper;

    @Autowired
    private MmpUserCardOperationLogMapper mmpUserCardOperationLogMapper;

    @Autowired
    private MmpUserCardDiscountInfoMapper mmpUserCardDiscountInfoMapper;

    @Autowired
    MmpUserCardInfoMapper mmpUserCardInfoMapper;

    public void preProcessOrderCondition(GetEffectiveActivityCardInput cardInput) {
        OrderConditionDto orderCondition = new OrderConditionDto();
        BeanCopyUtils.copyProperties(cardInput, orderCondition);
        preProcessOrderCondition(orderCondition);
        BeanCopyUtils.copyProperties(orderCondition, cardInput);
    }

    public void preProcessOrderCondition(GetAvailableCardByUseConditionInputDto cardInput) {
        OrderConditionDto orderCondition = new OrderConditionDto();
        BeanCopyUtils.copyProperties(cardInput, orderCondition);
        preProcessOrderCondition(orderCondition);
        BeanCopyUtils.copyProperties(orderCondition, cardInput);
    }

    public void preProcessOrderCondition(OrderConditionDto input) {
        /**
         * 将原代码中，多处相同的数据预处理逻辑迁移至此
         */
        input.setVehicleModelSeq(ComUtils.getValidSeq(input.getVehicleModelSeq()));
        input.setGoodsModelId(ComUtils.getValidSeq(input.getGoodsModelId()));
        input.setPickUpCity(ComUtils.getValidSeq(input.getPickUpCity()));
        input.setReturnCity(ComUtils.getValidSeq(input.getReturnCity()));
        Integer orderRentMethod = input.getRentMethod();
        boolean isMdOrder = ComUtils.getIsMdOrder(orderRentMethod);
        if(!isMdOrder) {
            preProcessOrderConditionOrigin(input);
        } else {
            preProcessOrderConditionForMd(input);
        }
    }

    private void preProcessOrderConditionOrigin(OrderConditionDto input) {
        String pickUpShopSeq = ComUtils.getValidSeq(input.getPickUpShopSeq());
        String returnShopSeq = ComUtils.getValidSeq(input.getReturnShopSeq());
        Integer vehicleModelSeq = ComUtils.getValidSeq(input.getVehicleModelSeq());

        //取车城市、还车城市
        Long returnCityId = input.getReturnCity();
        Long pickCityId = input.getPickUpCity();
        String pickupAreaCode = null;
        if(StringUtils.isNotBlank(pickUpShopSeq)) {
            ShopInfoDto pickUpShopInfo = shopService.getShopInfoById(Integer.valueOf(pickUpShopSeq));
            if(pickUpShopInfo != null) {
                pickupAreaCode = pickUpShopInfo.getAreaCode();
                City pickUpCity = cityMapper.selectByAreaid(pickUpShopInfo.getAreaCode());
                if(pickUpCity != null) {
                    pickCityId = pickUpCity.getCityid();
                }
            }
        }
        if (StringUtils.isNotBlank(returnShopSeq)) {
            returnCityId = pickCityId;
            if (!returnShopSeq.equals(pickUpShopSeq)) {
                ShopInfoDto returnShopInfo = shopService.getShopInfoById(Integer.valueOf(returnShopSeq));
                if (returnShopInfo != null && !StringUtils.equals(pickupAreaCode,returnShopInfo.getAreaCode())) {
                    City returnUpCity = cityMapper.selectByAreaid(returnShopInfo.getAreaCode());
                    returnCityId = returnUpCity.getCityid();
                }
            }
        }
        /**
         * 资产车型转换为商品车型
         */
        Integer goodsModelId = input.getGoodsModelId();
        if(vehicleModelSeq != null && input.getGoodsModelId() == null) {
            //将资产车型转换为商品车型再比较
            MappedGoodVehicleModelInfo goodVehicleModel = goodsModelService.getGoodsVehicleModel(vehicleModelSeq);
            if(goodVehicleModel != null) {
                input.setGoodsModelId(goodVehicleModel.getGoodsModelId().intValue());
            }
        }

        input.setPickUpShopSeq(input.getPickUpShopSeq());
        input.setReturnShopSeq(input.getReturnShopSeq());
        input.setPickUpCity(pickCityId);
        input.setReturnCity(returnCityId);
        input.setGoodsModelId(goodsModelId);
    }

    private void preProcessOrderConditionForMd(OrderConditionDto input) {
        String pickUpStoreSeq = ComUtils.getValidSeq(input.getPickUpStoreId());
        String returnStoreSeq = ComUtils.getValidSeq(input.getReturnStoreId());
        //取车城市、还车城市
        Long returnCityId = input.getReturnCity();
        Long pickCityId = input.getPickUpCity();

        if(pickCityId == null && StringUtils.isNotBlank(pickUpStoreSeq)) {
            StoreInfo storeInfo = storeServ.getStoreById(pickUpStoreSeq);
            if(storeInfo != null) {
                pickCityId = storeInfo.getOperCityId();
            }
        }
        if(returnCityId == null && StringUtils.isNotBlank(returnStoreSeq)) {
            if(StringUtils.equals(pickUpStoreSeq, returnStoreSeq)) {
                returnCityId = pickCityId;
            } else {
                StoreInfo storeInfo = storeServ.getStoreById(returnStoreSeq);
                if(storeInfo != null) {
                    returnCityId = storeInfo.getOperCityId();
                }
            }
        }
        input.setPickUpStoreId(input.getPickUpStoreId());
        input.setReturnStoreId(input.getReturnStoreId());
        input.setPickUpCity(pickCityId);
        input.setReturnCity(returnCityId);
    }


    @Transactional
    public BaseResponse cancelMemberCard(MmpUserCardInfo userCardInfo, OperatorDto operateDto) {
        if(operateDto == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        if(userCardInfo == null) {
            log.warn("cancelMemberCard：指定卡不存在, userCardInfo=null.");
            return new BaseResponse(-1, "会员卡不存在");
        }
        MmpUserCardDiscountInfo frozenRecords = mmpUserCardDiscountInfoMapper.userCardFrozenDiscounts(userCardInfo.getUserCardNo());
        if(frozenRecords != null) {
            /**
             * 添加待作废标记
             */
            MmpUserCardInfo record = new MmpUserCardInfo();
            record.setUserCardNo(userCardInfo.getUserCardNo());
            record.setActiveFlag(Constants.TO_DISABLE_TAG);
            record.setUpdateOperId(operateDto.getOperatorId());
            record.setUpdateOperName(operateDto.getOperatorName());
            mmpUserCardInfoMapper.updateByPrimaryKeySelective(record);
            //保存待作废日志
            MmpUserCardOperationLog operationLog = new MmpUserCardOperationLog();
            operationLog.setUserCardNo(userCardInfo.getUserCardNo());
            operationLog.setCardId(userCardInfo.getCardId());
            operationLog.setCardGroup(userCardInfo.getCardGroup());
            operationLog.setOrderSeq(StringUtils.EMPTY);
            operationLog.setOperationType(5L);
            operationLog.setMiscDesc("尚有折扣冻结中");
            operationLog.setOriginSystem(operateDto.getOriginSystem());
            operationLog.setCreateTime(new Date());
            operationLog.setCreateOperName(operateDto.getOperatorName());
            mmpUserCardOperationLogMapper.insertSelective(operationLog);
            log.warn("cancelMemberCard：尚有折扣冻结中，标记为待作废。 userCardNo={}.", userCardInfo.getUserCardNo());
            return new BaseResponse(0, "尚有折扣冻结中，卡片已变更为待作废");
        }

        operateDto = OperatorDto.buildOperator(operateDto);
        mmpUserCardInfoMapper.cancelUserCard(null, null, userCardInfo.getUserCardNo(),
                operateDto.getOperatorId(), operateDto.getOperatorName());
        //后续的折扣记录无效
        mmpUserCardDiscountInfoMapper.cancelUserCard(userCardInfo.getUserCardNo());
        //保存作废日志
        MmpUserCardOperationLog operationLog = new MmpUserCardOperationLog();
        operationLog.setUserCardNo(userCardInfo.getUserCardNo());
        operationLog.setCardId(userCardInfo.getCardId());
        operationLog.setCardGroup(userCardInfo.getCardGroup());
        operationLog.setOrderSeq(StringUtils.EMPTY);
        operationLog.setOperationType(2L);
        operationLog.setOriginSystem(operateDto.getOriginSystem());
        operationLog.setCreateTime(new Date());
        operationLog.setCreateOperName(operateDto.getOperatorName());
        mmpUserCardOperationLogMapper.insertSelective(operationLog);
        return new BaseResponse(0, "卡片作废成功");
    }

    public void saveCardConfigLog(String content, Long cardId, CardActivityOperateEnum opType, OperatorDto operateDTO){
        MmpCardConfigOperationLog record = ConfigLogBuilder.buildCard(content, cardId, opType, operateDTO);
        mmpCardConfigOperationLogMapper.insertSelective(record);
    }

    public void saveCardActivityConfigLog(String content, Long activityId, CardActivityOperateEnum opType, OperatorDto operateDTO){
        MmpCardConfigOperationLog record = ConfigLogBuilder.buildActivity(content, activityId, opType, operateDTO);
        mmpCardConfigOperationLogMapper.insertSelective(record);
    }

    public void batchSaveActivityConfigLogs(String content, List<Long> activityIds, CardActivityOperateEnum opType, OperatorDto operateDTO){
        if(CollectionUtils.isNotEmpty(activityIds)) {
            List<MmpCardConfigOperationLog> records = new ArrayList<>(1);
            for(Long activityId : activityIds) {
                MmpCardConfigOperationLog record = ConfigLogBuilder.buildActivity(content, activityId, opType, operateDTO);
                records.add(record);
            }
            mmpCardConfigOperationLogMapper.batchSave(records);
        }
    }

    public PageBeanBO<CardActivityConfigLogDto> queryLogs(List configIds, Integer configType,
                                                          Integer pageNum, Integer pageSize, Integer isAll) {
        PageBeanBO<CardActivityConfigLogDto> pageBeanBO = new PageBeanBO<>();
        if (isAll == null || isAll == 0) {
            PageHelper.startPage(pageNum, pageSize, false);
        } else {
            PageHelper.startPage(pageNum, pageSize);
        }
        List<CardActivityConfigLogDto> list = mmpCardConfigOperationLogMapper.selectByConfigTypeAndId(configIds, configType);
        PageInfo<CardActivityConfigLogDto> pageInfo = new PageInfo<>(list);
        pageBeanBO.setList(list);
        PageBO pageBO = new PageBO();
        BeanCopyUtils.copyProperties(pageInfo, pageBO);
        pageBeanBO.setPage(pageBO);
        return pageBeanBO;
    }

    public void batchSaveCardOfferLogs(String content, List<MmpUserCardInfo> list, OperatorDto operateDTO){
        if(CollectionUtils.isNotEmpty(list)) {
            operateDTO = OperatorDto.buildOperator(operateDTO);
            List<MmpUserCardOperationLog> records = new ArrayList<>(1);
            for(MmpUserCardInfo userCard : list) {
                MmpUserCardOperationLog record = new MmpUserCardOperationLog();
                record.setUserCardNo(userCard.getUserCardNo());
                record.setCardId(userCard.getCardId());
                record.setCardGroup(userCard.getCardGroup());
                record.setOperationType(0L);
                record.setOriginSystem(operateDTO.getOriginSystem());
                record.setRefKey(StringUtils.EMPTY);
                record.setMiscDesc(operateDTO.getRemark());
                record.setOrderSeq(StringUtils.EMPTY);
                record.setCreateOperId(operateDTO.getOperatorId());
                record.setCreateOperName(operateDTO.getOperatorName());
                record.setCreateTime(new Date());
                records.add(record);
            }
            mmpUserCardOperationLogMapper.batchSave(records);
        }
    }

    public void saveCardPurchaseLog(MmpUserCardInfo userCard, String content, OperatorDto operateDTO){
        MmpUserCardOperationLog record = new MmpUserCardOperationLog();
        record.setUserCardNo(userCard.getUserCardNo());
        record.setCardId(userCard.getCardId());
        record.setCardGroup(userCard.getCardGroup());
        record.setOperationType(0L);
        record.setOriginSystem(operateDTO.getOriginSystem());
        record.setRefKey(StringUtils.EMPTY);
        if(StringUtils.isBlank(content)) {
            content = operateDTO.getRemark();
        }
        record.setMiscDesc(StringUtils.abbreviate(content, 200));
        record.setOrderSeq(StringUtils.EMPTY);
        record.setCreateOperId(operateDTO.getOperatorId());
        record.setCreateOperName(operateDTO.getOperatorName());
        record.setCreateTime(new Date());
        mmpUserCardOperationLogMapper.insertSelective(record);
    }



    public String getCityDesc(String cityIdStr) {
        List<Long> cityIds = ComUtils.getLongList(cityIdStr);
        Map<Long, String> map = cityServ.getCityNames(cityIds);
        if(map != null && map.size() > 0) {
            return StringUtils.join(map.values(), Constants.STR_COMMA_ZH);
        }
        return StringUtils.EMPTY;
    }

    public String getVehicleModelDesc(String vehicleModelStr) {
        List<Long> vehicleModelSeqs = ComUtils.getLongList(vehicleModelStr);
        Map<Long, String> map = vehicleModelServ.getVehicleNames(vehicleModelSeqs);
        if(map != null && map.size() > 0) {
            return StringUtils.join(map.values(), Constants.STR_COMMA_ZH);
        }
        return StringUtils.EMPTY;
    }

    public String getGoodsVehicleModelDesc(String vehicleModelStr) {
        List<Long> vehicleModelSeqs = ComUtils.getLongList(vehicleModelStr);
        Map<Long, String> map = goodsModelService.getVehicleNames(vehicleModelSeqs);
        if(map != null && map.size() > 0) {
            return StringUtils.join(map.values(), Constants.STR_COMMA_ZH);
        }
        return StringUtils.EMPTY;
    }

    public String getRentMethodDesc(String rentMethodStr) {
        List<Integer> rentMethods = ComUtils.getIntList(rentMethodStr);
        String rentMethodNames = StringUtils.EMPTY;
        if(CollectionUtils.isNotEmpty(rentMethods)) {
            StringBuffer sb = new StringBuffer();
            for(Integer v : rentMethods) {
                if(v < 0 || v >= Constants.RENT_METHODS.length) {
                    continue;
                }
                sb.append(Constants.STR_COMMA_ZH).append(Constants.RENT_METHODS[v]);
            }
            rentMethodNames = sb.toString();
        }
        if(StringUtils.isNotBlank(rentMethodNames)) {
            rentMethodNames = StringUtils.substring(rentMethodNames, 1);
        }
        return rentMethodNames;
    }

    public String getRentMethodGroupDesc(String rentMethodGroupStr) {
        List<Integer> rentMethods = ComUtils.getIntList(rentMethodGroupStr);
        String rentMethodGroupNames = StringUtils.EMPTY;
        if(CollectionUtils.isNotEmpty(rentMethods)) {
            StringBuffer sb = new StringBuffer();
            for(Integer v : rentMethods) {
                if(v <= 0 || v > Constants.RENT_METHOD_GROUPS.length) {
                    continue;
                }
                sb.append(Constants.STR_COMMA_ZH).append(Constants.RENT_METHOD_GROUPS[v - 1]);
            }
            rentMethodGroupNames = sb.toString();
        }
        if(StringUtils.isNotBlank(rentMethodGroupNames)) {
            rentMethodGroupNames = StringUtils.substring(rentMethodGroupNames, 1);
        }
        return rentMethodGroupNames;
    }

    public String getStoreDesc(String storeIdsStr) {
        List<Long> storeIds = ComUtils.getLongList(storeIdsStr);
        Map<Long, String> map = storeServ.getStoreNames(storeIds);
        if(map != null && map.size() > 0) {
            return StringUtils.join(map.values(), Constants.STR_COMMA_ZH);
        }
        return StringUtils.EMPTY;
    }

    public void buildCardPurchaseListView(CardPurchaseRecordInfo record, CardPurchaseListViewDto dto){
        BeanCopyUtils.copyProperties(record, dto);
        dto.setUserCardNoDesc(Constants.CARD_PREFIX[3] + dto.getUserCardNo());
        if(dto.getCardType() != null && dto.getCardType() < Constants.CARD_TYPE_NAMES.length) {
            dto.setCardTypeDesc(Constants.CARD_TYPE_NAMES[dto.getCardType()]);
        }
        if(dto.getPurchaseType() != null && dto.getPurchaseType() < Constants.PURCHASE_TYPE_NAMES.length) {
            dto.setPurchaseTypeDesc(Constants.PURCHASE_TYPE_NAMES[dto.getPurchaseType()]);
        }
        if(dto.getIssueType() != null && dto.getIssueType() < Constants.ISSUE_TYPE_NAMES.length) {
            dto.setIssueTypeDesc(Constants.ISSUE_TYPE_NAMES[dto.getIssueType()]);
        }
        if(dto.getPaymentStatus() != null && dto.getPaymentStatus() < Constants.PURCHASE_STATUS_NAMES.length) {
            dto.setPaymentStatusDesc(Constants.PURCHASE_STATUS_NAMES[dto.getPaymentStatus() + 1]);
        }

        dto.setCardGroup(3);
        dto.setPurchaseTime(DateUtil.getFormatDate(record.getPayTime(), DateUtil.simple));
        dto.setCityNames(getCityDesc(dto.getCityLimit()));
        dto.setIssueUser(dto.getCreateOperName());
    }

    public List<Integer> convertRentMethodGroup(List<Integer> rentMethods) {
        if(CollectionUtils.isEmpty(rentMethods)) {
            return null;
        }
        Set<Integer> rentMethodGroup = new HashSet<>();
        for(Integer rentMethod : rentMethods) {
            if(ArrayUtils.contains(Constants.RENT_METHODS_OF_DAILY, rentMethod)) {
                rentMethodGroup.add(Constants.RENT_METHOD_GROUP_DAILY);
            }
            else if(ArrayUtils.contains(Constants.RENT_METHODS_OF_TIME, rentMethod)) {
                rentMethodGroup.add(Constants.RENT_METHOD_GROUP_TIME);
                continue;
            }
        }
        return new ArrayList<>(rentMethodGroup);
    }

    public Integer getRentMethodGroup(Integer rentMethod) {
        if(ArrayUtils.contains(Constants.RENT_METHODS_OF_DAILY, rentMethod)) {
            return Constants.RENT_METHOD_GROUP_DAILY;
        }
        if(ArrayUtils.contains(Constants.RENT_METHODS_OF_TIME, rentMethod)) {
            return Constants.RENT_METHOD_GROUP_TIME;
        }
        return -1;
    }
}
