package com.extracme.evcard.rpc.vipcard.model;

import java.math.BigDecimal;
import java.util.Date;

public class MmpCardDef {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.card_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Long cardId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.card_name
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private String cardName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.org_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private String orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.card_type
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Integer cardType;

    /**
     * 购卡条件： 0 默认  1学生卡
     */
    private Integer purchaseType;

    /**
     * 卡面样式  0自定义  1样式一  2样式二  3样式三
     */
    private Integer styleType;

    /**
     * 卡面背景图： 卡面样式选择0自定义时，必传
     */
    private String backUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.discount
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Integer discount;

    /**
     * 周期内累计折扣上限, 单位元
     */
    private Integer totalDiscountAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.max_value
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private BigDecimal maxValue;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.duration_limit
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Integer durationLimit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.city_limit
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private String cityLimit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.vehicle_model_limit
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private String vehicleModelLimit;

    private String goodsModelId;

    private String storeIds;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.rent_method
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private String rentMethod;

    private String rentMethodGroup;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.start_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private String startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.end_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private String endTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.available_days_of_week
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private String availableDaysOfWeek;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.rules
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private String rules;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.valid_time_type
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Integer validTimeType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.effective_days
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Integer effectiveDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.valid_days
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Integer validDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.status
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.create_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.update_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_card_def.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    private String updateOperName;

    public String getGoodsModelId() {
        return goodsModelId;
    }

    public void setGoodsModelId(String goodsModelId) {
        this.goodsModelId = goodsModelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.card_id
     *
     * @return the value of mmp_card_def.card_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Long getCardId() {
        return cardId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.card_id
     *
     * @param cardId the value for mmp_card_def.card_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.card_name
     *
     * @return the value of mmp_card_def.card_name
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public String getCardName() {
        return cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.card_name
     *
     * @param cardName the value for mmp_card_def.card_name
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.org_id
     *
     * @return the value of mmp_card_def.org_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.org_id
     *
     * @param orgId the value for mmp_card_def.org_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.card_type
     *
     * @return the value of mmp_card_def.card_type
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Integer getCardType() {
        return cardType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.card_type
     *
     * @param cardType the value for mmp_card_def.card_type
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.discount
     *
     * @return the value of mmp_card_def.discount
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Integer getDiscount() {
        return discount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.discount
     *
     * @param discount the value for mmp_card_def.discount
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.max_value
     *
     * @return the value of mmp_card_def.max_value
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public BigDecimal getMaxValue() {
        return maxValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.max_value
     *
     * @param maxValue the value for mmp_card_def.max_value
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setMaxValue(BigDecimal maxValue) {
        this.maxValue = maxValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.duration_limit
     *
     * @return the value of mmp_card_def.duration_limit
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Integer getDurationLimit() {
        return durationLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.duration_limit
     *
     * @param durationLimit the value for mmp_card_def.duration_limit
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setDurationLimit(Integer durationLimit) {
        this.durationLimit = durationLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.city_limit
     *
     * @return the value of mmp_card_def.city_limit
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public String getCityLimit() {
        return cityLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.city_limit
     *
     * @param cityLimit the value for mmp_card_def.city_limit
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setCityLimit(String cityLimit) {
        this.cityLimit = cityLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.vehicle_model_limit
     *
     * @return the value of mmp_card_def.vehicle_model_limit
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public String getVehicleModelLimit() {
        return vehicleModelLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.vehicle_model_limit
     *
     * @param vehicleModelLimit the value for mmp_card_def.vehicle_model_limit
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setVehicleModelLimit(String vehicleModelLimit) {
        this.vehicleModelLimit = vehicleModelLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.rent_method
     *
     * @return the value of mmp_card_def.rent_method
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public String getRentMethod() {
        return rentMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.rent_method
     *
     * @param rentMethod the value for mmp_card_def.rent_method
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setRentMethod(String rentMethod) {
        this.rentMethod = rentMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.start_time
     *
     * @return the value of mmp_card_def.start_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public String getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.start_time
     *
     * @param startTime the value for mmp_card_def.start_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.end_time
     *
     * @return the value of mmp_card_def.end_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public String getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.end_time
     *
     * @param endTime the value for mmp_card_def.end_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.available_days_of_week
     *
     * @return the value of mmp_card_def.available_days_of_week
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public String getAvailableDaysOfWeek() {
        return availableDaysOfWeek;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.available_days_of_week
     *
     * @param availableDaysOfWeek the value for mmp_card_def.available_days_of_week
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setAvailableDaysOfWeek(String availableDaysOfWeek) {
        this.availableDaysOfWeek = availableDaysOfWeek;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.rules
     *
     * @return the value of mmp_card_def.rules
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public String getRules() {
        return rules;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.rules
     *
     * @param rules the value for mmp_card_def.rules
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setRules(String rules) {
        this.rules = rules;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.valid_time_type
     *
     * @return the value of mmp_card_def.valid_time_type
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Integer getValidTimeType() {
        return validTimeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.valid_time_type
     *
     * @param validTimeType the value for mmp_card_def.valid_time_type
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setValidTimeType(Integer validTimeType) {
        this.validTimeType = validTimeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.effective_days
     *
     * @return the value of mmp_card_def.effective_days
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Integer getEffectiveDays() {
        return effectiveDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.effective_days
     *
     * @param effectiveDays the value for mmp_card_def.effective_days
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setEffectiveDays(Integer effectiveDays) {
        this.effectiveDays = effectiveDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.valid_days
     *
     * @return the value of mmp_card_def.valid_days
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Integer getValidDays() {
        return validDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.valid_days
     *
     * @param validDays the value for mmp_card_def.valid_days
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setValidDays(Integer validDays) {
        this.validDays = validDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.status
     *
     * @return the value of mmp_card_def.status
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.status
     *
     * @param status the value for mmp_card_def.status
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.create_time
     *
     * @return the value of mmp_card_def.create_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.create_time
     *
     * @param createTime the value for mmp_card_def.create_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.create_oper_id
     *
     * @return the value of mmp_card_def.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.create_oper_id
     *
     * @param createOperId the value for mmp_card_def.create_oper_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.create_oper_name
     *
     * @return the value of mmp_card_def.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.create_oper_name
     *
     * @param createOperName the value for mmp_card_def.create_oper_name
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.update_time
     *
     * @return the value of mmp_card_def.update_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.update_time
     *
     * @param updateTime the value for mmp_card_def.update_time
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.update_oper_id
     *
     * @return the value of mmp_card_def.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.update_oper_id
     *
     * @param updateOperId the value for mmp_card_def.update_oper_id
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_card_def.update_oper_name
     *
     * @return the value of mmp_card_def.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_card_def.update_oper_name
     *
     * @param updateOperName the value for mmp_card_def.update_oper_name
     *
     * @mbggenerated Thu Dec 24 14:01:23 CST 2020
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    public Integer getStyleType() {
        return styleType;
    }

    public void setStyleType(Integer styleType) {
        this.styleType = styleType;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public Integer getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    public void setTotalDiscountAmount(Integer totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    public String getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(String storeIds) {
        this.storeIds = storeIds;
    }

    public String getRentMethodGroup() {
        return rentMethodGroup;
    }

    public void setRentMethodGroup(String rentMethodGroup) {
        this.rentMethodGroup = rentMethodGroup;
    }
}