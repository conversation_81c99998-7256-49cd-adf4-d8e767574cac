<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.SuixiangCardPurchaseRecordMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecord">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_mobile" jdbcType="VARCHAR" property="userMobile" />
    <result column="card_base_id" jdbcType="BIGINT" property="cardBaseId" />
    <result column="card_name" jdbcType="VARCHAR" property="cardName" />
    <result column="card_rent_id" jdbcType="BIGINT" property="cardRentId" />
    <result column="card_price_id" jdbcType="BIGINT" property="cardPriceId" />
    <result column="cdk_id" jdbcType="BIGINT" property="cdkId" />
    <result column="issue_type" jdbcType="INTEGER" property="issueType" />
    <result column="payment_status" jdbcType="INTEGER" property="paymentStatus" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="real_amount" jdbcType="DECIMAL" property="realAmount" />
    <result column="out_trade_seq" jdbcType="VARCHAR" property="outTradeSeq" />
    <result column="card_use_id" jdbcType="BIGINT" property="cardUseId" />
    <result column="card_use_ids" jdbcType="VARCHAR" property="cardUseIds" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="order_seq" jdbcType="VARCHAR" property="orderSeq" />
    <result column="pay_order_no" jdbcType="VARCHAR" property="payOrderNo" />
    <result column="remind_status" jdbcType="INTEGER" property="remindStatus" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="merge_pay_origin" jdbcType="INTEGER" property="mergePayOrigin" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="obtain_type" jdbcType="INTEGER" property="obtainType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    id, org_id, user_id, user_mobile, card_base_id, card_name, card_rent_id, card_price_id, cdk_id,
    issue_type, payment_status, quantity, pay_time, real_amount, out_trade_seq, card_use_id, card_use_ids,
    start_time, end_time, order_seq, pay_order_no, remind_status, cancel_time, merge_pay_origin, 
    create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from suixiang_card_purchase_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from suixiang_card_purchase_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    delete from suixiang_card_purchase_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecord">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    insert into suixiang_card_purchase_record (id, org_id, user_id, 
      user_mobile, card_base_id, card_name, 
      card_rent_id, card_price_id, cdk_id, issue_type,
      payment_status, quantity, pay_time, 
      real_amount, out_trade_seq, card_use_id, card_use_ids,
      start_time, end_time, order_seq, 
      pay_order_no, remind_status, cancel_time, 
      merge_pay_origin, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, 
      #{userMobile,jdbcType=VARCHAR}, #{cardBaseId,jdbcType=BIGINT}, #{cardName,jdbcType=VARCHAR}, 
      #{cardRentId,jdbcType=BIGINT}, #{cardPriceId,jdbcType=BIGINT}, #{cdkId,jdbcType=BIGINT}, #{issueType,jdbcType=INTEGER},
      #{paymentStatus,jdbcType=INTEGER}, #{quantity,jdbcType=INTEGER}, #{payTime,jdbcType=TIMESTAMP}, 
      #{realAmount,jdbcType=DECIMAL}, #{outTradeSeq,jdbcType=VARCHAR}, #{cardUseId,jdbcType=BIGINT}, #{cardUseIds,jdbcType=BIGINT},
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{orderSeq,jdbcType=VARCHAR}, 
      #{payOrderNo,jdbcType=VARCHAR}, #{remindStatus,jdbcType=INTEGER}, #{cancelTime,jdbcType=TIMESTAMP}, 
      #{mergePayOrigin,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecord" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    insert into suixiang_card_purchase_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userMobile != null">
        user_mobile,
      </if>
      <if test="cardBaseId != null">
        card_base_id,
      </if>
      <if test="cardName != null">
        card_name,
      </if>
      <if test="cardRentId != null">
        card_rent_id,
      </if>
      <if test="cardPriceId != null">
        card_price_id,
      </if>
      <if test="cdkId != null">
        cdk_id,
      </if>
      <if test="issueType != null">
        issue_type,
      </if>
      <if test="paymentStatus != null">
        payment_status,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="realAmount != null">
        real_amount,
      </if>
      <if test="outTradeSeq != null">
        out_trade_seq,
      </if>
      <if test="cardUseId != null">
        card_use_id,
      </if>
      <if test="cardUseIds != null">
        card_use_ids,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="orderSeq != null">
        order_seq,
      </if>
      <if test="payOrderNo != null">
        pay_order_no,
      </if>
      <if test="remindStatus != null">
        remind_status,
      </if>
      <if test="cancelTime != null">
        cancel_time,
      </if>
      <if test="mergePayOrigin != null">
        merge_pay_origin,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="obtainType != null">
        obtain_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userMobile != null">
        #{userMobile,jdbcType=VARCHAR},
      </if>
      <if test="cardBaseId != null">
        #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardName != null">
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="cardRentId != null">
        #{cardRentId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null">
        #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="cdkId != null">
        #{cdkId,jdbcType=BIGINT},
      </if>
      <if test="issueType != null">
        #{issueType,jdbcType=INTEGER},
      </if>
      <if test="paymentStatus != null">
        #{paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realAmount != null">
        #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="outTradeSeq != null">
        #{outTradeSeq,jdbcType=VARCHAR},
      </if>
      <if test="cardUseId != null">
        #{cardUseId,jdbcType=BIGINT},
      </if>
      <if test="cardUseIds != null">
        #{cardUseIds,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSeq != null">
        #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="remindStatus != null">
        #{remindStatus,jdbcType=INTEGER},
      </if>
      <if test="cancelTime != null">
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mergePayOrigin != null">
        #{mergePayOrigin,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="obtainType != null">
        #{obtainType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecordExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    select count(*) from suixiang_card_purchase_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    update suixiang_card_purchase_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userMobile != null">
        user_mobile = #{record.userMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.cardBaseId != null">
        card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.cardName != null">
        card_name = #{record.cardName,jdbcType=VARCHAR},
      </if>
      <if test="record.cardRentId != null">
        card_rent_id = #{record.cardRentId,jdbcType=BIGINT},
      </if>
      <if test="record.cardPriceId != null">
        card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="record.cdkId != null">
        cdk_id = #{record.cdkId,jdbcType=BIGINT},
      </if>
      <if test="record.issueType != null">
        issue_type = #{record.issueType,jdbcType=INTEGER},
      </if>
      <if test="record.paymentStatus != null">
        payment_status = #{record.paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.realAmount != null">
        real_amount = #{record.realAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.outTradeSeq != null">
        out_trade_seq = #{record.outTradeSeq,jdbcType=VARCHAR},
      </if>
      <if test="record.cardUseId != null">
        card_use_id = #{record.cardUseId,jdbcType=BIGINT},
      </if>
      <if test="record.cardUseIds != null">
        card_use_ids = #{record.cardUseIds,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderSeq != null">
        order_seq = #{record.orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="record.payOrderNo != null">
        pay_order_no = #{record.payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.remindStatus != null">
        remind_status = #{record.remindStatus,jdbcType=INTEGER},
      </if>
      <if test="record.cancelTime != null">
        cancel_time = #{record.cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mergePayOrigin != null">
        merge_pay_origin = #{record.mergePayOrigin,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null">
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null">
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null">
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null">
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    update suixiang_card_purchase_record
    set id = #{record.id,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      user_mobile = #{record.userMobile,jdbcType=VARCHAR},
      card_base_id = #{record.cardBaseId,jdbcType=BIGINT},
      card_name = #{record.cardName,jdbcType=VARCHAR},
      card_rent_id = #{record.cardRentId,jdbcType=BIGINT},
      card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      cdk_id = #{record.cdkId,jdbcType=BIGINT},
      issue_type = #{record.issueType,jdbcType=INTEGER},
      payment_status = #{record.paymentStatus,jdbcType=INTEGER},
      quantity = #{record.quantity,jdbcType=INTEGER},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      real_amount = #{record.realAmount,jdbcType=DECIMAL},
      out_trade_seq = #{record.outTradeSeq,jdbcType=VARCHAR},
      card_use_id = #{record.cardUseId,jdbcType=BIGINT},
      card_use_ids = #{record.cardUseIds,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      order_seq = #{record.orderSeq,jdbcType=VARCHAR},
      pay_order_no = #{record.payOrderNo,jdbcType=VARCHAR},
      remind_status = #{record.remindStatus,jdbcType=INTEGER},
      cancel_time = #{record.cancelTime,jdbcType=TIMESTAMP},
      merge_pay_origin = #{record.mergePayOrigin,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecord">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    update suixiang_card_purchase_record
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userMobile != null">
        user_mobile = #{userMobile,jdbcType=VARCHAR},
      </if>
      <if test="cardBaseId != null">
        card_base_id = #{cardBaseId,jdbcType=BIGINT},
      </if>
      <if test="cardName != null">
        card_name = #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="cardRentId != null">
        card_rent_id = #{cardRentId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null">
        card_price_id = #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="cdkId != null">
        cdk_id = #{cdkId,jdbcType=BIGINT},
      </if>
      <if test="issueType != null">
        issue_type = #{issueType,jdbcType=INTEGER},
      </if>
      <if test="paymentStatus != null">
        payment_status = #{paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realAmount != null">
        real_amount = #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="outTradeSeq != null">
        out_trade_seq = #{outTradeSeq,jdbcType=VARCHAR},
      </if>
      <if test="cardUseId != null">
        card_use_id = #{cardUseId,jdbcType=BIGINT},
      </if>
      <if test="cardUseIds != null">
        card_use_ids = #{cardUseIds,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSeq != null">
        order_seq = #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="remindStatus != null">
        remind_status = #{remindStatus,jdbcType=INTEGER},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mergePayOrigin != null">
        merge_pay_origin = #{mergePayOrigin,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="obtainType != null">
        obtain_type = #{obtainType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardPurchaseRecord">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 30 19:27:33 CST 2023.
    -->
    update suixiang_card_purchase_record
    set org_id = #{orgId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      user_mobile = #{userMobile,jdbcType=VARCHAR},
      card_base_id = #{cardBaseId,jdbcType=BIGINT},
      card_name = #{cardName,jdbcType=VARCHAR},
      card_rent_id = #{cardRentId,jdbcType=BIGINT},
      card_price_id = #{cardPriceId,jdbcType=BIGINT},
      cdk_id = #{cdkId,jdbcType=BIGINT},
      issue_type = #{issueType,jdbcType=INTEGER},
      payment_status = #{paymentStatus,jdbcType=INTEGER},
      quantity = #{quantity,jdbcType=INTEGER},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      real_amount = #{realAmount,jdbcType=DECIMAL},
      out_trade_seq = #{outTradeSeq,jdbcType=VARCHAR},
      card_use_id = #{cardUseId,jdbcType=BIGINT},
      card_use_ids = #{cardUseIds,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      order_seq = #{orderSeq,jdbcType=VARCHAR},
      pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
      remind_status = #{remindStatus,jdbcType=INTEGER},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      merge_pay_origin = #{mergePayOrigin,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="countForPage" parameterType="map" resultType="java.lang.Integer">
    select
    count(1)
    from suixiang_card_purchase_record
    where is_deleted = 0
    <if test="record.orgId != null">
      and org_id = #{record.orgId,jdbcType=VARCHAR}
    </if>
    <if test="record.cardName != null">
      and card_name = #{record.cardName,jdbcType=VARCHAR}
    </if>
    <if test="record.cardBaseId != null">
      and card_base_id = #{record.cardBaseId,jdbcType=BIGINT}
    </if>
    <if test="record.issueType != null">
      and issue_type = #{record.issueType,jdbcType=INTEGER}
    </if>
    <if test="record.createTimeStart != null">
      and create_time >= #{record.createTimeStart,jdbcType=TIMESTAMP}
    </if>
    <if test="record.createTimeEnd != null">
      and create_time &lt;= #{record.createTimeEnd,jdbcType=TIMESTAMP}
    </if>
    <if test="record.paymentStatus != null">
      and payment_status = #{record.paymentStatus,jdbcType=INTEGER}
    </if>
    <if test="record.userMobile != null">
      and user_mobile = #{record.userMobile,jdbcType=VARCHAR}
    </if>
    <if test="record.payOrderNo != null">
      and pay_order_no = #{record.payOrderNo,jdbcType=VARCHAR}
    </if>
    <if test="record.orderSeq != null">
      and order_seq = #{record.orderSeq,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectForPage" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from suixiang_card_purchase_record
    where is_deleted = 0
    <if test="record.orgId != null">
      and org_id = #{record.orgId,jdbcType=VARCHAR}
    </if>
    <if test="record.cardName != null">
      and card_name = #{record.cardName,jdbcType=VARCHAR}
    </if>
    <if test="record.cardBaseId != null">
      and card_base_id = #{record.cardBaseId,jdbcType=BIGINT}
    </if>
    <if test="record.issueType != null">
      and issue_type = #{record.issueType,jdbcType=INTEGER}
    </if>
    <if test="record.createTimeStart != null">
      and create_time >= #{record.createTimeStart,jdbcType=TIMESTAMP}
    </if>
    <if test="record.createTimeEnd != null">
      and create_time &lt;= #{record.createTimeEnd,jdbcType=TIMESTAMP}
    </if>
    <if test="record.paymentStatus != null">
      and payment_status = #{record.paymentStatus,jdbcType=INTEGER}
    </if>
    <if test="record.userMobile != null">
      and user_mobile = #{record.userMobile,jdbcType=VARCHAR}
    </if>
    <if test="record.payOrderNo != null">
      and pay_order_no = #{record.payOrderNo,jdbcType=VARCHAR}
    </if>
    <if test="record.orderSeq != null">
      and order_seq = #{record.orderSeq,jdbcType=VARCHAR}
    </if>
    order by create_time desc
    <if test="page != null">
      LIMIT #{page.offSet},#{page.limitSet}
    </if>
  </select>

  <!-- 将新发卡记录标记为已读 -->
  <update id="readCards">
    UPDATE ${issSchema}.suixiang_card_purchase_record
    SET  remind_status = 1
    WHERE id  IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and issue_type in (2, 3)
  </update>


  <sql id="Base_Column_List3">

  </sql>
  <select id="selectUnreadCardList" resultType="com.extracme.evcard.rpc.vipcard.dto.suixiang.CardReMindDto">
    select
    cp.id as purchaseId,
    t3.sales_price as amount,t1.card_name as cardName,t1.card_status,t2.rent_days as days,uc.start_time as createTime
    from ${issSchema}.suixiang_card_purchase_record cp
    LEFT JOIN ${issSchema}.suixiang_card_use uc ON uc.purchase_id = cp.id
    LEFT JOIN ${issSchema}.suixiang_card_price t3 ON t3.id = cp.card_price_id
    left join ${issSchema}.suixiang_card_base t1 on t1.id = cp.card_base_id
    left join ${issSchema}.suixiang_card_rent_days t2 on t2.id = cp.card_rent_id
    where cp.payment_status = 2
    and (cp.issue_type = 2 or (cp.issue_type = 3 and cp.obtain_type = 1))
    and uc.merge_flag = 0
    and cp.user_id = #{userId}
    and cp.remind_status = 0
    and uc.card_status=1  AND uc.expires_time &gt;= NOW()
    and cp.create_time &gt;= #{startDate}
    and t1.is_deleted=0
    and t2.is_deleted=0
    and t3.is_deleted=0
    ORDER BY cp.create_time desc
    limit #{limit}
  </select>

  <select id="selectTimeoutUnpayOrder" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.suixiang_card_purchase_record
    where is_deleted = 0
        and payment_status = 1
        and merge_pay_origin = 0
        and create_time &lt;= #{endTime}
        and create_time &gt; #{startTime}
        and id &gt;= #{id}
    order by id asc
    limit #{limit}
  </select>

  <select id="selectUnreadCardMergeList" resultType="com.extracme.evcard.rpc.vipcard.dto.suixiang.CardReMindDto">
    select
    cp.id as purchaseId,
    t3.sales_price as amount,t1.card_name as cardName,t1.card_status,t2.rent_days as days,uc.start_time as createTime
    from ${issSchema}.suixiang_card_purchase_record cp
    LEFT JOIN ${issSchema}.suixiang_card_use uc ON uc.id = cp.card_use_id
    LEFT JOIN ${issSchema}.suixiang_card_price t3 ON t3.id = cp.card_price_id
    left join ${issSchema}.suixiang_card_base t1 on t1.id = cp.card_base_id
    left join ${issSchema}.suixiang_card_rent_days t2 on t2.id = cp.card_rent_id
    where cp.payment_status = 2
    and cp.issue_type = 3
    and cp.obtain_type = 1
    and uc.merge_flag = 1
    and cp.user_id = #{userId}
    and cp.remind_status = 0
    and uc.card_status=1  AND uc.expires_time &gt;= NOW()
    and cp.create_time &gt;= #{startDate}
    and t1.is_deleted=0
    and t2.is_deleted=0
    and t3.is_deleted=0
    ORDER BY cp.create_time desc
    limit #{limit}
  </select>
</mapper>