package com.extracme.evcard.rpc.vipcard.mq;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Discription
 * @date 2022/01/07
 */
@Configuration
public class OnsConsumer {

    @Value("${ali.AccessKey}")
    private String accessKeyId;

    @Value("${ali.SecretKey}")
    private String accessKeySecret;

    @Value("${ons.addr}")
    private String onsAddr;

    @Value("${ons.gid}")
    private String onsGid;

    @Value("${ons.topic.delay}")
    private String onsDelayTopic;

    @Value("${ons.consumeThreadNums:5}")
    private String consumeThreadNums;


    @Autowired
    private DelayMessageListener delayMessageListener;

    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean rawDataConsumer() {
        ConsumerBean consumerBean = new ConsumerBean();
        Properties properties = getOnsProperties();
        properties.setProperty(PropertyKeyConst.GROUP_ID, onsGid);
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, consumeThreadNums);

        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();

        Subscription subscription = new Subscription();
        subscription.setTopic(onsDelayTopic);
        String tags = "CARD_PURCHASE_CANCEL||SUIXIANGCARD_PURCHASE_CANCEL";
        subscription.setExpression(tags);
        //subscription.setExpression("*");

        subscriptionTable.put(subscription, delayMessageListener);
        consumerBean.setSubscriptionTable(subscriptionTable);
        consumerBean.setProperties(properties);
        return consumerBean;
    }

    public Properties getOnsProperties() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.AccessKey, this.accessKeyId);
        properties.setProperty(PropertyKeyConst.SecretKey, this.accessKeySecret);
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, this.onsAddr);
        return properties;
    }
}

