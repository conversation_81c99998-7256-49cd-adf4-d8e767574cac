package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseOpreationLog;
import com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseOpreationLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardUseOpreationLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    int countByExample(SuixiangCardUseOpreationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    int insert(SuixiangCardUseOpreationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    int insertSelective(SuixiangCardUseOpreationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    List<SuixiangCardUseOpreationLog> selectByExample(SuixiangCardUseOpreationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    SuixiangCardUseOpreationLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    int updateByExampleSelective(@Param("record") SuixiangCardUseOpreationLog record, @Param("example") SuixiangCardUseOpreationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    int updateByExample(@Param("record") SuixiangCardUseOpreationLog record, @Param("example") SuixiangCardUseOpreationLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    int updateByPrimaryKeySelective(SuixiangCardUseOpreationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_use_opreation_log
     *
     * @mbggenerated Tue Jan 31 16:13:21 CST 2023
     */
    int updateByPrimaryKey(SuixiangCardUseOpreationLog record);
}