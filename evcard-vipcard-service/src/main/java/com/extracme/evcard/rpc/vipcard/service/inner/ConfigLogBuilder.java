package com.extracme.evcard.rpc.vipcard.service.inner;

import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.enums.CardActivityOperateEnum;
import com.extracme.evcard.rpc.vipcard.model.MmpCardConfigOperationLog;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

public class ConfigLogBuilder {

    /**
     * 构建片售卖活动配置日志配置日志内容
     * @param content
     * @param activityId
     * @param opType
     * @param operateDTO
     */
    public static MmpCardConfigOperationLog buildActivity(String content, Long activityId, CardActivityOperateEnum opType, OperatorDto operateDTO){
        return build(content, 1, activityId, opType, operateDTO);
    }

    /**
     * 构建卡片配置日志内容
     * @param content
     * @param cardId
     * @param opType
     * @param operateDTO
     * @return
     */
    public static MmpCardConfigOperationLog buildCard(String content, Long cardId, CardActivityOperateEnum opType, OperatorDto operateDTO){
        return build(content, 0, cardId, opType, operateDTO);
    }

    /**
     * 构建配置日志
     * @param content
     * @param configType
     * @param configId
     * @param opType
     * @param operateDTO
     * @return
     */
    public static MmpCardConfigOperationLog build(String content, Integer configType, Long configId,
                                                  CardActivityOperateEnum opType, OperatorDto operateDTO){
        MmpCardConfigOperationLog record = new MmpCardConfigOperationLog();
        record.setConfigId(configId);
        record.setConfigType(configType);
        record.setOperateType(opType.getValue());
        record.setOperationContent(StringUtils.abbreviate(content, 500));
        record.setStatus(0);
        record.setCreateOperId(operateDTO.getOperatorId());
        record.setCreateOperName(operateDTO.getOperatorName());
        record.setCreateTime(new Date());
        record.setUpdateOperId(operateDTO.getOperatorId());
        record.setUpdateOperName(operateDTO.getOperatorName());
        record.setUpdateTime(record.getCreateTime());
        return record;
    }
}
