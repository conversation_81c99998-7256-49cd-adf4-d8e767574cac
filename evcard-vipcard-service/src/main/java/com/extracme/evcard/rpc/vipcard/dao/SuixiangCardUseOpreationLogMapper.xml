<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.rpc.vipcard.dao.SuixiangCardUseOpreationLogMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseOpreationLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_use_id" jdbcType="BIGINT" property="cardUseId" />
    <result column="card_price_id" jdbcType="BIGINT" property="cardPriceId" />
    <result column="purchase_id" jdbcType="BIGINT" property="purchaseId" />
    <result column="card_group" jdbcType="INTEGER" property="cardGroup" />
    <result column="operation_type" jdbcType="BIGINT" property="operationType" />
    <result column="origin_system" jdbcType="VARCHAR" property="originSystem" />
    <result column="ref_key" jdbcType="VARCHAR" property="refKey" />
    <result column="order_seq" jdbcType="VARCHAR" property="orderSeq" />
    <result column="order_operation_days" jdbcType="INTEGER" property="orderOperationDays" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="real_amount" jdbcType="DECIMAL" property="realAmount" />
    <result column="misc_desc" jdbcType="VARCHAR" property="miscDesc" />
    <result column="init_days" jdbcType="INTEGER" property="initDays" />
    <result column="available_days" jdbcType="INTEGER" property="availableDays" />
    <result column="used_days" jdbcType="INTEGER" property="usedDays" />
    <result column="frozen_days" jdbcType="INTEGER" property="frozenDays" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    id, card_use_id, card_price_id, purchase_id, card_group, operation_type, origin_system, 
    ref_key, order_seq, order_operation_days, discount_amount, amount, real_amount, misc_desc, 
    init_days, available_days, used_days, frozen_days, create_time, create_oper_id, create_oper_name, 
    update_time, update_oper_id, update_oper_name, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseOpreationLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from suixiang_card_use_opreation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from suixiang_card_use_opreation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    delete from suixiang_card_use_opreation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseOpreationLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    insert into suixiang_card_use_opreation_log (id, card_use_id, card_price_id, 
      purchase_id, card_group, operation_type, 
      origin_system, ref_key, order_seq, 
      order_operation_days, discount_amount, amount, 
      real_amount, misc_desc, init_days, 
      available_days, used_days, frozen_days, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name, 
      is_deleted)
    values (#{id,jdbcType=BIGINT}, #{cardUseId,jdbcType=BIGINT}, #{cardPriceId,jdbcType=BIGINT}, 
      #{purchaseId,jdbcType=BIGINT}, #{cardGroup,jdbcType=INTEGER}, #{operationType,jdbcType=BIGINT}, 
      #{originSystem,jdbcType=VARCHAR}, #{refKey,jdbcType=VARCHAR}, #{orderSeq,jdbcType=VARCHAR}, 
      #{orderOperationDays,jdbcType=INTEGER}, #{discountAmount,jdbcType=DECIMAL}, #{amount,jdbcType=DECIMAL}, 
      #{realAmount,jdbcType=DECIMAL}, #{miscDesc,jdbcType=VARCHAR}, #{initDays,jdbcType=INTEGER}, 
      #{availableDays,jdbcType=INTEGER}, #{usedDays,jdbcType=INTEGER}, #{frozenDays,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseOpreationLog" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    insert into suixiang_card_use_opreation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cardUseId != null">
        card_use_id,
      </if>
      <if test="cardPriceId != null">
        card_price_id,
      </if>
      <if test="purchaseId != null">
        purchase_id,
      </if>
      <if test="cardGroup != null">
        card_group,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="originSystem != null">
        origin_system,
      </if>
      <if test="refKey != null">
        ref_key,
      </if>
      <if test="orderSeq != null">
        order_seq,
      </if>
      <if test="orderOperationDays != null">
        order_operation_days,
      </if>
      <if test="discountAmount != null">
        discount_amount,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="realAmount != null">
        real_amount,
      </if>
      <if test="miscDesc != null">
        misc_desc,
      </if>
      <if test="initDays != null">
        init_days,
      </if>
      <if test="availableDays != null">
        available_days,
      </if>
      <if test="usedDays != null">
        used_days,
      </if>
      <if test="frozenDays != null">
        frozen_days,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cardUseId != null">
        #{cardUseId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null">
        #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="purchaseId != null">
        #{purchaseId,jdbcType=BIGINT},
      </if>
      <if test="cardGroup != null">
        #{cardGroup,jdbcType=INTEGER},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=BIGINT},
      </if>
      <if test="originSystem != null">
        #{originSystem,jdbcType=VARCHAR},
      </if>
      <if test="refKey != null">
        #{refKey,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null">
        #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="orderOperationDays != null">
        #{orderOperationDays,jdbcType=INTEGER},
      </if>
      <if test="discountAmount != null">
        #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="realAmount != null">
        #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="miscDesc != null">
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="initDays != null">
        #{initDays,jdbcType=INTEGER},
      </if>
      <if test="availableDays != null">
        #{availableDays,jdbcType=INTEGER},
      </if>
      <if test="usedDays != null">
        #{usedDays,jdbcType=INTEGER},
      </if>
      <if test="frozenDays != null">
        #{frozenDays,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseOpreationLogExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    select count(*) from suixiang_card_use_opreation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    update suixiang_card_use_opreation_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardUseId != null">
        card_use_id = #{record.cardUseId,jdbcType=BIGINT},
      </if>
      <if test="record.cardPriceId != null">
        card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="record.purchaseId != null">
        purchase_id = #{record.purchaseId,jdbcType=BIGINT},
      </if>
      <if test="record.cardGroup != null">
        card_group = #{record.cardGroup,jdbcType=INTEGER},
      </if>
      <if test="record.operationType != null">
        operation_type = #{record.operationType,jdbcType=BIGINT},
      </if>
      <if test="record.originSystem != null">
        origin_system = #{record.originSystem,jdbcType=VARCHAR},
      </if>
      <if test="record.refKey != null">
        ref_key = #{record.refKey,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSeq != null">
        order_seq = #{record.orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="record.orderOperationDays != null">
        order_operation_days = #{record.orderOperationDays,jdbcType=INTEGER},
      </if>
      <if test="record.discountAmount != null">
        discount_amount = #{record.discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DECIMAL},
      </if>
      <if test="record.realAmount != null">
        real_amount = #{record.realAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.miscDesc != null">
        misc_desc = #{record.miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.initDays != null">
        init_days = #{record.initDays,jdbcType=INTEGER},
      </if>
      <if test="record.availableDays != null">
        available_days = #{record.availableDays,jdbcType=INTEGER},
      </if>
      <if test="record.usedDays != null">
        used_days = #{record.usedDays,jdbcType=INTEGER},
      </if>
      <if test="record.frozenDays != null">
        frozen_days = #{record.frozenDays,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null">
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null">
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null">
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null">
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    update suixiang_card_use_opreation_log
    set id = #{record.id,jdbcType=BIGINT},
      card_use_id = #{record.cardUseId,jdbcType=BIGINT},
      card_price_id = #{record.cardPriceId,jdbcType=BIGINT},
      purchase_id = #{record.purchaseId,jdbcType=BIGINT},
      card_group = #{record.cardGroup,jdbcType=INTEGER},
      operation_type = #{record.operationType,jdbcType=BIGINT},
      origin_system = #{record.originSystem,jdbcType=VARCHAR},
      ref_key = #{record.refKey,jdbcType=VARCHAR},
      order_seq = #{record.orderSeq,jdbcType=VARCHAR},
      order_operation_days = #{record.orderOperationDays,jdbcType=INTEGER},
      discount_amount = #{record.discountAmount,jdbcType=DECIMAL},
      amount = #{record.amount,jdbcType=DECIMAL},
      real_amount = #{record.realAmount,jdbcType=DECIMAL},
      misc_desc = #{record.miscDesc,jdbcType=VARCHAR},
      init_days = #{record.initDays,jdbcType=INTEGER},
      available_days = #{record.availableDays,jdbcType=INTEGER},
      used_days = #{record.usedDays,jdbcType=INTEGER},
      frozen_days = #{record.frozenDays,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseOpreationLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    update suixiang_card_use_opreation_log
    <set>
      <if test="cardUseId != null">
        card_use_id = #{cardUseId,jdbcType=BIGINT},
      </if>
      <if test="cardPriceId != null">
        card_price_id = #{cardPriceId,jdbcType=BIGINT},
      </if>
      <if test="purchaseId != null">
        purchase_id = #{purchaseId,jdbcType=BIGINT},
      </if>
      <if test="cardGroup != null">
        card_group = #{cardGroup,jdbcType=INTEGER},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=BIGINT},
      </if>
      <if test="originSystem != null">
        origin_system = #{originSystem,jdbcType=VARCHAR},
      </if>
      <if test="refKey != null">
        ref_key = #{refKey,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null">
        order_seq = #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="orderOperationDays != null">
        order_operation_days = #{orderOperationDays,jdbcType=INTEGER},
      </if>
      <if test="discountAmount != null">
        discount_amount = #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="realAmount != null">
        real_amount = #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="miscDesc != null">
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="initDays != null">
        init_days = #{initDays,jdbcType=INTEGER},
      </if>
      <if test="availableDays != null">
        available_days = #{availableDays,jdbcType=INTEGER},
      </if>
      <if test="usedDays != null">
        used_days = #{usedDays,jdbcType=INTEGER},
      </if>
      <if test="frozenDays != null">
        frozen_days = #{frozenDays,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.rpc.vipcard.model.SuixiangCardUseOpreationLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 31 16:13:21 CST 2023.
    -->
    update suixiang_card_use_opreation_log
    set card_use_id = #{cardUseId,jdbcType=BIGINT},
      card_price_id = #{cardPriceId,jdbcType=BIGINT},
      purchase_id = #{purchaseId,jdbcType=BIGINT},
      card_group = #{cardGroup,jdbcType=INTEGER},
      operation_type = #{operationType,jdbcType=BIGINT},
      origin_system = #{originSystem,jdbcType=VARCHAR},
      ref_key = #{refKey,jdbcType=VARCHAR},
      order_seq = #{orderSeq,jdbcType=VARCHAR},
      order_operation_days = #{orderOperationDays,jdbcType=INTEGER},
      discount_amount = #{discountAmount,jdbcType=DECIMAL},
      amount = #{amount,jdbcType=DECIMAL},
      real_amount = #{realAmount,jdbcType=DECIMAL},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      init_days = #{initDays,jdbcType=INTEGER},
      available_days = #{availableDays,jdbcType=INTEGER},
      used_days = #{usedDays,jdbcType=INTEGER},
      frozen_days = #{frozenDays,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>