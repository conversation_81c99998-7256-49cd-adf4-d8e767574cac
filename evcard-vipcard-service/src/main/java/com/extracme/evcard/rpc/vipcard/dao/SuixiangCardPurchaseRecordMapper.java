package com.extracme.evcard.rpc.vipcard.dao;

import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.CardReMindDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardInfoDto;
import com.extracme.evcard.rpc.vipcard.model.*;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SuixiangCardPurchaseRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    int countByExample(SuixiangCardPurchaseRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    int insert(SuixiangCardPurchaseRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    int insertSelective(SuixiangCardPurchaseRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    List<SuixiangCardPurchaseRecord> selectByExample(SuixiangCardPurchaseRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    SuixiangCardPurchaseRecord selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    int updateByExampleSelective(@Param("record") SuixiangCardPurchaseRecord record, @Param("example") SuixiangCardPurchaseRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    int updateByExample(@Param("record") SuixiangCardPurchaseRecord record, @Param("example") SuixiangCardPurchaseRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    int updateByPrimaryKeySelective(SuixiangCardPurchaseRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table suixiang_card_purchase_record
     *
     * @mbggenerated Mon Jan 30 19:27:33 CST 2023
     */
    int updateByPrimaryKey(SuixiangCardPurchaseRecord record);

    int countForPage(@Param("record")SuixiangCardPurchaseRecord record);

    List<SuixiangCardPurchaseRecord> selectForPage(@Param("record") SuixiangCardPurchaseRecord record, @Param("page") Page page);

    /**
     * 将发卡记录 标记为已读
     * @param ids
     * @return
     */
    int readCards(@Param("list") List ids);

    List<CardReMindDto> selectUnreadCardList(@Param("userId") Long userId,
                                             @Param("startDate") Date startDate, @Param("limit") Integer limit);


    /**
     * 合并的卡片提醒
     * @param userId
     * @param startDate
     * @param limit
     * @return
     */
    List<CardReMindDto> selectUnreadCardMergeList(@Param("userId") Long userId,
                                             @Param("startDate") Date startDate, @Param("limit") Integer limit);
    /**
     * 查询时间段内未自动取消的订单
     * @param id
     * @param limit
     * @param startTime
     * @param endTime
     * @return
     */
    List<SuixiangCardPurchaseRecord> selectTimeoutUnpayOrder(@Param("id")Long id,
                                                            @Param("limit")Integer limit,
                                                            @Param("startTime")Date startTime,
                                                            @Param("endTime")Date endTime);
}