package com.extracme.evcard.rpc.vipcard.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.bvm.bo.ServiceResult;
import com.extracme.evcard.bvm.dto.GetAgencyRoleInfoDTO;
import com.extracme.evcard.bvm.dto.RestrictFuelTypeDTO;
import com.extracme.evcard.bvm.dto.RestrictGoodsModelDTO;
import com.extracme.evcard.bvm.dto.agencyPrice.CheckConditionDTO;
import com.extracme.evcard.bvm.dto.agencyPrice.CheckConditionResponse;
import com.extracme.evcard.bvm.dto.store.RestrictVehicleLevelDTO;
import com.extracme.evcard.bvm.service.IAgencyPriceService;
import com.extracme.evcard.membership.core.dto.AgencyInfoDto;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.agency.AgencyDiscountDTO;
import com.extracme.evcard.membership.core.dto.agency.DiscountRuleDTO;
import com.extracme.evcard.membership.core.service.IBaseInfoService;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.agency.IAgencyDiscountService;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.evcard.rpc.order.dto.OrderInfoDto;
import com.extracme.evcard.rpc.order.service.IOrderService;
import com.extracme.evcard.rpc.shop.service.IShopService;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.vehicle.service.IVehicleModelService;
import com.extracme.evcard.rpc.vipcard.dao.*;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.enums.*;
import com.extracme.evcard.rpc.vipcard.manager.SuixiangCardBaseManager;
import com.extracme.evcard.rpc.vipcard.model.*;
import com.extracme.evcard.rpc.vipcard.rest.entity.LowAndPeakSeasonConfig;
import com.extracme.evcard.rpc.vipcard.service.inner.BusinessCardService;
import com.extracme.evcard.rpc.vipcard.service.inner.CityServ;
import com.extracme.evcard.rpc.vipcard.service.inner.MemberCardInnerService;
import com.extracme.evcard.rpc.vipcard.service.store.ConfigLoader;
import com.extracme.evcard.rpc.vipcard.util.ComUtils;
import com.extracme.evcard.rpc.vipcard.util.ConfigUtil;
import com.extracme.evcard.rpc.vipcard.util.Constants;
import com.extracme.evcard.rpc.vipcard.util.DateUtil;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class MemberCardService implements IMemberCardService {

    Logger logger = LoggerFactory.getLogger(MemberCardService.class);

    @Autowired
    MmpUserCardInfoMapper mmpUserCardInfoMapper;

    @Autowired
    MmpCardDefMapper mmpCardDefMapper;

    @Autowired
    MmpCardPurchaseRecordMapper mmpCardPurchaseRecordMapper;

    @Autowired
    MmpCardSalesActivityMapper mmpCardSalesActivityMapper;

    @Resource
    private SuixiangCardUseMapper suixiangCardUseMapper;

    @Resource
    private SuixiangCardBaseMapper suixiangCardBaseMapper;

    @Autowired
    private MemberCardInnerService memberCardInnerService;

    @Autowired
    private IOrderService orderService;
    @Autowired
    private IShopService shopService;
    @Autowired
    private CityMapper cityMapper;
    @Autowired
    private IMemberShipService memberShipService;
    @Autowired
    MmpCardRemindMapper mmpCardRemindMapper;
    @Autowired
    private IAgencyPriceService agencyPriceService;
    @Autowired
    private IAgencyDiscountService agencyDiscountService;
    @Autowired
    private CityServ cityServ;
    @Autowired
    private IVehicleModelService vehicleModelService;
    @Autowired
    private MmpUserCardDiscountInfoMapper userCardDiscountInfoMapper;
    @Autowired
    private MmpUserCardDiscountInfoMapper mmpUserCardDiscountInfoMapper;

    @Autowired
    private MmpUserCardOperationLogMapper mmpUserCardOperationLogMapper;

    @Autowired
    private BusinessCardService businessCardService;

    @Resource
    private SuixiangCardBaseManager suixiangCardBaseManager;

    @Autowired
    private SuixiangCardPurchaseRecordMapper suixiangCardPurchaseRecordMapper;

    @Autowired
    private ISuixiangCardRefundService suixiangCardRefundService;

    @Resource
    private IBaseInfoService baseInfoService;

    @Autowired
    private ConfigLoader configLoader;

    @Override
    public QueryUserCardInfoDto queryUserVipCardInfo(Long id) {

        QueryUserCardInfoDto queryUserCardInfoDto = new QueryUserCardInfoDto();
        /**
         * 查询当前用户会员卡信息
         */
        List<MmpUserCardInfo> mmpUserCardInfos =  mmpUserCardInfoMapper.queryUserVipCardById(id);

        //获取待支付数量
        MmpCardPurchaseRecord mmpCardPurchaseRecord = new MmpCardPurchaseRecord();
        mmpCardPurchaseRecord.setUserId(id);
        mmpCardPurchaseRecord.setStatus(0);
        mmpCardPurchaseRecord.setIssueType(0);
        mmpCardPurchaseRecord.setPaymentStatus(0);
        int count = mmpCardPurchaseRecordMapper.selectCountByCondition(mmpCardPurchaseRecord);
        queryUserCardInfoDto.setWaitPayCardNum(count);
        if (CollectionUtils.isEmpty(mmpUserCardInfos)){
            return queryUserCardInfoDto;
        }

        /**
         * 付费会员卡信息
         */
        List<VipCardInfo> vipCardInfo = new ArrayList<>();
        /**
         * 企业会员卡信息
         */
         List<VipCardInfo> orgCardInfo= new ArrayList<>();
        /**
         * 企业个人卡信息
         */
        List<VipCardInfo> orgUserInfo= new ArrayList<>();
        /**
         * 已经过期会员卡
         */
        List<VipCardInfo> overTimeCardInfo= new ArrayList<>();
        final BigDecimal[] totalDiscountAmount = {BigDecimal.ZERO};
        final Boolean[] agencyCard = {false};
        final List<QueryCardInfoDetail>[] queryCardInfoDetails = new List[]{new ArrayList<>()};
        mmpUserCardInfos.stream().forEach(p->{

            totalDiscountAmount[0] = totalDiscountAmount[0].add(p.getTotalDiscountAmount());
            VipCardInfo cardInfo = new VipCardInfo();
            //  帮助 卡排序
            cardInfo.setPurchaseTime(p.getCreateTime());
            cardInfo.setCardGroup(p.getCardGroup());
            if (p.getCardGroup().equals(1) || p.getCardGroup().equals(2)){


                if (!agencyCard[0]){
                    queryCardInfoDetails[0] = queryOrgCardDetail(id, p.getAgencyId());
                    agencyCard[0] = true;
                }

                if (CollectionUtils.isNotEmpty(queryCardInfoDetails[0])) {
                    queryCardInfoDetails[0].stream().forEach(infoDetail -> {
                        // 设置企业 失效时间
                        cardInfo.setExpiresTime(infoDetail.getExpiresTime());
                        if (infoDetail.getOrgCardType().equals(1) && p.getCardGroup().equals(2)) {
                            cardInfo.setCardId(p.getUserCardNo());
                            cardInfo.setCardUseStatus(p.getCardStatus());
                            if (infoDetail.getDiscountRate() != null){
                                cardInfo.setCardName(p.getCardName());
                                cardInfo.setDiscount(infoDetail.getDiscountRate().intValue());
                            }
                            if (infoDetail.getPeakSeasonDiscountRate() != null) {
                                cardInfo.setPeakSeasonDiscount(infoDetail.getPeakSeasonDiscountRate().intValue());
                            }
                            cardInfo.setLimitCondition(infoDetail.getLimitCondition());
                            cardInfo.setOrgName(infoDetail.getOrgName());
                            orgUserInfo.add(cardInfo);
                        }
                        if (infoDetail.getOrgCardType().equals(2) && p.getCardGroup().equals(1)) {
                            cardInfo.setCardId(p.getUserCardNo());
                            cardInfo.setCardUseStatus(p.getCardStatus());
                            if (infoDetail.getDiscountRate() != null){
                                cardInfo.setCardName(p.getCardName());
                                cardInfo.setDiscount(infoDetail.getDiscountRate().intValue());
                            }
                            if (infoDetail.getPeakSeasonDiscountRate() != null) {
                                cardInfo.setPeakSeasonDiscount(infoDetail.getPeakSeasonDiscountRate().intValue());
                            }
                            cardInfo.setLimitCondition(infoDetail.getLimitCondition());
                            cardInfo.setOrgName(infoDetail.getOrgName());
                            orgCardInfo.add(cardInfo);
                        }
                    });
                }
            }else if (p.getCardGroup().equals(3)){
                //个人卡
                QueryCardInfoDetail queryCardInfoDetail =  queryCardInfoById(p.getCardId(),1,id);
                cardInfo.setCardName(queryCardInfoDetail.getCardName());
                cardInfo.setLimitCondition(queryCardInfoDetail.getLimitCondition());
                cardInfo.setCardType(queryCardInfoDetail.getCardType());
                cardInfo.setCardId(p.getUserCardNo());
                cardInfo.setCardUseStatus(p.getCardStatus());
                cardInfo.setBackUrl(queryCardInfoDetail.getBackUrl());
                cardInfo.setDiscount(queryCardInfoDetail.getDiscount());


                //查询是否可续费
                List<MmpCardSalesActivity> mmpCardSalesActivity = mmpCardSalesActivityMapper.getActivityByCardIdAndStatus(p.getCardId(),2);
                if (CollectionUtils.isEmpty(mmpCardSalesActivity)){
                    cardInfo.setCardStatus(2);
                }
                final BigDecimal[] lowPriceActivity = {null};
                if (CollectionUtils.isNotEmpty(mmpCardSalesActivity)){
                    mmpCardSalesActivity.forEach(activity->{
                        if (queryCardInfoDetail.getTimes() != null && queryCardInfoDetail.getTimes() < activity.getPersonPurchasesLimit()){
                            if (activity.getStock() > 0) {
                                cardInfo.setCardStatus(1);
                                if (lowPriceActivity[0] == null || lowPriceActivity[0].compareTo(activity.getSalesPrice()) > 0) {
                                    lowPriceActivity[0] = activity.getSalesPrice();
                                    cardInfo.setActivityId(activity.getId());
                                }
                            } else {
                                cardInfo.setCardStatus(4);
                            }
                        }else {
                            cardInfo.setCardStatus(3);
                        }
                    });
                }

                if (p.getCardStatus().equals(4) || p.getCardStatus().equals(5)){
                    //作废的
                    cardInfo.setExpiresTime(DateUtil.getFormatDate(p.getUpdateTime(),DateUtil.DATE_TYPE7));
                    overTimeCardInfo.add(cardInfo);
                }else if (p.getCardStatus().equals(3) || (p.getExpiresTime() != null && p.getExpiresTime().getTime()<=System.currentTimeMillis() )){
                    //过期的卡
                    cardInfo.setExpiresTime(DateUtil.getFormatDate(p.getExpiresTime(),DateUtil.DATE_TYPE7));
                    overTimeCardInfo.add(cardInfo);
                }else if (p.getCardStatus().equals(1)){
                    cardInfo.setExpiresTime(DateUtil.getFormatDate(p.getExpiresTime(),DateUtil.DATE_TYPE7));
                    if (p.getExpiresTime().getTime() - System.currentTimeMillis() < 1000 * 60 * 60 * 24 *2){
                        cardInfo.setOverTime(1);
                    }
                    if(queryCardInfoDetail.getPurchaseType() != null && queryCardInfoDetail.getPurchaseType().equals(1)){
                        //是学生卡
                        int userAge = memberShipService.getUserAge(id);
                        if (!(0 < userAge && userAge < 24)){
                            cardInfo.setCardStatus(2);
                        }
                    }
                    vipCardInfo.add(cardInfo);
                }
            }

        });

        if(CollectionUtils.isNotEmpty(overTimeCardInfo)){
            Collections.sort(overTimeCardInfo, new Comparator<VipCardInfo>() {
                @Override
                public int compare(VipCardInfo o1, VipCardInfo o2) {
                    if (o1.getExpiresTime().compareTo(o2.getExpiresTime())<0){
                        return 1;
                    }
                    return 0;
                }
            });
        }

        queryUserCardInfoDto.setTotalDiscountAmount(totalDiscountAmount[0]);
        queryUserCardInfoDto.setOrgCardInfo(orgCardInfo);
        queryUserCardInfoDto.setOrgUserInfo(orgUserInfo);
        queryUserCardInfoDto.setOverTimeCardInfo(overTimeCardInfo);
        queryUserCardInfoDto.setVipCardInfo(vipCardInfo);

        return queryUserCardInfoDto;
    }

    @Override
    public QueryCardInfoDetail queryUserCardDetail(Long userCardNo, Long id) throws BusinessException {

        MmpUserCardInfo mmpUserCardInfo = mmpUserCardInfoMapper.selectByPrimaryKey(userCardNo);
        if (mmpUserCardInfo == null){
            throw new BusinessException(-1,"会员卡不存在");
        }

        QueryCardInfoDetail queryCardInfoDetail = new QueryCardInfoDetail();
        if (mmpUserCardInfo.getCardGroup().equals(3)) {
            //个人卡
            queryCardInfoDetail = queryCardInfoById(mmpUserCardInfo.getCardId(), 1, id);
        } else {
            List<QueryCardInfoDetail> cardInfoDetails = queryOrgCardDetail(id, mmpUserCardInfo.getAgencyId());
            if (CollectionUtils.isEmpty(cardInfoDetails)) {
                return null;
            }
            for (QueryCardInfoDetail p : cardInfoDetails){
                if (mmpUserCardInfo.getCardGroup().equals(1) && p.getOrgCardType().equals(2)) {
                    BeanCopyUtils.copyProperties(p,queryCardInfoDetail);
                    if (p.getDiscountRate() != null){
                        queryCardInfoDetail.setCardName(mmpUserCardInfo.getCardName());
                        queryCardInfoDetail.setDiscount(p.getDiscountRate().intValue());
                    }
                    if (p.getPeakSeasonDiscountRate() != null) {
                        queryCardInfoDetail.setPeakSeasonDiscount(p.getPeakSeasonDiscountRate().intValue());
                    }
                }
                if (mmpUserCardInfo.getCardGroup().equals(2) && p.getOrgCardType().equals(1)) {
                    BeanCopyUtils.copyProperties(p,queryCardInfoDetail);
                    if (p.getDiscountRate() != null){
                        queryCardInfoDetail.setCardName(mmpUserCardInfo.getCardName());
                        queryCardInfoDetail.setDiscount(p.getDiscountRate().intValue());
                    }

                    if (p.getPeakSeasonDiscountRate() != null) {
                        queryCardInfoDetail.setPeakSeasonDiscount(p.getPeakSeasonDiscountRate().intValue());
                    }
                }
            }

        }
        return queryCardInfoDetail;
    }


    /**
     * 获取规则
     * @param ruleDTO
     * @return
     */
    public String getRulers(DiscountRuleDTO ruleDTO){
        String rulersTemplate = "1、折扣说明：旺季%s折，旺季时间为法定节假日及暑假 (%s至%s)，淡季%s折，淡季时间为除旺季以外日期。若订单租期包含任意一天为旺季时间，即视为旺季订单，适用旺季折扣。*若原订单租期属淡季，续租后涉及旺季日期，则整笔订单属旺季订单，最终结算订单金额时需补交费用，若原订单租期属旺季，提前还车后实际租期属淡季，则订单完成后会退还多交金额。\n" +
                "2、抵扣费用说明:仅折扣订单内车辆租金。\n" +
                "3、使用范围:可在EVCARD全国门店使用(门店详情可在APP或小程序内查看)。\n" +
                "4、折扣卡有效期:%s至%s，过期不可使用。";
        try {
            // 获取当前年份的夏天时间
            LocalDate date = LocalDate.now();
            String yearStr = String.valueOf(date.getYear());
            String summerStartDateString = StringUtils.EMPTY;
            String summerEndDateString = StringUtils.EMPTY;
            LowAndPeakSeasonConfig summerConfig = configLoader.getLowAndPeakSeasonConfig(yearStr,8);
            if (summerConfig != null) {
                String startDate = summerConfig.getStartDate();
                String endDate = summerConfig.getEndDate();
                summerStartDateString = DateUtil.getFormatDate(startDate, DateUtil.DATE_TYPE1, DateUtil.DATE_TYPE12);
                summerEndDateString = DateUtil.getFormatDate(endDate, DateUtil.DATE_TYPE1, DateUtil.DATE_TYPE12);
            }

            // 旺季折扣
            Double peakSeasonDiscountRate = ruleDTO.getPeakSeasonDiscountRate();
            Double discountRate = ruleDTO.getDiscountRate();

            String peakSeasonDiscount = new BigDecimal(peakSeasonDiscountRate).divide(new BigDecimal(10)).toPlainString();
            String discount = new BigDecimal(discountRate).divide(new BigDecimal(10)).toPlainString();
            Date validStartTime = ruleDTO.getValidStartTime();
            Date validEndTime = ruleDTO.getValidEndTime();
            String validStartDate = DateUtil.getFormatDate(validStartTime, DateUtil.DATE_TYPE12);
            String validEndDate = DateUtil.getFormatDate(validEndTime, DateUtil.DATE_TYPE12);
            //折扣规则
            return String.format(rulersTemplate,peakSeasonDiscount,summerStartDateString,summerEndDateString,discount,validStartDate,validEndDate);
        } catch (Exception e) {
            logger.error("拼接旺季折扣异常,agencyDiscountRule={}",JSON.toJSONString(ruleDTO),e);
        }

        return null;
    }

    public List<QueryCardInfoDetail> queryOrgCardDetail(Long userId, String agencyId) {
        String agencyExpireTime = StringUtils.EMPTY;
        try {
            AgencyInfoDto agencyInfoDto = baseInfoService.queryAgencyInfoByAgencyId(agencyId);
            if (agencyInfoDto != null) {
                agencyExpireTime = DateUtil.getFormatDate(agencyInfoDto.getExpireTime(), DateUtil.DATE_TYPE15);
            }
        } catch (Exception e) {
            logger.error("查询企业信息异常,agencyId={},userId={}",agencyId, userId,e);
        }

        List<QueryCardInfoDetail> queryCardInfoDetails = new ArrayList<>();
        MembershipBasicInfo membershipBasicInfo = memberShipService.getUserBasicInfoByPkId(userId);
        if (membershipBasicInfo == null) {
            return null;
        }

        AgencyDiscountDTO agencyDiscountDTO = agencyDiscountService.getAgencyDiscountDetail(agencyId, null, true);
        if (agencyDiscountDTO == null) {
            return null;
        }
        //门店限制：替换企业用车平台新接口(join网点限制&门店限制)
        ServiceResult<GetAgencyRoleInfoDTO> agencyRole = agencyPriceService.getAgencyRoleInfo(membershipBasicInfo.getAuthId());
        GetAgencyRoleInfoDTO getAgencyRoleInfoDTO = agencyRole.getResult();
        if (getAgencyRoleInfoDTO == null) {
            return null;
        }

        DiscountRuleDTO agencyDiscountRule = agencyDiscountDTO.getAgencyDiscountRule();
        if (getAgencyRoleInfoDTO.getBusinessPayFlag().equals(1) && agencyDiscountRule != null ) {
            //企业支付
            List<String> limitDetail = new ArrayList<>();
            StringBuilder limitCondition = new StringBuilder();

            // 旺季折扣
            Double peakSeasonDiscountRate = agencyDiscountRule.getPeakSeasonDiscountRate();
            Double discountRate = agencyDiscountRule.getDiscountRate();
            // 折扣规则
            String rulers = getRulers(agencyDiscountRule);
            if (getAgencyRoleInfoDTO.getRestrictContractReviewFlag() == 1) {
                rulers = rulers + "\n5、下单后需企业管理员审核，审核通过后才可取车。";
            }

            QueryCardInfoDetail queryCardInfoDetail = new QueryCardInfoDetail();
//            queryCardInfoDetail.setCardName(new BigDecimal(agencyDiscountDTO.getAgencyDiscountRule().getDiscountRate())
//                    .divide(new BigDecimal(10)).toPlainString() + "折" +cardInfo.getCardName());
            queryCardInfoDetail.setDiscountRate(discountRate);
            // 企业旺季折扣
            queryCardInfoDetail.setPeakSeasonDiscountRate(peakSeasonDiscountRate);

            if (StringUtils.isNotBlank(agencyDiscountDTO.getVehicleNo())){
                limitCondition.append("车牌/");
                limitDetail.add("适用车牌： " + agencyDiscountDTO.getVehicleNo());
            }
            if (CollectionUtils.isNotEmpty(agencyDiscountDTO.getPackageTypeList())){
                limitCondition.append("与套餐互斥/");
                String packageList = "活动套餐： ";
                if (agencyDiscountDTO.getPackageTypeList().contains("2")){
                    packageList = packageList + "包时段套餐,";
                }
                if (agencyDiscountDTO.getPackageTypeList().contains("3")){
                    packageList = packageList + "包时长套餐,";
                }
                if (agencyDiscountDTO.getPackageTypeList().contains("4")){
                    packageList = packageList + "多日折扣套餐,";
                }
                if (agencyDiscountDTO.getPackageTypeList().contains("5")){
                    packageList = packageList + "日租折扣套餐,";
                }
                if (agencyDiscountDTO.getPackageTypeList().contains("6")){
                    packageList = packageList + "日租时长套餐,";
                }
                if (agencyDiscountDTO.getPackageTypeList().contains("7")){
                    packageList = packageList + "日租预约折扣套餐,";
                }if (agencyDiscountDTO.getPackageTypeList().contains("8")){
                    packageList = packageList + "日租预约时长套餐,";
                }

                limitDetail.add(packageList.substring(0,packageList.length()-1));
            }
            if (getAgencyRoleInfoDTO.getRestrictTimeFlag().equals(1)){
                limitCondition.append("用车时段/");
                StringBuilder useCarTime = new StringBuilder();
                useCarTime.append("用车时段： ");
                if (CollectionUtils.isNotEmpty(getAgencyRoleInfoDTO.getRestrictTime())){
                    getAgencyRoleInfoDTO.getRestrictTime().stream().forEach(p->{
                        StringBuilder time = new StringBuilder();
                        if (p.getStartDay().equals(1)){
                            time.append("周一");
                        }if (p.getStartDay().equals(2)){
                            time.append("周二");
                        }if (p.getStartDay().equals(3)){
                            time.append("周三");
                        }if (p.getStartDay().equals(4)){
                            time.append("周四");
                        }if (p.getStartDay().equals(5)){
                            time.append("周五");
                        }if (p.getStartDay().equals(6)){
                            time.append("周六");
                        }if (p.getStartDay().equals(7)){
                            time.append("周日");
                        }
                        time.append(DateUtil.getFormatDate(p.getStartTime(),DateUtil.DATE_TYPE11,DateUtil.DATE_TYPE6));
                        if (p.getEndDay().equals(1)){
                            time.append("周一");
                        }if (p.getEndDay().equals(2)){
                            time.append("周二");
                        }if (p.getEndDay().equals(3)){
                            time.append("周三");
                        }if (p.getEndDay().equals(4)){
                            time.append("周四");
                        }if (p.getEndDay().equals(5)){
                            time.append("周五");
                        }if (p.getEndDay().equals(6)){
                            time.append("周六");
                        }if (p.getEndDay().equals(7)){
                            time.append("周日");
                        }
                        time.append(DateUtil.getFormatDate(p.getEndTime(),DateUtil.DATE_TYPE11,DateUtil.DATE_TYPE6));
                        useCarTime.append(time).append(" ");
                    });
                }
                limitDetail.add(useCarTime.toString());

            }
            if (getAgencyRoleInfoDTO.getRestrictPickupShopFlag().equals(1) || getAgencyRoleInfoDTO.getRestrictReturnShopFlag().equals(1)){
                limitCondition.append("取还网点/");
                final String[] pickUpShop = {"取车网点： "};
                final String[] returnShop = {"还车网点： "};
                if (CollectionUtils.isNotEmpty(getAgencyRoleInfoDTO.getRestrictPickupShop())){
                    getAgencyRoleInfoDTO.getRestrictPickupShop().forEach(p->{
                        pickUpShop[0] = pickUpShop[0] + p.getShopName() +",";
                    });
                    limitDetail.add(pickUpShop[0].substring(0,pickUpShop[0].length()-1));
                }
                if (CollectionUtils.isNotEmpty(getAgencyRoleInfoDTO.getRestrictReturnShop())){
                    getAgencyRoleInfoDTO.getRestrictReturnShop().forEach(p->{
                        returnShop[0] = returnShop[0] + p.getShopName() +",";
                    });
                    limitDetail.add(returnShop[0].substring(0,returnShop[0].length()-1));
                }
            }

            // 门店限制
            Integer restrictPickupStoreFlag = getAgencyRoleInfoDTO.getRestrictPickupStoreFlag();
            Integer restrictReturnStoreFlag = getAgencyRoleInfoDTO.getRestrictReturnStoreFlag();
            if ((restrictPickupStoreFlag != null && restrictPickupStoreFlag.equals(1)) || (restrictReturnStoreFlag != null && restrictReturnStoreFlag.equals(1))) {
                limitCondition.append("取还门店/");
                final String[] pickUpStore = {"取车门店： "};
                final String[] returnStore = {"还车门店： "};
                if (CollectionUtils.isNotEmpty(getAgencyRoleInfoDTO.getRestrictPickupStore())) {
                    getAgencyRoleInfoDTO.getRestrictPickupStore().forEach(p -> {
                        pickUpStore[0] = pickUpStore[0] + p.getStoreName() + ",";
                    });
                    limitDetail.add(pickUpStore[0].substring(0, pickUpStore[0].length() - 1));
                }
                if (CollectionUtils.isNotEmpty(getAgencyRoleInfoDTO.getRestrictReturnStore())) {
                    getAgencyRoleInfoDTO.getRestrictReturnStore().forEach(p -> {
                        returnStore[0] = returnStore[0] + p.getStoreName() + ",";
                    });
                    limitDetail.add(returnStore[0].substring(0, returnStore[0].length() - 1));
                }
            }

            if (getAgencyRoleInfoDTO.getVehicleModelRestrictFlag().equals(1)){
                limitCondition.append("车型单价/");
                limitDetail.add("车型单价： " + getAgencyRoleInfoDTO.getVehicleModelRestrictPrice());
            }
            if (getAgencyRoleInfoDTO.getOrderMaxRestrictFlag().equals(1)){
                limitCondition.append("单笔订单上限/");
                limitDetail.add("单笔订单上限： " + new BigDecimal(getAgencyRoleInfoDTO.getOrderMaxRestrictPrice()).stripTrailingZeros().toPlainString() + "元");

            }
            if (getAgencyRoleInfoDTO.getPersonalRestrictFlag().equals(1)){
                limitCondition.append("个人额度/");
                limitDetail.add("个人额度： " + new BigDecimal(getAgencyRoleInfoDTO.getPersonalRestrictAmount()).stripTrailingZeros().toPlainString() + "元");
            }

            if (getAgencyRoleInfoDTO.getRestrictGoodsModelFlag().equals(1)){
                limitCondition.append("商品车型/");
                List<RestrictGoodsModelDTO> restrictGoodsModel = getAgencyRoleInfoDTO.getRestrictGoodsModel();
                String restrictGoodsModelStr = restrictGoodsModel.stream().map(RestrictGoodsModelDTO::getGoodsModelName).collect(Collectors.joining(","));
                limitDetail.add("商品车型： " + restrictGoodsModelStr);
            }

            if (getAgencyRoleInfoDTO.getRestrictFuelTypeFlag().equals(1)){
                limitCondition.append("能源类型/");
                List<RestrictFuelTypeDTO> restrictFuelType = getAgencyRoleInfoDTO.getRestrictFuelType();
                String restrictFuelTypeStr = restrictFuelType.stream().map(RestrictFuelTypeDTO::getFuelTypeName).collect(Collectors.joining(","));
                limitDetail.add("能源类型： " + restrictFuelTypeStr);
            }

            if (getAgencyRoleInfoDTO.getRestrictVehicleLevelFlag().equals(1)){
                limitCondition.append("车身等级/");
                List<RestrictVehicleLevelDTO> restrictVehicleLevel = getAgencyRoleInfoDTO.getRestrictVehicleLevel();
                String restrictVehicleLevelStr = restrictVehicleLevel.stream().map(RestrictVehicleLevelDTO::getVehicleLevelName).collect(Collectors.joining(","));
                limitDetail.add("车身等级： " + restrictVehicleLevelStr);
            }

            if (StringUtils.isNotBlank(limitCondition.toString())){
                queryCardInfoDetail.setLimitCondition(limitCondition.toString().substring(0,limitCondition.toString().length()-1));
            }
            queryCardInfoDetail.setLimitDetail(limitDetail);
            queryCardInfoDetail.setOrgCardType(2);
            queryCardInfoDetail.setOrgName(agencyDiscountDTO.getAgencyName());
            queryCardInfoDetail.setExpiresTime(agencyExpireTime);
            queryCardInfoDetail.setRules(rulers);
            queryCardInfoDetails.add(queryCardInfoDetail);
        }

        DiscountRuleDTO personDiscountRule = agencyDiscountDTO.getPersonDiscountRule();
        if (getAgencyRoleInfoDTO.getPersonalPayFlag().equals(1) && personDiscountRule != null) {
            //个人支付

            List<String> limitDetail = new ArrayList<>();
            StringBuilder limitCondition = new StringBuilder();
            //折扣规则
            String rulers = getRulers(personDiscountRule);
            QueryCardInfoDetail queryCardInfoDetail = new QueryCardInfoDetail();
//            queryCardInfoDetail.setCardName(new BigDecimal(agencyDiscountDTO.getPersonDiscountRule().getDiscountRate()).
//                    divide(new BigDecimal(10)).toPlainString() + "折" +cardInfo.getCardName());
            queryCardInfoDetail.setDiscountRate(personDiscountRule.getDiscountRate());
            // 企业个人旺季折扣
            queryCardInfoDetail.setPeakSeasonDiscountRate(personDiscountRule.getPeakSeasonDiscountRate());
            if (StringUtils.isNotBlank(agencyDiscountDTO.getVehicleNo())){
                limitCondition.append("车牌/");
                limitDetail.add("适用车牌： " + agencyDiscountDTO.getVehicleNo());
            }
            if (CollectionUtils.isNotEmpty(agencyDiscountDTO.getPackageTypeList())){
                limitCondition.append("与套餐互斥/");
                String packageList = "活动套餐： ";
                if (agencyDiscountDTO.getPackageTypeList().contains("2")){
                    packageList = packageList + "包时段套餐,";
                }
                if (agencyDiscountDTO.getPackageTypeList().contains("3")){
                    packageList = packageList + "包时长套餐,";
                }
                if (agencyDiscountDTO.getPackageTypeList().contains("4")){
                    packageList = packageList + "多日折扣套餐,";
                }
                if (agencyDiscountDTO.getPackageTypeList().contains("5")){
                    packageList = packageList + "日租折扣套餐,";
                }
                if (agencyDiscountDTO.getPackageTypeList().contains("6")){
                    packageList = packageList + "日租时长套餐,";
                }
                if (agencyDiscountDTO.getPackageTypeList().contains("7")){
                    packageList = packageList + "日租预约折扣套餐,";
                }if (agencyDiscountDTO.getPackageTypeList().contains("8")){
                    packageList = packageList + "日租预约时长套餐,";
                }
                limitDetail.add(packageList.substring(0, packageList.length()-1));
            }
            if (StringUtils.isNotBlank(limitCondition.toString())){
                queryCardInfoDetail.setLimitCondition(limitCondition.toString().substring(0,limitCondition.toString().length()-1));
            }
            queryCardInfoDetail.setLimitDetail(limitDetail);
            queryCardInfoDetail.setOrgCardType(1);
            queryCardInfoDetail.setOrgName(agencyDiscountDTO.getAgencyName());
            queryCardInfoDetail.setExpiresTime(agencyExpireTime);
            queryCardInfoDetail.setRules(rulers);
            queryCardInfoDetails.add(queryCardInfoDetail);
        }
        return queryCardInfoDetails;

    }

    @Override
    public QueryCardInfoDetail queryCardInfoById(Long cardId, Integer type, Long id) {

        QueryCardInfoDetail queryCardInfoDetail = new QueryCardInfoDetail();

        MmpCardDef mmpCardDef = mmpCardDefMapper.selectByPrimaryKey(cardId);
        if (mmpCardDef == null){
            return queryCardInfoDetail;
        }

        BeanCopyUtils.copyProperties(mmpCardDef,queryCardInfoDetail);
        queryCardInfoDetail.setCardDefId(mmpCardDef.getCardId());
        if(StringUtils.isNotBlank(mmpCardDef.getBackUrl())){
            queryCardInfoDetail.setBackUrl(ConfigUtil.getConfigPropertiesStr("web_url") + mmpCardDef.getBackUrl());
        }


        List<String> limitDetail = new ArrayList<>();
        StringBuilder limitCondition = new StringBuilder();

        if (mmpCardDef.getMaxValue() != null && mmpCardDef.getMaxValue().compareTo(BigDecimal.ZERO)>0){
            limitDetail.add("单笔最高可抵： " + mmpCardDef.getMaxValue().stripTrailingZeros().toPlainString() + "元");
            limitCondition.append("最高可抵扣"+ mmpCardDef.getMaxValue().stripTrailingZeros().toPlainString() + "元/");
        }
        String availableDaysOfWeek = mmpCardDef.getAvailableDaysOfWeek();
        String pickUpStartTime = mmpCardDef.getStartTime();
        String pickUpEndTime = mmpCardDef.getEndTime();
        if (StringUtils.isNotBlank(pickUpStartTime) && StringUtils.isNotBlank(pickUpEndTime)
                && "000000".equals(pickUpStartTime) && "240000".equals(pickUpEndTime)) {
            pickUpStartTime = StringUtils.EMPTY;
            pickUpEndTime = StringUtils.EMPTY;
        }
        if (StringUtils.isNotBlank(availableDaysOfWeek)
                || StringUtils.isNotBlank(pickUpStartTime)
                || StringUtils.isNotBlank(pickUpEndTime)) {
            StringBuffer pickTimeLimitDesc = new StringBuffer("取车时段：限");
            StringBuffer weekLimit = new StringBuffer();
            if (StringUtils.isNotBlank(mmpCardDef.getAvailableDaysOfWeek())){
                String[] weekDescArray = new String[]{"周一、", "周二、", "周三、", "周四、", "周五、", "周六、", "周日"};
                String[] weekArray = mmpCardDef.getAvailableDaysOfWeek().split(",");
                for (String s : weekArray) {
                    weekLimit.append(weekDescArray[Integer.valueOf(s) - 1]);
                }
                String weekDesc = ComUtils.formatEndChar(weekLimit.toString(),"、");
                pickTimeLimitDesc.append(weekDesc);
            }
            if (StringUtils.isNotBlank(pickUpStartTime)) {
                pickTimeLimitDesc.append(DateUtil.getFormatDate(pickUpStartTime, DateUtil.timeShort, DateUtil.DATE_TYPE6));
            }
            if (StringUtils.isNotBlank(pickUpEndTime)) {
                pickTimeLimitDesc.append("-").append(pickUpEndTime.substring(0,2) + ":" + pickUpEndTime.substring(2,4));
            }
            limitDetail.add(pickTimeLimitDesc.toString());
            limitCondition.append("取车时段/");
        }

        if (mmpCardDef.getDurationLimit() != null && mmpCardDef.getDurationLimit() >0){
            limitDetail.add("用车时长： " + DateUtil.formatTimeDesc(mmpCardDef.getDurationLimit()) + "及以上");
            limitCondition.append("用车时长/");
        }

        if (StringUtils.isNotBlank(mmpCardDef.getCityLimit())){
            String[] split = mmpCardDef.getCityLimit().split(",");
            StringBuffer cityLimitBuffer = new StringBuffer("用车区域：限");
            for (String s : split) {
                cityLimitBuffer.append(cityServ.getCitynameByCityid(Long.valueOf(s))).append("、");
            }
            String cityLimitDesc = ComUtils.formatEndChar(cityLimitBuffer.toString(),"、");
            limitDetail.add(cityLimitDesc);
            limitCondition.append("用车区域/");
        }
        //门店
        String storeLimit = memberCardInnerService.getStoreDesc(mmpCardDef.getStoreIds());
        if(StringUtils.isNotBlank(storeLimit)) {
            StringBuffer storeLimitBuffer = new StringBuffer("可用门店： 限");
            storeLimitBuffer.append(storeLimit.replace(Constants.STR_COMMA_ZH, Constants.STR_SPLIT_ZH));
            limitDetail.add(storeLimitBuffer.toString());
            limitCondition.append("门店/");
        }
        //车型
        String vehicleModelLimit = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(mmpCardDef.getGoodsModelId())){
            vehicleModelLimit = memberCardInnerService.getGoodsVehicleModelDesc(mmpCardDef.getGoodsModelId());
        }
        if(StringUtils.isNotBlank(vehicleModelLimit)) {
            StringBuffer vehicleModelLimitBuffer = new StringBuffer("可用车型： 限");
            vehicleModelLimitBuffer.append(vehicleModelLimit.replace(Constants.STR_COMMA_ZH, Constants.STR_SPLIT_ZH));
            limitDetail.add(vehicleModelLimitBuffer.toString());
            limitCondition.append("车型/");
        }
        //产品线大类
        String rentMethodGroupLimit = memberCardInnerService.getRentMethodGroupDesc(mmpCardDef.getRentMethodGroup());
        if (StringUtils.isNotBlank(rentMethodGroupLimit)) {
            StringBuffer rentMethodGroupBuffer = new StringBuffer("产品线： 限");
            rentMethodGroupBuffer.append(rentMethodGroupLimit.replace(Constants.STR_COMMA_ZH, Constants.STR_SPLIT_ZH));
            limitDetail.add(rentMethodGroupBuffer.toString());
            limitCondition.append("产品线/");
        }
        //单位时间段内累计折扣
        if (mmpCardDef.getTotalDiscountAmount() != null && mmpCardDef.getTotalDiscountAmount() >0) {
            //？ 无需显示折扣上限
            String rentDesc = "该卡单位时间周期内累计总折扣上限： " + mmpCardDef.getTotalDiscountAmount() + "元";
        }
        queryCardInfoDetail.setLimitDetail(limitDetail);
        queryCardInfoDetail.setLimitCondition(limitCondition.toString().substring(0,limitCondition.toString().length()-1));
        /**
         * 根据卡片类型与有效期类型
         */
        CardTypeEnum cardType = CardTypeEnum.getCardType(mmpCardDef.getCardType());
        Integer effectiveDays = cardType.getDays();
        queryCardInfoDetail.setCardDesc("购买立即生效，单张会员卡生效后" + effectiveDays + "天过期，多张卡可累加");

        if (type != null && type.equals(1) && id != null){
            //会员该卡详情
            MmpUserCardInfo mmpUserCardInfo = mmpUserCardInfoMapper.queryUserVipCardByIdAndCard(id,cardId);
            if (mmpUserCardInfo != null){
                queryCardInfoDetail.setCreateTime(DateUtil.getFormatDate(mmpUserCardInfo.getStartTime(),DateUtil.DATE_TYPE7));
                queryCardInfoDetail.setExpiresTime(DateUtil.getFormatDate(mmpUserCardInfo.getExpiresTime(),DateUtil.DATE_TYPE7));
            }
            if (mmpUserCardInfo != null && mmpUserCardInfo.getCardStatus().equals(5)){
                queryCardInfoDetail.setExpiresTime(DateUtil.getFormatDate(mmpUserCardInfo.getUpdateTime(),DateUtil.DATE_TYPE7));
            }
            queryCardInfoDetail.setCardDesc("有效期至 " + DateUtil.getFormatDate(mmpUserCardInfo.getExpiresTime(),DateUtil.DATE_TYPE7));

            //查询卡的购买记录
            MmpCardPurchaseRecord mmpCardPurchaseRecord = new MmpCardPurchaseRecord();
            mmpCardPurchaseRecord.setUserId(id);
            mmpCardPurchaseRecord.setCardId(cardId);
            mmpCardPurchaseRecord.setStatus(0);
            mmpCardPurchaseRecord.setIssueType(0);
            List<MmpCardPurchaseRecord> record = mmpCardPurchaseRecordMapper.selectBySelective(mmpCardPurchaseRecord);
            final BigDecimal[] totalAmount = {BigDecimal.ZERO};

            queryCardInfoDetail.setTimes(0);
            if (CollectionUtils.isNotEmpty(record)){
                final Integer[] times = {0};
                record.forEach(p->{
                    if (Arrays.asList(0,1).contains(p.getPaymentStatus())) {
                        totalAmount[0] = totalAmount[0].add(new BigDecimal(p.getRealAmount()));
                        times[0] = times[0] + p.getQuantity();
                    }
                });
                queryCardInfoDetail.setTimes(times[0]);
            }
            queryCardInfoDetail.setTotalAmount(totalAmount[0]);
            //会员卡使用限制详情
            MmpUserCardDiscountInfo userCardDiscountInfo = userCardDiscountInfoMapper.userCardDiscount(mmpUserCardInfo.getUserCardNo());
            if (userCardDiscountInfo != null){
                UserCardDiscountDetail cardDiscountDetail = new UserCardDiscountDetail();
                BeanCopyUtils.copyProperties(userCardDiscountInfo,cardDiscountDetail);
                if (userCardDiscountInfo.getStartTime() != null && userCardDiscountInfo.getEndTime() != null){
                    cardDiscountDetail.setStartTime(DateUtil.getFormatDate(userCardDiscountInfo.getStartTime(),DateUtil.DATE_TYPE7));
                    cardDiscountDetail.setEndTime(DateUtil.getFormatDate(userCardDiscountInfo.getEndTime(),DateUtil.DATE_TYPE7));
                }
                queryCardInfoDetail.setCardDiscountDetail(cardDiscountDetail);
            }
        }
        return queryCardInfoDetail;
    }

    @Override
    public List<QueryAvailableCardInfoDto> getAvailableCardByOrder(GetAvailableCardByOrderInputDto inputDto) {
        logger.info("订单可用会员卡列表，入参：" + inputDto.toString());
        String orderSeq = inputDto.getOrderSeq();
        /**
         * 1.查询订单信息
         */
        List<QueryAvailableCardInfoDto> resultList = new ArrayList<>();
        OrderInfoDto orderInfo = orderService.getOrderInfoById(orderSeq);
        if (orderInfo != null) {
            if (orderInfo.getPaymentStatus() >= 5) {
                Integer orderRentMethod = orderInfo.getRentMethod();
                String pickUpShopSeq = orderInfo.getPickupStoreSeq();
                String returnShopSeq = orderInfo.getReturnStoreSeq();
                String orderPickUpDateTime = orderInfo.getPickupdatetime();
                Date orderPickUpDate = DateUtil.getDateFromStr(orderPickUpDateTime, DateUtil.dtLong);
                Date orderReturnDate = DateUtil.getDateFromStr(orderInfo.getReturndatetime(), DateUtil.dtLong);
                Integer orderCostTime = orderInfo.getCostTime();
                Integer orderVehicleModelSeq = orderInfo.getPayItem();

                GetAvailableCardByUseConditionInputDto conditionInputDto = new GetAvailableCardByUseConditionInputDto(
                        inputDto.getOrderAmount(), inputDto.getRentAmount(),inputDto.getUnitPrice(),
                        inputDto.getActivityType(), orderInfo.getAuthId(),
                        orderPickUpDate, orderReturnDate, orderCostTime,
                        pickUpShopSeq, returnShopSeq,
                        orderVehicleModelSeq, orderRentMethod, orderInfo.getVehicleNo());
                resultList = getAvailableCardByUseCondition(conditionInputDto);
            }
        }
        return resultList;
    }

    @Override
    public List<QueryAvailableCardInfoDto> getAvailableCardByUseCondition(GetAvailableCardByUseConditionInputDto inputDto) {
        logger.info("getAvailableCardByUseCondition根据用车条件获取可用会员卡列表，入参：" + JSON.toJSONString(inputDto));
        BigDecimal orderAmount = inputDto.getOrderAmount();
        BigDecimal rentAmount = inputDto.getRentAmount();
        BigDecimal unitPrice = inputDto.getUnitPrice();
        Integer activityType = inputDto.getActivityType();
        String authId = inputDto.getAuthId();
        List<QueryAvailableCardInfoDto> resultList = new ArrayList<>();
        /**
         * 2.查询用户信息
         */
        MembershipBasicInfo memberInfo = memberShipService.getUserBasicInfo(authId, (short) 0);
        Long pkId = memberInfo.getPkId();
        /**
         * 4.查询我的会员卡列表
         */
        Date nowTime = new Date();
        List<MmpUserCardInfo> effectiveCardList = mmpUserCardInfoMapper.queryEffectiveUserVipCardById(pkId);
        if (CollectionUtils.isNotEmpty(effectiveCardList)) {
            List<QueryAvailableCardInfoDto> availableList = new ArrayList<>();
            List<QueryAvailableCardInfoDto> unAvailableList = new ArrayList<>();
            /**
             * 付费会员卡
             */
            List<MmpUserCardInfo> payEffectiveCardList = effectiveCardList.stream().
                    filter(p -> p.getCardGroup() == 3).
                    filter(p -> p.getStartTime().getTime() <= nowTime.getTime()).
                    filter(p -> p.getExpiresTime().getTime() > nowTime.getTime()).
                    collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(payEffectiveCardList)) {
                GetEffectiveActivityCardInput cardInput = new GetEffectiveActivityCardInput();
                BeanUtils.copyProperties(inputDto, cardInput);
                payCardInfo(payEffectiveCardList, availableList, unAvailableList, cardInput, rentAmount);
            }

            /**
             * 企业会员卡
             */
            AgencyDiscountDTO agencyDiscountDetail = agencyDiscountService.getAgencyDiscountDetail(memberInfo.getAgencyId(), null, true);
            if (agencyDiscountDetail != null) {
                /**
                 * 查询还车企业折扣条件
                 */
                CheckConditionDTO businessCondition = null;
                CheckConditionDTO personalCondition = null;
                BusinessCardCheckExtInput checkExtInput = new BusinessCardCheckExtInput();
                checkExtInput.setAuthId(memberInfo.getAuthId());
                checkExtInput.setAmount(orderAmount);
                checkExtInput.setUnitPrice(unitPrice);
                checkExtInput.setActivityType(activityType);
                checkExtInput.setAgencyId(memberInfo.getAgencyId());
                CheckConditionResponse checkConditionResponse = businessCardService.checkCondition(inputDto, checkExtInput);

                if (checkConditionResponse != null) {
                    businessCondition = checkConditionResponse.getBusinessCondition();
                    personalCondition = checkConditionResponse.getPersonalCondition();
                }
                Optional<MmpUserCardInfo> businessCardOption = effectiveCardList.stream().filter(p -> p.getCardGroup() == 1).findFirst();
                if (businessCardOption.isPresent() && agencyDiscountDetail.getAgencyDiscountRule() != null) {
                    if (businessCondition != null) {
                        MmpUserCardInfo businessCardInfo = businessCardOption.get();
                        DiscountRuleDTO agencyDiscountRule = agencyDiscountDetail.getAgencyDiscountRule();
                        businessCardInfo(businessCondition, businessCardInfo, agencyDiscountRule, availableList, unAvailableList, rentAmount, inputDto.getOrderCostTime(),inputDto.getOrderStartDate(),inputDto.getOrderEndDate());
                    }
                }

                /**
                 * 企业个人会员卡
                 */
                Optional<MmpUserCardInfo> businessPersonCardOption = effectiveCardList.stream().filter(p -> p.getCardGroup() == 2).findFirst();
                if (businessPersonCardOption.isPresent() && agencyDiscountDetail.getPersonDiscountRule() != null) {
                    if (personalCondition != null) {
                        MmpUserCardInfo businessPersonCardInfo = businessPersonCardOption.get();
                        DiscountRuleDTO personDiscountRule = agencyDiscountDetail.getPersonDiscountRule();
                        businessCardInfo(personalCondition, businessPersonCardInfo, personDiscountRule, availableList, unAvailableList, rentAmount, inputDto.getOrderCostTime(), inputDto.getOrderStartDate(), inputDto.getOrderEndDate());
                    }
                }
            }
            /**
             * 组装返回数据
             */
            Collections.sort(availableList);
            resultList.addAll(availableList);
            resultList.addAll(unAvailableList);
        }
        logger.info("getAvailableCardByUseCondition根据用车条件获取可用会员卡列表，authId={}, result={}", authId, JSON.toJSONString(resultList));
        return resultList;
    }


    @Override
    public QueryAvailableCardInfoDto checkCardByUseCondition(CheckCardByUseConditionInput input) {
        return checkCardByUseCondition(input, input.getUserCardNo(),
                input.getDiscountRate(), input.getOrderTime(), input.getOrderFrozenAmount(),
                input.getNeedCheckUsedMaxValue());
    }

    @Override
    public QueryAvailableCardInfoDto checkCardByUseCondition(GetAvailableCardByUseConditionInputDto inputDto,
                                                             Long userCardNo,
                                                             BigDecimal discountRate,
                                                             Date orderTime,
                                                             BigDecimal orderFrozenAmount,
                                                             Boolean needCheckUsedMaxValue) {
        logger.info("根据用车条件判断是否能折扣,入参：inputDto=" + JSON.toJSONString(inputDto) + ",userCardNo=" + userCardNo +
                ",discountRate=" + discountRate + ",orderTime=" + orderTime + ",orderFrozenAmount=" + orderFrozenAmount);
        BigDecimal orderAmount = inputDto.getOrderAmount();
        BigDecimal rentAmount = inputDto.getRentAmount();
        BigDecimal unitPrice = inputDto.getUnitPrice();
        Integer activityType = inputDto.getActivityType();
        String authId = inputDto.getAuthId();
        if (orderFrozenAmount == null) {
            orderFrozenAmount = BigDecimal.ZERO;
        }
        QueryAvailableCardInfoDto infoDto = new QueryAvailableCardInfoDto();
        /**
         * 0. 预处理用车条件
         */
        memberCardInnerService.preProcessOrderCondition(inputDto);
        Long returnCityId = inputDto.getReturnCity();
        Long pickUpCityId = inputDto.getPickUpCity();

        /**
         * 1.查询会员卡信息
         */
        MmpUserCardInfo memberCardInfo = mmpUserCardInfoMapper.selectByPrimaryKey(userCardNo);
        /**
         * 2.查询用户信息
         */
        MembershipBasicInfo memberInfo = memberShipService.getUserBasicInfo(authId, (short) 0);
        if (memberCardInfo.getCardGroup() == 3) {
            /**
             * 付费会员卡
             */
            Integer orderRentMethod = inputDto.getRentMethod();
            String pickUpShopSeq = ComUtils.getValidSeq(inputDto.getPickUpShopSeq());
            String returnShopSeq = ComUtils.getValidSeq(inputDto.getReturnShopSeq());
            Date orderPickUpDate = inputDto.getOrderPickUpDate();
            String orderPickUpDateTime = DateUtil.getFormatDate(orderPickUpDate, DateUtil.dtLong);
            Integer orderCostTime = inputDto.getOrderCostTime();
            //用车资产车型
            Integer orderVehicleModelSeq = inputDto.getVehicleModelSeq();
            //用车商品车型
            Integer orderGoodsVehicleModelSeq = inputDto.getGoodsModelId();

            MmpCardDef cardModel = mmpCardDefMapper.selectByPrimaryKey(memberCardInfo.getCardId());
            infoDto.setUserCardNo(memberCardInfo.getUserCardNo());
            infoDto.setCardGroup(memberCardInfo.getCardGroup());
            infoDto.setCardName(memberCardInfo.getCardName());
            infoDto.setMaxValue(cardModel.getMaxValue());
            if (StringUtils.isNotBlank(cardModel.getBackUrl())){
                infoDto.setBackUrl(ConfigUtil.getConfigPropertiesStr("web_url") + cardModel.getBackUrl());
            }
            /**
             * 5.校验限制条件，订单是否能使用此会员卡
             */
            boolean availabilityFlag = true;
            StringBuffer unAvailableBuffer = new StringBuffer();
            //取车时间 星期
            boolean pickTimeCheck = true;
            String availableDaysOfWeek = cardModel.getAvailableDaysOfWeek();
            if (StringUtils.isNotBlank(availableDaysOfWeek)) {
                Instant instant = orderPickUpDate.toInstant();
                LocalDate now = instant.atZone(ZoneId.systemDefault()).toLocalDate();
                int dayOfWeek = now.getDayOfWeek().getValue();
                if (!availableDaysOfWeek.contains(String.valueOf(dayOfWeek))) {
                    availabilityFlag = false;
                    pickTimeCheck = false;
                    unAvailableBuffer.append("取车时段不可用/");
                }
            }
            //取车时间 时间段
            if (pickTimeCheck) {
                String pickUpStartTime = cardModel.getStartTime();
                String pickUpEndTime = cardModel.getEndTime();
                //不再校验取还车时段
//                if (StringUtils.isNotBlank(pickUpStartTime) && StringUtils.isNotBlank(pickUpEndTime)) {
//                    if (!("000000".equals(pickUpStartTime) && "240000".equals(pickUpEndTime))) {
//                        String pickDateDay = orderPickUpDateTime.substring(0, 8);
//                        String pickStartTime = pickDateDay + pickUpStartTime;
//                        String pickEndTime = pickDateDay + pickUpEndTime;
//                        Date pickStartDate = DateUtil.getDateFromStr(pickStartTime, DateUtil.dtLong);
//                        Date pickEndDate = DateUtil.getDateFromStr(pickEndTime, DateUtil.dtLong);
//                        if (orderPickUpDate.before(pickStartDate) || orderPickUpDate.after(pickEndDate)) {
//                            availabilityFlag = false;
//                            unAvailableBuffer.append("取车时段不可用/");
//                        }
//                    }
//                }
            }
            //用车时长
            Integer durationLimit = cardModel.getDurationLimit();
            if (durationLimit > 0) {
                if (orderCostTime < durationLimit) {
                    availabilityFlag = false;
                    unAvailableBuffer.append("用车时长不可用/");
                }
            }
            //用车区域
            String cityLimit = cardModel.getCityLimit();
            if (StringUtils.isNotBlank(cityLimit)) {
                List<Integer> cityIdList = ComUtils.turnToList(cityLimit, ",");
                if (pickUpCityId == null || returnCityId == null || !cityIdList.contains(pickUpCityId.intValue()) || !cityIdList.contains(returnCityId.intValue())) {
                //if (returnCityId == null || !cityIdList.contains(pickUpCityId) || !cityIdList.contains(returnCityId)) {
                    availabilityFlag = false;
                    unAvailableBuffer.append("用车区域不可用/");
                }
            }
            //可用车型
            String vehicleModelLimit = cardModel.getVehicleModelLimit();
            if (StringUtils.isNotBlank(vehicleModelLimit) && orderVehicleModelSeq != null) {
                List<Integer> vehicleModelSeqList = ComUtils.turnToList(vehicleModelLimit, ",");
                if (!vehicleModelSeqList.contains(orderVehicleModelSeq)) {
                    availabilityFlag = false;
                    unAvailableBuffer.append("车型不可用/");
                }
            }
            //处理商品车型校验
            String goodsVehicleModelLimit = cardModel.getGoodsModelId();
            if (StringUtils.isNotBlank(goodsVehicleModelLimit)) {
                if(orderGoodsVehicleModelSeq != null) {
                    List<Integer> vehicleModelSeqList = ComUtils.turnToList(goodsVehicleModelLimit, ",");
                    if (!vehicleModelSeqList.contains(orderGoodsVehicleModelSeq)) {
                        availabilityFlag = false;
                        unAvailableBuffer.append("车型不可用/");
                    }
                }

            }

            //新增： 若指定了取车门店，则校验可用门店限制(限制取车门店)
            String orderPickUpStore = inputDto.getPickUpStoreId();
            String storeId = cardModel.getStoreIds();
            if (StringUtils.isNotBlank(storeId) && StringUtils.isNotBlank(orderPickUpStore)) {
                List<Integer> storeIdList = ComUtils.turnToList(storeId, ",");
                if (!storeIdList.contains(orderVehicleModelSeq)) {
                    availabilityFlag = false;
                    unAvailableBuffer.append("取车门店不可用/");
                }
            }

            //产品线大类校验
            String rentMethodGroup = cardModel.getRentMethodGroup();
            if (StringUtils.isNotBlank(rentMethodGroup)) {
                List<Integer> rentMethodList = ComUtils.turnToList(rentMethodGroup, ",");
                Integer orderRentMethodGroup = memberCardInnerService.getRentMethodGroup(orderRentMethod);
                if(!rentMethodList.contains(orderRentMethodGroup)) {
                    availabilityFlag = false;
                    unAvailableBuffer.append("产品线不可用/");
                }
            } else {
                //未指定大类，则校验具体产品线
                String rentMethod = cardModel.getRentMethod();
                if (StringUtils.isNotBlank(rentMethod)) {
                    List<Integer> rentMethodList = ComUtils.turnToList(rentMethod, ",");
                    if (!rentMethodList.contains(orderRentMethod)) {
                        availabilityFlag = false;
                        unAvailableBuffer.append("产品线不可用/");
                    }
                }
            }

            //最高可抵扣
            BigDecimal maxValue = cardModel.getMaxValue();
            //如果有单笔最多抵扣X元，则判断冻结金额是否已经达到抵扣上限,计算剩余的最高抵扣金额
            BigDecimal remainderMaxValue = maxValue;
            if (maxValue.compareTo(BigDecimal.ZERO) > 0 && needCheckUsedMaxValue) {
                if (orderFrozenAmount != null && orderFrozenAmount.compareTo(BigDecimal.ZERO) > 0) {
                    remainderMaxValue = maxValue.subtract(orderFrozenAmount);
                    if (remainderMaxValue.compareTo(BigDecimal.ZERO) <= 0) {
                        availabilityFlag = false;
                        unAvailableBuffer.append("已达折扣上限/");
                    }
                }
            }
            //累计额度
            BigDecimal remainderDiscountAmount = null;
            MmpUserCardDiscountInfo mmpUserCardDiscountInfo = mmpUserCardDiscountInfoMapper.getUserDiscountByTime(memberCardInfo.getUserCardNo(), orderTime);
            if (mmpUserCardDiscountInfo != null) {
                remainderDiscountAmount = mmpUserCardDiscountInfo.getTotalDiscountAmount().subtract(mmpUserCardDiscountInfo.getDiscountAmount());
                if (!needCheckUsedMaxValue) {
                    remainderDiscountAmount = remainderDiscountAmount.add(orderFrozenAmount);
                }
                if (remainderDiscountAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    availabilityFlag = false;
                    unAvailableBuffer.append(DateUtil.getFormatDate(mmpUserCardDiscountInfo.getStartTime(), DateUtil.DATE_TYPE7))
                            .append(" - ")
                            .append(DateUtil.getFormatDate(mmpUserCardDiscountInfo.getEndTime(), DateUtil.DATE_TYPE7))
                            .append("有效期内折扣上限额度已用完/");
                }
            }

            infoDto.setAvailabilityFlag(availabilityFlag?1:0);
            if (discountRate == null) {
                discountRate = new BigDecimal(cardModel.getDiscount()).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            int discountInt = discountRate.multiply(new BigDecimal(100)).intValue();
            if (!availabilityFlag) {
                String unAvailableDesc = ComUtils.formatEndChar(unAvailableBuffer.toString(),"/");
                infoDto.setDiscount(discountInt);
                infoDto.setUnAvailableDesc(unAvailableDesc);
            } else {
                /**
                 * 6.计算折扣减免金额
                 */
                BigDecimal payAmount = rentAmount.multiply(discountRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal deductionAmount = rentAmount.subtract(payAmount);
                if (remainderMaxValue.compareTo(BigDecimal.ZERO) > 0) {
                    if (deductionAmount.compareTo(remainderMaxValue) > 0) {
                        deductionAmount = remainderMaxValue;
                    }
                }
                if (remainderDiscountAmount != null && remainderDiscountAmount.compareTo(BigDecimal.ZERO) > 0) {
                    if (deductionAmount.compareTo(remainderDiscountAmount) > 0) {
                        deductionAmount = remainderDiscountAmount;
                    }
                }
                infoDto.setDiscount(discountInt);
                infoDto.setDeductionAmount(deductionAmount);
            }
        } else {
            if(memberCardInfo.getStatus() == 1
                    || !memberInfo.getAgencyId().equals(memberCardInfo.getAgencyId())) {
                logger.error("企业卡当前已无效或会员关联企业已变更, userCardNo={}, memAgency={}", userCardNo, memberInfo.getAgencyId());
                infoDto.setAvailabilityFlag(0);
                infoDto.setUnAvailableDesc("企业卡已无效");
                return infoDto;
            }

            /**
             * 企业会员卡
             */
            AgencyDiscountDTO agencyDiscountDetail = agencyDiscountService.getAgencyDiscountDetail(memberInfo.getAgencyId(), null, true);
            if (agencyDiscountDetail != null) {
                /**
                 * 查询还车企业折扣条件
                 */
                CheckConditionDTO businessCondition = null;
                CheckConditionDTO personalCondition = null;
                BusinessCardCheckExtInput checkExtInput = new BusinessCardCheckExtInput();
                checkExtInput.setAuthId(memberInfo.getAuthId());
                checkExtInput.setAmount(orderAmount);
                checkExtInput.setUnitPrice(unitPrice);
                checkExtInput.setActivityType(activityType);
                checkExtInput.setAgencyId(memberInfo.getAgencyId());
                CheckConditionResponse checkConditionResponse = businessCardService.checkCondition(inputDto, checkExtInput);

                if (checkConditionResponse != null) {
                    businessCondition = checkConditionResponse.getBusinessCondition();
                    personalCondition = checkConditionResponse.getPersonalCondition();
                }
                if (memberCardInfo.getCardGroup() == 1) {
                    /**
                     * 企业会员卡
                     */
                    DiscountRuleDTO agencyDiscountRule = agencyDiscountDetail.getAgencyDiscountRule();
                    infoDto = businessCardByCard(businessCondition, agencyDiscountRule, memberCardInfo, rentAmount, discountRate, inputDto.getOrderCostTime(),inputDto.getOrderStartDate(),inputDto.getOrderEndDate());

                } else {
                    /**
                     * 企业个人会员卡
                     */
                    DiscountRuleDTO personDiscountRule = agencyDiscountDetail.getPersonDiscountRule();
                    infoDto = businessCardByCard(personalCondition, personDiscountRule, memberCardInfo, rentAmount, discountRate, inputDto.getOrderCostTime(), inputDto.getOrderStartDate(), inputDto.getOrderEndDate());

                }
            } else { // 企业会员卡不可用，设置两个默认字段，让调用方能正常走下去
                infoDto.setAvailabilityFlag(0);
                infoDto.setUnAvailableDesc("");
            }
        }
        logger.info("checkCardByUseCondition根据用车条件校验会员卡，authId={}, result={}", authId, JSON.toJSONString(infoDto));
        return infoDto;
    }


    public boolean isPeakSeason(String start,String end){
        try {
            if (StringUtils.isBlank(start) || StringUtils.isBlank(end)) {
                return false;
            }
            Date startDate = DateUtil.getDateFromStr(start, DateUtil.simple);
            Date endDate = DateUtil.getDateFromStr(end, DateUtil.simple);
            return isPeakSeason(startDate, endDate);
        } catch (Exception e) {
            logger.error("判断淡旺季异常，start={},end={}",start,end,e);
            return false;
        }
    }

    /**
     * 判断是否是 旺季
     * @param start
     * @param end
     * @return
     */
    public boolean isPeakSeason(Date start, Date end) {
        LocalDate startDate = DateUtil.dateToLocalDateTime(start).toLocalDate();
        LocalDate endDate = DateUtil.dateToLocalDateTime(end).toLocalDate();

        // 根据计费日期，拿到涉及年份
        int startYear = startDate.getYear();
        int endYear = endDate.getYear();

        // 根据年份查询旺季日接口，可能跨年
        List<LowAndPeakSeasonConfig> configurationList = new ArrayList<>();
        for (int i = startYear; i <= endYear; i++) {
            Map<Integer, LowAndPeakSeasonConfig> lowAndPeakSeasonConfig = configLoader.getLowAndPeakSeasonConfig(String.valueOf(i));
            if (lowAndPeakSeasonConfig != null) {
                configurationList.addAll(lowAndPeakSeasonConfig.values());
            }
        }
        // 判断计费的所有日期，是否落在任何旺季日范围内，得出是否是旺季日订单
        return isPeakSeasonOrder(startDate, endDate, configurationList);
    }

    private boolean isPeakSeasonOrder(LocalDate startDate, LocalDate endDate, List<LowAndPeakSeasonConfig> configurationList) {
        for (; startDate.compareTo(endDate) <= 0; startDate = startDate.plusDays(1)) {
            for (LowAndPeakSeasonConfig item : configurationList) {
                LocalDate itemStartDate = LocalDate.parse(item.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.DATE_TYPE1));
                LocalDate itemEndDate = LocalDate.parse(item.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.DATE_TYPE1));
                if (startDate.compareTo(itemStartDate) >= 0 && startDate.compareTo(itemEndDate) <= 0) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * @param businessCondition
     * @param memberCardInfo
     * @param rentAmount
     * @param discountRate
     * @param orderStart
     * @param orderEnd
     * @return
     */
    private QueryAvailableCardInfoDto businessCardByCard(CheckConditionDTO businessCondition,
                                                         DiscountRuleDTO discountRule,
                                                         MmpUserCardInfo memberCardInfo,
                                                         BigDecimal rentAmount, BigDecimal discountRate, Integer costTime, String orderStart, String orderEnd) {
        //未传入折扣率时，取最新的折扣率
        BigDecimal discountFloat;
        if (discountRate == null) {
            BigDecimal discount;
            if (businessCondition.getDiscountType() != null) {
                if (businessCondition.getDiscountType() == 1) {
                    //普通折扣
                    discount = businessCondition.getMemDiscount();
                } else {
                    //阶梯折扣
                    discount = agencyPriceService.gradeDiscount(costTime, businessCondition.getDiscountId());
                }
            } else {
                //追加判断： 请求时会员当前机构已不存在指定折扣类型，则返回包含卡且折扣不可用
                if(discountRule == null || discountRule.getDiscountRate() == null || discountRule.getPeakSeasonDiscountRate() == null) {
                    QueryAvailableCardInfoDto infoDto = new QueryAvailableCardInfoDto();
                    infoDto.setAvailabilityFlag(0);
                    infoDto.setUnAvailableDesc(StringUtils.EMPTY);
                    return infoDto;
                }
                // 淡旺季判断
                if (isPeakSeason(orderStart,orderEnd)) {
                    // 旺季
                    discount = new BigDecimal(discountRule.getPeakSeasonDiscountRate()/100).setScale(2, BigDecimal.ROUND_HALF_UP);
                }else{
                    // 非旺季
                    discount = new BigDecimal(discountRule.getDiscountRate()/100).setScale(2, BigDecimal.ROUND_HALF_UP);
                }
            }
            discountFloat = discount;
        } else {
            discountFloat = discountRate;
        }
        if (businessCondition != null) {
            QueryAvailableCardInfoDto infoDto = new QueryAvailableCardInfoDto();
            if (businessCondition.getPayFlag() != null && businessCondition.getPayFlag() == 1) {
                infoDto.setUserCardNo(memberCardInfo.getUserCardNo());
                infoDto.setCardGroup(memberCardInfo.getCardGroup());
                infoDto.setCardName(memberCardInfo.getCardName());
                infoDto.setDiscount(discountFloat.multiply(new BigDecimal(100)).intValue());
                if (businessCondition.getCheckPass()) {
                    BigDecimal payAmount = rentAmount.multiply(discountFloat).setScale(2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal deductionAmount = rentAmount.subtract(payAmount);
                    infoDto.setAvailabilityFlag(1);
                    infoDto.setDeductionAmount(deductionAmount);
                } else {
                    infoDto.setAvailabilityFlag(0);
                    infoDto.setUnAvailableDesc(businessCondition.getCheckFailReason());
                }
                return infoDto;
            } else {
                String checkFailReason = StringUtils.EMPTY;
                if (businessCondition != null) {
                    checkFailReason = businessCondition.getCheckFailReason();
                }
                infoDto.setAvailabilityFlag(0);
                infoDto.setUnAvailableDesc(checkFailReason);
                return infoDto;
            }
        }
        return null;
    }

    /**
     * 根据订单获取可用付费会员卡
     * @param effectiveCardList
     * @param availableList
     * @param unAvailableList
     * @param cardInput
     * @param rentAmount
     */
    private void payCardInfo(List<MmpUserCardInfo> effectiveCardList,
                             List<QueryAvailableCardInfoDto> availableList,
                             List<QueryAvailableCardInfoDto> unAvailableList,
                             GetEffectiveActivityCardInput cardInput,
                             BigDecimal rentAmount) {
        /**
         * 入参处理：获取订单的取车时间、用车时长、用车区域、车型、产品线
         */
        memberCardInnerService.preProcessOrderCondition(cardInput);

        Integer orderRentMethod = cardInput.getRentMethod();
        Date orderPickUpDate = cardInput.getOrderPickUpDate();
        String orderPickUpDateTime = DateUtil.getFormatDate(orderPickUpDate, DateUtil.dtLong);
        Integer orderCostTime = cardInput.getOrderCostTime();
        Integer orderVehicleModelSeq = cardInput.getVehicleModelSeq();
        Integer orderGoodsVehicleModelSeq = cardInput.getGoodsModelId();

        //取车城市、还车城市
        Long returnCityId = cardInput.getReturnCity();
        Long pickCityId = cardInput.getPickUpCity();
        List<Long> cardIdList = effectiveCardList.stream().filter(p->p.getCardGroup() == 3).map(MmpUserCardInfo::getCardId).collect(Collectors.toList());
        /**
         * 5.批量查询付费卡信息
         */
        List<MmpCardDef> mmpCardDefList =  mmpCardDefMapper.batchQueryByIdList(cardIdList);
        Map<Long, List<MmpCardDef>> cardInfoMap = mmpCardDefList.stream().collect(Collectors.groupingBy(MmpCardDef::getCardId));
        for (MmpUserCardInfo memberCardInfo : effectiveCardList) {
            MmpCardDef cardModel = cardInfoMap.get(memberCardInfo.getCardId()).get(0);
            QueryAvailableCardInfoDto infoDto = new QueryAvailableCardInfoDto();
            infoDto.setUserCardNo(memberCardInfo.getUserCardNo());
            infoDto.setCardGroup(memberCardInfo.getCardGroup());
            infoDto.setCardName(memberCardInfo.getCardName());
            infoDto.setDiscount(cardModel.getDiscount());
            infoDto.setMaxValue(cardModel.getMaxValue());
            if (StringUtils.isNotBlank(cardModel.getBackUrl())){
                infoDto.setBackUrl(ConfigUtil.getConfigPropertiesStr("web_url") + cardModel.getBackUrl());
            }
            /**
             * 5.校验限制条件，订单是否能使用此会员卡
             */
            boolean availabilityFlag = true;
            StringBuffer unAvailableBuffer = new StringBuffer();
            //取车时间 星期
            boolean pickTimeCheck = true;
            String availableDaysOfWeek = cardModel.getAvailableDaysOfWeek();
            if (StringUtils.isNotBlank(availableDaysOfWeek)) {
                Instant instant = orderPickUpDate.toInstant();
                LocalDate now = instant.atZone(ZoneId.systemDefault()).toLocalDate();
                int dayOfWeek = now.getDayOfWeek().getValue();
                if (!availableDaysOfWeek.contains(String.valueOf(dayOfWeek))) {
                    availabilityFlag = false;
                    pickTimeCheck = false;
                    unAvailableBuffer.append("取车时段不可用/");
                }
            }
            //取车时间 时间段-->取消时段限制
            if (pickTimeCheck) {
                String pickUpStartTime = cardModel.getStartTime();
                String pickUpEndTime = cardModel.getEndTime();
//                if (StringUtils.isNotBlank(pickUpStartTime) && StringUtils.isNotBlank(pickUpEndTime)) {
//                    if (!("000000".equals(pickUpStartTime) && "240000".equals(pickUpEndTime))) {
//                        String pickDateDay = orderPickUpDateTime.substring(0, 8);
//                        String pickStartTime = pickDateDay + pickUpStartTime;
//                        String pickEndTime = pickDateDay + pickUpEndTime;
//                        Date pickStartDate = DateUtil.getDateFromStr(pickStartTime, DateUtil.dtLong);
//                        Date pickEndDate = DateUtil.getDateFromStr(pickEndTime, DateUtil.dtLong);
//                        if (orderPickUpDate.before(pickStartDate) || orderPickUpDate.after(pickEndDate)) {
//                            availabilityFlag = false;
//                            unAvailableBuffer.append("取车时段不可用/");
//                        }
//                    }
//                }
            }
            //用车时长
            Integer durationLimit = cardModel.getDurationLimit();
            if (durationLimit > 0) {
                if (orderCostTime < durationLimit) {
                    availabilityFlag = false;
                    unAvailableBuffer.append("用车时长不可用/");
                }
            }
            //用车区域
            String cityLimit = cardModel.getCityLimit();
            if (StringUtils.isNotBlank(cityLimit)) {
                List<Integer> cityIdList = ComUtils.turnToList(cityLimit, ",");
                if (pickCityId == null || returnCityId == null || !cityIdList.contains(pickCityId.intValue()) || !cityIdList.contains(returnCityId.intValue())) {
                    availabilityFlag = false;
                    unAvailableBuffer.append("用车区域不可用/");
                }
            }
            //新增： 若指定了取车门店，则校验可用门店限制(限制取车门店)
            String orderPickUpStore = cardInput.getPickUpStoreId();
            String storeId = cardModel.getStoreIds();
            if (StringUtils.isNotBlank(storeId) && StringUtils.isNotBlank(orderPickUpStore)) {
                List<Integer> storeIdList = ComUtils.turnToList(storeId, ",");
                if (!storeIdList.contains(Integer.valueOf(orderPickUpStore))) {
                    availabilityFlag = false;
                    unAvailableBuffer.append("取车门店不可用/");
                }
            }
            //可用车型
            String vehicleModelLimit = cardModel.getVehicleModelLimit();
            if (StringUtils.isNotBlank(vehicleModelLimit) && orderVehicleModelSeq != null) {
                List<Integer> vehicleModelSeqList = ComUtils.turnToList(vehicleModelLimit, ",");
                if (!vehicleModelSeqList.contains(orderVehicleModelSeq)) {
                    availabilityFlag = false;
                    unAvailableBuffer.append("车型不可用/");
                }
            }
            //处理商品车型校验
            String goodsVehicleModelLimit = cardModel.getGoodsModelId();
            if (StringUtils.isNotBlank(goodsVehicleModelLimit)) {
                if(orderGoodsVehicleModelSeq != null) {
                    List<Integer> vehicleModelSeqList = ComUtils.turnToList(goodsVehicleModelLimit, ",");
                    if (!vehicleModelSeqList.contains(orderGoodsVehicleModelSeq)) {
                        availabilityFlag = false;
                        unAvailableBuffer.append("车型不可用/");
                    }
                }
            }

            //产品线大类校验
            String rentMethodGroup = cardModel.getRentMethodGroup();
            if (StringUtils.isNotBlank(rentMethodGroup)) {
                List<Integer> rentMethodList = ComUtils.turnToList(rentMethodGroup, ",");
                Integer orderRentMethodGroup = memberCardInnerService.getRentMethodGroup(orderRentMethod);
                if(!rentMethodList.contains(orderRentMethodGroup)) {
                    availabilityFlag = false;
                    unAvailableBuffer.append("产品线不可用/");
                }
            } else {
                //未指定大类，则校验具体产品线
                String rentMethod = cardModel.getRentMethod();
                if (StringUtils.isNotBlank(rentMethod)) {
                    List<Integer> rentMethodList = ComUtils.turnToList(rentMethod, ",");
                    if (!rentMethodList.contains(orderRentMethod)) {
                        availabilityFlag = false;
                        unAvailableBuffer.append("产品线不可用/");
                    }
                }
            }

            // app5.7 修改订单、更换车型需求，计算老订单的冻结金额
            BigDecimal needResumeDiscountAmount = BigDecimal.ZERO;
            try {
                String oldOrderSeq = cardInput.getOldOrderSeq();
                if (StringUtils.isNotEmpty(oldOrderSeq)) {
                    UserCardHistoryQueryDto queryDto = new UserCardHistoryQueryDto();
                    queryDto.setUserCardNo(memberCardInfo.getUserCardNo());
                    queryDto.setOperationType(3);
                    queryDto.setOrderSeq(oldOrderSeq);
                    List<MmpUserCardOperationLog> mmpUserCardOperationLogs = mmpUserCardOperationLogMapper.selectCardHistoryPage(queryDto);
                    if (CollectionUtils.isNotEmpty(mmpUserCardOperationLogs)) {
                        MmpUserCardOperationLog mmpUserCardOperationLog = mmpUserCardOperationLogs.get(0);
                        if (mmpUserCardOperationLog != null && mmpUserCardOperationLog.getDiscountAmount() != null) {
                            needResumeDiscountAmount = mmpUserCardOperationLog.getDiscountAmount();
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("计算冻结金额时，异常，入参cardInput={}",JSON.toJSONString(cardInput),e);
            }

            //累计额度
            BigDecimal remainderDiscountAmount = null;
            MmpUserCardDiscountInfo mmpUserCardDiscountInfo = mmpUserCardDiscountInfoMapper.userCardDiscount(memberCardInfo.getUserCardNo());
            if (mmpUserCardDiscountInfo != null) {
                remainderDiscountAmount = mmpUserCardDiscountInfo.getTotalDiscountAmount().subtract(mmpUserCardDiscountInfo.getDiscountAmount());
                // app5.7 修改订单、更换车型需求，把老订单的冻结金额加上
                remainderDiscountAmount = remainderDiscountAmount.add(needResumeDiscountAmount);
                if (remainderDiscountAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    availabilityFlag = false;
                    unAvailableBuffer.append(DateUtil.getFormatDate(mmpUserCardDiscountInfo.getStartTime(), DateUtil.DATE_TYPE7))
                            .append(" - ")
                            .append(DateUtil.getFormatDate(mmpUserCardDiscountInfo.getEndTime(), DateUtil.DATE_TYPE7))
                            .append("有效期内折扣上限额度已用完/");
                }
            }

            infoDto.setAvailabilityFlag(availabilityFlag?1:0);
            if (!availabilityFlag) {
                String unAvailableDesc = ComUtils.formatEndChar(unAvailableBuffer.toString(),"/");
                infoDto.setUnAvailableDesc(unAvailableDesc);
                unAvailableList.add(infoDto);
            } else {
                availableList.add(infoDto);
                /**
                 * 6.计算折扣减免金额
                 */
                Integer discount = cardModel.getDiscount();
                BigDecimal discountRate = new BigDecimal(discount).divide(new BigDecimal(100));
                BigDecimal payAmount = rentAmount.multiply(discountRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal deductionAmount = rentAmount.subtract(payAmount);
                BigDecimal maxValue = cardModel.getMaxValue();
                if (maxValue.compareTo(BigDecimal.ZERO) > 0) {
                    if (deductionAmount.compareTo(maxValue) > 0) {
                        deductionAmount = maxValue;
                    }
                }
                if (remainderDiscountAmount != null && remainderDiscountAmount.compareTo(BigDecimal.ZERO) > 0) {
                    if (deductionAmount.compareTo(remainderDiscountAmount) > 0) {
                        deductionAmount = remainderDiscountAmount;
                    }
                }
                infoDto.setDeductionAmount(deductionAmount);
            }
        }
    }

    /**
     * 根据订单获取可用企业会员卡
     *
     * @param businessCondition
     * @param cardInfo
     * @param discountRule
     * @param availableList
     * @param unAvailableList
     * @param rentAmount
     * @param costTime          订单时长
     * @param orderStart  订单开始时间
     * @param orderEnd
     */
    private void businessCardInfo(CheckConditionDTO businessCondition,
                                  MmpUserCardInfo cardInfo,
                                  DiscountRuleDTO discountRule,
                                  List<QueryAvailableCardInfoDto> availableList,
                                  List<QueryAvailableCardInfoDto> unAvailableList,
                                  BigDecimal rentAmount,
                                  Integer costTime, String orderStart, String orderEnd) {
        if (businessCondition != null) {
            QueryAvailableCardInfoDto infoDto = new QueryAvailableCardInfoDto();
            infoDto.setUserCardNo(cardInfo.getUserCardNo());
            infoDto.setCardGroup(cardInfo.getCardGroup());
            infoDto.setCardName(cardInfo.getCardName());
            if (businessCondition.getPayFlag() != null && businessCondition.getPayFlag() == 1) {
                BigDecimal discount;
                if (businessCondition.getDiscountType() != null) {
                    if (businessCondition.getDiscountType() == 1) {
                        //普通折扣
                        discount = businessCondition.getMemDiscount();
                    } else {
                        //阶梯折扣
                        discount = agencyPriceService.gradeDiscount(costTime, businessCondition.getDiscountId());
                    }
                } else {
                    // 判断
                    if(discountRule == null || discountRule.getDiscountRate() == null || discountRule.getPeakSeasonDiscountRate() == null) {
                        infoDto.setAvailabilityFlag(0);
                        infoDto.setUnAvailableDesc(StringUtils.EMPTY);
                        unAvailableList.add(infoDto);
                        return;
                    }

                    // 淡旺季判断
                    if (isPeakSeason(orderStart,orderEnd)) {
                        // 旺季
                        discount = new BigDecimal(discountRule.getPeakSeasonDiscountRate()/100).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }else{
                        // 非旺季
                        discount = new BigDecimal(discountRule.getDiscountRate()/100).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    // 设置旺季折扣 ，app前端展示
                    infoDto.setPeakSeasonDiscount(new BigDecimal(discountRule.getPeakSeasonDiscountRate()).setScale(2,BigDecimal.ROUND_HALF_UP).intValue());
                }

                infoDto.setDiscount(discount.multiply(new BigDecimal(100)).intValue());
                if (businessCondition.getCheckPass()) {
                    if (rentAmount.compareTo(BigDecimal.ZERO) == 0) {
                        infoDto.setAvailabilityFlag(0);
                        infoDto.setUnAvailableDesc("和套餐不可同享");
                    } else {
                        BigDecimal payAmount = rentAmount.multiply(discount).setScale(2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal deductionAmount = rentAmount.subtract(payAmount);
                        infoDto.setAvailabilityFlag(1);
                        infoDto.setDeductionAmount(deductionAmount);
                    }
                    availableList.add(infoDto);
                } else {
                    infoDto.setAvailabilityFlag(0);
                    infoDto.setUnAvailableDesc(businessCondition.getCheckFailReason());
                    unAvailableList.add(infoDto);
                }
            }else{
                // 折扣有效性
                infoDto.setAvailabilityFlag(0);
                infoDto.setUnAvailableDesc(businessCondition.getCheckFailReason());
                unAvailableList.add(infoDto);
            }
        }
    }

    @Override
    public List<Long> queryCardRemind(Long userId, List<Long> activityIds) {

        List<UserRemindActivityDto> remindActivityDto = mmpCardRemindMapper.queryByIdAndActivity(userId,activityIds);

        if (CollectionUtils.isNotEmpty(remindActivityDto)){
            return remindActivityDto.stream().map(UserRemindActivityDto::getActivityId).collect(Collectors.toList());
        }

        return null;
    }

    @Override
    public void submitCardRemind(UserRemindActivityDto userRemindActivityDto) {
        if (userRemindActivityDto.getActivityId() == null || userRemindActivityDto.getUserId() == null){
            return;
        }

        MmpCardRemind mmpCardRemind = new MmpCardRemind();
        mmpCardRemind.setActivityId(userRemindActivityDto.getActivityId());
        mmpCardRemind.setUserId(userRemindActivityDto.getUserId());

        mmpCardRemindMapper.insertSelective(mmpCardRemind);
    }

    @Override
    public QueryUserCardStatusDto userCardStatus(Long userId) {

        QueryUserCardStatusDto queryUserCardStatusDto = new QueryUserCardStatusDto();
        /**
         * 查询当前用户会员卡信息
         */
        List<MmpUserCardInfo> mmpUserCardInfos =  mmpUserCardInfoMapper.queryUserVipCardById(userId);

        if (CollectionUtils.isEmpty(mmpUserCardInfos)){
            queryUserCardStatusDto.setCardStatus(-1);
        }
        final BigDecimal[] totalAmount = {BigDecimal.ZERO};
        boolean overTime =false;
        boolean agencyCard = false;
        boolean isCard = false;
        boolean haveCard = false;
        if (CollectionUtils.isNotEmpty(mmpUserCardInfos)) {
            for (MmpUserCardInfo p : mmpUserCardInfos) {

                if (!p.getCardGroup().equals(1) && !p.getCardGroup().equals(2)) {
                    haveCard = true;
                }
                if (p.getCardStatus().equals(1)) {
                    //有效的付费会员卡
                    if (p.getCardGroup().equals(3)) {
                        if (p.getExpiresTime().getTime() - System.currentTimeMillis() < 1000 * 60 * 60 * 24 * 2
                                && p.getExpiresTime().getTime() - System.currentTimeMillis() >= 0) {
                            overTime = true;
                        }
                        isCard = true;

                    } else if (p.getCardGroup().equals(1) || p.getCardGroup().equals(2)) {
                        //企业卡
                        if (!agencyCard) {
                            List<QueryCardInfoDetail> queryCardInfoDetails = queryOrgCardDetail(p.getUserId(), p.getAgencyId());
                            if (CollectionUtils.isEmpty(queryCardInfoDetails)) {
                                queryUserCardStatusDto.setCardStatus(0);
                                agencyCard = true;
                            }
                        }
                    }
                }
                totalAmount[0] = totalAmount[0].add(p.getTotalDiscountAmount());
            }
        }
        if (agencyCard){
            //无有效会员卡
            queryUserCardStatusDto.setCardStatus(0);
            if (!haveCard){
                queryUserCardStatusDto.setCardStatus(-1);
            }
        }
        if (isCard){
            //存在有效卡
            queryUserCardStatusDto.setCardStatus(2);
        }
        if (overTime){
            //过期
            queryUserCardStatusDto.setCardStatus(1);
        }
        queryUserCardStatusDto.setTotalDiscountAmount(totalAmount[0]);

        //获取待支付数量
        MmpCardPurchaseRecord mmpCardPurchaseRecord = new MmpCardPurchaseRecord();
        mmpCardPurchaseRecord.setUserId(userId);
        mmpCardPurchaseRecord.setStatus(0);
        mmpCardPurchaseRecord.setIssueType(0);
        mmpCardPurchaseRecord.setPaymentStatus(0);
        int waitPayCount = mmpCardPurchaseRecordMapper.selectCountByCondition(mmpCardPurchaseRecord);
        queryUserCardStatusDto.setWaitPayCardNum(waitPayCount);
        if (queryUserCardStatusDto.getCardStatus() == -1 && waitPayCount == 0) {
            mmpCardPurchaseRecord = new MmpCardPurchaseRecord();
            mmpCardPurchaseRecord.setUserId(userId);
            mmpCardPurchaseRecord.setStatus(0);
            mmpCardPurchaseRecord.setIssueType(0);
            mmpCardPurchaseRecord.setPaymentStatus(-1);
            int cancelCount = mmpCardPurchaseRecordMapper.selectCountByCondition(mmpCardPurchaseRecord);
            queryUserCardStatusDto.setCancelCardNum(cancelCount);
        }
        return queryUserCardStatusDto;
    }


    @Override
    public BaseResponse batchOfferCorporateCard(Set<Long> userIds, String agencyId, OperatorDto operateDto) {
        if(CollectionUtils.isEmpty(userIds) || StringUtils.isBlank(agencyId) || operateDto == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        operateDto = OperatorDto.buildOperator(operateDto, "关联企业");
        BaseResponse response = new BaseResponse();
        List<MmpUserCardInfo> records = new ArrayList<>();
        Date now = new Date();
        //
        List<MmpUserCardInfo> list = mmpUserCardInfoMapper.queryUserCorporateCards(userIds, agencyId);
        if(CollectionUtils.isNotEmpty(list)) {
            for (MmpUserCardInfo userCardInfo : list){
                userIds.remove(userCardInfo.getUserId());
            }
        }
        for(Long userId : userIds) {
            MmpUserCardInfo userCard = new MmpUserCardInfo();
            userCard.setAgencyId(agencyId);
            userCard.setUserId(userId);
            userCard.setCardGroup(1);
            //企业卡发放后默认生效中
            userCard.setCardStatus(1);
            userCard.setCardId(-1L);
            userCard.setStartTime(now);
            userCard.setExpiresTime(null);
            userCard.setCreateOperId(operateDto.getOperatorId());
            userCard.setCreateOperName(operateDto.getOperatorName());
            userCard.setCreateTime(now);
            userCard.setCardName("企业折扣卡");
            records.add(userCard);
            MmpUserCardInfo userCardPersonal = new MmpUserCardInfo();
            BeanCopyUtils.copyProperties(userCard, userCardPersonal);
            userCardPersonal.setCardGroup(2);
            userCardPersonal.setCardName("个人折扣卡");
            records.add(userCardPersonal);
        }
        mmpUserCardInfoMapper.batchInsert(records);
        //添加企业卡发放日志
        memberCardInnerService.batchSaveCardOfferLogs("企业卡发放", records, operateDto);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse batchOfferCorporateCard(Set<Long> userIds, String agencyId, boolean disable, OperatorDto operateDto) {
        if(CollectionUtils.isEmpty(userIds) || StringUtils.isBlank(agencyId) || operateDto == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        if(disable) {
            //作废原有卡片
            batchDisableCorporateCard(userIds, null, operateDto);
        }
        //批量发放新的企业卡与个人
        batchOfferCorporateCard(userIds, agencyId, operateDto);
        return new BaseResponse();
    }

    @Override
    public BaseResponse batchDisableCorporateCard(Set<Long> userIds, OperatorDto operateDto) {
        return batchDisableCorporateCard(userIds, null, operateDto);
    }

    @Override
    public BaseResponse batchDisableCorporateCard(Set<Long> userIds, String agencyId, OperatorDto operateDto) {
        if(CollectionUtils.isEmpty(userIds) || operateDto == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        operateDto = OperatorDto.buildOperator(operateDto);
        //agencyId为空时，作废用户全部企业卡
        mmpUserCardInfoMapper.batchDisable(userIds, agencyId, operateDto.getOperatorId(), operateDto.getOperatorName());
        return new BaseResponse();
    }

    @Override
    public PageBeanBO<MemberCardListViewDto> queryPageByUserId(Long userId, MemberCardQueryDto queryDto) {
        if(userId == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        PageBeanBO<MemberCardListViewDto> pageBeanBO = new PageBeanBO<>();
        if(queryDto.getPageNum() == null) {
            queryDto.setPageNum(1);
        }
        if(queryDto.getPageSize() == null) {
            queryDto.setPageSize(10);
        }
        String startDate = queryDto.getStartDate();
        if (StringUtils.isNotBlank(startDate)) {
            queryDto.setStartDate(startDate.substring(0, 4) + "-" + startDate.substring(4, 6) + "-" + startDate.substring(6, 8) + " 00:00:00");
        }
        String endDate = queryDto.getEndDate();
        if (StringUtils.isNotBlank(endDate)) {
            queryDto.setEndDate(endDate.substring(0, 4) + "-" + endDate.substring(4, 6) + "-" + endDate.substring(6, 8) + " 23:59:59");
        }

        Page page = null;
        List<MemberCardListViewDto> result = new ArrayList<>();
        Date now = new Date();

        // 如果卡类型是 3随享卡，走新逻辑
        if (queryDto.getCardType() != null && queryDto.getCardType() == 3) {
            // 查询suixiang_card_use表
            page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
            List<SuixiangCardUse> useList = suixiangCardUseMapper.selectCardListByUserIdForMmp(userId, queryDto);
            if (CollectionUtils.isNotEmpty(useList)) {
                // 查询suixiang_card_base表
                List<Long> baseIdList = useList.stream().map(item -> item.getCardBaseId()).collect(Collectors.toList());
                SuixiangCardBaseExample baseExample = new SuixiangCardBaseExample();
                baseExample.createCriteria()
                        .andIdIn(baseIdList)
                        .andIsDeletedEqualTo(IsDeleteEnum.NORMAL.getIsDelete());
                List<SuixiangCardBase> baseList = suixiangCardBaseMapper.selectByExample(baseExample);
                Map<Long, SuixiangCardBase> baseMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(baseList)) {
                    baseMap = baseList.stream().collect(Collectors.toMap(SuixiangCardBase::getId, Function.identity(), (a, b) -> b));
                }

                for (SuixiangCardUse use : useList) {
                    MemberCardListViewDto dto = new MemberCardListViewDto();
                    dto.setUserCardNoDesc(String.valueOf(use.getId()));
                    dto.setCardId(use.getCardBaseId());

                    SuixiangCardBase base = baseMap.get(use.getCardBaseId());
                    if (base != null) {
                        dto.setCardName(base.getCardName());
                        dto.setCityNames(suixiangCardBaseManager.getCityNamesByCityId(base.getCityId()));
                    }

                    dto.setCardType(3); // 这里是固定 3-随享卡
                    dto.setStartTime(DateUtil.getFormatDate(use.getStartTime(), DateUtil.DATE_TYPE8));
                    dto.setPurchaseTime(DateUtil.getFormatDate(use.getStartTime(), DateUtil.DATE_TYPE8));
                    dto.setExpiresTime(DateUtil.getFormatDate(use.getExpiresTime(), DateUtil.DATE_TYPE8));

                    // 库表状态转化为页面展示的状态
                    Integer resultCardStatus = null;
                    // 1-生效中
                    if (use.getCardStatus() == 1) {
                        resultCardStatus = 1;
                    }
                    // 3 生效中-已冻结、8自动已冻 展示为 3-已冻结
                    else if (use.getCardStatus() == 3 || use.getCardStatus() == 8) {
                        resultCardStatus = 3;
                    }
                    // 其他状态展示为 2-已失效
                    else {
                        resultCardStatus = 2;
                    }
                    dto.setUserCardStatus(resultCardStatus);

                    // userCardStatus入参的定义：1-生效中、2-已失效、3-已冻结
                    // 如果查询条件没有送 状态或送的是 2-已失效，需要对库表状态1-生效中，但是时间过期的，进行转化
                    if (queryDto.getUserCardStatus() == null || queryDto.getUserCardStatus() == 2) {
                        // 如果当前状态是 1-生效中，但是时间已经过期，说明过期的批处理还没及时跑完，这里手动判断设置状态
                        if (null != use.getExpiresTime()) {
                            if (dto.getUserCardStatus() == 1 && now.after(use.getExpiresTime())) {
                                dto.setUserCardStatus(2);
                            }
                        }
                    }

                    dto.setTotalOrder(use.getTotalOrder().longValue());
                    dto.setTotalDiscountAmount(use.getTotalDiscountAmount());
                    dto.setAllowedRefund(suixiangCardRefundService.checkAllowedRefund(use.getId()).getCode() == 0);
                    result.add(dto);
                }
            }
        } else {
            if (queryDto.getIsAll() == null || queryDto.getIsAll() == 0) {
                page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize(), false);
            } else {
                page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
            }
            List<MemberCardListInfo> list = mmpUserCardInfoMapper.selectUserCardPage(userId, queryDto);

            for(MemberCardListInfo cardInfo : list) {
                MemberCardListViewDto dto = new MemberCardListViewDto();
                BeanCopyUtils.copyProperties(cardInfo, dto);
                dto.setUserCardNoDesc(String.valueOf(cardInfo.getUserCardNo()));
                if(dto.getCardGroup() != null && dto.getCardGroup() < Constants.CARD_PREFIX.length) {
                    dto.setUserCardNoDesc(Constants.CARD_PREFIX[dto.getCardGroup()] + cardInfo.getUserCardNo());
                }
                dto.setStartTime(DateUtil.getFormatDate(cardInfo.getStartTime(), DateUtil.DATE_TYPE8));
                dto.setExpiresTime(DateUtil.getFormatDate(cardInfo.getExpiresTime(), DateUtil.DATE_TYPE8));
                if(null != cardInfo.getExpiresTime()) {
                    if(dto.getCardStatus() == 1 && now.after(cardInfo.getExpiresTime())) {
                        dto.setCardStatus(3);
                    }
                }

                // 设置最新购买时间
                List<MmpCardPurchaseRecord> purchaseList = mmpCardPurchaseRecordMapper.selectLastUserPurchaseRecords(userId, cardInfo.getCardId());
                if (CollectionUtils.isNotEmpty(purchaseList)) {
                    Date purchasse = purchaseList.get(0).getPayTime();
                    dto.setPurchaseTime(DateUtil.getFormatDate(purchasse, DateUtil.DATE_TYPE8));
                }
                dto.setUserCardStatus(dto.getCardStatus());
                /**
                 * 组织disableFlag字段：0无 1允许作废 2待作废
                 */
                Integer disableFlag = (dto.getUserCardStatus().equals(1)) ? 1 : 0;
                if(cardInfo.getActiveFlag().equals(Constants.TO_DISABLE_TAG)) {
                    disableFlag = 2;
                }
                dto.setDisableFlag(disableFlag);
                //dto.setCreateTime(DateUtil.getFormatDate(cardInfo.getCreateTime(), DateUtil.simple));
                //dto.setUpdateTime(DateUtil.getFormatDate(cardInfo.getUpdateTime(), DateUtil.simple));
                dto.setCityNames(memberCardInnerService.getCityDesc(dto.getCityLimit()));
                //dto.setVehicleModelNames(memberCardInnerService.getVehicleModelDesc(dto.getVehicleModelLimit()));
                //dto.setRentMethodNames(memberCardInnerService.getRentMethodDesc(dto.getRentMethod()));
                result.add(dto);
            }
        }

        PageBO pageBO = new PageBO();
        pageBO.setTotal(page.getTotal());
        pageBO.setPageNum(page.getPageNum());
        pageBO.setPageSize(page.getPageSize());
        pageBeanBO.setPage(pageBO);
        pageBeanBO.setList(result);
        return pageBeanBO;
    }



    @Override
    @Deprecated
    public BaseResponse cancelMemberCard(Long userCardNo, OperatorDto operateDto) {
        if(userCardNo == null || operateDto == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        operateDto = OperatorDto.buildOperator(operateDto);
        int result = mmpUserCardInfoMapper.cancelUserCard(null, null, userCardNo,
                operateDto.getOperatorId(), operateDto.getOperatorName());
        mmpUserCardDiscountInfoMapper.cancelUserCard(userCardNo);
        if(result == 1) {
            MmpUserCardOperationLog operationLog = new MmpUserCardOperationLog();
            operationLog.setUserCardNo(userCardNo);
            operationLog.setCardId(-1L);
            operationLog.setCardGroup(3);
            operationLog.setOrderSeq(StringUtils.EMPTY);
            operationLog.setOperationType(2L);
            operationLog.setOriginSystem(operateDto.getOriginSystem());
            operationLog.setCreateTime(new Date());
            operationLog.setCreateOperName(operateDto.getOperatorName());
            mmpUserCardOperationLogMapper.insertSelective(operationLog);
        }
        return new BaseResponse();

//        MmpUserCardInfo userCardInfo = mmpUserCardInfoMapper.selectByPrimaryKey(userCardNo);
//        if(userCardInfo == null) {
//            logger.warn("cancelMemberCard：指定卡不存在，userCardNo={}.", userCardNo);
//            return new BaseResponse(-1, "会员卡不存在");
//        }
//        return memberCardInnerService.cancelMemberCard(userCardInfo, operateDto);
    }

    @Override
    public BaseResponse cancelMemberCard(Long userId, Long cardId, OperatorDto operateDto) {
        if(userId == null || cardId == null || operateDto == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        operateDto = OperatorDto.buildOperator(operateDto);
        MmpUserCardInfo userCardInfo = mmpUserCardInfoMapper.queryUserVipCardByIdAndCard(userId, cardId);
        if(userCardInfo == null) {
            logger.warn("cancelMemberCard：指定卡不存在，userId={}, cardId={}.", userId, cardId);
            return new BaseResponse(-1, "会员卡不存在");
        }
        return memberCardInnerService.cancelMemberCard(userCardInfo, operateDto);
    }

    @Override
    public CardPurchaseDetailDto getCardPurchaseDetail(Long recordId) {
        MmpCardPurchaseRecord record = mmpCardPurchaseRecordMapper.selectByPrimaryKey(recordId);
        return getPurchaseDetail(record);
    }

    @Override
    public CardPurchaseDetailDto getCardPurchaseDetail(String orderSeq) {
        MmpCardPurchaseRecord record = mmpCardPurchaseRecordMapper.selectByOrderSeq(orderSeq);
        return getPurchaseDetail(record);
    }

    public CardPurchaseDetailDto getPurchaseDetail(MmpCardPurchaseRecord record){
        if(record != null) {
            CardPurchaseDetailDto dto = new CardPurchaseDetailDto();
            BeanCopyUtils.copyProperties(record, dto);
            Long activityId = record.getCardActivityId();
            MmpCardSalesActivity activity = mmpCardSalesActivityMapper.selectByPrimaryKey(activityId);
            if(activity != null) {
                dto.setActivityId(activity.getId());
                dto.setActivityName(activity.getActivityName());
                dto.setOrgId(activity.getOrgId());
            }
            return dto;
        }
        return null;
    }

    @Override
    public List<CardPurchaseRecordDto> queryCardPurchaseInfo(QueryCardPurchaseInfoDto queryCardPurchaseInfoDto) {

        queryCardPurchaseInfoDto.setPageNum((queryCardPurchaseInfoDto.getPageNum()-1) * queryCardPurchaseInfoDto.getPageSize());
        List<MmpCardPurchaseRecord> record = mmpCardPurchaseRecordMapper.selectByUserId(queryCardPurchaseInfoDto);
        if (CollectionUtils.isEmpty(record)){
            return null;
        }

        List<CardPurchaseRecordDto> cardPurchaseRecordDtos = new ArrayList<>();
        record.stream().forEach(p->{
            CardPurchaseRecordDto cardPurchaseRecordDto = new CardPurchaseRecordDto();
            BeanCopyUtils.copyProperties(p,cardPurchaseRecordDto);
            cardPurchaseRecordDto.setPurchaseId(p.getId());
//            MmpCardSalesActivity mmpCardSalesActivity = mmpCardSalesActivityMapper.selectByPrimaryKey(p.getCardActivityId());
//            if (mmpCardSalesActivity != null){
//                cardPurchaseRecordDto.setActivityName(mmpCardSalesActivity.getActivityName());
//            }
            if(p.getPayTime() != null){
                cardPurchaseRecordDto.setPayTime(DateUtil.getFormatDate(p.getPayTime(), DateUtil.DATE_TYPE7));
            }
            if(p.getCancelTime() != null){
                cardPurchaseRecordDto.setCancelTime(DateUtil.getFormatDate(p.getCancelTime(), DateUtil.DATE_TYPE7));
            }
            if (p.getPaymentStatus() == 0) {
                Date createTime = p.getCreateTime();
                Date planCancelTime = DateUtil.addMin(createTime, 5);
                cardPurchaseRecordDto.setPlanCancelTime(DateUtil.getFormatDate(planCancelTime, DateUtil.simple));
            }
            MmpCardDef mmpCardDef = mmpCardDefMapper.selectByPrimaryKey(p.getCardId());
            if (mmpCardDef != null) {
                cardPurchaseRecordDto.setActivityName(mmpCardDef.getCardName());
                cardPurchaseRecordDto.setDiscount(mmpCardDef.getDiscount());
            }
            cardPurchaseRecordDtos.add(cardPurchaseRecordDto);
        });
        return cardPurchaseRecordDtos;
    }

    @Override
    public PageBeanBO<UserCardDiscountViewDto> selectCardDiscountPage(CardDiscountInfoQueryDto queryDto) {
        if(queryDto == null || queryDto.getUserCardNo() == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        PageBeanBO<UserCardDiscountViewDto> pageBeanBO = new PageBeanBO<>();
        if(queryDto.getPageNum() == null) {
            queryDto.setPageNum(1);
        }
        if(queryDto.getPageSize() == null) {
            queryDto.setPageSize(10);
        }
        Page page;
        if (queryDto.getIsAll() == null || queryDto.getIsAll() == 0) {
            page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize(), false);
        } else {
            page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        }
        //会员卡使用限制详情
        List<UserCardDiscountViewDto> list = userCardDiscountInfoMapper.selectPage(queryDto);
        PageBO pageBO = new PageBO();
        pageBO.setTotal(page.getTotal());
        pageBO.setPageNum(page.getPageNum());
        pageBO.setPageSize(page.getPageSize());
        pageBeanBO.setPage(pageBO);
        pageBeanBO.setList(list);
        return pageBeanBO;
    }

    @Override
    public PageBeanBO<UserCardHistoryDto> selectUserCardHistoryPage(UserCardHistoryQueryDto queryDto) {
        List<UserCardHistoryDto> result = new ArrayList<>();
        if(queryDto == null || queryDto.getUserCardNo() == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        PageBeanBO<UserCardHistoryDto> pageBeanBO = new PageBeanBO<>();
        if(queryDto.getPageNum() == null) {
            queryDto.setPageNum(1);
        }
        if(queryDto.getPageSize() == null) {
            queryDto.setPageSize(10);
        }
        Page page;
        if (queryDto.getIsAll() == null || queryDto.getIsAll() == 0) {
            page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize(), false);
        } else {
            page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        }
        //会员卡使用限制详情
        List<MmpUserCardOperationLog> list = mmpUserCardOperationLogMapper.selectCardHistoryPage(queryDto);
        for(MmpUserCardOperationLog opLog : list) {
            UserCardHistoryDto dto = new UserCardHistoryDto();
            BeanCopyUtils.copyProperties(opLog, dto);
            result.add(dto);
        }
        PageBO pageBO = new PageBO();
        pageBO.setTotal(page.getTotal());
        pageBO.setPageNum(page.getPageNum());
        pageBO.setPageSize(page.getPageSize());
        pageBeanBO.setPage(pageBO);
        pageBeanBO.setList(result);
        return pageBeanBO;
    }

    @Override
    public List<CardPurchaseListDetailDto> pullUnreadOfferedCards(Long userId, Integer size) {
        logger.debug("用户未读卡片：start, userId={}", userId);
        List<CardPurchaseListDetailDto> list = getUnreadOfferedCards(userId, size);
        if(CollectionUtils.isNotEmpty(list)) {
            List<Long> ids = new ArrayList<>();
            for(CardPurchaseListDetailDto record : list) {
                ids.add(record.getId());
            }
            readOfferedCards(ids);
        }
        logger.debug("用户未读卡片：start, userId={}, size={}", userId, list.size());
        return list;
    }


    public List<CardPurchaseListDetailDto> getUnreadOfferedCards(Long userId, Integer size) {
        List<CardPurchaseListDetailDto> result = new ArrayList<>();
        if (userId == null) {
            return result;
        }
        if(size == null || size > 30) {
            size = 10;
        }
        Date startDate = DateUtils.addMonths(new Date(), -3);
        List<CardPurchaseRecordInfo> list = mmpCardPurchaseRecordMapper.selectUnreadCardOfferList(userId, startDate, size);

        for(CardPurchaseRecordInfo record : list) {
            CardPurchaseListDetailDto dto = new CardPurchaseListDetailDto();
            MmpCardDef mmpCardDef = mmpCardDefMapper.selectByPrimaryKey(record.getCardId());
            if (mmpCardDef != null){
                dto.setDiscount(mmpCardDef.getDiscount());
            }
            memberCardInnerService.buildCardPurchaseListView(record, dto);
            result.add(dto);
        }
        return result;
    }



    @Override
    public int readOfferedCards(List<Long> ids) {
        if(CollectionUtils.isNotEmpty(ids)) {
            return mmpCardPurchaseRecordMapper.readCards(ids);
        }
        return 0;
    }

    @Override
    public List<CardPurchaseRecordDto> queryCardPurchaseInfoByUserId(Long userId) {
        MmpCardPurchaseRecord queryCardPurchaseInfoDto = new MmpCardPurchaseRecord();
        queryCardPurchaseInfoDto.setUserId(userId);

        List<MmpCardPurchaseRecord> record = mmpCardPurchaseRecordMapper.selectBySelective(queryCardPurchaseInfoDto);
        if (CollectionUtils.isEmpty(record)){
            return null;
        }

        List<CardPurchaseRecordDto> cardPurchaseRecordDtos = new ArrayList<>();
        record.stream().forEach(p->{
            CardPurchaseRecordDto cardPurchaseRecordDto = new CardPurchaseRecordDto();
            BeanCopyUtils.copyProperties(p,cardPurchaseRecordDto);
            cardPurchaseRecordDto.setPurchaseId(p.getId());
//            MmpCardSalesActivity mmpCardSalesActivity = mmpCardSalesActivityMapper.selectByPrimaryKey(p.getCardActivityId());
//            if (mmpCardSalesActivity != null){
//                cardPurchaseRecordDto.setActivityName(mmpCardSalesActivity.getActivityName());
//            }
            if(p.getPayTime() != null){
                cardPurchaseRecordDto.setPayTime(DateUtil.getFormatDate(p.getPayTime(), DateUtil.DATE_TYPE7));
            }
            if(p.getCancelTime() != null){
                cardPurchaseRecordDto.setCancelTime(DateUtil.getFormatDate(p.getCancelTime(), DateUtil.DATE_TYPE7));
            }
            if (p.getPaymentStatus() == 0) {
                Date createTime = p.getCreateTime();
                Date planCancelTime = DateUtil.addMin(createTime, 5);
                cardPurchaseRecordDto.setPlanCancelTime(DateUtil.getFormatDate(planCancelTime, DateUtil.simple));
            }
            MmpCardDef mmpCardDef = mmpCardDefMapper.selectByPrimaryKey(p.getCardId());
            if (mmpCardDef != null) {
                cardPurchaseRecordDto.setActivityName(mmpCardDef.getCardName());
                cardPurchaseRecordDto.setDiscount(mmpCardDef.getDiscount());
            }
            cardPurchaseRecordDtos.add(cardPurchaseRecordDto);
        });
        return cardPurchaseRecordDtos;
    }


    @Override
    public List<CardPurchaseRecordDto> queryCardPurchaseInfoListByCondition(Long userId, int paymentStatus) {
        try {
            MmpCardPurchaseRecord queryCardPurchaseInfoDto = new MmpCardPurchaseRecord();
            queryCardPurchaseInfoDto.setUserId(userId);
            queryCardPurchaseInfoDto.setPaymentStatus(paymentStatus);
            queryCardPurchaseInfoDto.setStatus(0);
            List<MmpCardPurchaseRecord> list = mmpCardPurchaseRecordMapper.selectBySelective(queryCardPurchaseInfoDto);
            if (CollectionUtils.isEmpty(list)){
                return null;
            }else{
                return list.stream().map(MmpCardPurchaseRecord::toDto).collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.error("queryCardPurchaseInfoListByCondition会员卡,userId<{}>,paymentStatus<{}>,异常",userId,paymentStatus,e);
            return null;
        }
    }
}
