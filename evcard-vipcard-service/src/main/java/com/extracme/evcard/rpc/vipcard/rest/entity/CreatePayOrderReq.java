package com.extracme.evcard.rpc.vipcard.rest.entity;

import lombok.Data;

@Data
public class CreatePayOrderReq {
    // 用户ID
    private String mid;
    // 订单类别 1:预付订单 2:后付订单 3:购卡 4:风控单 5:单独购买服务费
    private int billType;
    // 商品订单号
    private String orderNo;
    //金额 折扣后的金额
    private String amount;
    // 商品描述
    private String goodsSubject;
    // 费用详情  {cardFee:,amouttype:20}
    private String feeInfo;
    // 租车合同编号, 非租车不用传, 用于记录以及预付款
    private String contractId;
    //企业订单转个人支付 1=是 0=否
    private int agencyToPerson;


    // 哪个门店
    private long storeId;
    //1.Evcard 2.携程 3.飞猪
    private int source;
    //1.分时 2.短租 3.长租 4.门店 5 时行
    private int bizLine;


}
