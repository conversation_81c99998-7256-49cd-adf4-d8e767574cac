package com.extracme.evcard.rpc.vipcard.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;


public class ConfigUtil {

//	private static String webUrl;
//
//	@Value("${web_url}")
//	public void setWebUrl(String value) {
//		webUrl = value;
//	}
//
//	public static String getWebUrl() {
//		return webUrl;
//	}

	public static String getConfigPropertiesStr(String key) {
		Environment environment = SpringContextUtil.getBean(Environment.class);
		return environment.getProperty(key);
	}
}
