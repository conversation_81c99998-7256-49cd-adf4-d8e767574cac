package com.extracme.evcard.rpc.vipcard.model;

import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuiXiangCardBaseDto;
import org.springframework.beans.BeanUtils;
import java.math.BigDecimal;
import java.util.Date;

public class SuixiangCardBase {

    /**
     * 转换dto
     * @return
     */
    public SuiXiangCardBaseDto toDto(){
        SuiXiangCardBaseDto suiXiangCardBaseDto = new SuiXiangCardBaseDto();
        BeanUtils.copyProperties(this,suiXiangCardBaseDto);
        return suiXiangCardBaseDto;
    }
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.card_name
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private String cardName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.org_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private String orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.city_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private String cityId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.advance_notice_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Date advanceNoticeTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.sale_start_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Date saleStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.sale_end_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Date saleEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.effective_days
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Integer effectiveDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.valid_days_type
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Integer validDaysType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.init_stock
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Integer initStock;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.stock
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Integer stock;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.sales
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Integer sales;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.display_flag
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Integer displayFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.single_order_duration
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private BigDecimal singleOrderDuration;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.style_type
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Integer styleType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.back_url
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private String backUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.rules
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private String rules;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.card_status
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Integer cardStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.create_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.update_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column suixiang_card_base.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    private Integer isDeleted;

    private Date advanceNoticeTimeStart;

    private Date advanceNoticeTimeEnd;

    private Integer saleMin;

    private Integer saleMax;

    private Integer leftStockMin;

    private Integer leftStockMax;

    private Integer holidayAvailable;

    private String unavailableDate;

    private Integer purchaseLimitNum;

    private Integer mergeFlag; // 非必填，兑换卡是否可以合并，1：可以 2：不可以 默认为2

    private Integer landingPageFlag; // 非必填，是否有落地页，1：有 2：没有，默认为2

    private String vehicleBrandIds; // 非必填，合作车企品牌id集合

    public Integer getMergeFlag() {
        return mergeFlag;
    }

    public void setMergeFlag(Integer mergeFlag) {
        this.mergeFlag = mergeFlag;
    }

    public Integer getLandingPageFlag() {
        return landingPageFlag;
    }

    public void setLandingPageFlag(Integer landingPageFlag) {
        this.landingPageFlag = landingPageFlag;
    }

    public String getVehicleBrandIds() {
        return vehicleBrandIds;
    }

    public void setVehicleBrandIds(String vehicleBrandIds) {
        this.vehicleBrandIds = vehicleBrandIds;
    }

    public Integer getPurchaseLimitNum() { return purchaseLimitNum; }

    public void setPurchaseLimitNum(Integer purchaseLimitNum) { this.purchaseLimitNum = purchaseLimitNum; }

    public Integer getLeftStockMin() {
        return leftStockMin;
    }

    public void setLeftStockMin(Integer leftStockMin) {
        this.leftStockMin = leftStockMin;
    }

    public Integer getLeftStockMax() {
        return leftStockMax;
    }

    public void setLeftStockMax(Integer leftStockMax) {
        this.leftStockMax = leftStockMax;
    }

    public Integer getSaleMin() {
        return saleMin;
    }

    public void setSaleMin(Integer saleMin) {
        this.saleMin = saleMin;
    }

    public Integer getSaleMax() {
        return saleMax;
    }

    public void setSaleMax(Integer saleMax) {
        this.saleMax = saleMax;
    }

    public Date getAdvanceNoticeTimeStart() {
        return advanceNoticeTimeStart;
    }

    public void setAdvanceNoticeTimeStart(Date advanceNoticeTimeStart) {
        this.advanceNoticeTimeStart = advanceNoticeTimeStart;
    }

    public Date getAdvanceNoticeTimeEnd() {
        return advanceNoticeTimeEnd;
    }

    public void setAdvanceNoticeTimeEnd(Date advanceNoticeTimeEnd) {
        this.advanceNoticeTimeEnd = advanceNoticeTimeEnd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.id
     *
     * @return the value of suixiang_card_base.id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.id
     *
     * @param id the value for suixiang_card_base.id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.card_name
     *
     * @return the value of suixiang_card_base.card_name
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public String getCardName() {
        return cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.card_name
     *
     * @param cardName the value for suixiang_card_base.card_name
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.org_id
     *
     * @return the value of suixiang_card_base.org_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.org_id
     *
     * @param orgId the value for suixiang_card_base.org_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.city_id
     *
     * @return the value of suixiang_card_base.city_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public String getCityId() {
        return cityId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.city_id
     *
     * @param cityId the value for suixiang_card_base.city_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.advance_notice_time
     *
     * @return the value of suixiang_card_base.advance_notice_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Date getAdvanceNoticeTime() {
        return advanceNoticeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.advance_notice_time
     *
     * @param advanceNoticeTime the value for suixiang_card_base.advance_notice_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setAdvanceNoticeTime(Date advanceNoticeTime) {
        this.advanceNoticeTime = advanceNoticeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.sale_start_time
     *
     * @return the value of suixiang_card_base.sale_start_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Date getSaleStartTime() {
        return saleStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.sale_start_time
     *
     * @param saleStartTime the value for suixiang_card_base.sale_start_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setSaleStartTime(Date saleStartTime) {
        this.saleStartTime = saleStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.sale_end_time
     *
     * @return the value of suixiang_card_base.sale_end_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Date getSaleEndTime() {
        return saleEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.sale_end_time
     *
     * @param saleEndTime the value for suixiang_card_base.sale_end_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setSaleEndTime(Date saleEndTime) {
        this.saleEndTime = saleEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.effective_days
     *
     * @return the value of suixiang_card_base.effective_days
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Integer getEffectiveDays() {
        return effectiveDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.effective_days
     *
     * @param effectiveDays the value for suixiang_card_base.effective_days
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setEffectiveDays(Integer effectiveDays) {
        this.effectiveDays = effectiveDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.valid_days_type
     *
     * @return the value of suixiang_card_base.valid_days_type
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Integer getValidDaysType() {
        return validDaysType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.valid_days_type
     *
     * @param validDaysType the value for suixiang_card_base.valid_days_type
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setValidDaysType(Integer validDaysType) {
        this.validDaysType = validDaysType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.init_stock
     *
     * @return the value of suixiang_card_base.init_stock
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Integer getInitStock() {
        return initStock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.init_stock
     *
     * @param initStock the value for suixiang_card_base.init_stock
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setInitStock(Integer initStock) {
        this.initStock = initStock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.stock
     *
     * @return the value of suixiang_card_base.stock
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Integer getStock() {
        return stock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.stock
     *
     * @param stock the value for suixiang_card_base.stock
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setStock(Integer stock) {
        this.stock = stock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.sales
     *
     * @return the value of suixiang_card_base.sales
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Integer getSales() {
        return sales;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.sales
     *
     * @param sales the value for suixiang_card_base.sales
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setSales(Integer sales) {
        this.sales = sales;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.display_flag
     *
     * @return the value of suixiang_card_base.display_flag
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Integer getDisplayFlag() {
        return displayFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.display_flag
     *
     * @param displayFlag the value for suixiang_card_base.display_flag
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setDisplayFlag(Integer displayFlag) {
        this.displayFlag = displayFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.single_order_duration
     *
     * @return the value of suixiang_card_base.single_order_duration
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public BigDecimal getSingleOrderDuration() {
        return singleOrderDuration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.single_order_duration
     *
     * @param singleOrderDuration the value for suixiang_card_base.single_order_duration
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setSingleOrderDuration(BigDecimal singleOrderDuration) {
        this.singleOrderDuration = singleOrderDuration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.style_type
     *
     * @return the value of suixiang_card_base.style_type
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Integer getStyleType() {
        return styleType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.style_type
     *
     * @param styleType the value for suixiang_card_base.style_type
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setStyleType(Integer styleType) {
        this.styleType = styleType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.back_url
     *
     * @return the value of suixiang_card_base.back_url
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public String getBackUrl() {
        return backUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.back_url
     *
     * @param backUrl the value for suixiang_card_base.back_url
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.rules
     *
     * @return the value of suixiang_card_base.rules
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public String getRules() {
        return rules;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.rules
     *
     * @param rules the value for suixiang_card_base.rules
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setRules(String rules) {
        this.rules = rules;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.card_status
     *
     * @return the value of suixiang_card_base.card_status
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Integer getCardStatus() {
        return cardStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.card_status
     *
     * @param cardStatus the value for suixiang_card_base.card_status
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setCardStatus(Integer cardStatus) {
        this.cardStatus = cardStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.create_time
     *
     * @return the value of suixiang_card_base.create_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.create_time
     *
     * @param createTime the value for suixiang_card_base.create_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.create_oper_id
     *
     * @return the value of suixiang_card_base.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.create_oper_id
     *
     * @param createOperId the value for suixiang_card_base.create_oper_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.create_oper_name
     *
     * @return the value of suixiang_card_base.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.create_oper_name
     *
     * @param createOperName the value for suixiang_card_base.create_oper_name
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.update_time
     *
     * @return the value of suixiang_card_base.update_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.update_time
     *
     * @param updateTime the value for suixiang_card_base.update_time
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.update_oper_id
     *
     * @return the value of suixiang_card_base.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.update_oper_id
     *
     * @param updateOperId the value for suixiang_card_base.update_oper_id
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.update_oper_name
     *
     * @return the value of suixiang_card_base.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.update_oper_name
     *
     * @param updateOperName the value for suixiang_card_base.update_oper_name
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column suixiang_card_base.is_deleted
     *
     * @return the value of suixiang_card_base.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column suixiang_card_base.is_deleted
     *
     * @param isDeleted the value for suixiang_card_base.is_deleted
     *
     * @mbggenerated Wed Jan 11 20:28:14 CST 2023
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getHolidayAvailable() {
        return holidayAvailable;
    }

    public void setHolidayAvailable(Integer holidayAvailable) {
        this.holidayAvailable = holidayAvailable;
    }

    public String getUnavailableDate() {
        return unavailableDate;
    }

    public void setUnavailableDate(String unavailableDate) {
        this.unavailableDate = unavailableDate;
    }
}