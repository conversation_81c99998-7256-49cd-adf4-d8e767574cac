spring.profiles=siac

#datasource
spring.datasource.druid.url=*************************************************************************************************************************************
spring.datasource.druid.username=vipcard
spring.datasource.druid.password=y4QACaGi

#dubbo
dubbo.registry.address=zookeeper://**************:2181

#redis
#spring.redis.host=************
#spring.redis.port=6379

#elasticjob
elasticjob.regCenter.serverLists=**************:2181

#apollo
apollo.meta=http://apollo-st.evcard.vip:48080

#logger
logging.level.org.apache.dubbo=warn
logging.level.org.apache.zookeeper=warn
logging.level.root=debug
logging.level.dao=debug

#md.rest
md.rest.api.baseUrl=https://md-st.evcard.vip/

ons.nameSrvAddr = http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
ons.accessKey = LTAI4ClyMUapT6MD
ons.secretKey = iGN2b54Hdc3oo4BHk8t9M13dijyKnw
ons.contract.normal.topic = MD_CONTRACT_TOPIC_SIT
ons.contract.groupId = GID_MD_VIPCARD_SIT