spring.profiles = local

#datasource
spring.datasource.druid.url = ************************************************************************************************************************************************************
spring.datasource.druid.username = vipcard
spring.datasource.druid.password = y4QACaGi

#dubbo
dubbo.registry.address = zookeeper://zk-st.evcard.vip:2181

#redis
#spring.redis.host=************
#spring.redis.port=6379

#elasticjob
elasticjob.regCenter.serverLists = zk-st.evcard.vip:2181


#apollo
apollo.bootstrap.enabled=false
apollo.bootstrap.namespaces=config,ons
#apollo.meta = http://apollo-test.evcard.vip:58080
apollo.bootstrap.eagerLoad.enabled=false

#logger
logging.level.org.apache.dubbo = warn
logging.level.org.apache.zookeeper = warn
logging.level.root = info
logging.level.dao = info

host = evcard-st-wan.redis.rds.aliyuncs.com
port = 6379
pwd = Wp4uJK*Vc3v2
timeout = 10000
maxWaitMillis = -1
minIdle = 8
maxIdle = 16
maxTotal = 100


sys.app.code = vipcard-rpc
web_url = http://evcard.oss-cn-shanghai.aliyuncs.com/test

ons.topic = EVCARD_RAW_DATA_SAIC
ons.topic.delay = EVCARD_DELAYED_MSG_SAIC
ons.gid = GID_EVCARD_VIPCARD_SAIC
ons.addr = http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
ali.AccessKey = LTAI5t6i6Tb9XN6gRqkstxYH
ali.SecretKey = ******************************


#apollo
apollo.meta=http://apollo-st.evcard.vip:48080

#md.rest
md.rest.api.baseUrl=https://md-st.evcard.vip/

ons.nameSrvAddr = http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
ons.accessKey = LTAI5t6i6Tb9XN6gRqkstxYH
ons.secretKey = ******************************
ons.contract.normal.topic = MD_CONTRACT_TOPIC_DEV
ons.contract.groupId = GID_MD_VIPCARD_DEV
