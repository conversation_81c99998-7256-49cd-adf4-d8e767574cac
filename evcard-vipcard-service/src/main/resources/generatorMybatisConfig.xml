<?xml version="1.0" encoding="UTF-8" ?>
        <!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration >
<!--<classPathEntry location="libs/mysql-connector-java-5.1.35.jar"/>-->
    <classPathEntry location="C:\Users\<USER>\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar"/>
<context id="context1" >
    <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver" connectionURL="*******************************************************" userId="devuser" password="blk2ZsEB"/>
    <javaModelGenerator targetPackage="com.extracme.evcard.rpc.vipcard.model" targetProject="src\main\java" />
    <sqlMapGenerator targetPackage="com.extracme.evcard.rpc.vipcard.dao" targetProject="src\main\java"/>
    <javaClientGenerator targetPackage="com.extracme.evcard.rpc.vipcard.dao" targetProject="src\main\java" type="XMLMAPPER" />
    <table schema="iss" tableName="suixiang_card_cdk_config"   enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="false" enableSelectByExample="true" selectByExampleQueryId="false">
    </table>
    <table schema="iss" tableName="suixiang_card_cdk"   enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="false" enableSelectByExample="true" selectByExampleQueryId="false">
    </table>
    <table schema="iss" tableName="suixiang_card_cdk_config_detail"   enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="false" enableSelectByExample="true" selectByExampleQueryId="false">
    </table>
</context>
</generatorConfiguration>

<!--<?xml version="1.0" encoding="UTF-8" ?>-->
<!--<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >-->
<!--<generatorConfiguration >-->
<!--    &lt;!&ndash;<classPathEntry location="libs/mysql-connector-java-5.1.35.jar"/>&ndash;&gt;-->
<!--    <classPathEntry location="/Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.1.27/mysql-connector-java-5.1.27.jar" />-->
<!--    <context id="context1" >-->
<!--        <commentGenerator>-->
<!--            <property name="suppressDate" value="true"/>-->
<!--            <property name="suppressAllComments" value="true" />-->
<!--        </commentGenerator>-->
<!--        <jdbcConnection driverClass="com.mysql.jdbc.Driver" connectionURL="***********************************" userId="root" password="Extracme123" />-->
<!--        <javaModelGenerator targetPackage="com.extracme.evcard.rpc.vipcard.model" targetProject="src/main/java" />-->
<!--        <sqlMapGenerator targetPackage="com.extracme.evcard.rpc.vipcard.dao" targetProject="src/main/java"/>-->
<!--        <javaClientGenerator targetPackage="com.extracme.evcard.rpc.vipcard.dao" targetProject="src/main/java" type="XMLMAPPER" />-->
<!--        <table schema="iss" tableName="mmp_user_card_info"   enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--    </context>-->
<!--</generatorConfiguration>-->