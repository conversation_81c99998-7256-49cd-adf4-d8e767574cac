spring.profiles=dev

#datasource
spring.datasource.druid.url=***********************************************************
spring.datasource.druid.username=devuser
spring.datasource.druid.password=blk2ZsEB


#apollo
apollo.meta= http://apollo-test.evcard.vip:58080

#md.rest
md.rest.api.baseUrl=https://md-dev.evcard.vip/

#dubbo
dubbo.registry.address=zookeeper://zk-dev.evcard.vip:2181

#redis
#spring.redis.host=************
#spring.redis.port=6379

#elasticjob
elasticjob.regCenter.serverLists=zk-dev.evcard.vip:2181

#md.contract
md.inner.api.baseUrl=https://md-dev.evcard.vip/

ons.nameSrvAddr = http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
ons.accessKey = LTAI5t6i6Tb9XN6gRqkstxYH
ons.secretKey = ******************************
ons.contract.normal.topic = MD_CONTRACT_TOPIC_DEV
ons.contract.groupId = GID_MD_VIPCARD_DEV