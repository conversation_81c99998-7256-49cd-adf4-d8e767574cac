<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<!-- dubbo 服务发布 retries表示重试，查询可以重试，事务性操作，不建议启用重试设置为0 -->
	<dubbo:service interface="com.extracme.evcard.rpc.vipcard.service.ICardModelService" ref="cardModelService" protocol="dubbo"/>
	<dubbo:service interface="com.extracme.evcard.rpc.vipcard.service.IMemberCardService" ref="memberCardService" protocol="dubbo"/>
	<dubbo:service interface="com.extracme.evcard.rpc.vipcard.service.ICardSalesActivityService" ref="cardSalesActivityService" protocol="dubbo"/>
	<dubbo:service interface="com.extracme.evcard.rpc.vipcard.service.ICardSalesActivityConfigService" ref="cardSalesActivityConfigService" protocol="dubbo"/>
	<dubbo:service interface="com.extracme.evcard.rpc.vipcard.service.ICardTradeService" ref="cardTradeService" protocol="dubbo"/>

	<dubbo:service interface="com.extracme.evcard.rpc.vipcard.service.ISuixiangCardConfigService" ref="suixiangCardConfigService" protocol="dubbo">
        <dubbo:method name="giveSuiXiangCard" timeout="600000" />
		<dubbo:method name="sendSuiXiangCard" timeout="600000" />
    </dubbo:service>

	<dubbo:service interface="com.extracme.evcard.rpc.vipcard.service.ISuixiangCardSalesService" ref="suixiangCardSalesService" protocol="dubbo" timeout="20000"/>
	<dubbo:service interface="com.extracme.evcard.rpc.vipcard.service.ISuiXiangCardService" ref="suiXiangCardService" protocol="dubbo"/>
	<dubbo:service interface="com.extracme.evcard.rpc.vipcard.service.ISuiXiangCardTradeService" ref="suiXiangCardTradeService" protocol="dubbo"/>

	<dubbo:service interface="com.extracme.evcard.rpc.vipcard.service.ISuixiangCardRefundService" ref="suixiangCardRefundService" protocol="dubbo" />
	<!-- 随享卡cdk服务 -->
	<dubbo:service interface="com.extracme.evcard.rpc.vipcard.service.ISuixiangCardCdkService" ref="suixiangCardCdkService" protocol="dubbo">
		<dubbo:method name="queryAndExportSuiXiangCardCdk" timeout="600000" />
		<dubbo:method name="generateSuixiangCardCdk" timeout="600000" />
	</dubbo:service>

	<dubbo:reference interface="com.extracme.evcard.rpc.vehicle.service.IVehicleService" id="vehicleService" check="false"/>
	<dubbo:reference interface="com.extracme.evcard.rpc.vehicle.service.IVehicleModelService" id="vehicleModelService" check="false"/>

	<dubbo:reference interface="com.extracme.evcard.rpc.messagepush.service.IMessagepushServ" id="messageServ" protocol="dubbo" check="false"/>
	<dubbo:reference id="memberShipCoreService" interface="com.extracme.evcard.membership.core.service.IMemberShipService" check="false" />
	<dubbo:reference id="agencyDiscountService" interface="com.extracme.evcard.membership.core.service.agency.IAgencyDiscountService" check="false" />
	<dubbo:reference id="membershipWrapService" interface="com.extracme.evcard.membership.core.service.IMembershipWrapService" check="false" />
	<dubbo:reference id="baseInfoService" interface="com.extracme.evcard.membership.core.service.IBaseInfoService" check="false" />

	<dubbo:reference interface="com.extracme.evcard.rpc.order.service.IOrderService" id="orderService" protocol="dubbo" check="false"/>
	<dubbo:reference interface="com.extracme.evcard.rpc.shop.service.IShopService" id="shopService" protocol="dubbo" check="false"/>
	<dubbo:reference interface="com.extracme.evcard.membership.core.service.IShortlinkManagementService" id="shortlinkManagementService"  protocol="dubbo" check="false" timeout="600000"/>
	<!-- 企业会员相关服务 -->
	<dubbo:reference interface="com.extracme.evcard.bvm.service.IAgencyPriceService" id="agencyPriceService" protocol="dubbo" check="false"/>
	<dubbo:reference interface="com.extracme.evcard.bvm.service.IAgencyRoleService" id="agencyRoleService" protocol="dubbo" check="false"/>
	<dubbo:reference interface="com.extracme.evcard.bvm.service.IAgencyStoreService" id="agencyStoreService" protocol="dubbo" check="false"/>

	<dubbo:reference interface="com.extracme.evcard.rpc.pay.service.IEvcardPayService" id="evcardPayService" protocol="dubbo" check="false"/>

	<!-- 埋点 -->
	<dubbo:reference interface="com.extracme.evcard.rpc.messagepush.service.ISensorsdataService" id="sensorsdataService" check="false" protocol="dubbo"/>
</beans>