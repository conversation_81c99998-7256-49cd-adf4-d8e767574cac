spring.profiles.active=siac
spring.application.name=evcard-vipcard-rpc
spring.main.allow-bean-definition-overriding=true
spring.main.web-application-type=none

logging.config=classpath:logback-spring.xml
logging.file.max-history=30
logging.file.max-size=100MB

#datasource
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.max-active=100
spring.datasource.druid.initial-size=1
spring.datasource.druid.filters=dbauditlog
#spring.datasource.druid.max-wait=10000
spring.datasource.druid.min-idle=1
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=select 'x'
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-open-prepared-statements=50
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20


#dubbo
dubbo.application.logger=slf4j
dubbo.scan.base-packages=com.extracme.evcard.rpc.vipcard.service
dubbo.registry.register=true
dubbo.registry.file=./dubboregistry/dubbo-registry.properties
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.heartbeat=15000
dubbo.provider.timeout=10000
dubbo.provider.threadpool=fixed
dubbo.provider.threads=200
dubbo.provider.accepts=1000
dubbo.provider.protocol=dubbo
dubbo.provider.cluster=failfast
dubbo.provider.loadbalance=roundrobin
dubbo.provider.server=netty
dubbo.consumer.lazy=true
dubbo.consumer.check=false
dubbo.consumer.cluster=failfast
dubbo.provider.port=20919
#\u5F85\u786E\u8BA4\u670D\u52A1\u7AEF\u8BBE\u7F6Econnections\u662F\u5426\u6709\u6548&\u5408\u7406\u7684\u53D6\u503C
dubbo.consumer.connections=1
dubbo.provider.application.name=evcard-vipcard-service
dubbo.owner=wuyibo
dubbo.organization=extracme
dubbo.registry.address=zookeeper://**************:2181
dubbo.registry.timeout=400000
dubbo.protocol.payload=104857600
#mybatis
mybatis.mapper-locations=classpath*:com/extracme/evcard/rpc/vipcard/dao/*.xml
mybatis.configuration.variables.issSchema=iss
mybatis.configuration.variables.siacSchema=siac
mybatis.configuration.variables.isvSchema=isv

mybatis.configuration.cache-enabled=true
mybatis.configuration.lazy-loading-enabled=false
mybatis.configuration.multiple-result-sets-enabled=true
mybatis.configuration.use-column-label=true
mybatis.configuration.use-generated-keys=true
mybatis.configuration.auto-mapping-behavior=partial
mybatis.configuration.default-executor-type=simple
mybatis.configuration.default-statement-timeout=25
mybatis.configuration.safe-row-bounds-enabled=false
mybatis.configuration.map-underscore-to-camel-case=false
mybatis.configuration.local-cache-scope=session
mybatis.configuration.jdbc-type-for-null=other
mybatis.configuration.lazy-load-trigger-methods=equals,clone,hashCode,toString
mybatis.configuration.logPrefix=dao.


logging.level.org.apache.dubbo=warn
logging.level.org.apache.zookeeper=warn
logging.level.root=debug

#redis
#spring.redis.timeout=10000
#spring.redis.database=0
#spring.redis.jedis.pool.max-active=20
#spring.redis.jedis.pool.max-idle=5
#spring.redis.jedis.pool.min-idle=2


#elasticjob
elasticjob.regCenter.serverLists=**************:2181
elasticjob.regCenter.namespace=elastic-job


#apollo
app.id= ${spring.application.name}
apollo.cluster=evcard
#apollo enable during spring boot applyInitializers
apollo.bootstrap.enabled=true
apollo.bootstrap.namespaces=redis,config,application,ons
apollo.bootstrap.eagerLoad.enabled=true


md.rest.api.baseUrl=https://md-dev.evcard.vip/
md.rest.api.getCachedGoodsModelUrl=mdadmin/goods/inner/getCacheGoodsModelName
md.rest.api.getCachedStoreListUrl=mdadmin/store/inner/getCacheStore
md.rest.api.getGoodsModelByVehicleModelUrl=mdadmin/goods/inner/getGoodsModelIdByVehicleModelSeq
md.rest.api.payOrderIsPayingOrNotUrl=mdadmin/pay/inner/payOrderIsPayingOrNot
md.rest.api.getAllGoodsModeInfoUrl=mdadmin/goods/inner/getAllGoodsModeInfo
md.rest.api.createPayOrderUrl=mdadmin/pay/inner/createPayOrder
md.rest.api.cancelPayOrderUrl=mdadmin/pay/inner/cancelPayOrder
md.rest.api.returnCardFeeUrl=mdadmin/pay/inner/returnCardFee
md.rest.api.paySuiXiangCardOrderUrl=mdadmin/pay/inner/payOrder
md.rest.api.refundForCriticalPointPaymentUrl=mdadmin/pay/inner/refundForCriticalPointPayment
md.rest.api.getCacheLowAndPeakSeasonConfigUrl=mdadmin/store/inner/getCacheLowAndPeakSeasonConfig

hq.encrypt.config.outPublicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC9TgYD1FbvPiSwsUcEunx/IKTNRdRou+YrIvpT+gdvDEw4ElrjKcd9SmDl4P5n6EAKUjqmIY4jc0h8cXMA59tv40OdVrRh9blav6QSXzhl5iCnE0DoIj0ormMGv2o7oEDb4+00drlvxIm/xv6nwpt3UfPh7TLh8tCa3gmyd6ONsQIDAQAB

hq.encrypt.config.outPrivateKey=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL1OBgPUVu8+JLCxRwS6fH8gpM1F1Gi75isi+lP6B28MTDgSWuMpx31KYOXg/mfoQApSOqYhjiNzSHxxcwDn22/jQ51WtGH1uVq/pBJfOGXmIKcTQOgiPSiuYwa/ajugQNvj7TR2uW/Eib/G/qfCm3dR8+HtMuHy0JreCbJ3o42xAgMBAAECgYB/RhvpKzQfuao1WZsj5LWbSzlCAbbji6EeU2/2Ud35nU0JTd3paMeExyMKqxCgj5eu2MYxt9G2p+WONUBa1YfEM+IVooeNqGEHAgE8v8K+30227I2JbGn1CiQHPQZ5l8qqkovb1zGDqjSvh7QPdSqxuENlC81Ny0kgWeyb//CtuQJBAOMAw2etx+Q86iDlODuQgoPste4DEBmjhAhAHWLQ+kplPhfHUjxl6o78YCAUh6PWq+OuWF+3D1pLYCER+lBOLj8CQQDVfH0/xCVheWA06MLS42D+00kbdHJEGR4ctlCTclkP1ZVgpiGb2E8A6WHbiEFSr5szxSbtRt9owcWOWrLo6ygPAkAog+1cZt7jdJ/RCThVIUC6LRh3h6JrAQ1Cr7bO+T+C4bkIY3HhB8uqg47giul+q4T4WRi61KBv3D4nCN9lT97fAkA9rWeqIosCLETlRFH1B4ggVv6KFgxlLDSM6wdAcgrVDknH0HdpRQceJk8FU8o7j0xxmoXyi0VESKdAx2dk73t1AkEAjaNVQ/bKclNzQAG1kAH4HiHo8rEZw99q6dr5W/342XUfojWFx6f5RJj1bfpCHWCW6tWW3+Zn6vxVUYZE9Yat4w==

hq.encrypt.config.innerPublicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCc55NKtbKv9VWTIRiWBou/zElsN8oeGRFacIkWYkDYLvo+8LHMEUZALZR4lMCw7AumqCkc8gQyp1vGsaGiy+sPoUSdaHpmXyAFHcxAVxH6OG3cW2IBdFwLAm+24StHKudWLStIpszXmw6io/C3jWat8He7heSxy6iXRMSQnA1qeQIDAQAB

hq.encrypt.config.innerPrivateKey=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJznk0q1sq/1VZMhGJYGi7/MSWw3yh4ZEVpwiRZiQNgu+j7wscwRRkAtlHiUwLDsC6aoKRzyBDKnW8axoaLL6w+hRJ1oemZfIAUdzEBXEfo4bdxbYgF0XAsCb7bhK0cq51YtK0imzNebDqKj8LeNZq3wd7uF5LHLqJdExJCcDWp5AgMBAAECgYBM/taossEc16dGq9cEx/GMDRN6p2XnnyWE2/Rpp41qiKMEf9i/6zouBC/3fAeSykJtZdfG/HxS3w2KU2CeIrMWE0T1C6n6OaQZko2tbiz9xM6ubLU69ms1zXJk4PKEU43ThfwX2KIokL5nEvhuwd/HZzEZrB87t4j0t2wUweCsgQJBANaPxL28hg0LO5Q3VyAWIg+OIsX5O/02qu6B35yPmveslFquTdp5JYx7RNC4x9WVFLP7wQox57mUSVTFZATfuRcCQQC7NSmJopLa4fGalzQjKNX67JAZlUmTXxnU8kez/fR+e+klqM9UZga6+E2jlq5MdpZg+EctjcqnT9AZgsVQtBLvAkBTG6MAP0rJOKWeNoVX11rgHZUYi/6RiSBSfBnKQdGrx7w7F7LuQeiqboDsO/ajozg+svBF66ivOGHjJQYl4SaHAkEArcgy7/5HvwKBSlVUY9MTmCQjvGv16eUdUK073J1BamzSSFBFbn5rfwLi55oH4RkwjYHMaUA3wEr5zcS0MC+JZwJAC4HvZ5+BiGyQq0u0ZVmNY7oPjbl1bvv6oEbGjghPp7Rt5OO/QD1HIdkYnobBYt6GKPJbrLxXTx6drJMCfJPj/w==