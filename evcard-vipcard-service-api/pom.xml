<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.extracme.evcard</groupId>
		<artifactId>evcard-vipcard-rpc</artifactId>
		<version>2.2.68</version>
	</parent>
	<artifactId>evcard-vipcard-service-api</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.extracme</groupId>
			<artifactId>extracme-framework-core</artifactId>
			<version>1.0.0</version>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-core-rpc</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.16.18</version>
		</dependency>
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-mq-bean</artifactId>
			<version>********</version>
			<exclusions>
				<exclusion>
					<artifactId>javassist</artifactId>
					<groupId>javassist</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-annotations</artifactId>
			<version>1.5.22</version>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>compile</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
