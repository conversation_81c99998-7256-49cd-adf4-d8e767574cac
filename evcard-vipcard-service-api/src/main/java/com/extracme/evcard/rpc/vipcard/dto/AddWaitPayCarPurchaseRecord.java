package com.extracme.evcard.rpc.vipcard.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 10:28 2020/12/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddWaitPayCarPurchaseRecord implements Serializable {
    /**
     * 购买记录ID
     * @since 1.3.0
     */
    private Long purchaseId;

    /**
     * 1：单独购卡 2：混合支付
     */
    private Integer type;

    /**
     * 混合支付时折扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 混合支付时订单实际应收
     */
    private BigDecimal amount;


    /**
     * 混合支付时传入
     */
    private String orderSeq;

    /**
     * 用户的pkId 必填
     */
    private Long userId;

    /**
     * 会员卡活动id 非必填
     * @Deprecated 1.4.0后，此入参废弃
     */
    @Deprecated
    private Long cardActivityId;

    /**
     * 会员卡id 必填
     */
    private Long cardId;

    /**
     * 购买数量 必填
     */
    private Integer quantity;

    /**
     * 支付金额 必填
     */
    private Double realAmount;

    /**
     * 交易号 必填
     */
    private String outTradeSeq;

    /**
     * 操作人名称 必填
     */
    private String operator;

    /**
     * 本次发卡生效时间，非必传
     * 预付订单合并支付场景下： 使用下单时间作为卡片生效开始时间
     */
    private Date activeStartTime;
}
