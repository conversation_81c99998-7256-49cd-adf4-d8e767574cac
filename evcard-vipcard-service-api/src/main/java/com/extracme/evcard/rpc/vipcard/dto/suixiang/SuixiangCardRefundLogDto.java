package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SuixiangCardRefundLogDto implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 随享卡基础表id
     */
    private Long cardBaseId;

    /**
     * 随享卡用户使用表id
     */
    private Long cardUseId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 用户姓名
     */
    private String username;

    /**
     * 购买订单号
     */
    private String orderSeq;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    private String refundTime;

    /**
     * 退款类型，1=手动退款，2=自动原路
     */
    private Integer type;

    /**
     * 退款结果，0=失败，1=成功
     */
    private Integer success;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人id
     */
    private Integer createOperId;

    /**
     * 创建人
     */
    private String createOperName;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 修改人id
     */
    private Integer updateOperId;

    /**
     * 修改人
     */
    private String updateOperName;

    /**
     * 备注
     */
    private String remark;
}
