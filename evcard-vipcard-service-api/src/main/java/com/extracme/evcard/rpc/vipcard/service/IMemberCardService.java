package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.QueryAvailableCardInfoDto;
import com.extracme.evcard.rpc.vipcard.dto.QueryUserCardInfoDto;
import com.extracme.evcard.rpc.vipcard.dto.QueryUserCardStatusDto;
import com.extracme.evcard.rpc.vipcard.dto.UserRemindActivityDto;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.framework.core.bo.PageBeanBO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 用户会员卡服务
 */
public interface IMemberCardService {

    /**
     * 查询用户已经购买的会员卡
     *
     * @param id 会员id
     */
    QueryUserCardInfoDto queryUserVipCardInfo(Long id);

    /**
     * 查询用户卡详情
     * @param userCardNo
     * @param id 会员pkId
     * @return
     */
    QueryCardInfoDetail queryUserCardDetail(Long userCardNo, Long id) throws BusinessException;


    /**
     * 查询卡详情
     *
     * @param cardId
     * @param type   0：仅查询卡信息 1：用户购买详情
     * @param id     会员pkId
     * @return
     */
    QueryCardInfoDetail queryCardInfoById(Long cardId, Integer type, Long id);

    /**
     * 订单可用会员卡列表
     *
     * @param inputDto
     * @return
     */
    List<QueryAvailableCardInfoDto> getAvailableCardByOrder(GetAvailableCardByOrderInputDto inputDto);

    /**
     * 根据用车条件获取可用会员卡列表
     *
     * @param inputDto
     * @return
     */
    List<QueryAvailableCardInfoDto> getAvailableCardByUseCondition(GetAvailableCardByUseConditionInputDto inputDto);

    /**
     * 根据用车条件判断是否能折扣
     * @param inputDto
     * @param userCardNo
     * @param discountRate 0~1的折扣率
     * @param orderTime
     * @param orderFrozenAmount 此订单冻结的金额 还车最后重新计算时传入
     * @param needCheckUsedMaxValue 是否需要检查已经使用的最高抵扣金额 (续租的时候传true,还车的时候传false)
     * @return
     */
    QueryAvailableCardInfoDto checkCardByUseCondition(GetAvailableCardByUseConditionInputDto inputDto, Long userCardNo, BigDecimal discountRate, Date orderTime, BigDecimal orderFrozenAmount, Boolean needCheckUsedMaxValue);

    /**
     * 根据用车条件判断是否能折扣，重载
     * @param input
     * @return
     */
    QueryAvailableCardInfoDto checkCardByUseCondition(CheckCardByUseConditionInput input);

    /**
     * 用户设置的开售提醒，只有还未提醒过得
     *
     * @param userId
     * @param activityIds 为空时查询所有
     */
    List<Long> queryCardRemind(Long userId, List<Long> activityIds);


    /**
     * 设置卡的开售提醒
     *
     * @param userRemindActivityDto
     */
    void submitCardRemind(UserRemindActivityDto userRemindActivityDto);

    /**
     * 查询用户卡状态
     *
     * @param userId
     * @return
     */
    QueryUserCardStatusDto userCardStatus(Long userId);

    /**
     * 批量发放企业会员卡&企业个人卡
     *
     * @param userIds
     * @param agencyId
     * @param disable 是否先作废旧企业卡，再发放新卡
     * @param operateDto
     * @return
     * @remark 变更会员关联企业时使用, 将作废用户原关联企业卡
     * @since 1.0.0
     */
    BaseResponse batchOfferCorporateCard(Set<Long> userIds, String agencyId, boolean disable, OperatorDto operateDto);

    /**
     * 批量发放企业会员卡&企业个人卡
     *
     * @param userIds
     * @param agencyId
     * @param operateDto
     * @return
     * @remark 添加用户至企业时使用, 仅做发卡操作，不失效旧卡
     * @since 1.0.0
     */
    BaseResponse batchOfferCorporateCard(Set<Long> userIds, String agencyId, OperatorDto operateDto);

    /**
     * 批量作废企业会员卡&企业个人卡
     *
     * @param userIds
     * @param operateDto
     * @return
     * @remark 将用户移出企业时使用; agencyId为空时作废用户全部企业卡
     * @since 1.0.0
     */
    BaseResponse batchDisableCorporateCard(Set<Long> userIds, OperatorDto operateDto);

    /**
     * 批量作废企业会员卡&企业个人卡
     *
     * @param userIds
     * @param agencyId
     * @param operateDto
     * @return
     * @remark 将用户移出企业时使用; agencyId为空时作废用户全部企业卡
     * @since 1.0.0
     */
    BaseResponse batchDisableCorporateCard(Set<Long> userIds, String agencyId, OperatorDto operateDto);

    /**
     * 分页查询用户的会员卡TAB
     *
     * @param userId
     * @param queryDto
     * @return
     * @remark 业务系统使用
     * @since 1.0.0
     */
    PageBeanBO<MemberCardListViewDto> queryPageByUserId(Long userId, MemberCardQueryDto queryDto);

    /**
     * 作废用户的会员卡
     * @param userCardNo
     * @return
     * @Deprecated 未无效调旧折扣记录???，不建议使用
     */
    @Deprecated
    BaseResponse cancelMemberCard(Long userCardNo, OperatorDto operateDto);

    /**
     * 作废用户的会员卡
     * @param userId
     * @param cardId
     * @param operateDto
     * @return
     * @remark 无效后续折扣记录；存在冻结金额时不允许作废
     */
    BaseResponse cancelMemberCard(Long userId, Long cardId, OperatorDto operateDto);

    /**
     * 根据购买记录ID获取购买详情
     * @param recordId
     * @return
     * @remark 订单系统-退款单使用
     */
    CardPurchaseDetailDto getCardPurchaseDetail (Long recordId);

    /**
     * 根据购买记录ID获取购买详情
     * @param orderSeq 购买订单号
     * @return
     * @remark 订单系统-退款单使用
     * @since 1.1.0
     */
    CardPurchaseDetailDto getCardPurchaseDetail (String orderSeq);

    /**
    * 查询购卡订单
     * @param queryCardPurchaseInfoDto
    * @Return: [queryCardPurchaseInfoDto]
    * @Author: wudi
    */
    List<CardPurchaseRecordDto> queryCardPurchaseInfo(QueryCardPurchaseInfoDto queryCardPurchaseInfoDto);

    /**
     * 用户卡周期内折扣明细
     * @param queryDto
     * @return
     * @remark 分页，后端业务系统使用
     */
    PageBeanBO<UserCardDiscountViewDto> selectCardDiscountPage(CardDiscountInfoQueryDto queryDto);

    /**
     * 分页查询卡片操作履历
     * @param queryDto
     * @return
     * @since 1.1.0
     */
    PageBeanBO<UserCardHistoryDto> selectUserCardHistoryPage(UserCardHistoryQueryDto queryDto);

    /**
     * 获取用户未读的赠送类付费会员卡列表并标记为已读
     * @param userId
     * @param size
     * @return
     * @since 1.1.0
     */
    List<CardPurchaseListDetailDto> pullUnreadOfferedCards(Long userId, Integer size);

    /**
     * 将卡片标记为已读
     * @param userCardNos
     * @return
     * @since 1.1.0
     */
    int readOfferedCards(List<Long> userCardNos);


    /**
     * 查询购卡订单
     * @param
     * @Return: [queryCardPurchaseInfoDto]
     *
     */
    List<CardPurchaseRecordDto> queryCardPurchaseInfoByUserId(Long userId);

    /**
     * 查询用户 指定状态的 会员卡购卡记录
     * @param userId
     * @param paymentStatus
     * @return
     */
    List<CardPurchaseRecordDto> queryCardPurchaseInfoListByCondition(Long userId, int paymentStatus);
}