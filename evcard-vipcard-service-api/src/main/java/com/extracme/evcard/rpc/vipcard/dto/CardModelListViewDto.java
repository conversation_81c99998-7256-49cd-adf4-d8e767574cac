package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;


/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/28
 */
@Data
public class CardModelListViewDto extends CardModelDetailDto{
    /**
     * 产品线名称，逗号分隔
     */
    private String rentMethodNames;

    /**
     * 产品线大类名称，逗号分隔
     */
    private String rentMethodGroupNames;

    /**
     * 可用车型（多个车型名称，以,分割）
     */
    private String vehicleModelNames;

    /**
     * 可用门店（多个门店名称，以,分割）
     */
    private String storeNames;

    /**
     * 可用区域（多个城市名称，以,分割）
     */
    private String cityNames;

    /**
     * 是否可编辑，0可编辑 1不可编辑
     */
    private Integer editAvailable = 1;

    /**
     * 操作人信息
     */
    private String operatorName;

    /**
     * 操作人所属机构
     */
    private String operatorOrgName;
}
