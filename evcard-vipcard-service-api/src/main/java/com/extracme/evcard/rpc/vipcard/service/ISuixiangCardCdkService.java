package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;

public interface ISuixiangCardCdkService {

    /**
     * 发放cdkey
     * @param generateSuiXiangCardCdkInput
     * @return
     */
    GenerateSuiXiangCardCdkOutput generateSuixiangCardCdk(GenerateSuiXiangCardCdkInput generateSuiXiangCardCdkInput);


    ActivateSuixiangCardCdkOutput activateSuixiangCardCdk(ActivateSuixiangCardCdkInput activateSuixiangCardCdkInput);

    /**
     * 查询并导出cdkey
     * @param input
     * @return
     */
    QueryAndExportSuiXiangCardCdkOutput queryAndExportSuiXiangCardCdk(QueryAndExportSuiXiangCardCdkInput input);


    /**
     * 用户随享卡基础表id 查询 生成兑换码列表
     * @param input
     * @return
     */
    QuerySuiXiangCardCdkConfigDetailOutput querySuiXiangCardCdkConfigDetail(QuerySuiXiangCardCdkConfigDetailInput input);


    /**
     *
     * 根据基础表id 查询兑换码配置表记录
     * @param input
     * @return
     */
    QuerySuiXiangCardCdkConfigOutput querySuiXiangCardCdkConfig(QuerySuiXiangCardCdkConfigDetailInput input);

    /**
     * 发放预激活的cdkey（未注册发放）
     */
    OfferSuixiangCardPreCdkOutput offerSuixiangCardPreCdk(OfferSuixiangCardPreCdkInput input);
}
