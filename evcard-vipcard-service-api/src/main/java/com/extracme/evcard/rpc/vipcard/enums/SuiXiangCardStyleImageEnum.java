package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Getter
@AllArgsConstructor
public enum SuiXiangCardStyleImageEnum {
    ONE(1, "https://evcard.oss-cn-shanghai.aliyuncs.com/prod/suixiangcard/suixiangcard_style_1_1.png",
            "https://evcard.oss-cn-shanghai.aliyuncs.com/prod/suixiangcard/suixiangcard_style_1_gray.png",
            "https://evcard.oss-cn-shanghai.aliyuncs.com/prod/suixiangcard/suixiangcard_style_1_mycard_2.png"),
    TWO(2, "https://evcard.oss-cn-shanghai.aliyuncs.com/prod/suixiangcard/suixiangcard_style_2_1.png",
            "https://evcard.oss-cn-shanghai.aliyuncs.com/prod/suixiangcard/suixiangcard_style_2_gray.png",
            "https://evcard.oss-cn-shanghai.aliyuncs.com/prod/suixiangcard/suixiangcard_style_2_mycard_1.png"),
    THREE(3, "https://evcard.oss-cn-shanghai.aliyuncs.com/prod/suixiangcard/suixiangcard_style_3_1.png",
            "https://evcard.oss-cn-shanghai.aliyuncs.com/prod/suixiangcard/suixiangcard_style_3_gray.png",
            "https://evcard.oss-cn-shanghai.aliyuncs.com/prod/suixiangcard/suixiangcard_style_3_mycard_1.png");

    /**
     * 样式编号
     */
    private int styleNo;

    /**
     * 阿里云地址，亮图
     */
    private String url;

    /**
     * 阿里云地址，灰图
     */
    private String grayUrl;

    /**
     * 阿里云地址，我的卡片-背景图
     */
    private String myCardUrl;

    public static String getUrlByStyleNo(int styleNo) {
        SuiXiangCardStyleImageEnum[] enums = SuiXiangCardStyleImageEnum.values();
        for (SuiXiangCardStyleImageEnum item : enums) {
            if (item.styleNo == styleNo) {
                return item.url;
            }
        }
        return null;
    }

    public static String getGrayUrlByStyleNo(int styleNo) {
        SuiXiangCardStyleImageEnum[] enums = SuiXiangCardStyleImageEnum.values();
        for (SuiXiangCardStyleImageEnum item : enums) {
            if (item.styleNo == styleNo) {
                return item.grayUrl;
            }
        }
        return null;
    }

    public static String getMyCardUrlByStyleNo(int styleNo) {
        SuiXiangCardStyleImageEnum[] enums = SuiXiangCardStyleImageEnum.values();
        for (SuiXiangCardStyleImageEnum item : enums) {
            if (item.styleNo == styleNo) {
                return item.myCardUrl;
            }
        }
        return null;
    }
}
