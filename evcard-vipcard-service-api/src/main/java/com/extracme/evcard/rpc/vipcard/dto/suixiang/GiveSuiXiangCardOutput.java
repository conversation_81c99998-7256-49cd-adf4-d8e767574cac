package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
public class GiveSuiXiangCardOutput implements Serializable {
    private BaseResponse baseResponse; // 基础应答体
    private String resultDesc;  // 赠送结果
    private String resultFileUrl; // 结果清单的文件url，阿里云oss的完整文件地址（是个excel）
    private Long fileId; // 保存文件的主键id
}
