package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 *
 * 购卡随享卡详情 展示对象
 *
 */
@Data
public class EffectiveSuiXiangCardDetailDto implements Serializable {
    // 随享卡售卖信息
    private List<SuiXiangCardSalesInfoDto>  cardInfoList;
    // 随享卡基础信息
    private SuiXiangCardBaseInfoForSalesDto  cardBaseInfo;

    // 所有租期列表
    private List<Integer> daysList;
    // 所有车型组列表
    private List<String>  modelGroupList;
}
