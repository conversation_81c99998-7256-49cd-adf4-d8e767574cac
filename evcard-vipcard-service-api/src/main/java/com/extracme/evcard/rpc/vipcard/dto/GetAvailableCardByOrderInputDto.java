package com.extracme.evcard.rpc.vipcard.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 14:10 2021/1/5
 */
public class GetAvailableCardByOrderInputDto implements Serializable {

    /**
     * 订单号
     */
    private String orderSeq;

    /**
     * 订单应收金额：租车费+服务费-减免
     */
    private BigDecimal orderAmount;

    /**
     * 可使用会员卡租金
     */
    private BigDecimal rentAmount;

    /**
     * 可使用企业会员卡租金
     */
    private BigDecimal canBusinessDiscountAmount;

    /**
     * 订单单价
     */
    private BigDecimal unitPrice;

    /**
     * 订单活动类型
     */
    private Integer activityType;

    public GetAvailableCardByOrderInputDto() {
    }

    public GetAvailableCardByOrderInputDto(String orderSeq, BigDecimal orderAmount, BigDecimal rentAmount, BigDecimal canBusinessDiscountAmount, BigDecimal unitPrice, Integer activityType) {
        this.orderSeq = orderSeq;
        this.orderAmount = orderAmount;
        this.rentAmount = rentAmount;
        this.canBusinessDiscountAmount = canBusinessDiscountAmount;
        this.unitPrice = unitPrice;
        this.activityType = activityType;
    }

    public String getOrderSeq() {
        return orderSeq;
    }

    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getRentAmount() {
        return rentAmount;
    }

    public void setRentAmount(BigDecimal rentAmount) {
        this.rentAmount = rentAmount;
    }

    public BigDecimal getCanBusinessDiscountAmount() {
        return canBusinessDiscountAmount;
    }

    public void setCanBusinessDiscountAmount(BigDecimal canBusinessDiscountAmount) {
        this.canBusinessDiscountAmount = canBusinessDiscountAmount;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    @Override
    public String toString() {
        return "GetAvailableCardByOrderInputDto{" +
                "orderSeq='" + orderSeq + '\'' +
                ", orderAmount=" + orderAmount +
                ", rentAmount=" + rentAmount +
                ", canBusinessDiscountAmount=" + canBusinessDiscountAmount +
                ", unitPrice=" + unitPrice +
                ", activityType=" + activityType +
                '}';
    }
}
