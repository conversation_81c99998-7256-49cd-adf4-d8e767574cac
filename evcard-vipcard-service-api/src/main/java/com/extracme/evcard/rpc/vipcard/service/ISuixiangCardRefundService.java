package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;

public interface ISuixiangCardRefundService {

    /**
     * 查询随享卡退款日志
     * @param input
     * @return
     */
    QuerySuixiangCardRefundLogOutput querySuixiangCardRefundLog(QuerySuixiangCardRefundLogInput input);

    /**
     * 手动退款随享卡
     * @return
     */
    BaseResponse manualRefundSuixiangCard(Long cardUseId, OperatorDto operatorDto);

    /**
     * 检查是否允许退款
     * @param cardUseId
     * @return
     */
    BaseResponse checkAllowedRefund(Long cardUseId);

    /**
     * 退款
     * @param cardUseId
     * @param type
     * @param operatorDto
     * @return
     * @throws BusinessException
     */
    BaseResponse refund(Long cardUseId, int type, OperatorDto operatorDto) throws BusinessException;

    /**
     * 保存单张卡退款日志
     * @param cardUseId
     * @param type
     * @param success
     * @param operatorDto
     */
    boolean saveSuixiangCardRefundLog(Long cardUseId, int type, int success, OperatorDto operatorDto);

    /**
     * 基于退款中保存结果
     * @param cardUseId
     * @param success
     * @return
     */
    boolean saveSuixiangCardRefundLog(Long cardUseId, int success, int type);

}
