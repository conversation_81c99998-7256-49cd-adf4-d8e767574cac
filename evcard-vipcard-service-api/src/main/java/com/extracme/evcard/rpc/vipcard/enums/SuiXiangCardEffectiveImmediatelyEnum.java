package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
@Getter
@AllArgsConstructor
public enum SuiXiangCardEffectiveImmediatelyEnum {
    YES(1, "是（点 立即上架）"),
    NO(2, "否（点 保存）");

    /**
     * 是否立即上架：1-是（点 立即上架）、2-否（点 保存）
     */
    private int effectiveImmediately;

    /**
     * 描述
     */
    private String desc;
}
