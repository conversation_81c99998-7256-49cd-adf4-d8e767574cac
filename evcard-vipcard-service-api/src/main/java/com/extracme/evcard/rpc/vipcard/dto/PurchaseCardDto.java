package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 付费会员卡购买入参
 * <AUTHOR>
 * @Discription
 * @date 2021/6/21
 */
@Data
public class PurchaseCardDto implements Serializable {
    /**
     * 活动id，必须
     */
    private Long activityId;

    /**
     * 会员主键id，必须
     */
    private Long userId;

    /**
     * 张数，必须
     */
    private Integer quantity;

    /**
     * 支付金额，必须
     */
    private Double realAmount;

    /**
     * 混合支付时折扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 备注，非必须
     */
    private String remark;
    /**
     * 合并买卡来源 0单独&后付订单 1预付订单
     */
    private Integer mergePayOrigin = 0;
}
