package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SuixiangCardUseDto implements Serializable {

    private Long id;

    /**
     *
     */
    private Long purchaseId;

    /**
     *
     */
    private Long cardPriceId;

    /**
     *
     */
    private Long userId;


    /**
     *
     *
     */
    private String cardName;

    /**
     *卡状态：1：生效中 2 已过期（过期自动失效）  3 生效中-已冻结 4：已退卡 5：已作废 6：过期已冻结  7已使用  8自动已冻结
     */
    private Integer cardStatus;

    /**
     *
     * 卡片有效期开始时间
     */
    private Date startTime;

    /**
     *卡片有效结束时间
     *
     */
    private Date expiresTime;

    /**
     *累计使用订单数
     *
     */
    private Long totalOrder;

    /**
     *累计节省金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     *随享卡初始天数
     */
    private Integer initDays;

    /**
     *随享卡可用天数（剩余天数）
     */
    private Integer availableDays;

    /**
     *随享卡已经用过天数
     */
    private Integer usedDays;

    /**
     *随享卡冻结天数
     */
    private Integer frozenDays;

    /**
     *生效标识：0正常 1待作废
     */
    private Integer activeFlag;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Long createOperId;

    /**
     *
     */
    private String createOperName;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private Long updateOperId;

    /**
     *
     */
    private String updateOperName;

    /**
     *
     */
    private Integer isDeleted;
}
