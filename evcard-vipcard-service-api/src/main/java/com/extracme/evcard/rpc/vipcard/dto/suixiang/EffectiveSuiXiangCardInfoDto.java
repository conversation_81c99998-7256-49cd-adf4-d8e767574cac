package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 购卡列表页 随享卡展示对象
 */
@Data
public class EffectiveSuiXiangCardInfoDto implements Serializable {
    /**
     * 基础表id
     */
    private Long cardId;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 卡面背景图片
     */
    private String backUrl;

    /**
     * 卡片有效期天数类型 ：1-一个月、2-两个月、3-三个月、6-六个月、12-一年
     */
    private Integer validDaysType;

    /**
     * 天数
     */
    private Integer days;

    /**
     * 实际价格，单位：元
     */
    private BigDecimal salesPrice;


    /**
     * 未购卡时-状态
     * 1：可以购买 2：用户有生效的随享卡不可以买 3：未设置开售提醒 4：已设置开售提醒 5:已达购买上限
     */
    private Integer buyCardStatus;

    /**
     * 开售时间提示
     * 格式：明日12:00开抢
     */
    //private String buyTimeTip;

    /**
     * 上架时间
     * 格式：yyyyMMddHHmmss
     */
    private String startTime;

    /**
     * 库存
     */
    private Integer stock;

    private int allCarModelFlag; // 全部车型标记 1:全部车型 0：不是全部车型
    private int allCityFlag; // 是否支持所有城市，1：是 0：否

}
