package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Getter
@AllArgsConstructor
public enum SuiXiangCardDisplayFlagEnum {
    YES(1, "是"),
    NO(2, "否");

    /**
     * 是否对外展示：1-是、2-否
     */
    private int displayFlag;

    /**
     * 描述
     */
    private String desc;

    public static String getDescByDisplayFlag(int displayFlag) {
        SuiXiangCardDisplayFlagEnum[] array = SuiXiangCardDisplayFlagEnum.values();
        for (SuiXiangCardDisplayFlagEnum item : array) {
            if (item.displayFlag == displayFlag) {
                return item.desc;
            }
        }
        return null;
    }
}
