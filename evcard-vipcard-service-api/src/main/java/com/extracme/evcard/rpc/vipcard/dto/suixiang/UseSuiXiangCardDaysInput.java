package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class UseSuiXiangCardDaysInput implements Serializable {
    OperatorDto operateDto;
    // 使用卡 id
    private Long userCardId;
    // 天数
    private Integer days;

    // 合同号
    private String contractId;

    // 申请冻结时间
    private Date applyTime;

    private Long userId;

    // 扣除时 随享卡优惠总金额
    private BigDecimal totalAmount;
}
