package com.extracme.evcard.rpc.vipcard.dto.wrap;

import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.PurchaseCardDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class PurchaseCardInput extends PurchaseCardDto implements Serializable {
    private OperatorDto optUser;
}
