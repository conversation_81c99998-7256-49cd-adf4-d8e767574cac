package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/26
 */
@Data
public class QuerySuiXiangCardCdkConfigOutput implements Serializable {
    private static final long serialVersionUID = -2019324328704865226L;
    private BaseResponse baseResponse; // 基础应答体
    private List<QuerySuiXiangCardCdkConfigDto> list;

}
