package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *  随享卡基本配置信息
 *
 *
 */
@Data
public class SuixiangCardInfoDto implements Serializable {

    private Long cardBaseId;
    /**
     */
    private Long cardRentId;


    private Long cardPriceId;

    /**
     * 随想卡名称
     */
    private String cardName;

    /**
     * 运营机构
     */
    private String orgId;

    /**
     * 运营机构名称
     */
    private String orgName;

    /**
     * 可用区域：-1-全部（多个id以逗号分隔）
     */
    private String cityId;

    /**
     * 初始库存
     */
    private Long initStock;

    /**
     * 上架时间，精确到小时, 格式yyyyMMddHHmm
     */
    private Date saleStartTime;

    /**
     * 下架时间，精确到小时, 格式yyyyMMddHHmm
     */
    private Date saleEndTime;

    /**
     * 预告时间，  格式yyyyMMddHHmm
     */
    private Date advanceNoticeTime;

    /**
     * 卡片有效期天数类型
     * 有效期：1-一个月、2-两个月、3-三个月、6-六个月、12-一年
     */
    private Integer validDaysType;

    /**
     * 使用规则
     *
     */
    private String rules;


    /**
     * 当前库存
     */
    private Integer stock;

    /**
     * 总销量
     */
    private Integer totalSales;

    /**
     * 展示标记  1：对外展示 0：不对外展示
     */
    private Integer displayFlag;

    /**
     * 单订单时长（当前城市平均订单时长，单位是天）
     */
    private String singleOrderDuration;

    /**
     * 卡面样式 0：自定义  1：样式1 2：样式2 3：样式3
     */
    private Integer styleType;

    /**
     *卡面背景图片
     */
    private String backUrl;


    /**
     *0待提交 1待上架 2已上架(开放购买) 3已下架
     */
    private Integer cardStatus;


     //售价
    /**
     * 实际售价，单位：元
     */
    private BigDecimal salesPrice;

    /**
     *划线价格，单位：元
     */
    private BigDecimal underlinePrice;

    /**
     *车型组
     */
    private String carModelGroup;

    /**
     * 可用车型id（多个用逗号分割）
     */
    private String carModelIds;

    /**
     * 该类型销量
     */
    private Integer sales;


    // 租期表
    /**
     * 天数
     */
    private Integer rentDays;

    /**
     * 服务费，
     * 包含整备费、日租服务费、畅行服务费、加油服务费、加电服务费、夜间服务费。
     * 存储结构 日租服务费标志位,日租服务费划线价;  标志位0：不免费1：免费。
     */
    private String serviceFees;

    /**
     * 服务费总节省金额，单位元
     */
    private BigDecimal totalServiceFeesAmout;

    //节假日是否可用 1:可用  2：不可用
    private int holidayAvailable;

    /**
     * 不可用日期
     */
    private String unavailableDate;

    /**
     * 不可用日期集合
     */
    private List<UnavailableDate> unavailableDateList;

    /**
     * 购买上限数，0代表没有购买张数限制
     */
    private Integer purchaseLimitNum;


    private List<Integer> vehicleBrandIdList; // 非必填，合作车企品牌id

    private String vehicleBrandIds; // 非必填，合作车企品牌id,多个用逗号分割

    private String landingPagePicUrl; // 非必填，落地页引导图完整路径地址

    private String landingPageHeadPicUrl; // 非必填，落地页头图完整路径地址

    private Integer mergeFlag; // 非必填，兑换卡是否可以合并，1：可以 2：不可以 默认为2

    private int landingPageFlag; // 非必填，是否有落地页，1：有 2：没有，默认为2

    /**
     * 总节省金额
     * 公式 = 减免服务费 + 车辆划线价格 - 实际售价
     *
     * @return
     */
    public BigDecimal getSaveTotal(){
        try {
            if(salesPrice.compareTo(underlinePrice) < 0){
                return BigDecimal.ZERO;
            }

            return totalServiceFeesAmout.add(salesPrice.subtract(underlinePrice));
        } catch (Exception e) {
        }
        return BigDecimal.ZERO;
    }

}
