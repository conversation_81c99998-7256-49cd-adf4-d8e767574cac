package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CardReMindDto implements Serializable {
    /**
     * 卡片：卡片名称
     */
    private String cardName;

    /**
     * 卡片：卡片类型 0周卡 1月卡 2季卡  3随享卡
     */
    private Integer cardType;

    /**
     * 随想卡 属性
     *  金额
     */
    private BigDecimal amount;

    /**
     * 随想卡 属性
     *天数
     */
    private Integer days;

    /**
     * 会员卡 属性
     * 折扣
     */
    private Integer discount;

    private Date createTime;

    /**
     * 购买记录id
     */
    private Long purchaseId;

}
