package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.vipcard.dto.LeasePriceCfgDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
public class QuerySuiXiangCardDetailOutput implements Serializable {
    private BaseResponse baseResponse; // 基础应答体

    private long id; // id
    private String cardName;  // 随享卡名称
    private String orgCode;  // 运营公司CODE
    private String cityId; // 可用区域：-1-全部
    private String previewTime; // 预告时间，格式 yyyy-MM-dd HH:mm
    private String saleStartTime; // 售卖开始时间，格式 yyyy-MM-dd HH:mm
    private String saleEndTime; // 售卖结束时间，格式 yyyy-MM-dd HH:mm
    private int validTime; // 有效期：1-一个月、2-两个月、3-三个月、6-六个月、12-一年
    private int stock; // 库存数量
    private int isDisplay; // 是否对外展示：1-是、2-否
    private double singleOrderDuration; // 单订单时长（当前城市平均订单时长，单位是天）
    private List<LeasePriceCfgDto> leasePriceCfgDtoList; // 租期价格配置
    private String imageUrl; // 卡面图片（送阿里云完整的oss地址）
    private String purchaseNotes; // 购买须知
    private int imageNo; // 卡面图片编号（目前只有1/2/3），对应的地址是imageUrl
    private String orgName; // 运营公司名称
    private String cityName; // 可用区域名称，多个用“、”分隔
    private int cardStatus; // 卡片状态：1-待提交、2-待上架、3-已上架、4-已下架
    private int holidayAvailable;
    private List<UnavailableDate> unavailableDate;
    private int purchaseLimitNum;//购买上限数，0代表没有购买张数限制

    private int mergeFlag; // 非必填，兑换卡是否可以合并，1：可以 2：不可以 默认为2
    private int vehicleBrandId; // 非必填，合作车企品牌id
    private int landingPageFlag; // 非必填，是否有落地页，1：有 2：没有，默认为2

    private String secondAppKey; // 二级渠道
    private String secondAppName; // 二级渠道名称
}
