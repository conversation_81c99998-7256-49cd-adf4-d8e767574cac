package com.extracme.evcard.rpc.vipcard.dto.suixiang.pay;

import lombok.Data;

import java.io.Serializable;

@Data
public class PayOrderDto implements Serializable {

    private  Long userId;  // 当前用户
    private String payOrderNo; // 支付订单号


    private String payChannel; // 支付渠道 1: 支付宝app 2:银联app 3:账户余额 5:微信app 6:微信公众号 7:支付宝wap 8:银联wap 10:apple pay 11: 小米pay 12:华为pay 13:三星pay 14:魅族pay 15:支付宝小程序 16:银联app预授权 17:银联wap预授权  18:微信wap 19:微信小程序 20:支付宝app2.0 21:支付宝代扣 22:电信翼支付
    private String wapUrl;//网站地址，微信H5支付使用
    private String wapName;//网站名称，微信H5支付使用
    private String frontNotifyUrl;


    private String openId;   // 支付宝/微信用户id 小程序


    private long tenantId; // 租户ID
    private String clientIp; // 客户端IP
}
