package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 10:07 2021/9/26
 */
@Data
public class GetAvailableCardByUseConditionOriginInput implements Serializable {

    /**
     * 订单应收金额：租车费+服务费-减免
     */
    private BigDecimal orderAmount;

    /**
     * 可使用会员卡租金
     */
    private BigDecimal rentAmount;

    /**
     * 订单单价
     */
    private BigDecimal unitPrice;

    /**
     * 订单活动类型
     */
    private Integer activityType;

    /**
     * 用户id
     */
    private String authId;

    /**
     * 取车时间 必填项
     */
    private Date orderPickUpDate;

    /**
     * 还车时间 必填项
     */
    private Date orderReturnDate;

    /**
     * 用车时长 必填项
     */
    private Integer orderCostTime;

    /**
     * 取车网点 必填项
     */
    private String pickUpShopSeq;

    /**
     * 还车网点 必填项
     */
    private String returnShopSeq;

    /**
     * 车型编号 必填项
     */
    private Integer vehicleModelSeq;

    /**
     * 业务产品线 必填项
     */
    private Integer rentMethod;

    /**
     * 车牌号
     */
    private String vehicleNo;

    /**
     * 订单号
     */
    private String orderSeq;


    public GetAvailableCardByUseConditionOriginInput() {
    }

    public GetAvailableCardByUseConditionOriginInput(BigDecimal orderAmount, BigDecimal rentAmount, BigDecimal unitPrice,
                                                     Integer activityType, String authId,
                                                     Date orderPickUpDate, Date orderReturnDate,
                                                     Integer orderCostTime,
                                                     String pickUpShopSeq, String returnShopSeq,
                                                     Integer vehicleModelSeq, Integer rentMethod,
                                                     String vehicleNo) {
        this.orderAmount = orderAmount;
        this.rentAmount = rentAmount;
        this.unitPrice = unitPrice;
        this.activityType = activityType;
        this.authId = authId;
        this.orderPickUpDate = orderPickUpDate;
        this.orderReturnDate = orderReturnDate;
        this.orderCostTime = orderCostTime;
        this.pickUpShopSeq = pickUpShopSeq;
        this.returnShopSeq = returnShopSeq;
        this.vehicleModelSeq = vehicleModelSeq;
        this.rentMethod = rentMethod;
        this.vehicleNo = vehicleNo;
    }

}
