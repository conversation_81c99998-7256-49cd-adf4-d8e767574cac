package com.extracme.evcard.rpc.vipcard.dto;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class UnFreezeThenFreezeDiscountAmountDto implements Serializable {

    /**
     * 会员卡号
     */
    private Long userCardNo;

    /**
     * 申请冻结时间
     */
    private Date freezeTime;

    /**
     * 申请冻结的折扣金额
     */
    private BigDecimal freezeAmount;

    /**
     * 申请解冻的折扣金额
     */
    private BigDecimal unFreezeAmount;


    /**
     * 老订单号
     */
    private String oldOrderSeq;
    /**
     * 新订单号
     */
    private String newOrderSeq;


    public static FreezeDiscountAmountDto getFreezeDiscountAmountDto(UnFreezeThenFreezeDiscountAmountDto input){
        FreezeDiscountAmountDto result = new FreezeDiscountAmountDto();
        result.setAmount(input.getFreezeAmount());
        result.setFreezeTime(input.getFreezeTime());
        result.setOrderSeq(input.getNewOrderSeq());
        result.setUserCardNo(input.getUserCardNo());
        return result;
    }

    public static UnfreezeDiscountAmountDto getUnfreezeDiscountAmountDto(UnFreezeThenFreezeDiscountAmountDto input){
        UnfreezeDiscountAmountDto result = new UnfreezeDiscountAmountDto();
        result.setAmount(input.getUnFreezeAmount());
        result.setFreezeTime(input.getFreezeTime());
        result.setOrderSeq(input.getOldOrderSeq());
        result.setUserCardNo(input.getUserCardNo());
        return result;
    }

}
