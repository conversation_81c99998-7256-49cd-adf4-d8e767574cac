package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class GetAvailableCardByUseConditionMdInput {
    /**
     * 订单应收金额：租车费+服务费-减免
     */
    private BigDecimal orderAmount;

    /**
     * 可使用会员卡租金
     */
    private BigDecimal rentAmount;

    /**
     * 订单单价
     */
    private BigDecimal unitPrice;

    /**
     * 订单活动类型
     */
    private Integer activityType;

    /**
     * 用户id
     */
    private String authId;

    /**
     * 取车时间 必填项
     */
    private Date orderPickUpDate;

    /**
     * 还车时间 必填项
     */
    private Date orderReturnDate;

    /**
     * 用车时长 必填项
     */
    private Integer orderCostTime;

    /**
     * 车型编号 必填项
     */
    private Integer vehicleModelSeq;

    /**
     * 业务产品线 必填项
     */
    private Integer rentMethod;

    /**
     * 车牌号
     */
    private String vehicleNo;

    /**
     * 订单号
     */
    private String orderSeq;

    /**
     * 追加-商品车型
     */
    private String goodsModelId;

    /**
     * 追加-取车门店
     */
    private String pickUpStoreId;

    /**
     * 追加-还车门店，非必填，没有就不传
     */
    private String returnStoreId;

    /**
     * 追加-取车城市， 必传
     */
    private Long pickUpCity;

    /**
     * 追加-还车城市， 必传
     */
    private Long returnCity;

}
