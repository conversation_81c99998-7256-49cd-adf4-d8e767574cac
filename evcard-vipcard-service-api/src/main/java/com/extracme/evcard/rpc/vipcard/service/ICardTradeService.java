package com.extracme.evcard.rpc.vipcard.service;


import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dto.UnFreezeThenFreezeDiscountAmountDto;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.dto.wrap.PurchaseCardInput;
import com.extracme.framework.core.bo.PageBeanBO;

import java.util.Date;
import java.util.List;

public interface ICardTradeService {

    /**
     * 根据活动id/会员卡id批量查询购买记录
     * @param pkId 用户id
     * @param activityIdList 活动id
     * @return
     */
    List<MemberCardPurchaseRecordDto> getMemberCardPurchaseRecord(Long pkId,
                                                                  List<Long> activityIdList);

    /**
     * 购卡回调
     * @param callback
     * @throws BusinessException
     * @remark 1.3.0 变更
     */
    PayCarPurchaseDto payCarPurchaseCallBack(AddWaitPayCarPurchaseRecord callback) throws BusinessException;

    /**
     * 卡片消费记录
     * @param userCardOperationDto
     * @throws BusinessException
     */
    void userCardHistory(UserCardOperationDto userCardOperationDto) throws BusinessException;

    /**
     * 查询用户指定某张卡的购买明细，业务系统使用
     *
     * @param userId
     * @param cardId
     * @return
     * @remark 仅查询前100记录
     */
    List<MemberCardPurchaseLogDto> queryUserPurchaseListByCardId(Long userId, Long cardId);

    /**
     * 查询购买记录，业务系统使用(分页)
     * @param queryDto 查询条件
     * @return
     * @since 1.0.0
     * @remark cardId，usrId未指定时，默认查询半年内的购买记录
     */
    PageBeanBO<CardPurchaseListDetailDto> queryPurchaseRecordsPage(CardPurchasePageQueryDto queryDto);

    /**
     * 查询购买记录，业务系统使用(分页)
     * @param queryDto
     * @param id
     * @param limit 2000以内
     * @return
     */
    List<CardPurchaseListViewDto> queryPurchaseRecordsList(CardPurchaseListQueryDto queryDto, Long id, Integer limit);


    /**
     * 后台批量送卡
     * @param batchOfferDto
     * @param optUser
     * @return
     * @remark 扣减库存，不校验活动单人购买上限
     *         单次最多处理10个用户
     */
    void batchOfferCards(CardBatchOfferDto batchOfferDto, OperatorDto optUser) throws BusinessException;

    /**
     * 卡片发放
     * @param input
     * @param optUser
     * @return
     * @throws BusinessException
     * @remark 兑换及送卡使用;
     *         1.3.0之后，支付回调中不再调用此方法发卡
     *
     */
    CardPurchaseRecordDto issueCard(CardIssueDto input, OperatorDto optUser) throws BusinessException;

    /**
     * 卡片购买
     * 创建待支付购卡记录
     * @param input
     * @param optUser
     * @return
     * @throws BusinessException
     * @since 1.3.0
     * @remark 此接口扣减活动库存
     */
    PurchaseRecordInfo purchaseCard(PurchaseCardDto input, OperatorDto optUser) throws BusinessException;

    PurchaseRecordInfo purchaseCard(PurchaseCardInput input) throws BusinessException;

    /**
     * 根据购卡记录ID查询购卡记录信息
     * @param recordId
     * @return
     * @throws BusinessException
     * @since 1.3.0
     */
    PurchaseRecordInfo getPurchaseRecordById(Long recordId);

    /**
     * 更新支付流水号
     * @param recordId
     * @return
     * @throws BusinessException
     * @since 1.3.0
     * @remark 仅待支付状态允许修改；
     */
    void updateOutTradeSeq(Long recordId, String outTradeSeq, OperatorDto optUser) throws BusinessException;

    /**
     * 取消订单
     * @param record
     * @return
     * @throws BusinessException
     * @since 1.3.0
     * @remark
     */
    void cancelOrder(PurchaseRecordInfo record, OperatorDto optUser) throws BusinessException;

    /**
     * 取消订单
     * @param purchaseId 购卡记录编号
     * @param cancelType 取消方式： 1 app用户取消 2 系统自动取消(超时未付款)
     * @return
     * @throws BusinessException
     * @since 2.2.4
     * @remark
     */
    void cancelOrder(Long purchaseId, Integer cancelType, OperatorDto optUser) throws BusinessException;

    /**
     * 冻结 用户会员卡使用详情中的累计折扣上限
     * @param input -
     * @param operateDto -
     * @return
     */
    void freezeDiscountAmount(FreezeDiscountAmountDto input, OperatorDto operateDto)throws BusinessException;

    /**
     * 解冻 用户会员卡使用详情中的累计折扣上限
     * @param input
     * @param operateDto
     * @return
     */
    void unfreezeDiscountAmount(UnfreezeDiscountAmountDto input, OperatorDto operateDto)throws BusinessException;

    /**
     * 扣除 用户会员卡使用详情中的冻结折扣金额
     * @param input
     * @param operateDto
     * @return
     */
    void deductFrozenDiscountAmount(DeductFrozenDiscountAmountDto input, OperatorDto operateDto)throws BusinessException;


    /**
     * 先解冻  再冻结 用户会员卡使用详情中的累计折扣上限
     * @param input
     * @param operateDto
     * @return
     *
     */
    void unfreezeThenFreezeDiscountAmount(UnFreezeThenFreezeDiscountAmountDto input, OperatorDto operateDto)throws BusinessException;

    /**
     * 获取会员卡额度金额信息
     * @param userCardNo
     * @param queryTime
     * @return
     */
    GetCardDiscountAmountInfoDto getCardDiscountAmountInfo(Long userCardNo, Date queryTime);
}
