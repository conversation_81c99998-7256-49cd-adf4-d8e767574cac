package com.extracme.evcard.rpc.vipcard.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class UserCardOperationDto implements Serializable {
    private static final long serialVersionUID = -990336087899134792L;

    /**
     * 用户卡id
     */
    private Long userCardNo;

    /**
     * 操作来源  app
     */
    private String originSystem;


    private String orderSeq;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 订单应收金额
     */
    private BigDecimal amount;


    private String authId;

    public Long getUserCardNo() {
        return userCardNo;
    }

    public void setUserCardNo(Long userCardNo) {
        this.userCardNo = userCardNo;
    }

    public String getOriginSystem() {
        return originSystem;
    }

    public void setOriginSystem(String originSystem) {
        this.originSystem = originSystem;
    }

    public String getOrderSeq() {
        return orderSeq;
    }

    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }
}
