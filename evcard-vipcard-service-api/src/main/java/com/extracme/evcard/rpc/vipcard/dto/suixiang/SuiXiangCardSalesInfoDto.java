package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.vipcard.dto.ServiceFeeCfgDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 随享卡  购卡详情页 基础信息
 *
 */
@Data
public class SuiXiangCardSalesInfoDto implements Serializable {

    /**
     * 基础表id
     */
    private Long cardId;

    /**
     * 随想卡价格表id
     */
    private Long priceId;

    /**
     * 随想卡租期表id
     */
    private Long rentDayId;


    /**
     * 天数
     */
    private Integer days;


    /**
     * 车型组
     */
    private String modelGroup;


    /**
     * 可用车型id（多个用逗号分割）-1代表全部
     */
    private String carModelIds;



    /**
     * 实际售价，单位：元
     */
    private BigDecimal salesPrice;

    /**
     * 车辆划线价格，单位：元
     */
    private BigDecimal carUnderlinePrice;

    /**
     * 节省价格，单位：元 划线总价格-实际售价
     */
    private BigDecimal savePrice;

    /**
     *划线总价格，单位：元  车辆划线价格 + 服务费总节省金额 = 划线价总和
     */
    private BigDecimal totalUnderlinePrice;

    /**
     * 服务费总节省金额，单位元
     */
    private BigDecimal totalServiceFeesAmout;

    /**
     * 随享卡涉及所有 服务费减免
     *
     */
    List<ServiceFeeCfgDto> serviceFeeCfg;

    /**
     *
     * 是否所有车型 1是 0否
     */
    private int allCarModelFlag;
}
