package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/17
 */
@Getter
@AllArgsConstructor
public enum SuiXiangCardUseLogOperationTypeEnum {
    GIVE_OUT(0, "卡发放/购买"),
    CARD_CONSUME(1, "卡消费"),
    CARD_CANCELLATION(2, "卡作废"),
    FREEZE(3, "冻结"),
    UNFREEZE(4, "解冻"),
    APPLICATION_CANCELLATION(5, "申请作废"),
    EXPIRE(6, "已过期"),
    EXPIRE_FREEZED(7, "过期已冻结"),
    AUTO_FREEZED(8, "自动已冻结"),
    RETURNED(9, "已退卡"),

    RETURNED_FAIL(10, "退卡失败,状态还原"),
    MERGE(11, "卡合并");

    /**
     * 操作类型 0卡发放/购买 1卡消费 2卡作废 3冻结 4解冻 5申请作废 6已过期 7过期已冻结 8自动已冻结 9已退卡
     */
    private long operationType;

    /**
     * 描述
     */
    private String desc;
}
