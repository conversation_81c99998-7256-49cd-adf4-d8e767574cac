package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/24
 */
@Data
public class OperatorDto implements Serializable {
    /**
     * 操作触发系统，如bvm，mmp，ccs，app等
     */
    private String originSystem;
    /**
     * 操作人id
     */
    private Long operatorId;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 操作原因
     */
    private String remark;

    private static OperatorDto SYSTEM_OPT;

    public OperatorDto(){
    }

    public OperatorDto(Long operatorId, String operatorName, String originSystem) {
        this.setOperatorId(operatorId);
        this.setOperatorName(operatorName);
        this.setOriginSystem(originSystem);
    }

    public static final OperatorDto getSystemOp(){
        if(SYSTEM_OPT == null) {
            SYSTEM_OPT = new OperatorDto();
            SYSTEM_OPT.setOperatorName("system");
            SYSTEM_OPT.setOriginSystem("mmp");
            SYSTEM_OPT.setOperatorId(-1L);
            SYSTEM_OPT.setRemark("自动");
        }
        return SYSTEM_OPT;
    }

    private static OperatorDto USER_OPT;

    public static final OperatorDto getUserOp(){
        if(USER_OPT == null) {
            USER_OPT = new OperatorDto();
            SYSTEM_OPT.setOriginSystem("evcard");
            USER_OPT.setOperatorName("用户");
            USER_OPT.setOperatorId(-1L);
            USER_OPT.setRemark("用户");
        }
        return USER_OPT;
    }

    public static final OperatorDto getUserOp(Long userId, String originSystem) {
        if(StringUtils.isBlank(originSystem)) {
            originSystem = "evcard";
        }
        OperatorDto opt = new OperatorDto();
        opt.setOriginSystem(originSystem);
        opt.setOperatorName("用户");
        opt.setOperatorId(userId);
        return opt;
    }

    public static final OperatorDto buildOperator(OperatorDto operator) {
        return buildOperator(operator, StringUtils.EMPTY);
    }

    public static final OperatorDto buildOperator(OperatorDto operator, String remark) {
        if(operator == null) {
            return OperatorDto.SYSTEM_OPT;
        }
        if(operator.getOperatorId() == null) {
            operator.setOperatorId(-1L);
        }
        if(StringUtils.isBlank(operator.getOriginSystem())) {
            operator.setOriginSystem("system");
        }
        if(StringUtils.isBlank(operator.getOperatorName())) {
            operator.setOperatorName(operator.getOriginSystem());
        }
        if(StringUtils.isBlank(operator.getRemark())) {
            operator.setRemark(remark);
        }
        return operator;
    }
}
