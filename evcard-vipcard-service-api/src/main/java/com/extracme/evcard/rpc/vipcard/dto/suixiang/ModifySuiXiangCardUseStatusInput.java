package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import lombok.Data;

import java.io.Serializable;

@Data
public class ModifySuiXiangCardUseStatusInput implements Serializable {
    // 使用随享卡id(用户卡id)
    private Long userCardId;
    // 随享卡修改后状态
    private Integer cardStatus;
    //随享卡修改前状态
    private Integer oldCardStatus;
    // 修改类型 1：冻结 2：解冻 3：作废
    private Integer opertionType;

    private OperatorDto operatorDto; // 操作日志信息


}
