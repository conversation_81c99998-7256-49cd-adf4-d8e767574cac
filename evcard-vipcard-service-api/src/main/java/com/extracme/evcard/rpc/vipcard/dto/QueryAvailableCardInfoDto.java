package com.extracme.evcard.rpc.vipcard.dto;

import com.extracme.evcard.rpc.vipcard.dto.suixiang.UnavailableDate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 10:36 2020/12/29
 */
public class QueryAvailableCardInfoDto implements Serializable,Comparable<QueryAvailableCardInfoDto> {

    /**
     * 用户会员卡id
     */
    private Long userCardNo;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 折扣
     */
    private Integer discount;

    /**
     *  旺季折扣
     */
    private Integer peakSeasonDiscount;

    /**
     * 单笔订单最大抵扣金额，单位：元
     */
    private BigDecimal maxValue;

    /**
     * 会员卡是否可用 0否 1是
     */
    private Integer availabilityFlag;

    /**
     * 不可使用原因
     */
    private String unAvailableDesc;

    /**
     * 可抵扣金额
     */
    private BigDecimal deductionAmount = BigDecimal.ZERO;

    /**
     * 会员卡类别：1企业会员卡 2企业个人卡 3付费会员卡
     */
    private Integer cardGroup;

    /**
     * 卡面背景图片
     */
    private String backUrl;

    /**
     * 指定不可用日期
     * @return
     */
    private List<UnavailableDate> unavailableDate;

    private int holidayAvailable;//'节假日是否可用 ;1:可用  2：不可用',

    public Integer getPeakSeasonDiscount() {
        return peakSeasonDiscount;
    }

    public void setPeakSeasonDiscount(Integer peakSeasonDiscount) {
        this.peakSeasonDiscount = peakSeasonDiscount;
    }

    public Long getUserCardNo() {
        return userCardNo;
    }

    public void setUserCardNo(Long userCardNo) {
        this.userCardNo = userCardNo;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    public BigDecimal getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(BigDecimal maxValue) {
        this.maxValue = maxValue;
    }

    public Integer getAvailabilityFlag() {
        return availabilityFlag;
    }

    public void setAvailabilityFlag(Integer availabilityFlag) {
        this.availabilityFlag = availabilityFlag;
    }

    public String getUnAvailableDesc() {
        return unAvailableDesc;
    }

    public void setUnAvailableDesc(String unAvailableDesc) {
        this.unAvailableDesc = unAvailableDesc;
    }

    public BigDecimal getDeductionAmount() {
        return deductionAmount;
    }

    public void setDeductionAmount(BigDecimal deductionAmount) {
        this.deductionAmount = deductionAmount;
    }

    public Integer getCardGroup() {
        return cardGroup;
    }

    public void setCardGroup(Integer cardGroup) {
        this.cardGroup = cardGroup;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public List<UnavailableDate> getUnavailableDate() {
        return unavailableDate;
    }

    public void setUnavailableDate(List<UnavailableDate> unavailableDate) {
        this.unavailableDate = unavailableDate;
    }

    public int getHolidayAvailable() {
        return holidayAvailable;
    }

    public void setHolidayAvailable(int holidayAvailable) {
        this.holidayAvailable = holidayAvailable;
    }

    @Override
    public int compareTo(QueryAvailableCardInfoDto o) {
        if (this.getDeductionAmount().compareTo(o.getDeductionAmount()) < 0) {
            return 1;
        } else if (this.getDeductionAmount().compareTo(o.getDeductionAmount()) > 0) {
            return -1;
        } else {
            return 0;
        }
    }
}
