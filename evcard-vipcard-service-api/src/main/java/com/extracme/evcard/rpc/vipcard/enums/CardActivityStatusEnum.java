package com.extracme.evcard.rpc.vipcard.enums;

import java.io.Serializable;

/**
 * 任务(广告/消息)状态
 * <AUTHOR>
 * @Discription
 * @date 2019/9/16
 */
public enum CardActivityStatusEnum implements Serializable {
    /**
     * 待审核/发布(已创建)
     */
    CREATED(0, "待审核"),
    /**
     * 待上架(已发布)
     */
    PUBLISHED(1, "待上架"),
    /**
     * 上架中(已开始)
     */
    RUNNING(2, "已上架"),
    /**
     * 已下架
     */
    STOPPED(3, "已下架"),
    /**
     * 已暂停
     */
    SUSPENDED(4, "已暂停");

    CardActivityStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    /**
     * 状态码
     */
    private Integer status;
    /**
     * 描述
     */
    private String desc;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
