package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 付费会员卡-卡片配置详情
 * 卡片配置使用
 * <AUTHOR>
 * @Discription
 * @date 2020/12/24
 */
@Data
public class CardModelDetailDto implements Serializable {
    /**
     * 卡片ID
     */
    private Long cardId;

    /**
     * 卡片名称，必须
     */
    private String cardName;

    /**
     * 所属运营公司
     */
    private String orgId;

    /**
     * 卡片类型 0周卡 1月卡 季卡
     */
    private Integer cardType;


    /**
     * 购卡条件： 0 默认  1学生卡
     */
    private Integer purchaseType;

    /**
     * 卡面样式  0自定义  1样式一  2样式二  3样式三
     */
    private Integer styleType;

    /**
     * 卡面背景图： 卡面样式选择0自定义时，必传
     */
    private String backUrl;

    /**
     * 享有折扣，1~99整数
     */
    private Integer discount;
    /**
     * 周期内累计折扣上限, 单位元
     */
    private Integer totalDiscountAmount;

    /**
     * 单笔订单最大抵扣金额，单位：元
     */
    private BigDecimal maxValue;

    /**
     *订单时长限制，单位分钟
     */
    private Integer durationLimit;

    private String cityLimit;

    private String vehicleModelLimit;

    private String goodsModelId;

    /**
     * 可用门店
     */
    private String storeIds;

    private String rentMethod;

    private String rentMethodGroup;

    /**
     * 可用城市限制，最多10个
     */
    private List<Long> cityIds;

    /**
     * 可用车型（多个车型id以,分割）
     */
    private List<Long> vehicleModels;

    /**
     * 可用车型（多个车型id以,分割）
     */
    private List<Long> storeIdList;

    /**
     * 租车模式 0 即时分时 1 预约分时(废弃) 2即时日租 3预约日租
     */
    private List<Integer> rentMethods;

    /**
     * 租车模式大类 1 时租 2 日租
     */
    private List<Integer> rentMethodGroups;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 可使用时段限制(取车时间)，开始时间点(hhmmss)
     */
    private String startTime;

    /**
     *可使用时段限制(取车时间)，结束时间点(hhmmss)
     */
    private String endTime;

    /**
     * 可用天限制(取车时间)，1~7
     */
    private List<Integer> availableDaysOfWeek;

    /**
     * 可用天限制(取车时间)，1~7
     */
    private String availableDaysOfWeekLimit;

    /**
     * 有效期类型（0起止时段长度 1固定时段）
     */
    private Integer validTimeType;

    /**
     * 购买后n天生效，默认立即生效
     */
    private Integer effectiveDays;

    /**
     * 生效后有效天数m， 有效期为：购买完成时间+[n,m]
     */
    private Integer validDays;

    /**
     * 使用规则说明
     */
    private String rules;

    /**
     * 卡片状态 0启用 1禁用
     */
    private Integer status;

    /**
     * 创建时间 yyyy-MM-dd HH:mm:ss
     */
    private String createTime;

    private Long createOperId;

    private String createOperName;

    /**
     * 更新时间 yyyy-MM-dd HH:mm:ss
     */
    private String updateTime;

    private Long updateOperId;

    private String updateOperName;
}
