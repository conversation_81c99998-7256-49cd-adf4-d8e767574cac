package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum CommonFlagEnum {
    YES(1, "是"),
    NO(2, "否");


    private int flag;

    /**
     * 描述
     */
    private String desc;

    public static String getDescByFlag(int flag) {
        CommonFlagEnum[] array = CommonFlagEnum.values();
        for (CommonFlagEnum item : array) {
            if (item.flag == flag) {
                return item.desc;
            }
        }
        return null;
    }
}
