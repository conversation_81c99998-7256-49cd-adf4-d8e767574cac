package com.extracme.evcard.rpc.vipcard.enums;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/5/14
 */
public enum CardIssueTypeEnum {

    PURCHASE(0, "购买"),

    SEND(1, "赠送"),

    EXCHANGE(2, "积分兑换");

    CardIssueTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 根据issueType获取购买方式
     * @param issueType
     * @return
     */
    public static CardIssueTypeEnum getIssueType(Integer issueType){
        CardIssueTypeEnum[] issueTypes = CardIssueTypeEnum.values();
        for (int i = 0; i < issueTypes.length; i++) {
            if (issueTypes[i].getType().equals(issueType)) {
                return issueTypes[i];
            }
        }
        return CardIssueTypeEnum.PURCHASE;
    }

    /**
     * 类型
     */
    private Integer type;
    /**
     * 描述
     */
    private String desc;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
