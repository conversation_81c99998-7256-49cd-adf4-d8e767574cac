package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 会员指定卡片的购买明细记录
 * <AUTHOR>
 * @Discription
 * @date 2020/12/28
 */
@Data
public class MemberCardPurchaseLogDto implements Serializable {
    /**
     * 购买记录ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 销售活动ID
     */
    private Long cardActivityId;

    /**
     * 卡片ID
     */
    private Long cardId;

    /**
     * 0待支付 1已支付
     */
    private Integer paymentStatus;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 订单金额
     */
    private Double realAmount;

    /**
     * 交易记录编号
     */
    private String outTradeSeq;

    /**
     * 对应卡号
     */
    private String userCardNo;

    private Integer status;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;
}
