package com.extracme.evcard.rpc.vipcard.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/5/26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardOfferDto implements Serializable {
    /**
     * 会员主键id
     */
    private Long userId;
    /**
     * 张数
     */
    private int num;
}
