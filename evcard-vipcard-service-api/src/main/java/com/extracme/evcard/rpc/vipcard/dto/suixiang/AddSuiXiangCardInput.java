package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.vipcard.dto.LeasePriceCfgDto;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/3
 */
@Data
public class AddSuiXiangCardInput implements Serializable {
    private String cardName; // 随享卡名称
    private String orgCode;  // 运营公司CODE
    private List<String> cityId; // 可用区域
    private String previewTime; // 预告时间，格式 yyyy-MM-dd HH:mm，后端需要自定补充00秒
    private String saleStartTime; // 售卖开始时间，格式 yyyy-MM-dd HH:mm，后端需要自定补充00秒
    private String saleEndTime; // 售卖结束时间，格式 yyyy-MM-dd HH:mm，后端需要自定补充59秒
    private int validTime; // 有效期：1-一个月、2-两个月、3-三个月、6-六个月、12-一年
    private int stock; // 库存数量
    private int isDisplay; // 是否对外展示：1-是、2-否
    private double singleOrderDuration; // 单订单时长（当前城市平均订单时长，单位是天，精度到小数点后1位）
    private List<LeasePriceCfgDto> leasePriceCfgDtoList; // 租期价格配置
    private int imageNo; // 卡面图片编号（目前只有1/2/3）
    private String purchaseNotes; // 购买须知
    private int effectiveImmediately; // 是否立即上架：1-是（点 立即上架）、2-否（点 保存）
    private OperatorDto operatorDto; // 操作日志信息
    private int holidayAvailable;
    private List<UnavailableDate> unavailableDate;
    private int purchaseLimitNum;//购买上限数，0代表没有购买张数限制

    private int mergeFlag; // 非必填，兑换卡是否可以合并，1：可以 2：不可以 默认为2
    private int vehicleBrandId; // 非必填，合作车企品牌id
    private int landingPageFlag; // 非必填，是否有落地页，1：有 2：没有，默认为2

    private String secondAppKey; // 二级渠道
}
