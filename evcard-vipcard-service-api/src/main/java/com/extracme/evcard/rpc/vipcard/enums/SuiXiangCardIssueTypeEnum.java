package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/5/14
 */
@Getter
@AllArgsConstructor
public enum SuiXiangCardIssueTypeEnum {
    PURCHASE(1, "购买"),
    SEND(2, "赠送"),
    EXCHANGE(3, "兑换码");

    /**
     * 类型
     */
    private Integer type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 根据issueType获取购买方式
     * @param issueType
     * @return
     */
    public static SuiXiangCardIssueTypeEnum getIssueType(Integer issueType){
        SuiXiangCardIssueTypeEnum[] issueTypes = SuiXiangCardIssueTypeEnum.values();
        for (int i = 0; i < issueTypes.length; i++) {
            if (issueTypes[i].getType().equals(issueType)) {
                return issueTypes[i];
            }
        }
        return SuiXiangCardIssueTypeEnum.PURCHASE;
    }

}
