package com.extracme.evcard.rpc.vipcard.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 付费会员卡-卡片配置参数
 * <AUTHOR>
 * @Discription
 * @date 2020/12/24
 */
@Data
@ApiModel(value = "VipCardConfigDto对象", description = "付费会员卡配置对象")
public class CardConfigDto implements Serializable {
    /**
     * 卡片ID，更新时使用
     */
    @ApiModelProperty(value = "卡片ID")
    private Long cardId;

    /**
     * 卡片名称，必须
     */
    @ApiModelProperty(value = "卡片名称")
    private String cardName;

    /**
     * 所属运营公司
     */
    @ApiModelProperty(value = "所属运营公司")
    private String orgId;

    /**
     * 卡片类型 0周卡 1月卡 季卡
     */
    @ApiModelProperty(value = "卡片类型 0周卡 1月卡 季卡")
    private Integer cardType;

    /**
     * 购卡条件： 0 默认  1学生卡
     */
    @ApiModelProperty(value = "购卡条件： 0 默认  1学生卡")
    private Integer purchaseType;

    /**
     * 卡面样式  0自定义  1样式一  2样式二  3样式三
     */
    @ApiModelProperty(value = "卡面样式  0自定义  1样式一  2样式二  3样式三")
    private Integer styleType;

    /**
     * 卡面背景图： 卡面样式选择0自定义时，必传
     */
    @ApiModelProperty(value = "卡面背景图： 卡面样式选择0自定义时，必传")
    private String backUrl;

    /**
     * 享有折扣，1~99整数
     */
    @ApiModelProperty(value = "享有折扣，1~99整数")
    private Integer discount;

    /**
     * 周期内累计折扣上限, 单位元
     */
    @ApiModelProperty(value = "周期内累计折扣上限, 单位元")
    private Integer totalDiscountAmount;

    /**
     * 单笔订单最大抵扣金额，单位：元
     */
    @ApiModelProperty(value = "单笔订单最大抵扣金额，单位：元")
    private BigDecimal maxValue;

    /**
     * 订单时长限制，单位分钟
     */
    @ApiModelProperty(value = "订单时长限制，单位分钟")
    private Integer durationLimit;

    /**
     * 可用城市限制，最多10个
     */
    @ApiModelProperty(value = "可用城市限制，最多10个")
    private List<Long> cityIds;

    /**
     * 可用车型（多个车型id以,分割）
     */
    @ApiModelProperty(value = "可用车型-资产车型（多个车型id以,分割）")
    private List<Long> vehicleModels;

    @ApiModelProperty(value = "可用车型-商品车型（多个车型id以,分割）")
    private List<Long> goodsModelIds;

    /**
     * 租车模式 0 即时分时 1 预约分时(废弃) 2即时日租 3预约日租 4门店模式
     */
    @ApiModelProperty(value = "租车模式 0 即时分时 1 预约分时(废弃) 2即时日租 3预约日租 4门店模式")
    private List<Integer> rentMethods;

    @ApiModelProperty(value = "租车模式大类 1 时租 2日租")
    private List<Integer> rentMethodGroups;

    @ApiModelProperty(value = "门店ID列表")
    private List<Long> storeIds;

    /**
     * 可使用时段限制(取车时间)，开始时间点(hhmmss)
     * @Deprecated 5.0后弃用
     */
    @ApiModelProperty(value = "可使用时段限制(取车时间)，开始时间点(hhmmss)")
    @Deprecated
    private String startTime;

    /**
     *可使用时段限制(取车时间)，结束时间点(hhmmss)
     * @Deprecated 5.0后弃用
     */
    @ApiModelProperty(value = "可使用时段限制(取车时间)，结束时间点(hhmmss)")
    @Deprecated
    private String endTime;

    /**
     * 可用天限制(取车时间)，1~7
     */
    @ApiModelProperty(value = "可用天限制(取车时间)，1~7")
    private List<Integer> availableDaysOfWeek;

    /**
     * 有效期类型（0起止时段长度 1固定时段）
     */
    @ApiModelProperty(value = "有效期类型（0起止时段长度 1固定时段）")
    private Integer validTimeType;

    /**
     * 购买后n天生效，默认立即生效
     */
    @ApiModelProperty(value = "购买后n天生效")
    private Integer effectiveDays;

    /**
     * 生效后有效天数m， 有效期为：购买完成时间+[n,m]
     */
    @ApiModelProperty(value = "生效后有效天数m")
    private Integer validDays;

    /**
     * 使用规则说明
     */
    @ApiModelProperty(value = "使用规则说明")
    private String rules;
}
