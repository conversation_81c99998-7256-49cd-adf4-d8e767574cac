package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/16
 */
@Data
public class SuiXiangCardGiveCardDto implements Serializable {
    private String userInfo; // 会员手机号或会员编号
    private String rentDaysId; // 租期ID
    private String price; // 售价
    private String zhang; // 赠送的张数
    private int zhangInt; // 最终要赠送的张数，文件输入的张数可以为空，那么默认是1张
    private int succZhang; // 赠送成功的张数
    private String result; // 操作结果
}
