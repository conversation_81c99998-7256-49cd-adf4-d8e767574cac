package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/5/20
 */
@Data
public class UserCardHistoryDto implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户会员卡id
     */
    private Long userCardNo;

    /**
     * 卡id
     */
    private Long cardId;

    /**
     * 卡类别(冗余存储)：1企业会员卡 2企业个人卡 3付费会员卡
     */
    private Integer cardGroup;

    /**
     * 操作类型 0卡发放/购买 1卡消费 2卡作废
     */
    private Long operationType;

    /**
     * 操作来源：mmp/ccs/bvm/app
     */
    private String originSystem;

    /**
     * 关联主键, 卡购买记录ID(购买)/订单号(消费)/无(作废)
     */
    private String refKey;

    /**
     * 订单编号(消费)
     */
    private String orderSeq;

    /**
     * 折扣金额(消费)
     */
    private BigDecimal discountAmount;

    /**
     * 订单应收金额(消费)
     */
    private BigDecimal amount;

    /**
     * 订单现金实付金额(消费)
     */
    private BigDecimal realAmount;

    private String miscDesc;

    private Integer status;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;
}
