package com.extracme.evcard.rpc.vipcard.dto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 13:52 2020/12/28
 */
public class EffectiveActivityCardDetailDto extends EffectiveActivityCardInfoDto implements Serializable {

    /**
     * 卡片id
     */
    private Long cardId;

    /**
     * 下架时间
     * 格式：yyyyMMddHHmmss
     */
    private String endTime;

    /**
     * 使用限制详情列表
     */
    private List<String> limitDetail;

    /**
     * 会员卡有效期说明
     */
    private String cardDesc;

    /**
     * 规则
     */
    private String rules;

    /**
     * 可购买数量上限
     */
    private Integer canSaleNum;

    /**
     * 是否已上传学生证 0否 1是
     */
    private Integer studentCardFlag = 0;

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<String> getLimitDetail() {
        return limitDetail;
    }

    public void setLimitDetail(List<String> limitDetail) {
        this.limitDetail = limitDetail;
    }

    public String getCardDesc() {
        return cardDesc;
    }

    public void setCardDesc(String cardDesc) {
        this.cardDesc = cardDesc;
    }

    public String getRules() {
        return rules;
    }

    public void setRules(String rules) {
        this.rules = rules;
    }

    public Integer getCanSaleNum() {
        return canSaleNum;
    }

    public void setCanSaleNum(Integer canSaleNum) {
        this.canSaleNum = canSaleNum;
    }

    public Integer getStudentCardFlag() {
        return studentCardFlag;
    }

    public void setStudentCardFlag(Integer studentCardFlag) {
        this.studentCardFlag = studentCardFlag;
    }
}
