package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/3
 */
@Data
public class LeasePriceCfgDto implements Serializable {
    private int leaseTerm; // 租期，单位为天，大于0的整数
    private List<CarRentalCfgDto> carRentalCfgDtoList; // 车辆租金配置
    private List<ServiceFeeCfgDto> serviceFeeCfgDtoList; // 服务费配置
    private long rentDayId; // 随享卡租期表id
}
