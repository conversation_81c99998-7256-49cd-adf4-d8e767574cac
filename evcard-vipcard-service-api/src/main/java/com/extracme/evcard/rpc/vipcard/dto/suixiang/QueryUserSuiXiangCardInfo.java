package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;

@Data
public class QueryUserSuiXiangCardInfo implements Serializable {

    /**
     * 用户-卡id
     */
    private Long userCardId;

    /**
     * 购买卡id
     */
    private Long purchaseId;

    private String cardName;

    /**
     * 限制条件
     */
    private String limitCondition;

    /**
     * 卡片有效期
     */
    private String expiresTime;

    /**
     * 是否即将到期 0:否 1：是
     */
    private Integer overTime = 0;

    /**
     * 卡状态：1：生效中 2 已过期（过期自动失效）  3 生效中-已冻结 4：已退卡 5：已作废 6：过期已冻结  7已使用  8自动已冻结
     */
    private Integer cardUseStatus;

    /**
     * 0:否 1：是
     */
    private Integer sendStatus = 0;

    private String backUrl;

}
