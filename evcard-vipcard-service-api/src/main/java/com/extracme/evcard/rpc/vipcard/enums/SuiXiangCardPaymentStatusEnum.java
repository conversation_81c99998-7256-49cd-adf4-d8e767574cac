package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/13
 */
@Getter
@AllArgsConstructor
public enum SuiXiangCardPaymentStatusEnum {
    UNPAY(1, "待支付"),
    PAY(2, "已支付"),
    CANCEL(3, "已取消");

    /**
     * 购买状态 1：待支付 2：已支付 3：已取消
     */
    private int paymentStatus;

    /**
     * 描述
     */
    private String desc;

    public static String getDescByPaymentStatus(int paymentStatus) {
        SuiXiangCardPaymentStatusEnum[] array = SuiXiangCardPaymentStatusEnum.values();
        for (SuiXiangCardPaymentStatusEnum item : array) {
            if (item.paymentStatus == paymentStatus) {
                return item.desc;
            }
        }
        return null;
    }
}
