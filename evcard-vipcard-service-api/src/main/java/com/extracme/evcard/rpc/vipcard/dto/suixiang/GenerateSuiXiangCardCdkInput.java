package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GenerateSuiXiangCardCdkInput implements Serializable {
    private long cardBaseId;
    List<SuiXiangCardCdkDto> suiXiangCardCdkDtos;
    private OperatorDto operatorDto; // 操作日志信息
    private int purpose; // 用途 1=活动赠送，2=第三方售卖
}
