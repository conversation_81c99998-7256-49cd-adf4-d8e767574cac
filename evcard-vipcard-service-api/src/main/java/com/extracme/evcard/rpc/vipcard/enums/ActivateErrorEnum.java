package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivateErrorEnum {
    ACTIVATED(-3015001, "该兑换码已被领取"),
    ACTIVATED_REPEAT(-3015002, "您已领过该卡啦"),
    ACTIVATED_LIMIT(-3015003, "该手机号已达领取次数上限！"),
    CDKEY_EXPIRED(-3015004, "该活动的兑换码已失效"),
    CDKEY_ERROR(-3015005, "该兑换码不存在，请重新输入"),
    ACTIVATED_FREQUENT(-3015006, "请勿频繁提交！");

    /**
     * 状态
     */
    private int status;

    /**
     * 描述
     */
    private String desc;
}
