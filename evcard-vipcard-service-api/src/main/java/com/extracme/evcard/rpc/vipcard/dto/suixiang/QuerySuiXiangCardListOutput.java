package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
public class QuerySuiXiangCardListOutput implements Serializable {
    private BaseResponse baseResponse; // 基础应答体
    private List<SuiXiangCardItemDto> suiXiangCardItemDtoList;
    private int total;  // 总数
}
