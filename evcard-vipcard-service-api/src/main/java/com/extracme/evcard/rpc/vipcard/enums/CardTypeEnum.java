package com.extracme.evcard.rpc.vipcard.enums;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/5/14
 */
public enum CardTypeEnum {
    /**
     * 周卡，有效期7天
     */
    WEEK_CARD(0, 7, "周卡"),

    /**
     * 周卡，有效期7天
     */
    MONTH_CARD(1, 31, "月卡"),

    /**
     * 周卡，有效期7天
     */
    SEASON_CARD(2, 93, "季卡");

    CardTypeEnum(Integer type, Integer days, String desc) {
        this.type = type;
        this.days = days;
        this.desc = desc;
    }

    /**
     * 根据卡ID获取卡类型信息
     * @param cardType
     * @return
     */
    public static CardTypeEnum getCardType(Integer cardType){
        CardTypeEnum[] cardTypes = CardTypeEnum.values();
        for (int i = 0; i < cardTypes.length; i++) {
            if (cardTypes[i].getType().equals(cardType)) {
                return cardTypes[i];
            }
        }
        return CardTypeEnum.MONTH_CARD;
    }

    /**
     * 类型
     */
    private Integer type;
    /**
     * 有效时间
     */
    private Integer days;
    /**
     * 描述
     */
    private String desc;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
