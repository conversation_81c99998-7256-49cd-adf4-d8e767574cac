package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;

import java.io.InputStream;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/26
 */
@Data
public class QueryAndExportSuiXiangCardCdkOutput implements Serializable {
    private static final long serialVersionUID = 6669801388816549166L;
    private BaseResponse baseResponse; // 基础应答体
    private String csvUrl; // 阿里云相对路径
    private String zipUrl; // 阿里云相对路径
    private String targetName; // 目标文件名
    private byte[] bytes;
}
