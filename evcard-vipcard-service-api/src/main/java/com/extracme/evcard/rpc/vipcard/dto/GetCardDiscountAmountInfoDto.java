package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022/2/20 16:35
 */
@Data
public class GetCardDiscountAmountInfoDto implements Serializable {

    /**
     * 周期内总额度
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 已使用额度
     * 已使用额度+冻结额度
     */
    private BigDecimal discountAmount;

    /**
     * 冻结额度
     */
    private BigDecimal frozenAmount;

    /**
     * 可使用额度
     * 未冻结的额度 = 周期内总额度-已使用额度
     */
    private BigDecimal canUseAmount;
}
