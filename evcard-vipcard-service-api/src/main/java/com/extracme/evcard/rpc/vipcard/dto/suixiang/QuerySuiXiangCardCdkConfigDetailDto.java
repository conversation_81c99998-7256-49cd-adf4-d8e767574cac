package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class QuerySuiXiangCardCdkConfigDetailDto implements Serializable {

    private static final long serialVersionUID = -8390832816659710429L;

    private Long cardBaseId;


    private Long cardRentId;


    private Long cardPriceId;

    //租期+售价
    private String actDesc;

    // 第三方售卖价
    private BigDecimal thirdSalePrice;
}