package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/1/6
 */
@Data
public class CardPurchaseListQueryDto implements Serializable {
    /**
     * 用户手机号
     */
    private String mobilePhone;

    /**
     * 用户pkId
     */
    private Long userId;

    /**
     * 卡片ID
     */
    private Long cardId;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 卡片类型 0周卡 1月卡 季卡
     */
    private Integer cardType;

    /**
     * 可用区域，包含
     */
    private Long cityId;

    /**
     * 运营公司，仅用于选择城市
     */
    private String orgId;

    /**
     * 购买订单号，
     * 购买记录编号
     */
    private String refSeq;


    /**
     * 购买订单号
     */
    private String orderSeq;

    /**
     * 卡片类别 1企业卡 2企业个人卡 3付费会员卡
     */
    private Integer cardGroup;

    /**
     * 购买时间限制：开始 yyyyMMddHHmmss
     */
    private String purchaseTimeStart;

    /**
     * 购买时间限制：结束
     */
    private String purchaseTimeEnd;

    /**
     * 购卡方式： 0 购买  1 赠送 2积分兑换
     * @since 1.1.0
     */
    private Integer issueType;

    /**
     * 购卡条件： 0 无  1 学生卡
     * @since 1.1.0
     */
    private Integer purchaseType;

    /**
     * 购卡订单状态 -1已取消 0待支付 1已支付
     * @since 1.3.0
     */
    private Integer paymentStatus;
}
