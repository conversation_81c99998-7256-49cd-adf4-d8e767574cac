package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/17
 */
@Getter
@AllArgsConstructor
public enum SuiXiangCardRecordLogOperationTypeEnum {
    ORDER(0, "下单"),//创建支付单
    PAYED(1, "支付完成"),// 发起支付接口成功
    CANCELED(2, "取消"),
    GIVE_OUT(3, "卡发放"),//赠送 或者 购卡mq消息接口处理成功发卡
    ABNORMAL_PAY_PAYED(4, "异常支付-已支付"),
    ABNORMAL_PAY_CANCELED(5, "异常支付-已取消");
    //CREATE_PAY(6, "发起支付");

    /**
     * 操作类型 0下单 1支付完成 2取消 3卡发放 4异常支付-已支付  5异常支付-已取消 6 发起支付
     */
    private int operationType;

    /**
     * 描述
     */
    private String desc;
}
