package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.framework.core.bo.PageBeanBO;

/**
 * 卡片模板配置相关服务
 * <AUTHOR>
 */
public interface ICardModelService {

    /**
     * 新增付费会员卡卡片
     * @param configDTO 卡片配置信息
     * @param operateDTO 操作人信息
     * @return
     * @since 1.0.0
     */
    BaseResponse add(CardConfigDto configDTO, OperatorDto operateDTO);

    /**
     * 修改付费会员卡卡片
     * @param configDTO 卡片配置信息
     * @param operateDTO 操作人信息
     * @return
     * @since 1.0.
     * @remark 已被引用的卡片不可变更
     */
    BaseResponse update(CardConfigDto configDTO, OperatorDto operateDTO);

    /**
     * 禁用卡片(删除)卡片
     * @param id 卡片ID
     * @param operateDTO 操作人信息
     * @return
     * @remark 被引用的卡片可禁用，但不影响已使用的活动
     */
    BaseResponse disable(Long id, OperatorDto operateDTO);

    /**
     * 启用卡片
     * @param id 卡片ID
     * @param operateDTO 操作人信息
     * @return
     */
    BaseResponse enable(Long id, OperatorDto operateDTO);

    /**
     * 查询指定卡片的配置信息
     * @param id
     * @return
     */
    CardModelDetailDto getCardModelById(Long id);

    /**
     * 查询指定卡片的配置信息，包含车型及城市中文信息
     * 卡片配置详情页及编辑时使用
     * @param id
     * @return
     */
    CardModelListViewDto getCardModelDetailById(Long id);

    /**
     * 查询指定卡片的配置信息(分页)
     * @param queryDto 查询条件
     * @return
     */
    PageBeanBO<CardModelListViewDto> queryPage(CardModelQueryDto queryDto);

    /**
     * 查询指定卡片的展示信息
     * @param id
     * @return
     */
    CardModelViewDto getCardViewById(Long id);
}
