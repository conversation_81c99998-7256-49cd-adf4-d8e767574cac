package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/5/18
 */
@Data
public class CardDiscountInfoQueryDto implements Serializable {
    /**
     * 用户卡ID
     */
    private Long userCardNo;

    /*开始时间*/
    private Date startTime;

    /*结束时间*/
    private Date endTime;

    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 页面大小
     */
    private Integer pageSize = 10;
    /**
     * 是否显示总数
     */
    private Integer isAll = 0;
}
