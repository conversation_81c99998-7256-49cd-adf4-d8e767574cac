package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
public class CardActivityQueryDto implements Serializable {
    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动所属运营公司
     */
    private String orgId;

    /**
     * 卡片ID
     */
    private Long cardId;

    /**
     * 卡片名称，必须
     */
    private String cardName;

    /**
     * 卡片类型 0周卡 1月卡 季卡
     */
    private Integer cardType;

    /**
     * 活动状态 0待审核 1待上架 2已上架(开放购买) 3已下架(不影响已购卡片)
     */
    private Integer activityStatus;

    /**
     * 活动平台： 0：EVCARD 1：公众不可见
     * 非必传
     */
    private Integer platformType;

    /**
     * 上架开始日期， yyyyMMdd
     */
    private String startDate;

    /**
     * 上架结束日期， yyyyMMdd
     */
    private String endDate;

    /**
     * 是否开启预告 0开启 1未开启
     */
    private Integer preAnnounceEnable;

    /**
     * 库存最小值
     */
    private Long stockMin;

    /**
     * 库存最大值
     */
    private Long stockMax;

    /**
     * 销售量最小值
     */
    private Long salesVolumeMin;

    /**
     * 销售量最大值
     */
    private Long salesVolumeMax;

    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 页面大小
     */
    private Integer pageSize = 10;
    /**
     * 是否显示总数
     */
    private Integer isAll = 0;
}
