package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;

import java.util.List;
import java.util.Set;

/**
 * 随想卡
 * 销售活动服务
 */
public interface ISuixiangCardSalesService {


    /**
     * 查询售卡城市id
     * @param saleType 售卡的类型 1：折扣卡 2：随享卡 3:任何卡
     * @return -1代表全部城市
     */
    Set<Long> getSaleCardCityIds(int saleType);


    /**
     * 根据城市查询开始预售/已上架的 随享卡
     * @param userId 用户id，非必填
     * @param cityName  城市名称，必填
     * @return
     */
    List<EffectiveSuiXiangCardInfoDto> getSuiXiangCardListByCity(Long userId, String cityName);

    /**
     * 查询随享卡 购买详情（未购买，返回需要带上所有租期和车型）
     * @param userId
     * @param cardBaseId
     * @return
     */
    EffectiveSuiXiangCardDetailDto getSuiXiangCardDetail(Long userId, Long cardBaseId);

    /**
     * 根据城市id 和 车型id 查询 已上架的随享卡
     *  未购买
     *  供订单系统使用
     * @param input
     * @return
     */
    List<SuixiangCardInfoDto> getCanBuyCardList(GetCanBuyCardListInput input);


    /**
     * 根据城市id 和 车型id 查询 已上架的随享卡
     *  已购买
     *  供app使用
     * @param inputDto
     * @return
     */
    List<SuiXiangQueryAvailableCardInfoDto> getAvailableCardByUseCondition(GetAvailableCardByUseConditionInputDto inputDto);
    /**
     *  查询用户 已购买 生效中的随享卡
     *  已购买
     * @return
     */
    List<SuixiangCardUseBo> getEffectiveCardInfoListByUserId(Integer userPkId);


    /**
     * 查询创建了支付账单的 卡详情
     *
     * 购卡账单-未支付的购卡记录、我的卡片-已购买的卡
     * @param input
     * @return
     * @throws BusinessException
     */
    GetSuiXiangCardDetailForHasBuyBo  querySuiXiangCardDetailForHasBuy(GetSuiXiangCardDetailForHasBuyInput input)throws BusinessException;




// 未使用
    /**
     * 用户priceId查询
     * 未购买
     * @param cardPriceId
     * @return
     */
    SuixiangCardInfoBo queryCardInfoById(Long cardPriceId);

    /**
     * 用户userCardId 查询
     *  已购买
     * @param userCardId
     * @return
     */
    SuixiangCardInfoBo queryCardInfoByUserCardId(Long userCardId);

}
