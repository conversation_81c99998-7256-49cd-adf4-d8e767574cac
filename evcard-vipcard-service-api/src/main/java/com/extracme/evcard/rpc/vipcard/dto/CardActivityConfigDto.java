package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/29
 */
@Data
public class CardActivityConfigDto implements Serializable {
    /**
     * 活动ID，更新时使用
     */
    private Long id;

    /**
     * 卡片ID
     */
    private Long cardId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 运营机构
     */
    private String orgId;

    /**
     * 实际售价
     */
    private BigDecimal salesPrice;

    /**
     * 划线价格
     */
    private BigDecimal underlinePrice;

    /**
     * 单人购买限制
     */
    private Integer personPurchasesLimit;

    /**
     * 初始库存
     */
    private Long stock;

    /**
     * 上架时间，精确到小时, 格式yyyyMMddHHmmss
     */
    private String startTime;

    /**
     * 下架时间，精确到小时, 格式yyyyMMddHHmmss
     */
    private String endTime;

    /**
     * 预告时间，精确到小时，需早于上架时间, 格式yyyyMMddHHmmss
     */
    private String advanceNoticeTime;

    /**
     * 使用规则
     * @remark 1.1.0版本，此字段作废
     */
    private String rules;

    /**
     * 活动展示平台：  EVCARD/对公众不可见
     * @since 1.1.0 添加
     */
    private Integer platformType;

}
