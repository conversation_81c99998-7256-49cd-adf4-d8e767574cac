package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;
import org.apache.commons.lang3.time.DateUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 购卡记录
 *
 */
@Data
public class PurchaseSuiXiangCardRecordInfo implements Serializable {

    private Long id;

    /**
     *
     */
    private Long cardBaseId;

    /**
     *
     */
    private Long cardRentId;

    /**
     *
     */
    private Long cardPriceId;


    /**
     *
     *
     */
    private String cardName;

    /**
     * 购买状态 1：待支付 2：已支付 3：已取消
     */
    private Integer paymentStatus;

    /**
     * 购卡方式： 1 购买  2赠送 3兑换
     */
    private Integer issueType;

    /**
     * 购买张数
     */
    private Integer quantity;


    private Date payTime;

    private Date cancelTime;

    private Double realAmount;

    /**
     * 支付订单号
     */
    private String payOrderNo;

    /**
     * 随享卡用户使用表id（在支付成功后，此字段才有值）
     */
    private String cardUseId;

    private Date startTime;

    private Date endTime;


    /**
     * 合并买卡来源 0单独&后付订单 1预付订单
     */
    private Integer mergePayOrigin;

    private Integer isDeleted;

    private Date planCancelTime;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    public Date gainCancelTime() {
        Long tm = gainCancelTimeMills();
        return new Date(tm);
    }

    public Long gainCancelTimeMills() {
        return createTime.getTime() + 5 * DateUtils.MILLIS_PER_MINUTE;
    }
}
