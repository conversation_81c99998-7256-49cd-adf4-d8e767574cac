package com.extracme.evcard.rpc.vipcard.dto;

import com.extracme.evcard.rpc.vipcard.dto.suixiang.QueryMyCardInfo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class QueryUserCardInfoDto implements Serializable {
    private static final long serialVersionUID = 7252529617545526965L;

    /**
     * 总折扣金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 待支付购卡账单数量
     */
    private Integer waitPayCardNum = 0;

    /**
     * 付费会员卡信息
     */
    private List<VipCardInfo> vipCardInfo;


    /**
     * 企业会员卡信息
     */
    private List<VipCardInfo> orgCardInfo;

    /**
     * 企业个人卡信息
     */
    private List<VipCardInfo> orgUserInfo;

    /**
     * 已经过期会员卡
     * @return
     */
    private List<VipCardInfo> overTimeCardInfo;


    /**
     * 随享卡信息
     */
    private List<QueryMyCardInfo> suiXiangCardInfo;


    /**
     * 卡片排序规则：生效中>已冻结>已失效
     * 按照购买时间
     *
     */
    private List<QueryMyCardInfo> allCardInfo;


    public List<QueryMyCardInfo> getAllCardInfo() {
        return allCardInfo;
    }

    public void setAllCardInfo(List<QueryMyCardInfo> allCardInfo) {
        this.allCardInfo = allCardInfo;
    }

    public List<QueryMyCardInfo> getSuiXiangCardInfo() {
        return suiXiangCardInfo;
    }

    public void setSuiXiangCardInfo(List<QueryMyCardInfo> suiXiangCardInfo) {
        this.suiXiangCardInfo = suiXiangCardInfo;
    }


    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    public Integer getWaitPayCardNum() {
        return waitPayCardNum;
    }

    public void setWaitPayCardNum(Integer waitPayCardNum) {
        this.waitPayCardNum = waitPayCardNum;
    }

    public List<VipCardInfo> getVipCardInfo() {
        return vipCardInfo;
    }

    public void setVipCardInfo(List<VipCardInfo> vipCardInfo) {
        this.vipCardInfo = vipCardInfo;
    }

    public List<VipCardInfo> getOrgCardInfo() {
        return orgCardInfo;
    }

    public void setOrgCardInfo(List<VipCardInfo> orgCardInfo) {
        this.orgCardInfo = orgCardInfo;
    }

    public List<VipCardInfo> getOrgUserInfo() {
        return orgUserInfo;
    }

    public void setOrgUserInfo(List<VipCardInfo> orgUserInfo) {
        this.orgUserInfo = orgUserInfo;
    }

    public List<VipCardInfo> getOverTimeCardInfo() {
        return overTimeCardInfo;
    }

    public void setOverTimeCardInfo(List<VipCardInfo> overTimeCardInfo) {
        this.overTimeCardInfo = overTimeCardInfo;
    }

}
