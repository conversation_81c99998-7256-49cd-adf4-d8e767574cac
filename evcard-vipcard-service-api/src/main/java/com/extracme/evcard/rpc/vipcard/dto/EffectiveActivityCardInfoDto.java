package com.extracme.evcard.rpc.vipcard.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 16:07 2020/12/24
 */
public class EffectiveActivityCardInfoDto implements Serializable {

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 实际价格，单位：元
     */
    private BigDecimal salesPrice;

    /**
     * 划线价格，单位：元
     */
    private BigDecimal underlinePrice;

    /**
     * 状态 1待上架 2已上架(开放购买)
     */
    private Integer activityStatus;

    /**
     * 上架时间
     * 格式：yyyyMMddHHmmss
     */
    private String startTime;

    /**
     * 是否已设置开售提醒
     * 0否 1是
     */
    private Integer saleRemind = 0;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 付费会员卡类型（0周卡 1月卡 2季卡）
     */
    private Integer cardType;

    /**
     * 会员卡使用限制
     */
    private String limitCondition;

    /**
     * 是否已达到购买上限
     * 0否 1是
     */
    private Integer purchaseLimitFlag = 0;

    /**
     * 购买条件 0：无  1：学生卡
     */
    private Integer purchaseType;

    /**
     * 卡面背景图片
     */
    private String backUrl;

    /**
     * 享有折扣，1~99整
     */
    private Integer discount;

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public BigDecimal getSalesPrice() {
        return salesPrice;
    }

    public void setSalesPrice(BigDecimal salesPrice) {
        this.salesPrice = salesPrice;
    }

    public BigDecimal getUnderlinePrice() {
        return underlinePrice;
    }

    public void setUnderlinePrice(BigDecimal underlinePrice) {
        this.underlinePrice = underlinePrice;
    }

    public Integer getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(Integer activityStatus) {
        this.activityStatus = activityStatus;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public Integer getSaleRemind() {
        return saleRemind;
    }

    public void setSaleRemind(Integer saleRemind) {
        this.saleRemind = saleRemind;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public String getLimitCondition() {
        return limitCondition;
    }

    public void setLimitCondition(String limitCondition) {
        this.limitCondition = limitCondition;
    }

    public Integer getPurchaseLimitFlag() {
        return purchaseLimitFlag;
    }

    public void setPurchaseLimitFlag(Integer purchaseLimitFlag) {
        this.purchaseLimitFlag = purchaseLimitFlag;
    }

    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }
}
