package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 随想卡发卡入参
 *
 */
@Data
public class CreateCardDto implements Serializable {
    /**
     * 基于purchaseId
     */
    private Long purchaseId;

    /**
     *
     */
    private Long cardBaseId;

    /**
     *
     */
    private Long cardRentId;

    /**
     *
     */
    private Long cardPriceId;


    /**
     * 发卡方式： 0 购买  1赠送 2兑换，必须
     */
    int issueType;

    /**
     * 会员主键id，必须
     */
    private Long userId;

    /**
     * 张数，必须
     */
    private Integer quantity;

    /**
     *  支付订单号
     * 购买，才有
     */
    private String payOrderNo;

  /*  *//**
     * 支付金额，必须
     *//*
    private Double realAmount;

    *//**
     * 混合支付时折扣金额
     *//*
    private BigDecimal discountAmount;

    *//**
     * 备注，非必须
     *//*
    private String remark;

    *//**
     * 本次发卡生效时间，非必传
     * 预付订单合并支付场景下： 使用下单时间作为卡片生效开始时间
     *//*
    private Date activeStartTime;*/
}
