package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 会员卡片信息
 * <AUTHOR>
 * @Discription
 * @date 2020/12/28
 */
@Data
public class MemberCardListViewDto implements Serializable {
    /**
     * 会员卡ID
     */
    private Long userCardNo;

    /**
     * 会员卡ID:
     * 付费会员卡 V10001
     * 企业折扣卡 E10001
     * 个人折扣卡 P10001
     */
    private String userCardNoDesc;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 会员卡类别 1企业卡 2企业个人卡 3付费会员卡
     */
    private Integer cardGroup;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 会员卡生效状态：0未生效 1生效中 3已过期 4已禁用 5作废
     */
    private Integer cardStatus;

    /**
     * 同cardStatus
     */
    private Integer userCardStatus;

    /**
     * 卡片可操作标识
     * 0无 1 允许作废 2 待作废
     */
    private Integer disableFlag;

    /**
     * 付费会员卡：卡片ID
     */
    private Long cardId;

    /**
     * 企业id， 针对企业卡和企业个人卡
     */
    private String agencyId;

    /**
     * 生效起始日期(购买日期)， yyyy-MM-dd HH:mm:ss
     */
    private String startTime;

    /**
     * 最新一笔购买日期， yyyy-MM-dd HH:mm:ss
     */
    private String purchaseTime;

    /**
     * 卡片到期时间， yyyy-MM-dd HH:mm:ss
     */
    private String expiresTime;

    /**
     * 累计订单数
     */
    private Long totalOrder;

    /**
     * 累计节省金额，单位元
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 付费会员卡：可用区域（多个城市名称，以,分割）
     */
    private String cityNames;

    /**
     * 付费会员卡：：卡片类型 0周卡 1月卡 季卡
     */
    private Integer cardType;

    /**
     * 购卡条件： 0 无  1 学生卡
     * @since 1.1.0
     */
    private Integer purchaseType;

    /**
     * 付费会员卡：：享有折扣，1~99整数
     */
    private Integer discount;

    /**
     * 付费会员卡：：单笔订单最大抵扣金额，单位：元
     */
    private BigDecimal maxValue;

    /**
     *付费会员卡：：订单时长限制，单位分钟
     */
    private Integer durationLimit;

    /**
     * 付费会员卡：：卡片配置信息-可用区域限制
     */
    private String cityLimit;

    /**
     * 付费会员卡：：卡片配置信息-车型限制
     */
    private String vehicleModelLimit;

    /**
     * 付费会员卡：卡片配置信息-产品线限制
     */
    private String rentMethod;

    /**
     * 是否允许退款
     */
    private boolean allowedRefund;

    /**
     * 创建时间
     */
    private Date createTime;

    private Long createOperId;

    private String createOperName;

    /**
     * 更新时间
     */
    private String updateTime;

    private Long updateOperId;

    private Date updateOperName;
}
