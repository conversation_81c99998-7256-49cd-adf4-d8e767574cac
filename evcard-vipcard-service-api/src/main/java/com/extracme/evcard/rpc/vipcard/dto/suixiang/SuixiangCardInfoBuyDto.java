package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *  随享卡基本配置信息 （已经购买）
 *
 *
 */
@Data
public class SuixiangCardInfoBuyDto extends SuixiangCardInfoDto implements Serializable {


    private Long id;

    /**
     *
     */
    private Long cardPriceId;

    /**
     *
     */
    private Long userId;

    /**
     *
     */
    private Integer cardType;

    /**
     *
     */
    private String cardName;

    /**
     * 卡状态：0未生效 1生效中 3已过期 4已禁用 5已冻结（申请退卡）
     */
    private Integer cardStatus;

    /**
     * 卡片有效期开始时间
     */
    private Date startTime;

    /**
     * 卡片有效结束时间
     */
    private Date expiresTime;

    /**
     * 累计使用订单数
     */
    private Long totalOrder;

    /**
     * 累计节省金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 初始天数
     */
    private Integer initDays;

    /**
     *随享卡可用天数（剩余天数）
     */
    private Integer availableDays;

    /**
     *
     * 随享卡已经用过天数
     */
    private Integer usedDays;

    /**
     *随享卡冻结天数
     */
    private Integer frozenDays;

}
