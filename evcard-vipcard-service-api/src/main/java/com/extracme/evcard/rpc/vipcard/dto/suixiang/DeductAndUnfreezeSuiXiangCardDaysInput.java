package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;

@Data
public class DeductAndUnfreezeSuiXiangCardDaysInput extends UseSuiXiangCardDaysInput implements Serializable {

    // 扣除天数的合同号
    private String deductContractId;
    // 解冻天数的合同号
    private String unFreezeContractId;
    // 扣除天数
    private Integer deductDays;
    // 解冻冻天数
    private Integer unFreezeDays;

}
