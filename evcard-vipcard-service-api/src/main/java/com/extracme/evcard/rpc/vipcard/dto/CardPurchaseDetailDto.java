package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/29
 */
@Data
public class CardPurchaseDetailDto implements Serializable {
    /**
     * 购买记录id
     */
    private Long id;

    /**
     * 用户编号/pkId
     */
    private Long userId;

    /**
     * 用户付费会员卡-卡号
     */
    private String userCardNo;

    /**
     * 交易号
     */
    private String outTradeSeq;

    /**
     * 卡片ID
     */
    private Long cardId;

    /**
     * 销售活动id
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动运营机构
     */
    private String orgId;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 购买金额
     */
    private Double realAmount;
}
