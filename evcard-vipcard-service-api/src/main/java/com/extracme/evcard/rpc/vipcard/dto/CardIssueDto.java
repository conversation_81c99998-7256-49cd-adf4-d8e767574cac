package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 付费会员卡发卡入参
 * <AUTHOR>
 * @Discription
 * @date 2021/5/27
 */
@Data
public class CardIssueDto {
    /**
     * 基于purchaseId
     */
    private Long purchaseId;

    /**
     * 活动id，必须
     */
    private Long activityId;

    /**
     * 发卡方式： 0 购买  1赠送 2兑换，必须
     */
    int issueType;

    /**
     * 会员主键id，必须
     */
    private Long userId;

    /**
     * 张数，必须
     */
    private Integer quantity;

    /**
     * 交易流水号，必须
     * 若非购买，则为批次请求id
     */
    private String outTradeSeq;

    /**
     * 支付金额，必须
     */
    private Double realAmount;

    /**
     * 混合支付时折扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 备注，非必须
     */
    private String remark;

    /**
     * 本次发卡生效时间，非必传
     * 预付订单合并支付场景下： 使用下单时间作为卡片生效开始时间
     */
    private Date activeStartTime;
}
