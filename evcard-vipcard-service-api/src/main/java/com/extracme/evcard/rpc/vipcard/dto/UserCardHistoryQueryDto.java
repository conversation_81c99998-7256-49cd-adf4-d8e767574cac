package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/5/18
 */
@Data
public class UserCardHistoryQueryDto implements Serializable {
    /**
     * 用户卡ID， 必传
     */
    private Long userCardNo;

    /**
     * 操作时间： 开始
     */
    private Date startTime;

    /**
     * 操作时间： 结束
     */
    private Date endTime;

    /**
     * 卡类别(冗余存储)：1企业会员卡 2企业个人卡 3付费会员卡
     */
    private Integer cardGroup;

    /**
     * 操作类型  0卡发放/购买 1卡消费 2卡作废
     */
    private Integer operationType;

    /**
     * 订单编号
     */
    private String orderSeq;

    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 页面大小
     */
    private Integer pageSize = 10;
    /**
     * 是否显示总数
     */
    private Integer isAll = 0;
}
