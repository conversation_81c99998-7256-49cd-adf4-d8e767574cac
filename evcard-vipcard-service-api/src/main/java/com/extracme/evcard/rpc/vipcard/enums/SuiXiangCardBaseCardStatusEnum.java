package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
@Getter
@AllArgsConstructor
public enum SuiXiangCardBaseCardStatusEnum {
    TO_BE_SUBMITTED(1, "待提交"),
    TO_BE_PUT_ON_SHELF(2, "待上架"),
    EFFECTIVE(3, "已上架(开放购买)"),
    OFF_SHELF(4, "已下架");

    /**
     * 状态
     */
    private int status;

    /**
     * 描述
     */
    private String desc;
}
