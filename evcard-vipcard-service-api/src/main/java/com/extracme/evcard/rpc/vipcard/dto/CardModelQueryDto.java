package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class CardModelQueryDto implements Serializable {
    /**
     * 卡片ID
     */
    private Long cardId;

    /**
     * 卡片名称，必须
     */
    private String cardName;

    /**
     * 所属运营公司
     */
    private String orgId;

    /**
     * 卡片类型 0周卡 1月卡 季卡
     */
    private Integer cardType;

    /**
     * 购卡条件： 0 默认  1学生卡
     */
    private Integer purchaseType;

    /**
     * 可用城市限制(模糊查询)
     */
    private Long cityId;

    /**
     * 租车模式 0 即时分时 1 预约分时(废弃) 2即时日租 3预约日租 -1不限制
     */
    private List<Integer> rentMethods;

    private List<Integer> rentMethodGroups;

    /**
     * 卡片状态 0启用 1禁用
     */
    private Integer status;

    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 页面大小
     */
    private Integer pageSize = 10;
    /**
     * 是否显示总数
     */
    private Integer isAll = 0;
}
