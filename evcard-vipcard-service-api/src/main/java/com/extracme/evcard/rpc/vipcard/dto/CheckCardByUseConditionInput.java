package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
public class CheckCardByUseConditionInput extends GetAvailableCardByUseConditionInputDto implements Serializable {
    private Long userCardNo;
    private BigDecimal discountRate;
    private Date orderTime;
    private BigDecimal orderFrozenAmount;
    private Boolean needCheckUsedMaxValue;
}
