package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SendSuiXiangCardInput implements Serializable {

    private List<String> authIds;

    private long id; // 随享卡的配置id

    private String rentDaysId; // 租期id

    private String price; // 价格

    private int listType; // 名单类型：1-手机号码、2-会员ID

    private int number; // 张数

    private OperatorDto operatorDto; // 操作日志信息
}
