package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.vipcard.dto.VipCardInfo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

@Data
public class QueryMyCardInfo extends VipCardInfo implements Serializable {

    /**
     * '会员卡类别：1企业会员卡 2企业个人卡 3付费会员卡 4随享卡
     */
    private Integer cardGroup;


    // 随享卡特殊
    /**
    /**
     * 购买卡id
     */
    private Long purchaseId;

    /**
     * 卡状态：1：生效中 2 已过期（过期自动失效）  3 生效中-已冻结 4：已退卡 5：已作废 6：过期已冻结  7已使用  8自动已冻结
     */
    private Integer suiXiangCardUseStatus;

    /**
     * 0:否 1：是
     */
    private Integer sendStatus = 0;

    /**
     * 落地页地址
     * 无：默认没有配置
     */
    private String landingPageUrl;

    /**
     * 是否有落地页，1：有 2：没有，默认为2
     */
    private Integer landingPageFlag = 2;

    /**
     * 剩余可用天数
     *
     */
    private int availableDays;

    /**
     * 随享卡活动id，基础表id
     */
    private Long cardBaseId;

    public static QueryMyCardInfo getQueryUserSuiXiangCardInfo2(VipCardInfo vipCardInfo){
        QueryMyCardInfo result = new QueryMyCardInfo();
        BeanUtils.copyProperties(vipCardInfo,result);
        return result;
    }
}
