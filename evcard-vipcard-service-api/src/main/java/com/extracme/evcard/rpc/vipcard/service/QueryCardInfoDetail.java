package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.vipcard.dto.UserCardDiscountDetail;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class QueryCardInfoDetail implements Serializable {
    private static final long serialVersionUID = -9188382194158633420L;

    /**
     * 总价
     */
    private BigDecimal totalAmount;

    /**
     * 购买数量
     */
    private Integer times;

    /**
     * 购卡日期
     */
    private String createTime;

    /**
     * 有效期至
     */
    private String expiresTime;

    /**
     * 名称
     */
    private String cardName;

    /**
     * 限制条件
     */
    private String limitCondition;

    /**
     * 付费会员卡类型（0周卡 1月卡 2季卡）
     */
    private Integer cardType;

    /**
     * 使用限制详情
     */
    private List<String> limitDetail;

    /**
     * 有效期
     */
    private String cardDesc;

    /**
     * 规则
     */
    private String rules;

    /**
     * 购买上限
     */
    private Integer personPurchasesLimit;

    /**
     * 企业卡类型 1：个人 2：企业
     */
    private Integer orgCardType;
    private String orgName;
    /**
     * 折扣率(淡季折扣率 通用折扣率)
     */
    private Double discountRate;

    /**
     *  旺季折扣率
     */
    private Double peakSeasonDiscountRate;

    /**
     * 卡id
     */
    private Long cardDefId;

    /**
     * 卡背景
     */
    private String backUrl;

    private Integer discount;

    /**
     *  旺季折扣
     */
    private Integer peakSeasonDiscount;

    /**
     * 购卡条件： 0 默认  1学生卡
     */
    private Integer purchaseType;

    private UserCardDiscountDetail cardDiscountDetail;


    public Integer getPeakSeasonDiscount() {
        return peakSeasonDiscount;
    }

    public void setPeakSeasonDiscount(Integer peakSeasonDiscount) {
        this.peakSeasonDiscount = peakSeasonDiscount;
    }

    public Double getPeakSeasonDiscountRate() {
        return peakSeasonDiscountRate;
    }

    public void setPeakSeasonDiscountRate(Double peakSeasonDiscountRate) {
        this.peakSeasonDiscountRate = peakSeasonDiscountRate;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getExpiresTime() {
        return expiresTime;
    }

    public void setExpiresTime(String expiresTime) {
        this.expiresTime = expiresTime;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getLimitCondition() {
        return limitCondition;
    }

    public void setLimitCondition(String limitCondition) {
        this.limitCondition = limitCondition;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public List<String> getLimitDetail() {
        return limitDetail;
    }

    public void setLimitDetail(List<String> limitDetail) {
        this.limitDetail = limitDetail;
    }

    public String getCardDesc() {
        return cardDesc;
    }

    public void setCardDesc(String cardDesc) {
        this.cardDesc = cardDesc;
    }

    public String getRules() {
        return rules;
    }

    public void setRules(String rules) {
        this.rules = rules;
    }

    public Integer getPersonPurchasesLimit() {
        return personPurchasesLimit;
    }

    public void setPersonPurchasesLimit(Integer personPurchasesLimit) {
        this.personPurchasesLimit = personPurchasesLimit;
    }

    public Integer getOrgCardType() {
        return orgCardType;
    }

    public void setOrgCardType(Integer orgCardType) {
        this.orgCardType = orgCardType;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Double getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Double discountRate) {
        this.discountRate = discountRate;
    }

    public Long getCardDefId() {
        return cardDefId;
    }

    public void setCardDefId(Long cardDefId) {
        this.cardDefId = cardDefId;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public void setCardDiscountDetail(UserCardDiscountDetail cardDiscountDetail) {
        this.cardDiscountDetail = cardDiscountDetail;
    }

    public UserCardDiscountDetail getCardDiscountDetail() {
        return cardDiscountDetail;
    }

    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }
}
