package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/3
 */
@Data
public class CarRentalCfgDto implements Serializable {
    private int price; // 售价，单位为元，大于0的整数
    private int crossedPrice; // 划线价，单位为元，大于0的整数
    private String modelGroup; // 车型组
    private List<String> modelIdList; // 可用车型id
    private String modelName; // 在随享卡详情页，后端会设置好返回，多个以“、”分隔
    private String landingPagePicUrl; // 非必填，落地页引导图完整路径地址
    private String landingPageHeadPicUrl; // 非必填，落地页头图完整路径地址
}
