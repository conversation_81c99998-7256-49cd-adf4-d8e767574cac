package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;


@Data
public class MemberCardQueryDto implements Serializable {
    /**
     * 卡片ID
     */
    private Long cardId;

    /**
     * 卡片名称，必须
     */
    private String cardName;

    /**
     * 卡片类型 0周卡 1月卡 2季卡 3随享卡
     */
    private Integer cardType;

    /**
     * 卡片类别 1企业卡 2企业个人卡 3付费会员卡
     */
    private Integer cardGroup;

    /**
     * 购卡条件： 0 无  1 学生卡
     * @since 1.1.0
     */
    private Integer purchaseType;

    /**
     * 用户会员卡状态：0未生效 1生效中 3已过期 4已禁用 5已作废
     * 当cardType=3-随享卡的时候，此字段定义为：1-生效中、2-已失效、3-已冻结
     */
    private Integer userCardStatus;

    /**
     * 生效开始日期， yyyyMMdd
     */
    private String startDate;

    /**
     * 生效结束日期， yyyyMMdd
     */
    private String endDate;

    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 页面大小
     */
    private Integer pageSize = 10;
    /**
     * 是否显示总数
     */
    private Integer isAll = 0;
}
