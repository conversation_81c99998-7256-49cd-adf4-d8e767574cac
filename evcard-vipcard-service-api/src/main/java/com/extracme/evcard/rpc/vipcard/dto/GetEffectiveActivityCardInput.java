package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 9:37 2021/9/26
 */
@Data
public class GetEffectiveActivityCardInput implements Serializable {

    /**
     * 用户id 必填项
     */
    private Long userId;

    /**
     * 可使用会员卡的租金 必填项
     */
    private BigDecimal rentAmount;

    /**
     * 取车时间 必填项
     */
    private Date orderPickUpDate;

    /**
     * 还车时间 必填项
     */
    private Date orderReturnDate;

    /**
     * 用车时长 必填项
     */
    private Integer orderCostTime;

    /**
     * 取车网点 必填项(门店与网点二选一)
     */
    private String pickUpShopSeq;

    /**
     * 还车网点 非必填项
     * 没有就不传还车网点
     */
    private String returnShopSeq;

    /**
     * 车型编号 必填项（此处为资产车型）
     */
    private Integer vehicleModelSeq;

    /**
     * 业务产品线 必填项
     * 租车模式 0 即时分时 2即时日租 3预约日租 4门店
     */
    private Integer rentMethod;

    /**
     * 追加-商品车型
     */
    private Integer goodsModelId;

    /**
     * 追加-取车门店
     */
    private String pickUpStoreId;

    /**
     * 追加-还车门店，非必填，没有就不传
     */
    private String returnStoreId;

    /**
     * 追加-取车城市， 若已提供取还车网点(此字段可不传)
     */
    private Long pickUpCity;

    /**
     * 追加-还车城市， 若已提供取还车网点(此字段可不传)
     */
    private Long returnCity;

    /**
     * 用作修改订单和更换车型前，把老订单的抵扣金额还原
     * 老的订单号
     */
    private String oldOrderSeq;

    public GetEffectiveActivityCardInput() {
    }



    public GetEffectiveActivityCardInput(Long userId, BigDecimal rentAmount, Date orderPickUpDate, Integer orderCostTime, String pickUpShopSeq, String returnShopSeq, Integer vehicleModelSeq, Integer rentMethod) {
        this.userId = userId;
        this.rentAmount = rentAmount;
        this.orderPickUpDate = orderPickUpDate;
        this.orderCostTime = orderCostTime;
        this.pickUpShopSeq = pickUpShopSeq;
        this.returnShopSeq = returnShopSeq;
        this.vehicleModelSeq = vehicleModelSeq;
        this.rentMethod = rentMethod;
    }
}
