package com.extracme.evcard.rpc.vipcard.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 17:26 2020/12/28
 */
public class CanUseActivityCardInfoDto implements Serializable,Comparable<CanUseActivityCardInfoDto> {

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 实际价格，单位：元
     */
    private BigDecimal salesPrice;

    /**
     * 划线价格，单位：元
     */
    private BigDecimal underlinePrice;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 付费会员卡类型（0周卡 1月卡 2季卡）
     */
    private Integer cardType;

    /**
     * 会员卡使用限制
     */
    private String limitCondition;

    /**
     * 可抵扣金额
     */
    private BigDecimal deductionAmount;

    /**
     * 享有折扣，1~99整
     */
    private Integer discount;

    /**
     * 购买条件 0：无  1：学生卡
     */
    private Integer purchaseType;

    /**
     * 是否已上传学生证 0否 1是
     */
    private Integer studentCardFlag = 0;

    /**
     * 卡面背景图片
     */
    private String backUrl;

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public BigDecimal getSalesPrice() {
        return salesPrice;
    }

    public void setSalesPrice(BigDecimal salesPrice) {
        this.salesPrice = salesPrice;
    }

    public BigDecimal getUnderlinePrice() {
        return underlinePrice;
    }

    public void setUnderlinePrice(BigDecimal underlinePrice) {
        this.underlinePrice = underlinePrice;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public String getLimitCondition() {
        return limitCondition;
    }

    public void setLimitCondition(String limitCondition) {
        this.limitCondition = limitCondition;
    }

    public BigDecimal getDeductionAmount() {
        return deductionAmount;
    }

    public void setDeductionAmount(BigDecimal deductionAmount) {
        this.deductionAmount = deductionAmount;
    }

    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    public Integer getStudentCardFlag() {
        return studentCardFlag;
    }

    public void setStudentCardFlag(Integer studentCardFlag) {
        this.studentCardFlag = studentCardFlag;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    @Override
    public int compareTo(CanUseActivityCardInfoDto o) {
        if (this.getDeductionAmount().compareTo(o.getDeductionAmount()) < 0) {
            return 1;
        } else if (this.getDeductionAmount().compareTo(o.getDeductionAmount()) > 0) {
            return -1;
        } else {
            if (this.getSalesPrice().compareTo(o.getSalesPrice()) > 0) {
                return 1;
            } else if (this.getSalesPrice().compareTo(o.getSalesPrice()) < 0) {
                return -1;
            } else {
                return 0;
            }
        }
    }
}
