package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;

@Data
public class CardPurchaseDetailOutput  implements Serializable {

    /**
     * 购买记录id
     */
    private Long id;

    /**
     * 类型 1：随享卡 0：折扣卡
     */
    private Integer type;

    /**
     * 活动运营机构
     */
    private String orgId;

    /**
     * 活动运营名称
     */
    private String orgName;
}
