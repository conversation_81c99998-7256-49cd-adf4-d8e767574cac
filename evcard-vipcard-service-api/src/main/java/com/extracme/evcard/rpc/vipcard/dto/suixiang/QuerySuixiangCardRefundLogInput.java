package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;

@Data
public class QuerySuixiangCardRefundLogInput implements Serializable {
    /**
     * 随享卡基础表id
     */
    private Long cardBaseId;

    /**
     * 随享卡用户使用表id
     */
    private Long cardUseId;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 购买订单号
     */
    private String orderSeq;

    /**
     * 退款开始时间
     */
    private String refundStartTime;

    /**
     * 退款结束时间
     */
    private String refundEndTime;

    /**
     * 退款类型，1=手动退款，2=自动原路
     */
    private Integer type;

    /**
     * 退款结果，0=失败，1=成功
     */
    private Integer success;

    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 页面大小
     */
    private Integer pageSize = 10;
}
