package com.extracme.evcard.rpc.vipcard.dto;

import java.io.Serializable;
import java.util.Date;

public class VipCardInfo implements Serializable {
    private static final long serialVersionUID = 1973058598089253618L;

    private Long cardId;

    private String cardName;

    /**
     * 限制条件
     */
    private String limitCondition;

    /**
     * '会员卡类别：1企业会员卡 2企业个人卡 3付费会员卡
     */
    private Integer cardGroup;


    /**
     * 卡片有效期
     */
    private String expiresTime;

    /**
     * 是否即将到期 0:否 1：是
     */
    private Integer overTime=0;

    /**
     * 企业信息
     */
    private String orgName;


    /**
     * 付费会员卡类型（0周卡 1月卡 2季卡）
     */
    private Integer cardType;

    /**
     * 卡类型 1：可续费 2：已下架 3：已达购买上限 4：库存被占
     */
    private Integer cardStatus = 1;

    /**
     * 卡的状态 会员卡状态：0未生效 1生效中 3已过期 4已禁用 5作废
     */
    private Integer cardUseStatus;

    /**
     * 活动id
     */
    private Long activityId;

    private String backUrl;

    /**
     *  通用折扣（淡季折扣，原来的字段）
     */
    private Integer discount;

    /**
     *  旺季折扣
     */
    private Integer peakSeasonDiscount;

    /**
     * 购买时间
     */
    private Date purchaseTime;


    public Integer getPeakSeasonDiscount() {
        return peakSeasonDiscount;
    }

    public void setPeakSeasonDiscount(Integer peakSeasonDiscount) {
        this.peakSeasonDiscount = peakSeasonDiscount;
    }

    public Date getPurchaseTime() {
        return purchaseTime;
    }

    public void setPurchaseTime(Date purchaseTime) {
        this.purchaseTime = purchaseTime;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getLimitCondition() {
        return limitCondition;
    }

    public void setLimitCondition(String limitCondition) {
        this.limitCondition = limitCondition;
    }

    public String getExpiresTime() {
        return expiresTime;
    }

    public void setExpiresTime(String expiresTime) {
        this.expiresTime = expiresTime;
    }

    public Integer getOverTime() {
        return overTime;
    }

    public void setOverTime(Integer overTime) {
        this.overTime = overTime;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public Integer getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(Integer cardStatus) {
        this.cardStatus = cardStatus;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public Integer getCardGroup() {
        return cardGroup;
    }

    public void setCardGroup(Integer cardGroup) {
        this.cardGroup = cardGroup;
    }

    public Integer getCardUseStatus() {
        return cardUseStatus;
    }

    public void setCardUseStatus(Integer cardUseStatus) {
        this.cardUseStatus = cardUseStatus;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }
}
