package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
public class SuiXiangCardItemDto implements Serializable {
    private long id; // 随享卡ID
    private String cardName; // 随享卡名称
    private int cardStatus; // 卡片状态：1-待提交、2-待上架、3-已上架、4-已下架
    private int isDisplay; // 是否对外展示：1-是、2-否
    private String minLeaseTerm; // 租期（几天起）
    private int leftStock; // 剩余库存
    private String previewTime; // 预告时间，格式 yyyy-MM-dd HH:mm
    private String saleTimeRange; // 售卖时间，格式 yyyy-MM-dd HH:mm~yyyy-MM-dd HH:mm
    private String cityNames; // 可用区域，如“上海、昆山”，后端拼接好给出
    private String orgName; // 运营公司
    private String updateOperName; // 操作人
    private String secondAppKey; // 二级渠道
    private String secondAppName; // 二级渠道名称
}
