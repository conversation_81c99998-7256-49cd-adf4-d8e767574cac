package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;
import org.apache.commons.lang3.time.DateUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 购卡记录
 * <AUTHOR>
 * @Discription
 * @date 2021/6/22
 * @since 1.3.0
 */
@Data
public class PurchaseRecordInfo implements Serializable {
    private Long id;

    private Long userId;

    private Long cardActivityId;

    private Long cardId;

    private Integer paymentStatus;

    /**
     * 购卡方式： 0 购买  1 赠送 2积分兑换
     */
    private Integer issueType;

    private Integer quantity;

    private Date payTime;

    private Date cancelTime;

    private Double realAmount;

    private String outTradeSeq;

    private String userCardNo;

    private Date startTime;

    private Date endTime;

    private String orderSeq;

    /**
     * 合并买卡来源 0单独&后付订单 1预付订单
     */
    private Integer mergePayOrigin;

    private Integer status;

    private Date planCancelTime;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    public Date gainCancelTime() {
        Long tm = gainCancelTimeMills();
        return new Date(tm);
    }

    public Long gainCancelTimeMills() {
        return createTime.getTime() + 5 * DateUtils.MILLIS_PER_MINUTE;
    }
}
