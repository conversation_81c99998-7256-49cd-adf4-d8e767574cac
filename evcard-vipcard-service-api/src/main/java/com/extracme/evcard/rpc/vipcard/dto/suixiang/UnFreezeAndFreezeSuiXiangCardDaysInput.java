package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;

@Data
public class UnFreezeAndFreezeSuiXiangCardDaysInput extends UseSuiXiangCardDaysInput implements Serializable {

    // 新合同号
    private String newContractId;
    // 老合同号
    private String oldContractId;
    // 冻结天数
    private Integer freezeDays;
    // 解冻冻天数
    private Integer unFreezeDays;

}
