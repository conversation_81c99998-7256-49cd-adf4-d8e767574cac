package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 批量送卡入参
 * <AUTHOR>
 * @Discription
 * @date 2021/5/26
 */
@Data
public class CardBatchOfferDto implements Serializable {
    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 请求id
     */
    private String requestId;

    /**
     * 送卡方式 1赠送 2积分兑换
     */
    private int offerType;

    /**
     * 卡片信息列表
     */
    private List<CardOfferDto> list;

    /**
     * 送卡原因
     */
    private String remark;
}
