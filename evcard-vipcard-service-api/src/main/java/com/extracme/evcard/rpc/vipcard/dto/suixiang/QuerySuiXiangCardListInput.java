package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/3
 */
@Data
public class QuerySuiXiangCardListInput implements Serializable {
    private String cardName; // 随享卡名称
    private long cardId; // 随享卡ID
    private int cardStatus; // 卡片状态：1-待提交、2-待上架、3-已上架、4-已下架
    private String orgCode; // 运营公司CODE
    private String saleStartTime; // 售卖开始日期，格式 yyyy-MM-dd
    private String saleEndTime; // 售卖结束日期，格式 yyyy-MM-dd
    private String previewTime; // 预告日期，格式 yyyy-MM-dd
    private String cityId; // 可用区域
    private int isDisplay; // 是否对外展示：1-是、2-否
    private String saleMin; // 售出数量下限
    private String saleMax; // 售出数量上限
    private String leftStockMin; // 剩余库存下限
    private String leftStockMax; // 剩余库存上限
    private String secondAppKey; // 二级渠道

    private int pageNum; // 页码
    private int pageSize; // 每页大小
}
