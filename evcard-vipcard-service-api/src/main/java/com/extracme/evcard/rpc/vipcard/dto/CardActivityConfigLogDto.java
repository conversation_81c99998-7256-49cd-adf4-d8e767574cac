package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/1/5
 */
@Data
public class CardActivityConfigLogDto implements Serializable {
    /**操作时间*/
    private String operateTime;

    /**操作人全称*/
    private String operateUser;

    /**操作人所属公司*/
    private String orgName;

    /**
     * 操作类别
     */
    private Integer operateType;

    /**操作记录*/
    private String content;

    /**操作人*/
    private String createOperName;

    /**操作人*/
    private Long createOperId;
}
