package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.enums.CardActivityOperateEnum;
import com.extracme.framework.core.bo.PageBeanBO;

import java.util.List;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/29
 */
public interface ICardSalesActivityConfigService {
    /**
     * 新增卡片销售活动
     * @param configDTO 活动配置信息
     * @param operateDTO 操作人信息
     * @return
     * @since 1.0.0
     */
    BaseResponse add(CardActivityConfigDto configDTO, OperatorDto operateDTO);

    /**
     * 修改卡片销售活动
     * @param configDTO 活动配置信息
     * @param operateDTO 操作人信息
     * @return
     * @since 1.0.
     */
    BaseResponse update(CardActivityConfigDto configDTO, OperatorDto operateDTO);

    /**
     * 删除活动
     * @param id
     * @param operateDTO 操作人信息
     * @return
     * @remark 不可再购买，但不影响已购买的卡片
     */
    BaseResponse delete(Long id, OperatorDto operateDTO);

    /**
     * 审核通过活动
     * @param id
     * @param operateDTO 操作人信息
     * @return
     */
    BaseResponse publish(Long id, OperatorDto operateDTO);

    /**
     * 上架活动
     * @param id
     * @param operateDTO 操作人信息
     * @return
     */
    BaseResponse start(Long id, OperatorDto operateDTO);

    /**
     * 下架活动
     * @param id 卡片ID
     * @param operateDTO 操作人信息
     * @return
     */
    BaseResponse stop(Long id, OperatorDto operateDTO);

    /**
     * 查询指定活动的配置信息，包含卡片信息
     * @param id
     * @return
     */
    CardActivityConfigFullDto getDetail(Long id);

    /**
     * 查询指定活动列表(分页)
     * @param queryDto 查询条件
     * @return
     */
    PageBeanBO<CardActivityListViewDto> queryPage(CardActivityQueryDto queryDto);

    /**
     * 卡片
     * @param configId
     * @param pageNum
     * @param pageSize
     * @param isAll
     * @return
     */
    PageBeanBO<CardActivityConfigLogDto> queryLogs(Long configId, Integer pageNum, Integer pageSize, Integer isAll);

    /**
     * 保存卡片活动操作日志
     * @param activityId
     * @param opType
     * @param content
     * @param operateDTO
     */
    void saveCardActivityLog(Long activityId, CardActivityOperateEnum opType,
                        String content, OperatorDto operateDTO);
}
