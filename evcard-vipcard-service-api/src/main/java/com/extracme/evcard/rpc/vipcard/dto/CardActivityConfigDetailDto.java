package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/29
 */
@Data
public class CardActivityConfigDetailDto implements Serializable {
    /**
     * 活动ID，更新时使用
     */
    private Long id;

    /**
     * 卡片ID
     */
    private Long cardId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 运营机构
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 实际售价
     */
    private BigDecimal salesPrice;

    /**
     * 划线价格
     */
    private BigDecimal underlinePrice;

    /**
     * 单人购买限制
     */
    private Integer personPurchasesLimit;

    /**
     * 活动平台
     */
    private Integer platformType;

    /**
     * 当前库存
     */
    private Long stock;

    /**
     * 初始库存
     */
    private Long openingStock;

    /**
     * 售出数量
     */
    private Long salesVolume;

    /**
     * 上架时间，精确到小时, 格式yyyyMMdd HH:mm:ss
     */
    private String startTime;

    /**
     * 下架时间，精确到小时, 格式yyyyMMdd HH:mm:ss
     */
    private String endTime;

    /**
     * 预告时间，精确到小时，需早于上架时间, 格式yyyyMMdd HH:mm:ss
     */
    private String advanceNoticeTime;

    /**
     * 使用规则
     */
    private String rules;

    /**
     * 活动状态：0待审核 1待上架 2已上架(开放购买) 3已下架(不影响已购卡片)
     */
    private Integer activityStatus;

    private Integer status;

    /**
     * 创建时间 yyyy-MM-dd HH:mm:ss
     */
    private String createTime;

    private Long createOperId;

    private String createOperName;

    /**
     * 更新时间 yyyy-MM-dd HH:mm:ss
     */
    private String updateTime;

    private Long updateOperId;

    private String updateOperName;
}
