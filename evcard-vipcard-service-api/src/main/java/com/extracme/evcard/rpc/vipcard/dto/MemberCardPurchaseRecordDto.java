package com.extracme.evcard.rpc.vipcard.dto;

import java.io.Serializable;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 17:15 2020/12/25
 */
public class MemberCardPurchaseRecordDto implements Serializable {

    /**
     * 付费会员卡购买记录id
     */
    private Long id;

    /**
     * 会员pk_id
     */
    private Long userId;

    /**
     * 卡销售活动id
     */
    private Long cardActivityId;

    /**
     * 卡id
     */
    private Long cardId;

    /**
     * 数量
     */
    private Integer quantity;

    private Integer issueType;

    /**
     * 购买状态 -1已取消 0待支付 1已支付
     */
    private Integer paymentStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCardActivityId() {
        return cardActivityId;
    }

    public void setCardActivityId(Long cardActivityId) {
        this.cardActivityId = cardActivityId;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getIssueType() {
        return issueType;
    }

    public void setIssueType(Integer issueType) {
        this.issueType = issueType;
    }

    public Integer getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(Integer paymentStatus) {
        this.paymentStatus = paymentStatus;
    }
}
