package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;


@Data
public class SuiXiangCardUseQueryDto implements Serializable {

    /**
     *  用户id
     */
    private Long userId;


    /**
     * 卡片名称，必须
     */
    private String cardName;

    /**
     * 用户会员卡状态： -1：所有 1：生效中 2 已过期（过期自动失效）  3 生效中-已冻结 4：已退卡 5：已作废 6：过期已冻结  7已使用  8自动已冻结
     */
    private Integer userCardStatus;

    /**
     * 生效开始日期， yyyyMMddHHmmdd
     */
    private String startDate;

    /**
     * 生效结束日期， yyyyMMddHHmmdd
     */
    private String endDate;

    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 页面大小
     */
    private Integer pageSize = 10;




    /**
     * 随享卡价格ID
     */
    private Long purchaseId;

    /**
     * 随享卡价格ID
     */
    private Long cardPriceId;
}
