package com.extracme.evcard.rpc.vipcard.dto.suixiang.pay;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ContractItemFeeDto implements Serializable {

    // 费用类型
    // 1租车费-套餐计费 2租车费-标准计费 3燃油费 4畅行服务费 5日租服务费 6车辆整备费 7跨门店服务费(异点) 8跨门店服务费(异地)
    // 9送车上门费 10上门取车费 11订单取消违约金  12上门取车取消违约金 13超时还车违约金 14燃油差额(需要退E币) 32 购买随享卡
    private Integer amountType;

    // 金额
    private BigDecimal amount;
    // 单价
    private BigDecimal unitPrice;
    // 数量
    private BigDecimal num;

    // 购卡支付记录id (购卡时才会有)
    private Long purchaseId;

    // 是否是早鸟套餐：1-是、2-否
    private int isEarlyBird;

    // 夜间服务费id
    private long nightServiceId;

    public ContractItemFeeDto() {
    }
    public ContractItemFeeDto(Integer amountType, BigDecimal amount) {
        this.amountType = amountType;
        this.amount = amount;
    }
    public ContractItemFeeDto(Integer amountType, BigDecimal amount, Long purchaseId) {
        this.amountType = amountType;
        this.amount = amount;
        this.purchaseId = purchaseId;
    }
    public ContractItemFeeDto(Integer amountType, BigDecimal amount, BigDecimal unitPrice, BigDecimal num) {
        this.amountType = amountType;
        this.amount = amount;
        this.unitPrice = unitPrice;
        this.num = num;
    }


}
