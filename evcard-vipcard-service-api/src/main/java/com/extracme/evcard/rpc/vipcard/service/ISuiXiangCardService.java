package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;
import com.extracme.framework.core.bo.PageBeanBO;

import java.util.List;

public interface ISuiXiangCardService {

    /**
     * 分页条件查询 用户名下 的随享卡
     *
     * @param queryDto
     * @return
     *
     */
    PageBeanBO<SuixiangCardUseBo> querySuiXiangCardPageByUserId(SuiXiangCardUseQueryDto queryDto)throws BusinessException;


    /**
     * 判断 用户是否有 生效中的随享卡
     * 包括 生效中、生效中已冻结
     *
     * @param userId
     * @return
     */
    Boolean hasEffectiveSuiXiangCard(Long userId);

    /**
     * 获取 已购买的随享卡数
     *
     * @param userId
     * @return
     */
    Integer getPurchaseSuiXiangCardNum(Long userId,Long baseCardId);

    /**
     *  查询用户设置的开售提醒，
     *  只返回 已经提醒过得
     *
     * @param userId
     * @param cardBaseIds 为空时查询所有
     */
    List<Long> queryCardHasRemind(Long userId, List<Long> cardBaseIds);


    /**
     * 设置卡的开售提醒
     *
     * @param userRemindActivityDto
     */
    void submitCardRemind(UserRemindActivityDto userRemindActivityDto) throws BusinessException;


    /**
     * 查询购卡订单
     * @param
     * @Return:
     *
     */
    List<SuiXiangCardPurchaseRecordDto> queryCardPurchaseInfoByUserId(Long userId);


    /**
     * 查询用户随享卡卡状态
     *
     * @param userId
     * @return
     */
    QueryUserCardStatusDto queryUserSuiXiangCardStatus(Long userId);


    /**
     * 查询用户已经购买的所有卡
     *  先调用之前的查询折扣卡 memberCardService.queryUserVipCardInfo，
     *  然后加上随享卡
     *
     * @param id 会员id
     */
    QueryUserCardInfoDto queryUserAllCardInfo(Long id);


    /**
     * 获取用户未读的赠送类付费卡列表 并标记为已读
     * 包括会员卡 和 随享卡
     * @param userId
     * @param size
     * @return
     * @since 1.1.0
     */
    List<CardReMindDto> pullUnreadOfferedCards(Long userId, Integer size);

    /**
     * 将随享卡卡片标记为已读
     * @param userCardNos
     * @return
     * @since 1.1.0
     */
    int readOfferedCards(List<Long> userCardNos);

    /**
     * 修改suixiang_card_use 使用卡 的状态
     * 供退款系统使用
     * @param input
     * @return
     */
    BaseResponse modifySuiXiangCardUseStatus(ModifySuiXiangCardUseStatusInput input);


    /**
     * 根据购买记录id 查询组织信息
     * 供支付系统使用
     * @param input
     * @return
     */
    CardPurchaseDetailOutput getCardOrgByPurchaseId(CardPurchaseDetailInput input);


    /**
     * initDays 10
     * usedDay  0
     * availableDays 10
     * frozenDays 0
     *
     * 操作天数days
     * 冻结   frozenDays+ days     availableDays-days
     * 扣除  frozenDays-days       usedDay+days     usedDay=initDays 卡用完
     * 解冻 frozenDays-days       availableDays+days
     *
     * 冻结 用户随享卡卡使用详情中的累计天数上限
     * @param input
     *
     * @return
     */
    void freezeSuiXiangCardDays(UseSuiXiangCardDaysInput input)throws BusinessException;

    /**
     * 解冻 用户随享卡卡使用详情中的累计天数上限
     * @param input
     * @return
     */
    void unfreezeSuiXiangCardDays(UseSuiXiangCardDaysInput input)throws BusinessException;

    /**
     * 扣除 用户随享卡卡使用详情中的累计天数上限
     * @param input
     * @return
     */
    void deductFrozenSuiXiangCardDays(UseSuiXiangCardDaysInput input)throws BusinessException;

    /**
     * 先解冻 再冻结
     *  修改订单 或更换车型
     * @param input
     * @return
     */
    void unFreezeAndFreezeSuiXiangCardDays(UnFreezeAndFreezeSuiXiangCardDaysInput input)throws BusinessException;


    /**
     * 先扣除天数，再解冻天数
     *  修改订单 或更换车型
     * @param input
     * @return
     */
    void deductAndUnfreezeSuiXiangCardDays(DeductAndUnfreezeSuiXiangCardDaysInput input)throws BusinessException;


    /**
     *  查询用户 指定状态 购买记录
     * @param userId
     * @param paymentStatus
     * @return
     */
    List<SuiXiangCardPurchaseRecordDto> queryCardPurchaseInfoListByCondition(Long userId, int paymentStatus);

    QuerySuiXiangCardRentDayOutput querySuiXiangCardRentDay(QuerySuiXiangCardRentDayInput querySuiXiangCardRentDayInput);

    QuerySuiXiangCardPriceOutput querySuiXiangCardPrice(QuerySuiXiangCardPriceInput querySuiXiangCardPriceInput);

    Integer getPurchaseSuiXiangCardNum(String mobile,Long baseCardId);
}
