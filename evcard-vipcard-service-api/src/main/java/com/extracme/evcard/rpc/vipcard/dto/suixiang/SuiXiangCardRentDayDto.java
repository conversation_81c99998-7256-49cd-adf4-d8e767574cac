package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SuiXiangCardRentDayDto implements Serializable {

    /**
     * 随享卡基礎表id
     */
    private Long cardBaseId;

    /**
     * 随享卡租期id
     */
    private Long id;

    /**
     * 天数
     */
    private Integer rentDays;

    /**
     * 服务费，
     * 包含整备费、日租服务费、畅行服务费、加油服务费、加电服务费、夜间服务费。
     * 存储结构 日租服务费标志位,日租服务费划线价;  标志位0：不免费1：免费。
     */
    private String serviceFees;


    /**
     * 服务费总节省金额，单位元
     */
    private BigDecimal totalServiceFeesAmout;
    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Long createOperId;

    /**
     *
     */
    private String createOperName;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private Long updateOperId;

    /**
     *
     */
    private String updateOperName;

    /**
     *
     */
    private Integer isDeleted;

}
