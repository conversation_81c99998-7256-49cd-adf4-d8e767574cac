package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
public class QuerySuiXiangCardPurchaseRecordInput implements Serializable {
    private String orgCode; // 运营公司CODE
    private String cardName; // 随享卡名称
    private long cardId; // 随享卡ID
    private int purchaseMethod; // 购买方式：1-购买、2-赠送
    private String purchaseStartTime; // 购买开始时间，格式 yyyy-MM-dd
    private String purchaseEndTime; // 购买结束时间，格式 yyyy-MM-dd
    private int orderStatus; // 订单状态：1-待支付、2-已支付、3-已取消
    private String mobilePhone; // 用户手机号
    private String orderId; // 购买订单号

    private int pageNum; // 页码
    private int pageSize; // 每页大小
}
