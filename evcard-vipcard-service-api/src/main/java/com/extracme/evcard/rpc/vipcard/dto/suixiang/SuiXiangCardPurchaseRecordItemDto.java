package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
public class SuiXiangCardPurchaseRecordItemDto implements Serializable {
    private long id; // id
    private String userNameMask; // 用户姓名掩码
    private String userName; // 用户姓名
    private String mobilePhoneMask; // 用户手机号掩码
    private String mobilePhone; // 用户手机号
    private int purchaseMethod; // 购买方式：1-购买、2-赠送
    private int buyCount; // 购买张数
    private String orderId; // 购买订单号
    private String purchaseTime; // 购买时间，格式 yyyy-MM-dd HH:mm:ss
    private String payAmount; // 支付金额
    private int orderStatus; // 订单状态：1-待支付、2-已支付、3-已取消
    private long cardId; // 随享卡ID
    private String cardName; // 随享卡名称
    private String cityNames; // 可用区域，如“上海、昆山”，后端拼接好给出
    private String leaseTerm; // 租期
    private String modelNames; // 车型名称
    private String serviceFees; // 已选服务费
    private String orgName; // 运营公司
    private String updateOperName; // 操作人
    private String cdkey; // 兑换码
}
