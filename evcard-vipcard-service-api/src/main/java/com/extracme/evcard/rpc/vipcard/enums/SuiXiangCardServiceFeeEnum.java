package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Getter
@AllArgsConstructor
public enum SuiXiangCardServiceFeeEnum {
    ONE(1, "基础服务费", "基础"),
    TWO(2, "优享服务费", "优享"),
    THREE(3, "车辆整备费", "车辆"),
    FOUR(4, "加油服务费", "加油"),
    FIVE(5, "充电服务费", "充电"),
    SIX(6, "夜间服务费", "夜间");

    /**
     * 服务费类型(feeType)：1-基础服务费（原名 日租服务费）、2-优享服务费（原名 畅行服务费）、3-车辆整备费、4-加油服务费、5-充电服务费、6-夜间服务费；
     */
    private int feeType;

    /**
     * 描述
     */
    private String desc;

    /**
     * 简要描述
     */
    private String shortDesc;

    public static String getDescByFeeType(int feeType) {
        SuiXiangCardServiceFeeEnum[] array = SuiXiangCardServiceFeeEnum.values();
        for (SuiXiangCardServiceFeeEnum item : array) {
            if (item.feeType == feeType) {
                return item.desc;
            }
        }
        return null;
    }
    public static String getShortDescByFeeType(int feeType) {
        SuiXiangCardServiceFeeEnum[] array = SuiXiangCardServiceFeeEnum.values();
        for (SuiXiangCardServiceFeeEnum item : array) {
            if (item.feeType == feeType) {
                return item.shortDesc;
            }
        }
        return null;
    }
}
