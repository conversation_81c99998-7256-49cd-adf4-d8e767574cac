package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
public class GiveSuiXiangCardInput implements Serializable {
    private int listType; // 名单类型：1-手机号码、2-会员ID
    private String fileUrl; // 文件地址，阿里云oss的完整文件地址（是个excel）
    private long id; // 随享卡的配置id
    private OperatorDto operatorDto; // 操作日志信息
}
