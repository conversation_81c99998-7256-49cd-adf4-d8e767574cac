package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName: CardPurchaseRecordDto
 * @Author: wudi
 * @Date: 2021/5/18 14:20
 */
@Data
public class CardPurchaseRecordDto implements Serializable {

    private String activityName;

    private Long cardActivityId;

    private Long cardId;

    private Integer paymentStatus;

    /**
     * 购卡方式： 0 购买  1 赠送 2积分兑换
     */
    private Integer issueType;

    private Integer quantity;

    private String payTime;

    private Double realAmount;

    private String outTradeSeq;

    private String userCardNo;

    private Date startTime;

    private Date endTime;

    private String orderSeq;

    private Integer discount;

    private String planCancelTime;

    private String cancelTime;

    private Long purchaseId;

    /**
     * 合并买卡来源 0单独&后付订单 1预付订单
     */
    private Integer mergePayOrigin = 0;

    private Date createTime;
}
