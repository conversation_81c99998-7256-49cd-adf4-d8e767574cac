package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class DeductFrozenDiscountAmountDto implements Serializable {


    /**
     * 会员卡号
     */
    private Long userCardNo;

    /**
     * 申请冻结折扣的时间
     */
    private Date freezeTime;

    /**
     * 申请扣除的折扣金额
     */
    private BigDecimal amount;

    private String orderSeq;
}
