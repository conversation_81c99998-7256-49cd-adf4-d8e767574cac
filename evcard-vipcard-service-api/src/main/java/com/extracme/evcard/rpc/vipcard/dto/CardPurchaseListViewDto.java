package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/28
 */
@Data
public class CardPurchaseListViewDto implements Serializable {
    /**
     * 购买记录ID
     */
    private Long id;

    /**
     * 购买订单号
     */
    private String orderSeq;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 销售活动ID
     */
    private Long cardActivityId;

    /**
     * 卡片ID
     */
    private Long cardId;

    /**
     * 用户卡类别： 1企业折扣卡 2企业个人卡 3付费会员卡
     */
    private Integer cardGroup;

    /**
     * -1已取消 0待支付 1已支付
     */
    private Integer paymentStatus;

    private String paymentStatusDesc;

    /**
     * 购卡方式： 0 购买  1 赠送 2积分兑换
     */
    private Integer issueType;

    /**
     * 购卡条件： 0 无  1 学生卡
     */
    private Integer purchaseType;

    /**
     * 卡片类型名称
     */
    private String issueTypeDesc;
    /**
     * 卡片类型名称
     */
    private String purchaseTypeDesc;


    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 支付时间(购买时间)
     */
    private Date payTime;

    /**
     * 支付时间(购买时间)， yyyyMMddHHmmss
     */
    private String purchaseTime;

    /**
     * 订单金额
     */
    private Double realAmount;

    /**
     * 交易记录编号
     */
    private String outTradeSeq;

    /**
     * 对应卡号，实际编号
     */
    private String userCardNo;

    /**
     * 对应卡号，显示用
     */
    private String userCardNoDesc;

    private Integer status;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    /**
     * 产品线名称，逗号分隔
     */
    private String rentMethodNames;

    /**
     * 可用车型（多个车型名称，以,分割）
     */
    private String vehicleModelNames;

    /**
     * 可用区域（多个城市名称，以,分割）
     */
    private String cityNames;

    /**
     * 卡片：卡片名称
     */
    private String cardName;

    /**
     * 卡片：卡片类型 0周卡 1月卡 季卡
     */
    private Integer cardType;

    /**
     * 卡片类型名称
     */
    private String cardTypeDesc;

    /**
     * 卡片：卡片配置信息-可用区域限制
     */
    private String cityLimit;

    /**
     * 卡片：卡片配置信息-产品线限制
     */
    private String rentMethod;

    private String orgId;

    private String orgName;

    /**
     * 发放人（赠送与积分兑换时，使用）
     * @since 1.1.0
     */
    private String issueUser;
}
