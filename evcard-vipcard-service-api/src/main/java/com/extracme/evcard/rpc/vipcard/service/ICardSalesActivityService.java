package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.vipcard.dto.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 付费会员卡
 * 销售活动服务
 */
public interface ICardSalesActivityService {

    /**
     * 根据城市查询开始预售/已上架的会员卡购买活动
     * @param pkId 用户id，非必填
     * @param cityName  城市名称，必填
     * @return
     */
    List<EffectiveActivityCardInfoDto> getEffectiveActivityCardList(Long pkId, String cityName);

    /**
     * 查询会员卡购买活动详情
     * @param pkId
     * @param activityId
     * @return
     */
    EffectiveActivityCardDetailDto getEffectiveActivityCardDetail(Long pkId, Long activityId);

    /**
     * 获取订单可购买使用的会员卡活动（未兼容门店订单，门店不使用此接口）
     * @param orderSeq 订单号
     * @param rentAmount 可折扣租金
     * @return
     * TODO 确认此接口不需兼容门店订单
     */
    List<CanUseActivityCardInfoDto> getEffectiveActivityCardByOrder(String orderSeq, BigDecimal rentAmount);

    /**
     * 活动id查询
     * @param activityId
     * @return
     */
    CardActivityConfigDto queryActivityBuId(Long activityId);

    /**
     * 根据用车条件获取可购买使用的会员卡活动（付费会员卡）
     * (兼容门店)
     * @param input
     * @return
     */
    List<CanUseActivityCardInfoDto> getEffectiveActivityCard(GetEffectiveActivityCardInput input);
}
