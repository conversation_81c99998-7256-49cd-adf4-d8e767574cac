package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
@Getter
@AllArgsConstructor
public enum SuiXiangCardConfigOperateTypeEnum {
    ADD(0, "创建"),
    MODIFY(1, "修改"),
    DELETE(2, "删除"),
    RELEASE(3, "发布(审核通过)"),
    ONLINE(4, "上线(自动上线)"),
    OFFLINE(5, "下线(自动下线)"),
    DISABLE(6, "禁用"),
    RECOVERY(7, "恢复"),
    GIVE(8, "赠送");

    /**
     * 操作类型
     */
    private int operateType;

    /**
     * 描述
     */
    private String desc;
}
