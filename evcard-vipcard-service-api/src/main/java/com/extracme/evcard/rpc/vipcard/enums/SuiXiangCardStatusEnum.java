package com.extracme.evcard.rpc.vipcard.enums;

import java.io.Serializable;

/**
 * 卡状态：1：生效中 2 已过期（过期自动失效）  3 生效中-已冻结(人工发起退款) 4：已退卡 5：已作废 6：过期已冻结(人工发起退款后过期)  7已使用  8自动已冻结（未使用卡，自动发起退款，未到账）
 */
public enum SuiXiangCardStatusEnum implements Serializable {

    /**
     * 生效中
     */
    EFFECTIVE(1, "生效中"),
    /**
     * 已过期
     */
    EXPIRED(2, "已过期"),

    /**
     * 生效中-已冻结
     */
    FROZENED(3, "生效中-已冻结"),

    /**
     * 已退卡
     */
    RETURN(4, "已退卡"),

    /**
     * 已作废
     */
    DISCARD(5, "已作废"),

    /**
     * 过期已冻结
     */
    EXPIRED_FROZENED(6, "过期已冻结"),

    /**
     * 已使用
     */
    USED(7, "已使用"),

    /**
     * 自动已冻结
     */
    AUTO_FROZENED(8, "自动已冻结"),
    ;

    SuiXiangCardStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    /**
     * 状态码
     */
    private Integer status;
    /**
     * 描述
     */
    private String desc;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
