package com.extracme.evcard.rpc.vipcard.dto;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class FreezeDiscountAmountDto implements Serializable {

    /**
     * 会员卡号
     */
    private Long userCardNo;

    /**
     * 申请冻结时间
     */
    private Date freezeTime;

    /**
     * 申请冻结的折扣金额
     */
    private BigDecimal amount;

    private String orderSeq;

}
