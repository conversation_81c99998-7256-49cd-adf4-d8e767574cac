package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName: UserCardDiscountDetail
 * @Author: wudi
 * @Date: 2021/5/18 10:24
 */
@Data
public class UserCardDiscountDetail implements Serializable {

    private String startTime;

    private String endTime;

    private BigDecimal totalDiscountAmount;

    private BigDecimal discountAmount;
}
