package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.vipcard.dto.QueryAvailableCardInfoDto;
import lombok.Data;

import java.io.Serializable;

@Data
public class SuiXiangQueryAvailableCardInfoDto extends QueryAvailableCardInfoDto implements Serializable {

    // 随想卡天数
    int days;

    // 已用天数
    int usedDays;

    /**
     * 会员卡类别：1企业会员卡 2企业个人卡 3付费会员卡 4:随享卡
     */
    private Integer cardGroup;
}
