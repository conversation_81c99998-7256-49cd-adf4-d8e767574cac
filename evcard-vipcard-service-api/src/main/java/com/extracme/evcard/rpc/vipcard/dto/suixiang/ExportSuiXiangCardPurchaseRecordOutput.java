package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.google.protobuf.ByteString;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/16
 */
@Data
public class ExportSuiXiangCardPurchaseRecordOutput implements Serializable {
    private BaseResponse baseResponse; // 基础应答体
    private String fileUrl; // 完整的阿里云文件地址，可以直接链接下载
}
