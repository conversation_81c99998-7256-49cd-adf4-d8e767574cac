package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.pay.PayOrderBo;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.pay.PayOrderDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.pay.PaySuiXiangCardPurchaseDto;

import java.util.List;

public interface ISuiXiangCardTradeService {

    /**
     * 卡片购买
     * 创建支付订单
     * @param input
     * @param optUser
     * @return
     * @throws BusinessException
     * @since 1.3.0
     * @remark 此接口扣减活动库存
     */
    PurchaseSuiXiangCardRecordInfo createSuiXiangCardOrder(PurchaseSuiXiangCardDto input, OperatorDto optUser) throws BusinessException;

    /**
     * 卡片购买
     * 去支付
     * @param input
     * @param optUser
     * @return
     * @throws BusinessException
     * @since 1.3.0
     * @remark 此接口扣减活动库存
     */
    PayOrderBo payeSuiXiangCardOrder(PayOrderDto input, OperatorDto optUser) throws BusinessException;

    /**
     * 取消订单
     * @return
     * @throws BusinessException
     * @since 2.2.4
     * @remark
     */
    void cancelOrder(Long purchaseId, OperatorDto optUser) throws BusinessException;



    /**
     * 购卡成功后回调
     * @param purchaseId
     * @throws BusinessException
     * @remark
     */
    PaySuiXiangCardPurchaseDto paySuiXiangCardPurchaseCallBack(Long purchaseId) throws BusinessException;


    /**
     *  实际发卡操作
     * @param input
     * @param checkActivity
     * @param optUser
     * @return
     * @throws BusinessException
     */
    public List<Long> issueCard(CreateCardDto input, boolean checkActivity, OperatorDto optUser) throws BusinessException;



}
