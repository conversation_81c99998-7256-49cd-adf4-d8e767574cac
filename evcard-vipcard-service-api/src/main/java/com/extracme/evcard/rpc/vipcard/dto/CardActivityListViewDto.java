package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/28
 */
@Data
public class CardActivityListViewDto extends CardActivityConfigDetailDto {
    /**
     * 操作人信息
     */
    private String operatorName;

    /**
     * 操作人所属机构
     */
    private String operatorOrgName;

    /**
     * 产品线名称，逗号分隔
     */
    private String rentMethodNames;

    /**
     * 产品线大类名称，逗号分隔
     */
    private String rentMethodGroupNames;

    /**
     * 产品线大类，逗号分隔
     */
    private String rentMethodGroup;

    /**
     * 可用车型（多个车型名称，以,分割）
     */
    private String vehicleModelNames;

    /**
     * 可用区域（多个城市名称，以,分割）
     */
    private String cityNames;

    /**
     * 卡片：卡片名称
     */
    private String cardName;

    /**
     * 卡片：卡片类型 0周卡 1月卡 季卡
     */
    private Integer cardType;

    /**
     * 卡片：享有折扣，1~99整数
     */
    private Integer discount;

    /**
     * 卡片：单笔订单最大抵扣金额，单位：元
     */
    private BigDecimal maxValue;

    /**
     *卡片：订单时长限制，单位分钟
     */
    private Integer durationLimit;

    /**
     * 卡片：卡片配置信息-可用区域限制
     */
    private String cityLimit;

    /**
     * 卡片：卡片配置信息-车型限制
     */
    private String vehicleModelLimit;

    /**
     * 卡片：卡片配置信息-车型限制
     */
    private String goodsModelId;

    /**
     * 卡片：卡片配置信息-产品线限制
     */
    private String rentMethod;
}
