package com.extracme.evcard.rpc.vipcard.service;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.*;

/**
 * 随享卡 控台配置相关服务
 * <AUTHOR>
 * @date 2023/1/3
 */
public interface ISuixiangCardConfigService {

    AddSuiXiangCardOutput addSuiXiangCard(AddSuiXiangCardInput addSuiXiangCardInput);

    BaseResponse modifySuiXiangCard(ModifySuiXiangCardInput modifySuiXiangCardInput);

    BaseResponse onlineSuiXiangCard(OnlineSuiXiangCardInput onlineSuiXiangCardInput);

    BaseResponse offlineSuiXiangCard(OfflineSuiXiangCardInput offlineSuiXiangCardInput);

    QuerySuiXiangCardListOutput querySuiXiangCardList(QuerySuiXiangCardListInput querySuiXiangCardListInput);

    QuerySuiXiangCardDetailOutput querySuiXiangCardDetail(QuerySuiXiangCardDetailInput querySuiXiangCardDetailInput);

    // 赠送随享卡（excel)
    GiveSuiXiangCardOutput giveSuiXiangCard(GiveSuiXiangCardInput giveSuiXiangCardInput);

    QueryGiveSuiXiangCardOutput queryGiveSuiXiangCard(QueryGiveSuiXiangCardInput queryGiveSuiXiangCardInput);

    QuerySuiXiangCardPurchaseRecordOutput querySuiXiangCardPurchaseRecord(QuerySuiXiangCardPurchaseRecordInput querySuiXiangCardPurchaseRecordInput);

    ExportSuiXiangCardPurchaseRecordOutput exportSuiXiangCardPurchaseRecord(ExportSuiXiangCardPurchaseRecordInput exportSuiXiangCardPurchaseRecordInput);

    SearchOperateLogOutput searchOperateLog(SearchOperateLogInput searchOperateLogInput);

    // 赠送随享卡（非excel)
    SendSuiXiangCardOutput sendSuiXiangCard(SendSuiXiangCardInput sendSuiXiangCardInput);

}
