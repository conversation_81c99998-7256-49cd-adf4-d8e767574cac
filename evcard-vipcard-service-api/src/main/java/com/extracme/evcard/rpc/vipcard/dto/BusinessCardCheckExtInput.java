package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BusinessCardCheckExtInput {
    /**
     * 机构ID
     */
    private String agencyId;

    /**
     * 会员ID
     */
    private String authId;

    /**
     * 未折扣时的应收金额（根据该金额判断单笔订单金额上限）
     */
    private BigDecimal amount;

    /**
     * 用车单价
     */
    private BigDecimal unitPrice;

    /**
     * 活动类型
     * 1:普通
     */
    private Integer activityType;
}
