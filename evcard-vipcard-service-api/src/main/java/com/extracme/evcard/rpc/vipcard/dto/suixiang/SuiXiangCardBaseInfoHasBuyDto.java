package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 随享卡  购卡详情页 基础信息(已经发起购买，待支付或已支付)
 *
 */
@Data
public class SuiXiangCardBaseInfoHasBuyDto  extends SuiXiangCardBaseInfoForSalesDto implements Serializable {

    /**
     * 支付标记 1已支付 2待支付
     */
    private Integer payFlag;


    //待支付情况下
    /**
     * 剩余支付时间 mmss
     */
    private String remainPayTime;


    /**
     * 支付订单号
     */
    private String payOrderNO;



    // 已支付情况下
    /**
     * 随享卡使用状态  卡状态：1：生效中 2 已过期（过期自动失效）  3 生效中-已冻结 4：已退卡 5：已作废 6：过期已冻结  7已使用  8自动已冻结
     */
    private Integer cardUseStatus;

    /**
     *  赠送标记 1是 2否
     */
    private Integer sendStatus;

    /**
     * 随享卡卡面 有效期描述
     * 例（退卡时间 2022年12月1日 12:00、作废时间 2022年12月1日 12:00、有效期至 2022年12月1日 12:00、购买后6个月有效）
     */
    private String cardDateDesc;

    /**
     *  购买的张数
     */
    private Integer cardNum;

    /**
     *  购卡方式： 1购买 2 赠送 3兑换
     */
    private Integer issueType;


    /**
     * issueType = 1 时，返回
     * 单张卡 价格
     */
    private String singleCardPrice;

    /**
     * issueType = 3 时，返回
     * 兑换码用途：活动赠送/第三方售卖
     *  1=活动赠送，2=第三方售卖
     */
    private Integer cdkPurpose;

    /**
     * issueType = 3 时，返回
     * 第三方售卖价
     */
    private String thirdSalesPrice;


}
