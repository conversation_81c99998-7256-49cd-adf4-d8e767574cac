package com.extracme.evcard.rpc.vipcard.enums;


public enum CardActivityOperateEnum {
    /**
     * 创建
     */
    CREATE(0),
    /**
     * 更新
     */
    UPDATE(1),
    /**
     * 删除
     */
    DELETE(2),
    /**
     * 审核通过(发布)
     */
    PUBLISH(3),
    /**
     * 启动/上架/启用
     */
    START(4),
    /**
     * 停止/下架/禁用
     */
    STOP(5),
    /**
     * 暂停，预留暂未使用
     */
    SUSPEND(6),
    /**
     * 恢复，预留暂未使用
     */
    RESUME(7),
    /**
     * 执行导入发卡
     */
    SEND_CARD(8);

    CardActivityOperateEnum(int value){
        this.value = value;
    }

    /**
     * 操作类别
     */
    private int value;

    public int getValue(){
        return this.value;
    }
}
