package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SuiXiangCardPriceDto implements Serializable {

    /**
     *  随享卡价格id
     */
    private Long id;

    private Long cardBaseId;

    /**
     */
    private Long cardRentId;

    /**
     * 实际售价，单位：元
     */
    private BigDecimal salesPrice;

    /**
     *划线价格，单位：元
     */
    private BigDecimal underlinePrice;

    /**
     *车型组
     */
    private String carModelGroup;

    /**
     * 可用车型id（多个用逗号分割）
     */
    private String carModelIds;

    /**
     * 该类型销量
     */
    private Integer sales;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Long createOperId;

    /**
     *
     */
    private String createOperName;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private Long updateOperId;

    /**
     *
     */
    private String updateOperName;

    /**
     *
     */
    private Integer isDeleted;
}
