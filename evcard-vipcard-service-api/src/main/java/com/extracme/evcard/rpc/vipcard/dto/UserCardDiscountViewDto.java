package com.extracme.evcard.rpc.vipcard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 周期内剩余额度
 * <AUTHOR>
 * @Discription
 * @date 2021/5/18
 */
@Data
public class UserCardDiscountViewDto implements Serializable {
    /**
     * 记录id
     */
    private Long id;

    /**
     * 用户卡id
     */
    private Long userCardNo;

    /**
     * 周期起始时间
     */
    private Date startTime;

    /**
     * 周期结束时间
     */
    private Date endTime;

    /**
     * 周期内累计折扣上限
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 周期内已使用折扣
     */
    private BigDecimal discountAmount;

    /**
     * 周期内冻结中的金额
     * @remark since 1.4.0
     */
    private BigDecimal frozenAmount;

    /**
     * 周期内剩余折扣额度
     */
    private BigDecimal balanceDiscount;
}
