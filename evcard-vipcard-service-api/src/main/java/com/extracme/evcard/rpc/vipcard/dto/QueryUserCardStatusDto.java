package com.extracme.evcard.rpc.vipcard.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class QueryUserCardStatusDto implements Serializable {

    /**
     * -1没有任何卡 0:无有效 1：即将过期 2：生效中
     */
    private Integer cardStatus=0;

    private BigDecimal totalDiscountAmount;

    /**
     * 待支付购卡账单数量
     */
    private Integer waitPayCardNum = 0;

    /**
     * 未购卡状态下 取消账单数量
     */
    private Integer cancelCardNum = 0;

    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    public Integer getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(Integer cardStatus) {
        this.cardStatus = cardStatus;
    }

    public Integer getWaitPayCardNum() {
        return waitPayCardNum;
    }

    public void setWaitPayCardNum(Integer waitPayCardNum) {
        this.waitPayCardNum = waitPayCardNum;
    }

    public Integer getCancelCardNum() {
        return cancelCardNum;
    }

    public void setCancelCardNum(Integer cancelCardNum) {
        this.cancelCardNum = cancelCardNum;
    }
}
