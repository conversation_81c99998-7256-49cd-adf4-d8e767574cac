package com.extracme.evcard.rpc.vipcard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Getter
@AllArgsConstructor
public enum SuixiangCardValidDaysTypeEnum {
    ONE_MON(1, "一个月"),
    TWO_MON(2, "两个月"),
    THREE_MON(3, "三个月"),
    SIX_MON(6, "六个月"),
    ONE_YEAR(12, "一年");

    /**
     * 卡片有效期天数类型 ：1-一个月、2-两个月、3-三个月、6-六个月、12-一年
     */
    private int validDaysType;

    /**
     * 描述
     */
    private String desc;

    public static String getDescByType(int validDaysType) {
        SuixiangCardValidDaysTypeEnum[] array = SuixiangCardValidDaysTypeEnum.values();
        for (SuixiangCardValidDaysTypeEnum item : array) {
            if (item.validDaysType == validDaysType) {
                return item.desc;
            }
        }
        return null;
    }

    public static boolean isInValid(int validDaysType) {
        return getDescByType(validDaysType) == null;
    }
}
