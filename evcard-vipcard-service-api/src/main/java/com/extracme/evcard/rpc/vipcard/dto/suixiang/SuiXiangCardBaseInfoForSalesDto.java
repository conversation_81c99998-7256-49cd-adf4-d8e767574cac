package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 随享卡  购卡详情页 基础信息
 *
 */
@Data
public class SuiXiangCardBaseInfoForSalesDto implements Serializable {

    /**
     * 基础表id
     */
    private Long cardId;

    /**
     * 随想卡名称
     */
    private String cardName;

    /**
     * 卡片有效期天数类型 ：1-一个月、2-两个月、3-三个月、6-六个月、12-一年
     */
    private Integer validDaysType;

    /**
     * 销量
     */
    private Integer saleNum;

    /**
     * 使用限制 例（可用城市：限制取车门店所属城市）
     */
    private String cityLimitDesc;

    /**
     * 活动有效期 例（2021年11月1日至2021年12月1日）
     */
    private String activityValidDesc;

    /**
     * 使 随享卡有效期 例（购买立即生效，生效后31天过期）
     */
    private String cardValidDesc;

    /**
     * 使用规则
     */
    private String rules;


    /**
     * 未购卡时-状态
     *  1：可以购买 2：用户有生效的随享卡不可以买 3：未设置开售提醒 4：已设置开售提醒 5:已达购买上限
     */
    private Integer buyCardStatus;


    /**
     * 是否已设置开售提醒 1:是 2否
     */
    private Integer saleRemind;

    /**
     * 上架时间 格式：yyyyMMddHHmmss
     */
    private String startTime;

    /**
     * 随享卡涉及所有车型信息
     *
     */
    List<CarModelInfoDto> carList;


    /**
     * 卡面url
     */
    private String backUrl;

    /**
     * 不可用日期
     */
    private List<UnavailableDate> unavailableDate;

    /**
     * 不可用日期 for service
     */
    private List<UnavailableDate> unavailableDateForServer;

    //节假日是否可用 1:可用  2：不可用
    private int holidayAvailable;

    /**
     * 购买上限数，0代表没有购买张数限制
     */
    private Integer purchaseLimitNum;

    /**
     * 当前库存
     */
    private Integer stock;

    /**
     * 是否所有城市可用 1:是 其他:否
     */
    private int allCityFlag;
}
