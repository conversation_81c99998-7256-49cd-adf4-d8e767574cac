package com.extracme.evcard.rpc.vipcard.dto.suixiang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SuiXiangCardBaseDto implements Serializable {

    /**
     * 随享卡租期id
     */
    private Long id;

    /**
     * 随想卡名称
     */
    private String cardName;

    /**
     * 运营机构
     */
    private String orgId;

    /**
     * 可用区域：（多个id以逗号分隔）
     */
    private String cityId;

    /**
     * 初始库存
     */
    private Long initStock;

    /**
     * 上架时间，精确到小时, 格式yyyyMMddHHmm
     */
    private String saleStartTime;

    /**
     * 下架时间，精确到小时, 格式yyyyMMddHHmm
     */
    private String saleEndTime;

    /**
     * 预告时间，  格式yyyyMMddHHmm
     */
    private String advanceNoticeTime;

    /**
     * 有效期
     */
    private Integer validDays;

    /**
     * 使用规则
     *
     */
    private String rules;


    /**
     * 当前库存
     */
    private Integer stock;

    /**
     * 总销量
     */
    private Integer totalSales;

    /**
     * 展示标记  1：对外展示 0：不对外展示
     */
    private Integer displayFlag;

    /**
     * 单订单时长（当前城市平均订单时长，单位是天）
     */
    private String singleOrderDuration;

    /**
     * 卡面样式 0：自定义  1：样式1 2：样式2 3：样式3
     */
    private Integer styleType;

    /**
     *卡面背景图片
     */
    private String backUrl;


    /**
     *0待提交 1待上架 2已上架(开放购买) 3已下架
     */
    private Integer cardStatus;


    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Long createOperId;

    /**
     *
     */
    private String createOperName;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private Long updateOperId;

    /**
     *
     */
    private String updateOperName;

    /**
     *
     */
    private Integer isDeleted;
}
